---
description: 
globs: 
alwaysApply: true
---
# 🧠 Smart Agent Development Principles (Autonomy-First)

## General Principles

1. **DRY++ (Context-Aware Reuse)**  
   Go beyond avoiding repetition — learn from prior solutions.  
   ⮕ Detect similar patterns and suggest or auto-apply existing logic across the codebase.

2. **TDD-Driven Exploration**  
   Prioritize writing tests before code.  
   ⮕ When missing specs, proactively ask the user for examples or infer behavior from related modules.

3. **Self-Diagnosing Systems**  
   Embed fail-fast diagnostic checks and recovery suggestions.  
   ⮕ On error, identify the cause, suggest solutions, and optionally offer refactor patches.

4. **Autonomous Refactoring (Structure ≫ Patching)**  
   Don’t pile on fixes — structurally improve problematic logic.  
   ⮕ Recognize bloated files or responsibilities and split/refactor accordingly.

5. **Iterate with <PERSON><PERSON><PERSON> (Lean-MVP)**  
   Deliver the fastest functional prototype, then iterate.  
   ⮕ Collect real feedback or detect usage patterns to guide improvements.

6. **Split for Clarity, Not Just Line Count**  
   Organize logic by domain responsibility, not just file size.  
   ⮕ Separate responsibilities horizontally (features, languages, configs) as needed.

---

## MCP Tool Usage Principles

> ⚠️ **Important Distinction**  
> The `browser-tools` MCP and the `playwright` MCP are **independent and non-interoperable**.  
> They have different commands, engines, and use cases:
> - `browser-tools` is best for debugging (e.g., console logs, screenshot, selected elements).
> - `playwright` is for real browser automation (e.g., click text, fill forms, evaluate JS).
>  
> Do not mix both in a single action flow unless the session boundaries are clearly managed.

1. **Tool-Aware Reasoning**  
   Use each MCP based on its strength.  
   ⮕ Prefer `browser-tools` for debugging or visual analysis.  
   ⮕ Use `playwright` for simulating real user interaction or E2E testing.

2. **Active Stack Discovery**  
   Infer the tech stack automatically from code/configs.  
   ⮕ Detect frameworks, schema, tailwind classes, etc., before executing transformations.

3. **Supabase-Aware Actions**  
   Always call `get_schemas` before generating queries.  
   ⮕ Avoid assumptions and dynamically adapt to structure.

4. **Smart Notification Handling**  
   Only notify the user when action is required.  
   ⮕ Use `apple-notifier` with `prompt_user` or `speak` to confirm steps or surface important findings.

5. **Autonomous Documentation Handling**  
   Prefer in-context explanations. If something is unclear, suggest an intelligent guess or ask minimal clarification from the user.

---

## Adaptive Behavior Rules

1. **Memory Consolidation**  
   Combine overlapping task memories to reduce duplication and improve inference quality.

2. **Goal-Oriented Memory Creation**  
   Create new memories only when:
   - Context or domain is new
   - Precision requires it
   - Prior memory would be misleading

3. **Reasoning Before Acting**  
   Always simulate or verbalize a thought process before executing a task.  
   ⮕ Use `prompt_user` when decisions affect critical data or cannot be undone.

---

## Application Specifics

### Website Migration & Improvements
- Clone the website from `baberrih.ma`, maintaining its aesthetics and original logo.
- Migrate from Astro+Qwik to a Svelte boilerplate.
- Reference the GitHub repo `baberrih-frontend`.
- Use `firecrawl` MCP to clone and extract relevant UI and structural patterns.
- Apply DRY principles to reduce redundancy and improve maintainability.
- Address performance issues, especially loading lag.
- Improve UI/UX across all pages with a mobile-first, responsive approach.
- Ensure all 'Book Now' buttons redirect to the reservations page.
- Add the logo to the navigation bar and footer.

### Cloudbeds Integration
- Integrate Cloudbeds reservations at `/cloudbeds/reservations`.
- Style this section to match `baberrih.ma`, potentially using `firecrawl` again.
- Improve the UI/UX specifically for the accommodation/reservations page.

### Blog Feature
- Add a blog feature that pulls content from an external API.
- The blog is hosted separately and exposes content via a `curl`-accessible API endpoint.
- Use `PUBLIC_BLOG_API_URL` as an environment variable to reference the blog API.

### Development Preferences
- Keep the dev server running to allow real-time monitoring of terminal output.

### Tailwind CSS
- Tailwind 4.0 is used with pure CSS and **no config file**.

### User Notification
- Notify the user via `apple-mcp` using `send_notification` or `prompt_user` once a task is completed.

---

## Example Behaviors

- 🧠 Before writing Supabase queries, inspect schemas with `get_schemas`.
- 🕸️ For UI bugs, use `getConsoleErrors`, `takeScreenshot`, and `getSelectedElement` via `browser-tools`.
- 🎯 For form automation or click flows, use `playwright` (e.g., `browser_fill`, `browser_click_text`).
- 📣 For alerts, confirmations, or progress updates, use `apple-notifier` (`speak`, `send_notification`).

---

> ✅ These principles ensure a truly autonomous, safe, and goal-aligned agent that leverages the full power of your current MCP stack effectively.
