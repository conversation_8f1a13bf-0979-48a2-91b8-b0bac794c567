-- Create facilities table
CREATE TABLE public.facilities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  site_id UUID NOT NULL REFERENCES public.sites(id),
  name TEXT NOT NULL,
  slug TEXT NOT NULL,
  description TEXT,
  features <PERSON><PERSON><PERSON><PERSON> DEFAULT '[]'::jsonb,
  images JSONB DEFAULT '[]'::jsonb,
  videos JSONB DEFAULT '[]'::jsonb,
  hours TEXT,
  restrictions TEXT,
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create activities table
CREATE TABLE public.activities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  site_id UUID NOT NULL REFERENCES public.sites(id),
  name TEXT NOT NULL,
  slug TEXT NOT NULL,
  description TEXT,
  features <PERSON><PERSON><PERSON><PERSON> DEFAULT '[]'::jsonb,
  images JSONB DEFAULT '[]'::jsonb,
  videos JSONB DEFAULT '[]'::jsonb,
  schedule TEXT,
  duration TEXT,
  price_info JSONB DEFAULT '{}'::jsonb,
  location TEXT,
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create views for easier querying
CREATE VIEW public.facilities_with_site_info AS
SELECT 
  f.*,
  s.name AS site_name,
  s.domain AS site_domain
FROM 
  public.facilities f
JOIN 
  public.sites s ON f.site_id = s.id;

CREATE VIEW public.activities_with_site_info AS
SELECT 
  a.*,
  s.name AS site_name,
  s.domain AS site_domain
FROM 
  public.activities a
JOIN 
  public.sites s ON a.site_id = s.id;

-- Enable Row Level Security
ALTER TABLE public.facilities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activities ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for facilities
CREATE POLICY "Users can view facilities of sites they have access to"
  ON public.facilities
  FOR SELECT
  USING (
    has_site_access(site_id)
  );

CREATE POLICY "Admins can manage facilities"
  ON public.facilities
  FOR ALL
  USING (
    is_site_admin(site_id)
  );

-- Create RLS policies for activities
CREATE POLICY "Users can view activities of sites they have access to"
  ON public.activities
  FOR SELECT
  USING (
    has_site_access(site_id)
  );

CREATE POLICY "Admins can manage activities"
  ON public.activities
  FOR ALL
  USING (
    is_site_admin(site_id)
  );

-- Create triggers to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_facilities_updated_at
BEFORE UPDATE ON public.facilities
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_activities_updated_at
BEFORE UPDATE ON public.activities
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();
