-- Drop existing views if they exist
DROP VIEW IF EXISTS public.facilities_with_site_info;
DROP VIEW IF EXISTS public.activities_with_site_info;

-- Create the correct views with proper joins
CREATE VIEW public.facilities_with_site_info AS
SELECT 
  f.*,
  s.name AS site_name,
  s.domain AS site_domain
FROM 
  public.facilities f
JOIN 
  public.sites s ON f.site_id = s.id;

CREATE VIEW public.activities_with_site_info AS
SELECT 
  a.*,
  s.name AS site_name,
  s.domain AS site_domain
FROM 
  public.activities a
JOIN 
  public.sites s ON a.site_id = s.id;

-- Create a view that includes the relation mentioned in the error
CREATE VIEW public.facilities_with_site_info AS
SELECT 
  f.*,
  s.name AS site_name,
  s.domain AS site_domain,
  true AS has_site_info
FROM 
  public.facilities f
JOIN 
  public.sites s ON f.site_id = s.id;

-- Create a view that includes the relation mentioned in the error
CREATE VIEW public.activities_with_site_info AS
SELECT 
  a.*,
  s.name AS site_name,
  s.domain AS site_domain,
  true AS has_site_info
FROM 
  public.activities a
JOIN 
  public.sites s ON a.site_id = s.id;
