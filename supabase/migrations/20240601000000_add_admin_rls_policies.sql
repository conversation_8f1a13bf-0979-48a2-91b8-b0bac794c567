-- Habilitar Row Level Security (RLS) en todas las tablas relevantes
ALTER TABLE public.site_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_site_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.suites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.suite_cloudbeds_mapping ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reservations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_permissions ENABLE ROW LEVEL SECURITY;

-- Crear una función para verificar si un usuario es administrador de un sitio
CREATE OR REPLACE FUNCTION public.is_site_admin(site_id uuid)
RETURNS boolean AS $$
DECLARE
  is_admin boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM public.site_users su
    JOIN public.user_site_permissions usp ON su.id = usp.site_user_id
    WHERE su.user_id = auth.uid()
    AND su.site_id = is_site_admin.site_id
    AND usp.permission = 'admin'
  ) INTO is_admin;
  
  RETURN is_admin;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Crear una función para verificar si un usuario es propietario de un cliente
CREATE OR REPLACE FUNCTION public.is_client_owner(client_id uuid)
RETURNS boolean AS $$
DECLARE
  is_owner boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM public.client_permissions
    WHERE user_id = auth.uid()
    AND client_id = is_client_owner.client_id
    AND role = 'owner'
  ) INTO is_owner;
  
  RETURN is_owner;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Crear una función para verificar si un usuario tiene acceso a un sitio
CREATE OR REPLACE FUNCTION public.has_site_access(site_id uuid)
RETURNS boolean AS $$
DECLARE
  has_access boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM public.site_users
    WHERE user_id = auth.uid()
    AND site_id = has_site_access.site_id
  ) INTO has_access;
  
  RETURN has_access;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Políticas para site_users
CREATE POLICY "Los administradores pueden ver todos los usuarios de sitio"
  ON public.site_users
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1
      FROM public.site_users su
      JOIN public.user_site_permissions usp ON su.id = usp.site_user_id
      WHERE su.user_id = auth.uid()
      AND usp.permission = 'admin'
    )
  );

CREATE POLICY "Los usuarios pueden ver sus propios registros de sitio"
  ON public.site_users
  FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Los administradores pueden crear usuarios de sitio"
  ON public.site_users
  FOR INSERT
  WITH CHECK (
    is_site_admin(site_id)
  );

CREATE POLICY "Los administradores pueden actualizar usuarios de sitio"
  ON public.site_users
  FOR UPDATE
  USING (
    is_site_admin(site_id)
  );

CREATE POLICY "Los administradores pueden eliminar usuarios de sitio"
  ON public.site_users
  FOR DELETE
  USING (
    is_site_admin(site_id)
  );

-- Políticas para user_site_permissions
CREATE POLICY "Los administradores pueden ver todos los permisos de usuario"
  ON public.user_site_permissions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1
      FROM public.site_users su
      JOIN public.user_site_permissions usp ON su.id = usp.site_user_id
      WHERE su.user_id = auth.uid()
      AND usp.permission = 'admin'
    )
  );

CREATE POLICY "Los usuarios pueden ver sus propios permisos"
  ON public.user_site_permissions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1
      FROM public.site_users
      WHERE id = site_user_id
      AND user_id = auth.uid()
    )
  );

CREATE POLICY "Los administradores pueden gestionar permisos"
  ON public.user_site_permissions
  FOR ALL
  USING (
    EXISTS (
      SELECT 1
      FROM public.site_users su
      WHERE su.id = site_user_id
      AND is_site_admin(su.site_id)
    )
  );

-- Políticas para sites
CREATE POLICY "Los usuarios pueden ver los sitios a los que tienen acceso"
  ON public.sites
  FOR SELECT
  USING (
    has_site_access(id) OR
    is_client_owner(client_id)
  );

CREATE POLICY "Los propietarios de clientes pueden gestionar sitios"
  ON public.sites
  FOR ALL
  USING (
    is_client_owner(client_id)
  );

-- Políticas para suites
CREATE POLICY "Los usuarios pueden ver las suites de los sitios a los que tienen acceso"
  ON public.suites
  FOR SELECT
  USING (
    has_site_access(site_id)
  );

CREATE POLICY "Los administradores pueden gestionar suites"
  ON public.suites
  FOR ALL
  USING (
    is_site_admin(site_id)
  );

-- Políticas para suite_cloudbeds_mapping
CREATE POLICY "Los usuarios pueden ver los mapeos de suites de los sitios a los que tienen acceso"
  ON public.suite_cloudbeds_mapping
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1
      FROM public.suites
      WHERE id = suite_id
      AND has_site_access(site_id)
    )
  );

CREATE POLICY "Los administradores pueden gestionar mapeos de suites"
  ON public.suite_cloudbeds_mapping
  FOR ALL
  USING (
    EXISTS (
      SELECT 1
      FROM public.suites
      WHERE id = suite_id
      AND is_site_admin(site_id)
    )
  );

-- Políticas para reservations
CREATE POLICY "Los usuarios pueden ver las reservas de los sitios a los que tienen acceso"
  ON public.reservations
  FOR SELECT
  USING (
    has_site_access(site_id)
  );

CREATE POLICY "Los administradores pueden gestionar reservas"
  ON public.reservations
  FOR ALL
  USING (
    is_site_admin(site_id)
  );

-- Políticas para clients
CREATE POLICY "Los propietarios pueden ver sus clientes"
  ON public.clients
  FOR SELECT
  USING (
    is_client_owner(id)
  );

-- Políticas para client_permissions
CREATE POLICY "Los propietarios pueden ver los permisos de sus clientes"
  ON public.client_permissions
  FOR SELECT
  USING (
    is_client_owner(client_id)
  );

CREATE POLICY "Los propietarios pueden gestionar permisos de sus clientes"
  ON public.client_permissions
  FOR ALL
  USING (
    is_client_owner(client_id)
  );
