/**
 * Cloudbeds API Parameter Test
 *
 * Este script prueba diferentes combinaciones de parámetros para identificar
 * cuáles son necesarios para obtener datos de disponibilidad correctamente.
 */

import fetch from 'node-fetch';
import { config } from 'dotenv';

// Cargar variables de entorno
config();

// Configuración - Usar variables de entorno
const API_KEY = process.env.CLOUDBEDS_API_KEY; // Usar la API key del .env
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID; // Usar el property ID del .env

// Función para formatear fechas como YYYY-MM-DD
const formatDate = (date) => {
  return date.toISOString().split('T')[0];
};

/**
 * Prueba el endpoint getAvailableRoomTypes con diferentes combinaciones de parámetros
 */
async function testAvailabilityParameters() {
  console.log('\n=== Probando diferentes parámetros para getAvailableRoomTypes ===');

  // Generar fechas para la prueba
  const today = new Date();
  const startDate = formatDate(new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000));
  const endDate = formatDate(new Date(today.getTime() + 31 * 24 * 60 * 60 * 1000));

  // Definir diferentes combinaciones de parámetros para probar
  const parameterSets = [
    {
      name: 'Parámetros básicos',
      params: {
        propertyID: PROPERTY_ID,
        startDate: startDate,
        endDate: endDate
      }
    },
    {
      name: 'Con adultos y niños',
      params: {
        propertyID: PROPERTY_ID,
        startDate: startDate,
        endDate: endDate,
        adults: 2,
        children: 0
      }
    },
    {
      name: 'Con currency',
      params: {
        propertyID: PROPERTY_ID,
        startDate: startDate,
        endDate: endDate,
        currency: 'MAD' // Dirham marroquí
      }
    },
    {
      name: 'Con todos los parámetros',
      params: {
        propertyID: PROPERTY_ID,
        startDate: startDate,
        endDate: endDate,
        adults: 2,
        children: 0,
        currency: 'MAD',
        includeRoomTypeDetails: true
      }
    }
  ];

  // Probar cada conjunto de parámetros
  for (const paramSet of parameterSets) {
    console.log(`\n--- Probando: ${paramSet.name} ---`);

    const url = new URL('https://api.cloudbeds.com/api/v1.2/getAvailableRoomTypes');

    // Añadir parámetros a la URL
    Object.entries(paramSet.params).forEach(([key, value]) => {
      url.searchParams.append(key, value);
    });

    console.log(`URL: ${url.toString()}`);

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'x-api-key': API_KEY
        }
      });

      const data = await response.json();

      console.log('Código de respuesta:', response.status);

      if (data.success) {
        if (data.data && data.data.length > 0) {
          console.log(`Éxito! Se encontraron ${data.data.length} tipos de habitación disponibles`);
          console.log('Primer tipo de habitación:', data.data[0].roomTypeName);
          if (data.data[0].dates && data.data[0].dates.length > 0) {
            console.log(`Precio: ${data.data[0].dates[0].price || 'N/A'}`);
          }
        } else {
          console.log('No se encontraron habitaciones disponibles con estos parámetros');
        }
      } else {
        console.log('Error en la respuesta:', data.message || 'Sin mensaje de error');
      }
    } catch (error) {
      console.error('Error al realizar la solicitud:', error.message);
    }
  }
}

/**
 * Prueba el endpoint getRate con diferentes combinaciones de parámetros
 */
async function testRateParameters() {
  console.log('\n=== Probando diferentes parámetros para getRate ===');

  // Generar fechas para la prueba
  const today = new Date();
  const startDate = formatDate(new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000));
  const endDate = formatDate(new Date(today.getTime() + 31 * 24 * 60 * 60 * 1000));

  // Definir diferentes combinaciones de parámetros para probar
  const parameterSets = [
    {
      name: 'Parámetros básicos',
      params: {
        propertyID: PROPERTY_ID,
        startDate: startDate,
        endDate: endDate
      }
    },
    {
      name: 'Con ratePlanID',
      params: {
        propertyID: PROPERTY_ID,
        startDate: startDate,
        endDate: endDate,
        ratePlanID: 'default' // Intenta con 'default' o un ID específico
      }
    },
    {
      name: 'Con roomTypeID',
      params: {
        propertyID: PROPERTY_ID,
        startDate: startDate,
        endDate: endDate,
        roomTypeID: '' // Reemplaza con un ID real de tipo de habitación
      }
    },
    {
      name: 'Con currency',
      params: {
        propertyID: PROPERTY_ID,
        startDate: startDate,
        endDate: endDate,
        currency: 'MAD' // Dirham marroquí
      }
    }
  ];

  // Probar cada conjunto de parámetros
  for (const paramSet of parameterSets) {
    console.log(`\n--- Probando: ${paramSet.name} ---`);

    const url = new URL('https://api.cloudbeds.com/api/v1.2/getRate');

    // Añadir parámetros a la URL
    Object.entries(paramSet.params).forEach(([key, value]) => {
      url.searchParams.append(key, value);
    });

    console.log(`URL: ${url.toString()}`);

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'x-api-key': API_KEY
        }
      });

      const data = await response.json();

      console.log('Código de respuesta:', response.status);

      if (data.success) {
        if (data.data && Object.keys(data.data).length > 0) {
          console.log('Éxito! Se encontraron tarifas');
          const roomTypeIds = Object.keys(data.data);
          console.log(`Tipos de habitación con tarifas: ${roomTypeIds.length}`);

          // Mostrar la primera tarifa como ejemplo
          if (roomTypeIds.length > 0 && data.data[roomTypeIds[0]].length > 0) {
            console.log(`Ejemplo de tarifa para ${roomTypeIds[0]}: ${data.data[roomTypeIds[0]][0].rate}`);
          }
        } else {
          console.log('No se encontraron tarifas con estos parámetros');
        }
      } else {
        console.log('Error en la respuesta:', data.message || 'Sin mensaje de error');
      }
    } catch (error) {
      console.error('Error al realizar la solicitud:', error.message);
    }
  }
}

/**
 * Función principal para ejecutar todas las pruebas
 */
async function runTests() {
  console.log('=== Iniciando pruebas de parámetros de la API de Cloudbeds ===');

  // Probar diferentes parámetros para getAvailableRoomTypes
  await testAvailabilityParameters();

  // Probar diferentes parámetros para getRate
  await testRateParameters();

  console.log('\n=== Pruebas completadas ===');
  console.log('Revisa los resultados para identificar qué combinación de parámetros funciona mejor.');
}

// Ejecutar todas las pruebas
runTests().catch(error => {
  console.error('Error al ejecutar las pruebas:', error);
});
