import tailwindcss from "@tailwindcss/vite";
import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';

export default defineConfig({
	plugins: [sveltekit(), tailwindcss()],
	// Optimizaciones de rendimiento
	build: {
		// Reducir el tamaño del bundle
		minify: 'terser',
		terserOptions: {
			compress: {
				drop_console: true, // Eliminar console.log en producción
				drop_debugger: true
			}
		},
		// Dividir el código en chunks más pequeños
		rollupOptions: {
			output: {
				manualChunks: {
					'vendor': ['svelte', 'svelte/internal'],
					'ui': ['lucide-svelte'],
				}
			}
		},
		// Comprimir assets
		assetsInlineLimit: 4096, // Inlinear archivos pequeños (< 4kb)
	},
	// Optimizaciones para el servidor de desarrollo
	server: {
		hmr: {
			overlay: false // Desactivar overlay para mejorar rendimiento
		}
	},
	// Optimizaciones para CSS
	css: {
		devSourcemap: false
	}
});
