#!/usr/bin/env node

/**
 * Fixed Cloudbeds API Test Script
 * 
 * This script addresses the issues identified in the previous test:
 * 1. <PERSON><PERSON><PERSON> handles room type IDs
 * 2. Uses hardcoded IDs from <PERSON>'s email if needed
 * 3. Tests with specific room type IDs mentioned in documentation
 * 
 * Usage: node test/cloudbeds-fixed-test.js
 */

// Import required modules
const fetch = require('node-fetch');
const FormData = require('form-data');
require('dotenv').config();

// Configuration
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Known room type IDs from documentation
const KNOWN_ROOM_TYPE_IDS = [
  '653496', // Garden Deluxe (mentioned in docs)
  '653497', // Ocean Deluxe (mentioned in docs)
  '653498', // <PERSON> (mentioned in docs)
  '653499', // <PERSON> Junior (mentioned in docs)
  '650743'  // Used in test scripts
];

// Source IDs to test
const SOURCE_IDS = [
  's-1-1',  // Website/booking engine (from <PERSON>'s example)
  's-2-1',  // Website (alternative)
  's-5-1',  // Default Corporate Client
];

// Date ranges to test (all in the future)
const getDateRanges = () => {
  const ranges = [];
  const today = new Date();
  
  // 30 days in the future (1 night)
  const date1 = new Date(today);
  date1.setDate(today.getDate() + 30);
  const date2 = new Date(date1);
  date2.setDate(date1.getDate() + 1);
  ranges.push({
    startDate: formatDate(date1),
    endDate: formatDate(date2),
    description: '30 days in future (1 night)'
  });
  
  // 60 days in the future (2 nights)
  const date3 = new Date(today);
  date3.setDate(today.getDate() + 60);
  const date4 = new Date(date3);
  date4.setDate(date3.getDate() + 2);
  ranges.push({
    startDate: formatDate(date3),
    endDate: formatDate(date4),
    description: '60 days in future (2 nights)'
  });
  
  return ranges;
};

// Helper function to format dates as YYYY-MM-DD
function formatDate(date) {
  return date.toISOString().split('T')[0];
}

// Helper function for console output formatting
function logSection(title) {
  console.log('\n' + '='.repeat(80));
  console.log(`${title}`);
  console.log('='.repeat(80));
}

function logSubSection(title) {
  console.log('\n' + '-'.repeat(60));
  console.log(`${title}`);
  console.log('-'.repeat(60));
}

function logSuccess(message) {
  console.log(`✅ ${message}`);
}

function logError(message) {
  console.log(`❌ ${message}`);
}

function logInfo(message) {
  console.log(`ℹ️ ${message}`);
}

function logWarning(message) {
  console.log(`⚠️ ${message}`);
}

function logJson(data) {
  console.log(JSON.stringify(data, null, 2));
}

/**
 * Step 1: Get all room types for the property
 */
async function getRoomTypes() {
  logSection('STEP 1: GETTING AVAILABLE ROOM TYPES');
  
  try {
    const url = new URL(`${BASE_URL}/getRoomTypes`);
    url.searchParams.append('propertyID', PROPERTY_ID);
    
    logInfo(`Fetching room types from: ${url.toString()}`);
    
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'x-api-key': API_KEY
      }
    });
    
    const data = await response.json();
    
    if (data.success && data.data && data.data.length > 0) {
      logSuccess(`Found ${data.data.length} room types`);
      
      // Log room type details
      console.log('\nROOM TYPES:');
      console.log('-'.repeat(100));
      console.log('ID\t\tName\t\t\tMax Guests\tisPrivate\tRoom Type Class');
      console.log('-'.repeat(100));
      
      data.data.forEach(room => {
        console.log(`${room.roomTypeId || 'N/A'}\t${(room.roomTypeName || 'Unknown').padEnd(20)}\t${room.maxOccupancy || 'N/A'}\t\t${room.isPrivate ? 'Yes' : 'No'}\t\t${room.roomTypeClass || 'N/A'}`);
      });
      
      return data.data;
    } else {
      logError('No room types found or API error');
      console.log('API Response:');
      logJson(data);
      
      // Return known room type IDs as fallback
      logWarning('Using known room type IDs from documentation as fallback');
      return KNOWN_ROOM_TYPE_IDS.map(id => ({ 
        roomTypeId: id, 
        roomTypeName: `Room Type ${id}`,
        isPrivate: true
      }));
    }
  } catch (error) {
    logError(`Error fetching room types: ${error.message}`);
    
    // Return known room type IDs as fallback
    logWarning('Using known room type IDs from documentation as fallback');
    return KNOWN_ROOM_TYPE_IDS.map(id => ({ 
      roomTypeId: id, 
      roomTypeName: `Room Type ${id}`,
      isPrivate: true
    }));
  }
}

/**
 * Step 2: Check rate availability for each room type
 */
async function checkRateAvailability(roomTypes, dateRanges) {
  logSection('STEP 2: CHECKING RATE AVAILABILITY FOR EACH ROOM TYPE');
  
  const results = [];
  
  for (const roomType of roomTypes) {
    logSubSection(`Checking rates for: ${roomType.roomTypeName} (ID: ${roomType.roomTypeId})`);
    
    for (const dateRange of dateRanges) {
      logInfo(`Date range: ${dateRange.description} (${dateRange.startDate} to ${dateRange.endDate})`);
      
      try {
        const url = new URL(`${BASE_URL}/getAvailableRoomTypes`);
        url.searchParams.append('propertyID', PROPERTY_ID);
        url.searchParams.append('startDate', dateRange.startDate);
        url.searchParams.append('endDate', dateRange.endDate);
        
        if (roomType.roomTypeId) {
          url.searchParams.append('roomTypeID', roomType.roomTypeId);
        }
        
        logInfo(`Fetching availability from: ${url.toString()}`);
        
        const response = await fetch(url.toString(), {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'x-api-key': API_KEY
          }
        });
        
        const data = await response.json();
        
        if (data.success && data.data && data.data.length > 0) {
          logSuccess(`Found availability for ${roomType.roomTypeName}`);
          
          // Check if there are rates
          let hasRates = false;
          if (data.data[0].dates && data.data[0].dates.length > 0) {
            const rates = data.data[0].dates.filter(date => date.price && parseFloat(date.price) > 0);
            if (rates.length > 0) {
              hasRates = true;
              logSuccess(`Found rates: ${rates.map(r => r.price).join(', ')}`);
            }
          }
          
          if (!hasRates) {
            logWarning('No rates found for this room type and date range');
          }
          
          results.push({
            roomTypeId: roomType.roomTypeId,
            roomTypeName: roomType.roomTypeName,
            dateRange: dateRange,
            hasAvailability: true,
            hasRates: hasRates,
            data: data.data[0]
          });
        } else {
          logWarning(`No availability found for ${roomType.roomTypeName} in this date range`);
          results.push({
            roomTypeId: roomType.roomTypeId,
            roomTypeName: roomType.roomTypeName,
            dateRange: dateRange,
            hasAvailability: false,
            hasRates: false,
            data: null
          });
        }
      } catch (error) {
        logError(`Error checking availability: ${error.message}`);
        results.push({
          roomTypeId: roomType.roomTypeId,
          roomTypeName: roomType.roomTypeName,
          dateRange: dateRange,
          hasAvailability: false,
          hasRates: false,
          error: error.message
        });
      }
    }
  }
  
  return results;
}

/**
 * Step 3: Attempt to create reservations with different parameters
 */
async function createReservations(availabilityResults) {
  logSection('STEP 3: ATTEMPTING TO CREATE RESERVATIONS WITH KNOWN ROOM TYPE IDS');
  
  // Use all room types regardless of availability
  const roomTypesToTest = KNOWN_ROOM_TYPE_IDS.map(id => ({
    roomTypeId: id,
    roomTypeName: `Room Type ${id}`
  }));
  
  const dateRanges = getDateRanges();
  const reservationResults = [];
  
  // Test each room type with each source ID
  for (const roomType of roomTypesToTest) {
    for (const sourceId of SOURCE_IDS) {
      for (const dateRange of dateRanges) {
        logSubSection(`Testing reservation for ${roomType.roomTypeName} with source ID ${sourceId}`);
        logInfo(`Date range: ${dateRange.startDate} to ${dateRange.endDate}`);
        
        try {
          // Create form data exactly as in Manuel's example
          const formData = new FormData();
          
          // Property information
          formData.append('propertyID', PROPERTY_ID);
          formData.append('startDate', dateRange.startDate);
          formData.append('endDate', dateRange.endDate);
          
          // Guest information
          formData.append('guestFirstName', 'Test');
          formData.append('guestLastName', 'User');
          formData.append('guestCountry', 'US');
          formData.append('guestZip', '1234');
          formData.append('guestEmail', '<EMAIL>');
          formData.append('guestPhone', '4567');
          
          // Payment information
          formData.append('paymentMethod', 'credit');
          
          // Room information
          formData.append('rooms[0][roomTypeID]', roomType.roomTypeId);
          formData.append('rooms[0][quantity]', '1');
          
          // Guest counts
          formData.append('adults[0][roomTypeID]', roomType.roomTypeId);
          formData.append('adults[0][quantity]', '1');
          formData.append('children[0][roomTypeID]', roomType.roomTypeId);
          formData.append('children[0][quantity]', '0');
          
          // Source ID and identifier
          formData.append('sourceID', sourceId);
          formData.append('thirdPartyIdentifier', `test-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`);
          
          logInfo('Sending reservation request...');
          
          const response = await fetch(`${BASE_URL}/postReservation`, {
            method: 'POST',
            headers: {
              'Accept': 'application/json',
              'Authorization': `Bearer ${API_KEY}`
            },
            body: formData
          });
          
          const responseText = await response.text();
          let responseData;
          
          try {
            responseData = JSON.parse(responseText);
          } catch (e) {
            responseData = { success: false, message: 'Invalid JSON response', rawResponse: responseText };
          }
          
          if (responseData.success) {
            logSuccess('Reservation created successfully!');
            logInfo(`Reservation ID: ${responseData.reservationID}`);
            logInfo(`Confirmation Code: ${responseData.confirmationCode}`);
          } else {
            logError(`Reservation creation failed: ${responseData.message || 'Unknown error'}`);
          }
          
          reservationResults.push({
            roomTypeId: roomType.roomTypeId,
            roomTypeName: roomType.roomTypeName,
            dateRange: dateRange,
            sourceId: sourceId,
            success: responseData.success,
            response: responseData
          });
        } catch (error) {
          logError(`Error creating reservation: ${error.message}`);
          reservationResults.push({
            roomTypeId: roomType.roomTypeId,
            roomTypeName: roomType.roomTypeName,
            dateRange: dateRange,
            sourceId: sourceId,
            success: false,
            error: error.message
          });
        }
      }
    }
  }
  
  return reservationResults;
}

/**
 * Main function to run all tests
 */
async function runTests() {
  logSection('STARTING FIXED CLOUDBEDS API TEST');
  logInfo(`Property ID: ${PROPERTY_ID}`);
  logInfo(`API Key present: ${API_KEY ? 'YES' : 'NO'}`);
  logInfo(`Known Room Type IDs: ${KNOWN_ROOM_TYPE_IDS.join(', ')}`);
  
  if (!API_KEY) {
    logError('No API key provided. Set the CLOUDBEDS_API_KEY environment variable.');
    return;
  }
  
  try {
    // Step 1: Get room types
    const roomTypes = await getRoomTypes();
    
    if (roomTypes.length === 0) {
      logError('No room types found. Cannot continue testing.');
      return;
    }
    
    // Step 2: Check rate availability
    const dateRanges = getDateRanges();
    const availabilityResults = await checkRateAvailability(roomTypes, dateRanges);
    
    // Step 3: Create reservations with known room type IDs
    const reservationResults = await createReservations(availabilityResults);
    
    // Summary
    logSection('TEST SUMMARY');
    
    logSubSection('Room Types');
    console.log(`Total room types found: ${roomTypes.length}`);
    
    logSubSection('Availability Results');
    const availableRooms = availabilityResults.filter(r => r.hasAvailability).length;
    const roomsWithRates = availabilityResults.filter(r => r.hasRates).length;
    console.log(`Rooms with availability: ${availableRooms}/${availabilityResults.length}`);
    console.log(`Rooms with rates: ${roomsWithRates}/${availabilityResults.length}`);
    
    logSubSection('Reservation Results');
    const successfulReservations = reservationResults.filter(r => r.success).length;
    console.log(`Successful reservations: ${successfulReservations}/${reservationResults.length}`);
    
    if (successfulReservations > 0) {
      logSuccess('At least one reservation was created successfully!');
      
      // Show details of successful reservations
      logSubSection('Successful Reservation Details');
      reservationResults.filter(r => r.success).forEach(result => {
        console.log(`Room: ${result.roomTypeName} (${result.roomTypeId})`);
        console.log(`Dates: ${result.dateRange.startDate} to ${result.dateRange.endDate}`);
        console.log(`Source ID: ${result.sourceId}`);
        console.log(`Reservation ID: ${result.response.reservationID}`);
        console.log(`Confirmation Code: ${result.response.confirmationCode}`);
        console.log('-'.repeat(40));
      });
    } else {
      logError('No reservations were created successfully.');
      
      // Analyze common error patterns
      logSubSection('Error Analysis');
      const errorMessages = {};
      
      reservationResults.forEach(result => {
        if (!result.success) {
          const message = result.response?.message || result.error || 'Unknown error';
          errorMessages[message] = (errorMessages[message] || 0) + 1;
        }
      });
      
      console.log('Common error messages:');
      Object.entries(errorMessages)
        .sort((a, b) => b[1] - a[1])
        .forEach(([message, count]) => {
          console.log(`- "${message}" (${count} occurrences)`);
        });
    }
    
    logSection('TEST COMPLETED');
  } catch (error) {
    logError(`Unexpected error in test execution: ${error.message}`);
    console.error(error);
  }
}

// Run the tests
runTests();
