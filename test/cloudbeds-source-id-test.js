#!/usr/bin/env node

/**
 * Script para probar la creación de reservas usando el formato exacto proporcionado por <PERSON>
 * Ejecutar con: node test/cloudbeds-source-id-test.js
 */

// Importar fetch y form-data
const fetch = require('node-fetch');
const FormData = require('form-data');

// Cargar variables de entorno
require('dotenv').config();

// Constantes
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Fechas para la reserva (formato YYYY-MM-DD)
const today = new Date();
const futureDate = new Date(today);
futureDate.setMonth(today.getMonth() + 1);
const START_DATE = futureDate.toISOString().split('T')[0];
const nextDay = new Date(futureDate);
nextDay.setDate(futureDate.getDate() + 1);
const END_DATE = nextDay.toISOString().split('T')[0];

/**
 * Función principal para probar la creación de reservas con el formato de Manuel Arbelo
 */
async function testManuelFormat() {
  console.log('=== PRUEBA DE CREACIÓN DE RESERVAS CON FORMATO DE MANUEL ARBELO ===');
  console.log(`URL: ${BASE_URL}/postReservation`);
  console.log(`Fechas de reserva: ${START_DATE} a ${END_DATE}`);
  console.log(`ID de propiedad: ${PROPERTY_ID}`);
  console.log(`ID de tipo de habitación: ${ROOM_TYPE_ID}`);
  
  if (!API_KEY) {
    console.error('ERROR: No se ha proporcionado una API key. Configura la variable de entorno CLOUDBEDS_API_KEY.');
    return;
  }
  
  try {
    // Crear FormData exactamente como en el ejemplo de Manuel
    const formData = new FormData();
    
    // Información de la propiedad
    formData.append('propertyID', PROPERTY_ID);
    formData.append('startDate', START_DATE);
    formData.append('endDate', END_DATE);
    
    // Información del huésped
    formData.append('guestFirstName', 'Test');
    formData.append('guestLastName', 'User');
    formData.append('guestCountry', 'FR');
    formData.append('guestZip', '1234');
    formData.append('guestEmail', '<EMAIL>');
    formData.append('guestPhone', '4567');
    
    // Método de pago
    formData.append('paymentMethod', 'credit');
    
    // Habitaciones
    formData.append('rooms[0][roomTypeID]', ROOM_TYPE_ID);
    formData.append('rooms[0][quantity]', '1');
    
    // Adultos y niños
    formData.append('adults[0][roomTypeID]', ROOM_TYPE_ID);
    formData.append('adults[0][quantity]', '1');
    formData.append('children[0][roomTypeID]', ROOM_TYPE_ID);
    formData.append('children[0][quantity]', '0');
    
    // Source ID - Usar exactamente s-1-1 como en el ejemplo de Manuel
    formData.append('sourceID', 's-1-1');
    
    // Identificador único
    formData.append('thirdPartyIdentifier', `test-manuel-${Date.now()}`);
    
    console.log('Datos del formulario creados con el formato exacto de Manuel Arbelo');
    
    // Cabeceras
    const headers = {
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    // Realizar la petición
    console.log('Enviando solicitud...');
    const response = await fetch(`${BASE_URL}/postReservation`, {
      method: 'POST',
      headers: headers,
      body: formData
    });
    
    // Procesar la respuesta
    const responseText = await response.text();
    console.log(`Respuesta (status ${response.status}):`);
    
    try {
      // Intentar parsear como JSON
      const responseData = JSON.parse(responseText);
      console.log(JSON.stringify(responseData, null, 2));
      
      if (responseData.success) {
        console.log('\n✅ ÉXITO: La reserva se ha creado correctamente');
        console.log(`ID de reserva: ${responseData.reservationID}`);
        console.log(`Código de confirmación: ${responseData.confirmationCode}`);
      } else {
        console.log('\n❌ ERROR: No se pudo crear la reserva');
        console.log(`Mensaje: ${responseData.message || 'No hay mensaje de error'}`);
      }
    } catch (e) {
      // Si no es JSON, mostrar el texto
      console.log('Respuesta no es JSON:');
      console.log(responseText);
    }
  } catch (error) {
    console.error('Error en la solicitud:', error);
  }
}

// Ejecutar la prueba
testManuelFormat();
