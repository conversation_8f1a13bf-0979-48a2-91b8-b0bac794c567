#!/usr/bin/env node

/**
 * Final test script for Cloudbeds reservation API
 * Run with: node test/cloudbeds-final-test.js
 */

// Import node's built-in fetch
const fetch = require('node-fetch');

// Try to load dotenv
try {
  require('dotenv').config({ path: './.env' });
  console.log("Loaded .env file");
} catch (e) {
  console.log('dotenv error:', e);
}

// Constants
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

/**
 * Test with combined approach for Cloudbeds reservation API
 */
async function testFinalApproach() {
  console.log('=== FINAL TEST FOR CLOUDBEDS RESERVATION API ===');
  
  // Test 1: Combined form URL encoded with all parameters
  console.log('\n=== TEST 1: Combined Form URL Encoded ===');
  try {
    const url = `${BASE_URL}/postReservation`;
    
    // Create combined form data with all parameters
    const formData = new URLSearchParams();
    // Property
    formData.append('propertyID', PROPERTY_ID);
    // Guest
    formData.append('guestFirstName', 'Test');
    formData.append('guestLastName', 'User');
    formData.append('guestEmail', '<EMAIL>');
    formData.append('guestPhone', '+1234567890');
    // Dates
    formData.append('startDate', '2025-05-01');
    formData.append('endDate', '2025-05-02');
    // Room
    formData.append('roomTypeID', ROOM_TYPE_ID);
    // Required parameters
    formData.append('adults', '1');
    formData.append('children', '0');
    // Room array 
    formData.append('rooms[0][roomTypeID]', ROOM_TYPE_ID);
    formData.append('rooms[0][startDate]', '2025-05-01');
    formData.append('rooms[0][endDate]', '2025-05-02');
    formData.append('rooms[0][adults]', '1');
    formData.append('rooms[0][children]', '0');
    // Other
    formData.append('source', 's-2-1');
    formData.append('status', 'confirmed');
    formData.append('thirdPartyIdentifier', `test-final-${Date.now()}`);
    formData.append('sendEmailConfirmation', 'false');
    
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    console.log('Form data:', formData.toString());
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: formData
    });
    
    const responseText = await response.text();
    console.log('Status:', response.status);
    console.log('Response:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error in combined test:', error);
  }
  
  // Test 2: Using Manuel's format with all parameters
  console.log('\n=== TEST 2: Manuel\'s Format with Additional Parameters ===');
  try {
    const url = `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}&startDate=2025-05-01&endDate=2025-05-02`;
    
    // Create comprehensive JSON body with all parameters
    const jsonBody = {
      propertyID: PROPERTY_ID,
      startDate: "2025-05-01",
      endDate: "2025-05-02",
      guestFirstName: "Test",
      guestLastName: "User",
      guestEmail: "<EMAIL>",
      guestPhone: "+1234567890",
      adults: 1,
      children: 0,
      rooms: [
        {
          roomTypeID: ROOM_TYPE_ID,
          startDate: "2025-05-01",
          endDate: "2025-05-02",
          adults: 1,
          children: 0
        }
      ],
      roomTypeID: ROOM_TYPE_ID,
      source: "s-2-1",
      status: "confirmed",
      thirdPartyIdentifier: `test-manuel-${Date.now()}`,
      sendEmailConfirmation: false
    };
    
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    console.log('URL:', url);
    console.log('JSON body:', JSON.stringify(jsonBody, null, 2));
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(jsonBody)
    });
    
    const responseText = await response.text();
    console.log('Status:', response.status);
    console.log('Response:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error in Manuel\'s format test:', error);
  }
  
  // Test 3: Using default client pattern
  console.log('\n=== TEST 3: Default Client Pattern ===');
  try {
    const url = `${BASE_URL}/postReservation`;
    
    // Basic form data with pattern similar to browser clients
    const formData = new URLSearchParams();
    formData.append('propertyID', PROPERTY_ID);
    formData.append('startDate', '2025-05-01');
    formData.append('endDate', '2025-05-02');
    formData.append('roomTypeID', ROOM_TYPE_ID);
    formData.append('guestFirstName', 'Test');
    formData.append('guestLastName', 'User');
    formData.append('guestEmail', '<EMAIL>');
    formData.append('numAdults', '1');
    formData.append('numChildren', '0');
    formData.append('thirdPartyIdentifier', `test-client-${Date.now()}`);
    formData.append('status', 'confirmed');
    formData.append('source', 's-2-1');
    formData.append('sendEmailConfirmation', 'false');
    
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    console.log('Form data:', formData.toString());
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: formData
    });
    
    const responseText = await response.text();
    console.log('Status:', response.status);
    console.log('Response:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error in client pattern test:', error);
  }
}

// Run the test
testFinalApproach(); 