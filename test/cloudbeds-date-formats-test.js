#!/usr/bin/env node

/**
 * Test script for Cloudbeds reservation API with different date formats
 * Run with: node test/cloudbeds-date-formats-test.js
 */

// Import node's built-in fetch
const fetch = require('node-fetch');

// Try to load dotenv
try {
  require('dotenv').config({ path: './.env' });
  console.log("Loaded .env file");
} catch (e) {
  console.log('dotenv error:', e);
}

// Constants
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Different date formats to test
const DATE_FORMATS = [
  // ISO format (YYYY-MM-DD)
  {
    startDate: "2025-05-01",
    endDate: "2025-05-02",
    description: "ISO format (YYYY-MM-DD)"
  },
  // MM/DD/YYYY format
  {
    startDate: "05/01/2025",
    endDate: "05/02/2025",
    description: "MM/DD/YYYY format"
  },
  // YYYY/MM/DD format
  {
    startDate: "2025/05/01",
    endDate: "2025/05/02",
    description: "YYYY/MM/DD format"
  },
  // DD-MM-YYYY format
  {
    startDate: "01-05-2025",
    endDate: "02-05-2025",
    description: "DD-MM-YYYY format"
  },
  // Unix timestamp
  {
    startDate: Math.floor(new Date('2025-05-01').getTime() / 1000).toString(),
    endDate: Math.floor(new Date('2025-05-02').getTime() / 1000).toString(),
    description: "Unix timestamp"
  },
  // ISO 8601 format
  {
    startDate: new Date('2025-05-01').toISOString(),
    endDate: new Date('2025-05-02').toISOString(),
    description: "ISO 8601 format"
  }
];

/**
 * Main function to test the Cloudbeds reservation API with different date formats
 */
async function testDateFormats() {
  console.log('=== TESTING CLOUDBEDS RESERVATION API WITH DIFFERENT DATE FORMATS ===');
  
  if (!API_KEY) {
    console.error('ERROR: No API key provided. Set CLOUDBEDS_API_KEY environment variable.');
    return;
  }
  
  try {
    for (const [index, dateFormat] of DATE_FORMATS.entries()) {
      console.log(`\n=== TEST ${index + 1}: ${dateFormat.description} ===`);
      
      // Build base reservation with this date format
      const reservation = {
        propertyID: PROPERTY_ID,
        guestFirstName: "Test",
        guestLastName: "User",
        guestEmail: "<EMAIL>",
        guestPhone: "+1234567890",
        startDate: dateFormat.startDate,
        endDate: dateFormat.endDate,
        numAdults: 1,
        numChildren: 0,
        roomTypeID: ROOM_TYPE_ID,
        source: "s-2-1",
        status: "confirmed",
        thirdPartyIdentifier: `test-date-format-${index}-${Date.now()}`,
        sendEmailConfirmation: false
      };
      
      // URL with date parameters
      const url = new URL(`${BASE_URL}/postReservation`);
      url.searchParams.append('propertyID', PROPERTY_ID);
      url.searchParams.append('startDate', dateFormat.startDate);
      url.searchParams.append('endDate', dateFormat.endDate);
      
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-api-key': API_KEY
      };
      
      console.log('Date format:', dateFormat);
      console.log('URL:', url.toString());
      console.log('Body excerpt:', {
        startDate: reservation.startDate,
        endDate: reservation.endDate
      });
      
      try {
        const response = await fetch(url.toString(), {
          method: 'POST',
          headers,
          body: JSON.stringify(reservation)
        });
        
        const responseText = await response.text();
        console.log('Status:', response.status);
        console.log('Response:', responseText);
        
        try {
          const jsonResponse = JSON.parse(responseText);
          console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
        } catch (e) {
          console.log('Response is not valid JSON');
        }
      } catch (error) {
        console.error(`Error in test ${index + 1}:`, error);
      }
    }
  } catch (error) {
    console.error('General error in test script:', error);
  } finally {
    console.log('\n=== ALL DATE FORMAT TESTS COMPLETED ===');
  }
}

/**
 * Test parameters using x-www-form-urlencoded format
 */
async function testFormUrlEncoded() {
  console.log('\n=== TESTING WITH FORM URL ENCODED FORMAT ===');
  
  try {
    const url = `${BASE_URL}/postReservation`;
    
    // Basic form data
    const formData = new URLSearchParams();
    formData.append('propertyID', PROPERTY_ID);
    formData.append('startDate', '2025-05-01');
    formData.append('endDate', '2025-05-02');
    formData.append('guestFirstName', 'Test');
    formData.append('guestLastName', 'User');
    formData.append('guestEmail', '<EMAIL>');
    formData.append('numAdults', '1');
    formData.append('numChildren', '0');
    formData.append('roomTypeID', ROOM_TYPE_ID);
    formData.append('source', 's-2-1');
    formData.append('status', 'confirmed');
    formData.append('thirdPartyIdentifier', `test-form-${Date.now()}`);
    formData.append('sendEmailConfirmation', 'false');
    
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    console.log('Form data:', formData.toString());
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: formData
    });
    
    const responseText = await response.text();
    console.log('Status:', response.status);
    console.log('Response:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error in form URL encoded test:', error);
  }
}

// Run the test
(async () => {
  await testDateFormats();
  await testFormUrlEncoded();
})(); 