#!/usr/bin/env node

/**
 * Comprehensive test script for Cloudbeds reservation API
 * Run with: node test/cloudbeds-comprehensive-test.js
 */

// Import node's built-in fetch
const fetch = require('node-fetch');

// Try to load dotenv if available
try {
  require('dotenv').config({ path: './.env' });
  console.log("Loaded .env file");
} catch (e) {
  console.log('dotenv error:', e);
  console.log('Using process.env directly');
}

// Constants - replace with your actual values
console.log("Environment variables:", Object.keys(process.env));
console.log("API KEY present:", process.env.CLOUDBEDS_API_KEY ? "YES" : "NO");
const API_KEY = process.env.CLOUDBEDS_API_KEY;
console.log("API KEY value:", API_KEY);
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Test reservation data - base template
const baseReservation = {
  propertyID: PROPERTY_ID,
  guestFirstName: "Test",
  guestLastName: "User",
  guestEmail: "<EMAIL>",
  guestPhone: "+1234567890",
  startDate: "2025-05-01",
  endDate: "2025-05-02",
  numAdults: 1,
  numChildren: 0,
  roomTypeID: ROOM_TYPE_ID,
  source: "s-2-1", // Web source
  status: "confirmed",
  thirdPartyIdentifier: `test-${Date.now()}`,
  sendEmailConfirmation: false
};

/**
 * Main function to test the Cloudbeds reservation API with multiple approaches
 */
async function testReservationAPI() {
  console.log('=== COMPREHENSIVE CLOUDBEDS RESERVATION API TEST ===');
  console.log('Using Property ID:', PROPERTY_ID);
  console.log('Using Room Type ID:', ROOM_TYPE_ID);
  
  if (!API_KEY) {
    console.error('ERROR: No API key provided. Set CLOUDBEDS_API_KEY environment variable.');
    return;
  }
  
  try {
    // TEST 1: Manuel's suggested URL format
    console.log('\n=== TEST 1: Manuel\'s suggested URL format ===');
    await testReservation({
      name: "Manuel's format",
      url: `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}`,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-api-key': API_KEY
      },
      body: baseReservation
    });
    
    // TEST 2: URL parameters with startDate and endDate
    console.log('\n=== TEST 2: URL with startDate and endDate ===');
    const url2 = new URL(`${BASE_URL}/postReservation`);
    url2.searchParams.append('propertyID', PROPERTY_ID);
    url2.searchParams.append('startDate', baseReservation.startDate);
    url2.searchParams.append('endDate', baseReservation.endDate);
    await testReservation({
      name: "URL with startDate/endDate",
      url: url2.toString(),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-api-key': API_KEY
      },
      body: baseReservation
    });
    
    // TEST 3: Authorization header instead of x-api-key
    console.log('\n=== TEST 3: Authorization header ===');
    await testReservation({
      name: "Authorization header",
      url: `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}`,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: baseReservation
    });
    
    // TEST 4: Alternative body format (roomsData)
    console.log('\n=== TEST 4: Alternative body format (roomsData) ===');
    const bodyWithRoomsData = {
      propertyID: PROPERTY_ID,
      guestData: {
        firstName: "Test",
        lastName: "User",
        email: "<EMAIL>",
        phone: "+1234567890"
      },
      roomsData: [{
        roomTypeID: ROOM_TYPE_ID,
        startDate: "2025-05-01",
        endDate: "2025-05-02",
        adults: 1,
        children: 0
      }],
      startDate: "2025-05-01",
      endDate: "2025-05-02",
      status: "confirmed",
      sourceID: "s-2-1",
      thirdPartyIdentifier: `test-alt-${Date.now()}`,
      sendEmailConfirmation: false
    };
    await testReservation({
      name: "Alternative body format",
      url: `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}`,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-api-key': API_KEY
      },
      body: bodyWithRoomsData
    });
    
    // TEST 5: X-PROPERTY-ID header
    console.log('\n=== TEST 5: X-PROPERTY-ID header ===');
    await testReservation({
      name: "X-PROPERTY-ID header",
      url: `${BASE_URL}/postReservation`,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-api-key': API_KEY,
        'X-PROPERTY-ID': PROPERTY_ID
      },
      body: baseReservation
    });
    
  } catch (error) {
    console.error('General error in test script:', error);
  } finally {
    console.log('\n=== ALL TESTS COMPLETED ===');
  }
}

/**
 * Generic test function to make a reservation request
 */
async function testReservation({ name, url, headers, body }) {
  console.log(`\nRunning test: ${name}`);
  console.log('URL:', url);
  console.log('Headers:', JSON.stringify(headers, (key, value) => 
    key === 'x-api-key' || key === 'Authorization' ? '[HIDDEN]' : value
  ));
  console.log('Body:', JSON.stringify(body, null, 2));
  
  try {
    // Add a unique thirdPartyIdentifier for each test
    const uniqueBody = { 
      ...body, 
      thirdPartyIdentifier: body.thirdPartyIdentifier || `test-${name.replace(/\s+/g, '-')}-${Date.now()}`
    };
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(uniqueBody)
    });
    
    const responseText = await response.text();
    console.log('Status:', response.status);
    console.log('Response text:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
      return jsonResponse;
    } catch (e) {
      console.log('Response is not valid JSON');
      return null;
    }
  } catch (error) {
    console.error(`Error in test "${name}":`, error.message);
    return null;
  }
}

// Run the test
testReservationAPI(); 