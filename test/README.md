# Cloudbeds API Test Scripts

Simple test scripts to troubleshoot Cloudbeds API integration issues.

## Setup

1. Install dependencies:
   ```
   cd test
   npm install
   ```

2. Create a `.env` file from the sample:
   ```
   cp .env.sample .env
   ```

3. Edit the `.env` file to add your Cloudbeds API key.

## Running the Tests

### Basic Test
```
npm test
```

This runs a simple test that tries two different approaches to create a reservation:
- With URL parameters (propertyID, startDate, endDate)
- With <PERSON>'s suggested format (only propertyID in URL)

### Comprehensive Test
```
npm run test:comprehensive
```

This runs a more comprehensive test that tries multiple approaches:
1. <PERSON>'s suggested URL format
2. URL with startDate and endDate parameters
3. Authorization header instead of x-api-key
4. Alternative body format with roomsData structure
5. X-PROPERTY-ID header approach

## Troubleshooting

If all tests fail with the same error, check:
- API key validity and permissions
- Network connectivity
- Correct property ID and room type ID

## Notes

- These test scripts use Node.js and are designed to isolate API issues from the main application.
- All tests use fake reservation data and won't create actual reservations if API access is denied. 