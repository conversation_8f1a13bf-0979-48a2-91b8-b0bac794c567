#!/usr/bin/env node

/**
 * Script para probar la creación de reservas usando el ejemplo exacto de <PERSON>
 * Ejecutar con: node test/create-reservation-manuel-example.js
 */

// Importar fetch y form-data
const fetch = require('node-fetch');
const FormData = require('form-data');

// Cargar variables de entorno
require('dotenv').config();

// Constantes
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Fechas para la reserva (formato YYYY-MM-DD)
const START_DATE = "2025-05-01";
const END_DATE = "2025-05-02";

/**
 * Función principal para probar la creación de reservas usando el ejemplo de Manuel
 */
async function testManuelExample() {
  console.log('=== PROBANDO CREACIÓN DE RESERVAS USANDO EJEMPLO DE MANUEL ARBELO ===');
  
  if (!API_KEY) {
    console.error('ERROR: No se ha proporcionado API_KEY. Configure la variable de entorno CLOUDBEDS_API_KEY.');
    return;
  }
  
  try {
    // Construir URL con solo propertyID como parámetro (formato sugerido por Manuel)
    const url = new URL(`${BASE_URL}/postReservation`);
    url.searchParams.append('propertyID', PROPERTY_ID);
    
    console.log('URL:', url.toString());
    
    // Crear FormData exactamente como en el ejemplo de Manuel
    const formData = new FormData();
    
    // Información de la propiedad
    formData.append('propertyID', '');
    formData.append('startDate', START_DATE);
    formData.append('endDate', END_DATE);
    
    // Información del huésped
    formData.append('guestFirstName', 'Test');
    formData.append('guestLastName', 'User');
    formData.append('guestEmail', '<EMAIL>');
    formData.append('guestPhone', '+1234567890');
    formData.append('guestCountry', 'ES');
    formData.append('guestZip', '28001');
    
    // Información de pago
    formData.append('paymentMethod', 'credit');
    
    // Datos de habitación anidados (formato exacto de Manuel)
    formData.append('rooms[0][roomTypeID]', '');
    formData.append('rooms[0][quantity]', '1');
    
    // Adultos y niños (formato exacto de Manuel)
    formData.append('adults[0][roomTypeID]', '');
    formData.append('adults[0][quantity]', '1');
    formData.append('children[0][roomTypeID]', '');
    formData.append('children[0][quantity]', '0');
    
    // Otros parámetros que podrían ser útiles
    formData.append('source', 's-2-1');
    formData.append('status', 'confirmed');
    formData.append('thirdPartyIdentifier', `test-manuel-example-${Date.now()}`);
    formData.append('sendEmailConfirmation', 'false');
    
    console.log('Datos del formulario creados según el ejemplo de Manuel');
    
    // Cabeceras
    const headers = {
      // FormData establece automáticamente el Content-Type como multipart/form-data con el boundary correcto
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    // Realizar la petición
    console.log('Enviando solicitud...');
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: headers,
      body: formData
    });
    
    // Obtener respuesta
    const responseText = await response.text();
    console.log('Código de estado:', response.status);
    console.log('Respuesta:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('Respuesta JSON:', JSON.stringify(jsonResponse, null, 2));
      
      if (jsonResponse.success) {
        console.log('¡Reserva creada exitosamente!');
        console.log('ID de reserva:', jsonResponse.reservationID);
        console.log('Código de confirmación:', jsonResponse.confirmationCode);
      } else {
        console.error('Error al crear la reserva:', jsonResponse.message);
      }
    } catch (e) {
      console.log('La respuesta no es un JSON válido');
    }
  } catch (error) {
    console.error('Error general:', error);
  } finally {
    console.log('=== PROCESO COMPLETADO ===');
  }
}

// Ejecutar la función principal
testManuelExample();
