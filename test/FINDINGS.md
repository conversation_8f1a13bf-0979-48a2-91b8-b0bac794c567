# Cloudbeds API Reservation Testing Findings

## Summary of Tests

We conducted extensive testing of the Cloudbeds Reservation API and encountered several issues. This document summarizes our findings to guide future implementation.

## Required Parameters

Through progressive testing, we identified that the Cloudbeds Reservation API requires the following parameters:

1. **Property Information**:
   - `propertyID` (can be in URL params, request body, or as `X-PROPERTY-ID` header)

2. **Guest Information**:
   - `guestFirstName`
   - `guestLastName`
   - `guestEmail`
   - `guestPhone`
   - `guestCountry` (ISO country code, e.g., "ES" for Spain)
   - `guestAddress`
   - `guestCity`
   - `guestZip`

3. **Dates**:
   - `startDate` (format: YYYY-MM-DD)
   - `endDate` (format: YYYY-MM-DD)

4. **Room Information**:
   - `roomTypeID`
   - `adults`
   - `children`
   - Nested room data:
     ```
     rooms[0][roomTypeID]
     rooms[0][startDate]
     rooms[0][endDate]
     rooms[0][adults]
     rooms[0][children]
     ```

5. **Payment**:
   - `paymentMethod` (e.g., "noPayment")

6. **Other**:
   - `source` (e.g., "s-2-1")
   - `status` (e.g., "confirmed")
   - `thirdPartyIdentifier` (unique identifier)
   - `sendEmailConfirmation` (boolean)

## Error Messages Encountered

1. "Parameter startDate is required" - Despite including startDate in both URL and body
2. "Parameter rooms is required" - The API requires the nested rooms array parameter
3. "Parameter adults is required" - Adults count is required
4. "Parameter paymentMethod is required" - Payment method must be specified
5. "Parameter guestCountry is not valid" - Country code must be valid ISO format
6. "Invalid Parameter Format" - Likely due to format issues with nested parameters
7. "You don't have access to property ID" - Permission issue with the property ID

## Content-Type

The most compatible format appears to be `application/x-www-form-urlencoded` with proper nested parameter formatting for the rooms array.

## Authentication

The API accepts both `x-api-key` header and OAuth 2.0 authentication. OAuth is likely required for write operations and may require specific scopes for reservations.

## Recommendations

1. **Use x-www-form-urlencoded format**: This format appears most compatible with the Cloudbeds API.

2. **Include all required parameters**: Ensure all required guest information, dates, and room details are included.

3. **Format nested parameters correctly**: Pay special attention to the rooms array format.

4. **Verify property ID access**: Ensure you have proper permission for the property ID you're using.

5. **Implement OAuth 2.0 for write operations**: While API key works for some operations, OAuth may be required for reservations.

6. **Handle error responses appropriately**: The API returns specific error messages that can guide error handling.

## Next Steps

1. Confirm property ID access with Cloudbeds support
2. Ensure OAuth scopes include reservation write permissions
3. Develop a proper implementation based on these findings
4. Test with valid property ID and OAuth credentials

## Issues to Resolve with Cloudbeds Support

1. Clarify why "Parameter startDate is required" error occurs even when startDate is included
2. Confirm the exact format required for the rooms parameter
3. Verify property ID access permissions 