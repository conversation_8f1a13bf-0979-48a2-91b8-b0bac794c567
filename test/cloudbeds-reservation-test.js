#!/usr/bin/env node

/**
 * Simple script to test Cloudbeds reservation API
 * Run with: node test/cloudbeds-reservation-test.js
 */

// Import node's built-in fetch
const fetch = require('node-fetch');

// Constants - replace with your actual values
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Test reservation data
const testReservation = {
  propertyID: PROPERTY_ID,
  guestFirstName: "Test",
  guestLastName: "User",
  guestEmail: "<EMAIL>",
  guestPhone: "+1234567890",
  startDate: "2025-05-01",
  endDate: "2025-05-02",
  numAdults: 1,
  numChildren: 0,
  roomTypeID: "650743", // Replace with your actual room type ID
  source: "s-2-1", // Web source
  status: "confirmed",
  thirdPartyIdentifier: `test-${Date.now()}`,
  sendEmailConfirmation: false
};

/**
 * Main function to test the Cloudbeds reservation API
 */
async function testReservationAPI() {
  console.log('=== TESTING CLOUDBEDS RESERVATION API ===');
  
  try {
    // 1. Test with URL Parameter approach
    await testWithUrlParameters();
    
    // 2. Test with Manuel's suggested format
    await testWithManuelFormat();
    
  } catch (error) {
    console.error('General error in test script:', error);
  } finally {
    console.log('=== TEST COMPLETED ===');
  }
}

/**
 * Test with URL parameters
 */
async function testWithUrlParameters() {
  console.log('\n==== TEST 1: Using URL parameters ====');
  
  // Construct URL with query parameters
  const url = new URL(`${BASE_URL}/postReservation`);
  url.searchParams.append('propertyID', PROPERTY_ID);
  url.searchParams.append('startDate', testReservation.startDate);
  url.searchParams.append('endDate', testReservation.endDate);
  
  console.log('URL:', url.toString());
  
  // Headers
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'x-api-key': API_KEY
  };
  
  try {
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers,
      body: JSON.stringify(testReservation)
    });
    
    const responseText = await response.text();
    console.log('Status:', response.status);
    console.log('Response:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error in URL parameter test:', error);
  }
}

/**
 * Test with Manuel's suggested format
 */
async function testWithManuelFormat() {
  console.log('\n==== TEST 2: Using Manuel\'s suggested format ====');
  
  // Construct URL with Manuel's suggested format
  const url = `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}`;
  console.log('URL:', url);
  
  // Headers
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'x-api-key': API_KEY
  };
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(testReservation)
    });
    
    const responseText = await response.text();
    console.log('Status:', response.status);
    console.log('Response:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error in Manuel format test:', error);
  }
}

// Run the test
testReservationAPI(); 