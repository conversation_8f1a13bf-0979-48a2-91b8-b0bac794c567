/**
 * Test script for Cloudbeds dynamic room type resolution
 * 
 * This script tests the dynamic room type resolution system by:
 * 1. Fetching room types from the Cloudbeds API
 * 2. Testing various resolution strategies
 * 3. Verifying that room type IDs are correctly resolved
 * 
 * Usage:
 * node test/cloudbeds-dynamic-room-type-test.js
 */

// Load environment variables from .env file
require('dotenv').config();

// Import required modules
const fetch = require('node-fetch');
const FormData = require('form-data');

// Configuration
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Mock implementation of the room type resolution system for testing
class RoomTypeCache {
  constructor() {
    this.cache = new Map();
    this.roomTypes = [];
  }

  async getRoomTypes(propertyId, forceRefresh = false) {
    const cacheKey = `property_${propertyId}`;
    const now = Date.now();

    // Check if we have a valid cache entry
    if (!forceRefresh && this.cache.has(cacheKey)) {
      const cacheEntry = this.cache.get(cacheKey);
      if (cacheEntry && now < cacheEntry.expiresAt) {
        console.log(`Using cached room types for property ${propertyId}`);
        return cacheEntry.roomTypes;
      }
    }

    // Fetch fresh data
    console.log(`Fetching room types for property ${propertyId}`);
    try {
      const response = await this.fetchRoomTypes();
      
      if (response.success && response.data && response.data.length > 0) {
        // Update cache
        this.cache.set(cacheKey, {
          roomTypes: response.data,
          lastUpdated: now,
          expiresAt: now + 3600000 // 1 hour
        });
        
        this.roomTypes = response.data;
        return response.data;
      } else {
        console.warn(`No room types found for property ${propertyId}`);
        return [];
      }
    } catch (error) {
      console.error(`Error fetching room types for property ${propertyId}:`, error);
      return [];
    }
  }

  async fetchRoomTypes() {
    try {
      const url = `${BASE_URL}/getRoomTypes`;
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Accept': 'application/json',
          'X-PROPERTY-ID': PROPERTY_ID
        }
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching room types:', error);
      throw error;
    }
  }

  async buildRoomTypeMapping(propertyId) {
    const roomTypes = await this.getRoomTypes(propertyId);
    const mapping = new Map();

    for (const roomType of roomTypes) {
      const id = roomType.roomTypeID || roomType.id;
      const name = roomType.roomTypeName || roomType.name || '';
      const nameShort = roomType.roomTypeNameShort || '';

      if (id && name) {
        // Store lowercase for case-insensitive matching
        mapping.set(name.toLowerCase(), id);

        if (nameShort) {
          mapping.set(nameShort.toLowerCase(), id);
        }
      }
    }

    return mapping;
  }
}

class RoomTypeResolver {
  constructor(roomTypeCache) {
    this.roomTypeCache = roomTypeCache;
  }

  async validateRoomTypeId(roomTypeId, propertyId) {
    if (!roomTypeId) return false;

    const roomTypes = await this.roomTypeCache.getRoomTypes(propertyId);

    return roomTypes.some(roomType =>
      (roomType.roomTypeID === roomTypeId) || (roomType.id === roomTypeId)
    );
  }

  async findRoomTypeIdByName(roomName, propertyId) {
    if (!roomName) return undefined;

    const mapping = await this.roomTypeCache.buildRoomTypeMapping(propertyId);
    const normalizedName = roomName.toLowerCase();

    // Direct match
    if (mapping.has(normalizedName)) {
      return mapping.get(normalizedName);
    }

    // Try to match patterns
    for (const [mappedName, id] of mapping.entries()) {
      if (normalizedName.includes(mappedName) || mappedName.includes(normalizedName)) {
        return id;
      }
    }

    return undefined;
  }

  async getDefaultRoomTypeId(propertyId) {
    const roomTypes = await this.roomTypeCache.getRoomTypes(propertyId);

    if (roomTypes.length > 0) {
      const firstRoom = roomTypes[0];
      return firstRoom.roomTypeID || firstRoom.id;
    }

    return undefined;
  }

  async resolveRoomTypeId(roomTypeId, roomName, propertyId) {
    console.log(`Resolving room type ID: ${roomTypeId}, name: ${roomName}, property: ${propertyId}`);

    // Strategy 1: Validate the provided room type ID
    if (roomTypeId) {
      const isValid = await this.validateRoomTypeId(roomTypeId, propertyId);
      if (isValid) {
        console.log(`Room type ID ${roomTypeId} is valid`);
        return roomTypeId;
      }
    }

    // Strategy 2: Find by room name
    if (roomName) {
      const idByName = await this.findRoomTypeIdByName(roomName, propertyId);
      if (idByName) {
        console.log(`Found room type ID ${idByName} by name "${roomName}"`);
        return idByName;
      }
    }

    // Strategy 3: Use default room type
    const defaultId = await this.getDefaultRoomTypeId(propertyId);
    if (defaultId) {
      console.log(`Using default room type ID ${defaultId}`);
      return defaultId;
    }

    console.warn(`Could not resolve room type ID for property ${propertyId}`);
    return undefined;
  }
}

// Main test function
async function testDynamicRoomTypeResolution() {
  try {
    console.log('=== TESTING DYNAMIC ROOM TYPE RESOLUTION ===');
    
    // Initialize the components
    const cache = new RoomTypeCache();
    const resolver = new RoomTypeResolver(cache);
    
    // Fetch room types
    console.log('Fetching room types...');
    const roomTypes = await cache.getRoomTypes(PROPERTY_ID);
    
    if (roomTypes.length === 0) {
      console.error('No room types found. Check your API key and property ID.');
      return;
    }
    
    console.log(`Found ${roomTypes.length} room types:`);
    for (const roomType of roomTypes) {
      console.log(`- ${roomType.roomTypeName || roomType.name} (ID: ${roomType.roomTypeID || roomType.id})`);
    }
    
    // Test cases
    const testCases = [
      // Test with valid room type ID
      { 
        id: roomTypes[0].roomTypeID || roomTypes[0].id, 
        name: null,
        description: 'Valid room type ID'
      },
      // Test with invalid room type ID but valid name
      { 
        id: 'invalid-id', 
        name: roomTypes[0].roomTypeName || roomTypes[0].name,
        description: 'Invalid ID, valid name'
      },
      // Test with only name
      { 
        id: null, 
        name: roomTypes[0].roomTypeName || roomTypes[0].name,
        description: 'Only name'
      },
      // Test with partial name
      { 
        id: null, 
        name: (roomTypes[0].roomTypeName || roomTypes[0].name).split(' ')[0],
        description: 'Partial name'
      },
      // Test with invalid name
      { 
        id: null, 
        name: 'Completely Unknown Room Type',
        description: 'Invalid name'
      }
    ];
    
    // Run test cases
    console.log('\nRunning test cases:');
    for (const testCase of testCases) {
      console.log(`\nTest case: ${testCase.description}`);
      console.log(`- ID: ${testCase.id || 'null'}`);
      console.log(`- Name: ${testCase.name || 'null'}`);
      
      try {
        const resolvedId = await resolver.resolveRoomTypeId(
          testCase.id,
          testCase.name,
          PROPERTY_ID
        );
        
        if (resolvedId) {
          console.log(`✅ Successfully resolved to ID: ${resolvedId}`);
          
          // Verify the resolved ID
          const isValid = await resolver.validateRoomTypeId(resolvedId, PROPERTY_ID);
          console.log(`- Is valid: ${isValid ? 'Yes' : 'No'}`);
          
          // Find the room type name
          const roomType = roomTypes.find(rt => 
            (rt.roomTypeID === resolvedId) || (rt.id === resolvedId)
          );
          
          if (roomType) {
            console.log(`- Resolved to: ${roomType.roomTypeName || roomType.name}`);
          }
        } else {
          console.log(`❌ Failed to resolve room type ID`);
        }
      } catch (error) {
        console.error(`❌ Error resolving room type ID:`, error);
      }
    }
    
    console.log('\n=== DYNAMIC ROOM TYPE RESOLUTION TEST COMPLETE ===');
  } catch (error) {
    console.error('Error in test:', error);
  }
}

// Run the test
testDynamicRoomTypeResolution();
