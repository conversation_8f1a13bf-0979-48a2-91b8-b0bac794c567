#!/usr/bin/env node

/**
 * Script para verificar el estado del flag isPrivate en los tipos de habitación
 * y confirmar que el workaround está funcionando correctamente.
 * 
 * Ejecutar con: node test/cloudbeds-private-flag-verification.js
 */

// Importar fetch
const fetch = require('node-fetch');

// Cargar variables de entorno
require('dotenv').config();

// Constantes
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Función para formatear fechas como YYYY-MM-DD
const formatDate = (date) => {
  return date.toISOString().split('T')[0];
};

/**
 * Verifica el flag isPrivate en los tipos de habitación
 */
async function checkRoomTypesPrivateFlag() {
  console.log('\n=== VERIFICANDO FLAG isPrivate EN TIPOS DE HABITACIÓN ===');

  const url = new URL(`${BASE_URL}/getRoomTypes`);
  url.searchParams.append('propertyID', PROPERTY_ID);

  console.log(`URL: ${url.toString()}`);

  try {
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'x-api-key': API_KEY
      }
    });

    const data = await response.json();

    if (data.success) {
      if (data.data && data.data.length > 0) {
        console.log(`\nSe encontraron ${data.data.length} tipos de habitación configurados`);
        
        // Tabla de tipos de habitación
        console.log('\n=== TIPOS DE HABITACIÓN Y SU CONFIGURACIÓN ===');
        console.log('ID\t\tNombre\t\t\tisPrivate\tTipo');
        console.log('----------------------------------------------------------');
        
        data.data.forEach(room => {
          const roomType = room.roomTypeClass && room.roomTypeClass.toLowerCase().includes('dorm') 
            ? 'Shared dorm room' 
            : 'Private accommodation';
            
          console.log(`${room.roomTypeId}\t${room.roomTypeName.padEnd(20)}\t${room.isPrivate ? 'TRUE' : 'FALSE'}\t\t${roomType}`);
        });
        
        // Contar cuántos tienen isPrivate=true
        const privateRooms = data.data.filter(room => room.isPrivate);
        console.log(`\n${privateRooms.length} de ${data.data.length} tipos de habitación tienen isPrivate=true`);
        
        if (privateRooms.length > 0) {
          console.log('\nHabitaciones con isPrivate=true:');
          privateRooms.forEach(room => {
            console.log(`- ${room.roomTypeName} (ID: ${room.roomTypeId})`);
          });
        }
        
        return data.data;
      } else {
        console.log('No se encontraron tipos de habitación');
        return [];
      }
    } else {
      console.error('Error en la respuesta:', data.message || 'No hay mensaje de error');
      return [];
    }
  } catch (error) {
    console.error('Error al realizar la solicitud:', error);
    return [];
  }
}

/**
 * Verifica la disponibilidad pública (endpoint que filtra por isPrivate)
 */
async function checkPublicAvailability(roomTypeId = '') {
  console.log('\n=== VERIFICANDO DISPONIBILIDAD PÚBLICA (CON FILTRO isPrivate) ===');
  
  // Generar fechas para la prueba (30 días en el futuro)
  const today = new Date();
  const startDate = formatDate(new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000));
  const endDate = formatDate(new Date(today.getTime() + 31 * 24 * 60 * 60 * 1000));
  
  console.log(`Fechas de prueba: ${startDate} a ${endDate}`);
  
  const url = new URL(`${BASE_URL}/getAvailableRoomTypes`);
  url.searchParams.append('propertyID', PROPERTY_ID);
  url.searchParams.append('startDate', startDate);
  url.searchParams.append('endDate', endDate);
  
  if (roomTypeId) {
    url.searchParams.append('roomTypeID', roomTypeId);
  }
  
  console.log(`URL: ${url.toString()}`);
  
  try {
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'x-api-key': API_KEY
      }
    });
    
    const data = await response.json();
    
    if (data.success) {
      if (data.data && data.data.length > 0) {
        console.log(`\nSe encontraron ${data.data.length} tipos de habitación disponibles públicamente`);
        console.log('\nHabitaciones disponibles públicamente:');
        data.data.forEach(room => {
          console.log(`- ${room.roomTypeName || 'Sin nombre'} (ID: ${room.roomTypeId || 'N/A'})`);
          console.log(`  isPrivate: ${room.isPrivate ? 'TRUE' : 'FALSE'}`);
          if (room.dates && room.dates.length > 0) {
            console.log(`  Precio: ${room.dates[0].price || 'N/A'}`);
          }
        });
      } else {
        console.log('\n⚠️ No se encontraron habitaciones disponibles públicamente');
        console.log('Esto es esperado si todas las habitaciones tienen isPrivate=true');
      }
      
      return data.data || [];
    } else {
      console.error('Error en la respuesta:', data.message || 'No hay mensaje de error');
      return [];
    }
  } catch (error) {
    console.error('Error al realizar la solicitud:', error);
    return [];
  }
}

/**
 * Verifica el workaround local (endpoint all-availability)
 */
async function checkLocalWorkaround(roomTypeId = '') {
  console.log('\n=== VERIFICANDO WORKAROUND LOCAL (SIN FILTRO isPrivate) ===');
  
  // Generar fechas para la prueba (30 días en el futuro)
  const today = new Date();
  const startDate = formatDate(new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000));
  const endDate = formatDate(new Date(today.getTime() + 31 * 24 * 60 * 60 * 1000));
  
  console.log(`Fechas de prueba: ${startDate} a ${endDate}`);
  
  // Usar el endpoint local que implementa el workaround
  const url = new URL('http://localhost:5174/api/cloudbeds/all-availability');
  url.searchParams.append('startDate', startDate);
  url.searchParams.append('endDate', endDate);
  
  if (roomTypeId) {
    url.searchParams.append('roomTypeId', roomTypeId);
  }
  
  console.log(`URL: ${url.toString()}`);
  console.log('NOTA: Asegúrate de que el servidor local esté en ejecución');
  
  try {
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });
    
    const data = await response.json();
    
    if (data.success) {
      if (data.data && data.data.length > 0) {
        console.log(`\nSe encontraron ${data.data.length} tipos de habitación con el workaround`);
        console.log('\nHabitaciones disponibles con el workaround:');
        data.data.forEach(room => {
          console.log(`- ${room.roomTypeName || 'Sin nombre'} (ID: ${room.roomTypeId || 'N/A'})`);
          if (room.isPrivate !== undefined) {
            console.log(`  isPrivate: ${room.isPrivate ? 'TRUE' : 'FALSE'}`);
          }
          if (room.dates && room.dates.length > 0) {
            console.log(`  Precio: ${room.dates[0].price || 'N/A'}`);
          }
        });
      } else {
        console.log('\n⚠️ No se encontraron habitaciones disponibles con el workaround');
      }
      
      return data.data || [];
    } else {
      console.error('Error en la respuesta:', data.message || 'No hay mensaje de error');
      return [];
    }
  } catch (error) {
    console.error('Error al realizar la solicitud:', error);
    console.log('Asegúrate de que el servidor local esté en ejecución');
    return [];
  }
}

/**
 * Función principal para ejecutar todas las pruebas
 */
async function runTests() {
  console.log('=== VERIFICACIÓN DEL FLAG isPrivate Y WORKAROUND ===');
  console.log(`Propiedad ID: ${PROPERTY_ID}`);
  
  if (!API_KEY) {
    console.error('ERROR: No se ha proporcionado una API key. Configura la variable de entorno CLOUDBEDS_API_KEY.');
    return;
  }
  
  // Verificar el flag isPrivate en los tipos de habitación
  const roomTypes = await checkRoomTypesPrivateFlag();
  
  // Si hay tipos de habitación, usar el primero para pruebas específicas
  let testRoomTypeId = '';
  if (roomTypes.length > 0) {
    testRoomTypeId = roomTypes[0].roomTypeId;
    console.log(`\nUsando el tipo de habitación "${roomTypes[0].roomTypeName}" (ID: ${testRoomTypeId}) para pruebas específicas`);
  }
  
  // Verificar la disponibilidad pública (con filtro isPrivate)
  await checkPublicAvailability(testRoomTypeId);
  
  // Verificar el workaround local (sin filtro isPrivate)
  await checkLocalWorkaround(testRoomTypeId);
  
  console.log('\n=== RESUMEN DE LA VERIFICACIÓN ===');
  console.log('1. Verifica si los tipos de habitación tienen isPrivate=true');
  console.log('2. Verifica si la API pública filtra correctamente por isPrivate');
  console.log('3. Verifica si el workaround local funciona correctamente');
  
  console.log('\n=== PRÓXIMOS PASOS ===');
  console.log('1. Contactar a Cloudbeds para cambiar el flag isPrivate a false');
  console.log('2. Usar el formato exacto de Manuel Arbelo para crear reservas');
  console.log('3. Una vez que Cloudbeds cambie la configuración, revertir el workaround');
}

// Ejecutar todas las pruebas
runTests().catch(error => {
  console.error('Error al ejecutar las pruebas:', error);
});
