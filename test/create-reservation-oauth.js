#!/usr/bin/env node

/**
 * Script para probar la creación de reservas usando OAuth
 * Ejecutar con: node test/create-reservation-oauth.js
 */

// Importar fetch
const fetch = require('node-fetch');

// Cargar variables de entorno
require('dotenv').config();

// Constantes
const CLIENT_ID = process.env.CLOUDBEDS_CLIENT_ID;
const CLIENT_SECRET = process.env.CLOUDBEDS_CLIENT_SECRET;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';
const AUTH_URL = 'https://hotels.cloudbeds.com/api/v1.1/oauth/token';

// Fechas para la reserva (formato YYYY-MM-DD)
const START_DATE = "2025-05-01";
const END_DATE = "2025-05-02";

/**
 * Obtiene un token OAuth
 * @returns {Promise<string>} Token de acceso
 */
async function getOAuthToken() {
  console.log('Obteniendo token OAuth...');
  
  if (!CLIENT_ID || !CLIENT_SECRET) {
    throw new Error('Se requieren CLIENT_ID y CLIENT_SECRET para OAuth');
  }
  
  const tokenBody = new URLSearchParams();
  tokenBody.append('grant_type', 'client_credentials');
  tokenBody.append('client_id', CLIENT_ID);
  tokenBody.append('client_secret', CLIENT_SECRET);
  
  const response = await fetch(AUTH_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: tokenBody
  });
  
  const tokenData = await response.json();
  
  if (!tokenData.access_token) {
    throw new Error('No se pudo obtener el token OAuth: ' + JSON.stringify(tokenData));
  }
  
  console.log('Token OAuth obtenido correctamente');
  return tokenData.access_token;
}

/**
 * Función principal para probar la creación de reservas usando OAuth
 */
async function testOAuthReservation() {
  console.log('=== PROBANDO CREACIÓN DE RESERVAS USANDO OAUTH ===');
  
  try {
    // Obtener token OAuth
    const accessToken = await getOAuthToken();
    
    // Construir URL con parámetros requeridos
    const url = new URL(`${BASE_URL}/postReservation`);
    url.searchParams.append('propertyID', PROPERTY_ID);
    url.searchParams.append('startDate', START_DATE);
    url.searchParams.append('endDate', END_DATE);
    
    console.log('URL:', url.toString());
    
    // Crear datos de la reserva
    const reservationData = {
      propertyID: PROPERTY_ID,
      startDate: START_DATE,
      endDate: END_DATE,
      guestFirstName: "Test",
      guestLastName: "User",
      guestEmail: "<EMAIL>",
      guestPhone: "+1234567890",
      guestCountry: "ES",
      guestAddress: "Calle de Prueba 123",
      guestCity: "Madrid",
      guestZip: "28001",
      roomTypeID: ROOM_TYPE_ID,
      adults: 1,
      children: 0,
      rooms: [
        {
          roomTypeID: ROOM_TYPE_ID,
          startDate: START_DATE,
          endDate: END_DATE,
          adults: 1,
          children: 0
        }
      ],
      paymentMethod: "noPayment",
      source: "s-2-1",
      status: "confirmed",
      thirdPartyIdentifier: `test-oauth-${Date.now()}`,
      sendEmailConfirmation: false
    };
    
    console.log('Datos de la reserva:', JSON.stringify(reservationData, null, 2));
    
    // Cabeceras con OAuth
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    };
    
    // Realizar la petición
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(reservationData)
    });
    
    // Obtener respuesta
    const responseText = await response.text();
    console.log('Código de estado:', response.status);
    console.log('Respuesta:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('Respuesta JSON:', JSON.stringify(jsonResponse, null, 2));
      
      if (jsonResponse.success) {
        console.log('¡Reserva creada exitosamente!');
        console.log('ID de reserva:', jsonResponse.reservationID);
        console.log('Código de confirmación:', jsonResponse.confirmationCode);
      } else {
        console.error('Error al crear la reserva:', jsonResponse.message);
      }
    } catch (e) {
      console.log('La respuesta no es un JSON válido');
    }
  } catch (error) {
    console.error('Error general:', error);
  } finally {
    console.log('=== PROCESO COMPLETADO ===');
  }
}

// Ejecutar la función principal
testOAuthReservation();
