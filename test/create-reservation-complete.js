#!/usr/bin/env node

/**
 * Script para probar la creación de reservas con datos completos y correctos
 * Ejecutar con: node test/create-reservation-complete.js
 */

// Importar fetch y form-data
const fetch = require('node-fetch');
const FormData = require('form-data');

// Cargar variables de entorno
require('dotenv').config();

// Constantes
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Generar fechas futuras (1 mes a partir de hoy)
const today = new Date();
const futureDate = new Date(today);
futureDate.setMonth(today.getMonth() + 1);
const nextDay = new Date(futureDate);
nextDay.setDate(futureDate.getDate() + 1);

// Formatear fechas como YYYY-MM-DD
const START_DATE = futureDate.toISOString().split('T')[0];
const END_DATE = nextDay.toISOString().split('T')[0];

/**
 * Función principal para probar la creación de reservas con datos completos
 */
async function testCompleteReservation() {
  console.log('=== PROBANDO CREACIÓN DE RESERVAS CON DATOS COMPLETOS Y CORRECTOS ===');
  
  if (!API_KEY) {
    console.error('ERROR: No se ha proporcionado API_KEY. Configure la variable de entorno CLOUDBEDS_API_KEY.');
    return;
  }
  
  try {
    // Construir URL con propertyID
    const url = new URL(`${BASE_URL}/postReservation`);
    url.searchParams.append('propertyID', PROPERTY_ID);
    
    console.log('URL:', url.toString());
    console.log('Fechas de reserva:', START_DATE, 'a', END_DATE);
    console.log('ID de propiedad:', PROPERTY_ID);
    console.log('ID de tipo de habitación:', ROOM_TYPE_ID);
    
    // Crear FormData con datos completos y correctos
    const formData = new FormData();
    
    // Información de la propiedad
    formData.append('propertyID', PROPERTY_ID); // Usar el ID real de la propiedad
    formData.append('startDate', START_DATE);
    formData.append('endDate', END_DATE);
    
    // Información del huésped
    formData.append('guestFirstName', 'John');
    formData.append('guestLastName', 'Doe');
    formData.append('guestEmail', '<EMAIL>');
    formData.append('guestPhone', '+1234567890');
    formData.append('guestCountry', 'US');
    formData.append('guestZip', '12345');
    formData.append('guestAddress', '123 Test St');
    formData.append('guestCity', 'Test City');
    
    // Información de pago
    formData.append('paymentMethod', 'credit');
    
    // Datos de habitación anidados con ID real
    formData.append('rooms[0][roomTypeID]', ROOM_TYPE_ID); // Usar el ID real del tipo de habitación
    formData.append('rooms[0][quantity]', '1');
    
    // Adultos y niños
    formData.append('adults[0][roomTypeID]', ROOM_TYPE_ID); // Usar el ID real del tipo de habitación
    formData.append('adults[0][quantity]', '1');
    formData.append('children[0][roomTypeID]', ROOM_TYPE_ID); // Usar el ID real del tipo de habitación
    formData.append('children[0][quantity]', '0');
    
    // Identificador único
    formData.append('thirdPartyIdentifier', `test-complete-${Date.now()}`);
    
    console.log('Datos del formulario creados con valores reales y completos');
    
    // Cabeceras
    const headers = {
      // FormData establece automáticamente el Content-Type como multipart/form-data con boundary
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    // Realizar la petición
    console.log('Enviando solicitud...');
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: headers,
      body: formData
    });
    
    // Obtener respuesta
    const responseText = await response.text();
    console.log('Código de estado:', response.status);
    console.log('Respuesta:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('Respuesta JSON:', JSON.stringify(jsonResponse, null, 2));
      
      if (jsonResponse.success) {
        console.log('¡Reserva creada exitosamente!');
        console.log('ID de reserva:', jsonResponse.reservationID);
        console.log('Código de confirmación:', jsonResponse.confirmationCode);
      } else {
        console.error('Error al crear la reserva:', jsonResponse.message);
      }
    } catch (e) {
      console.log('La respuesta no es un JSON válido');
    }
  } catch (error) {
    console.error('Error general:', error);
  } finally {
    console.log('=== PROCESO COMPLETADO ===');
  }
}

// Ejecutar la función principal
testCompleteReservation();
