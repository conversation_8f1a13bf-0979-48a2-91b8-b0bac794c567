#!/usr/bin/env node

/**
 * Script para probar la creación de reservas usando formato de formulario
 * Ejecutar con: node test/create-reservation-form.js
 */

// Importar fetch
const fetch = require('node-fetch');

// Cargar variables de entorno
require('dotenv').config();

// Constantes
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Fechas para la reserva (formato YYYY-MM-DD)
const START_DATE = "2025-05-01";
const END_DATE = "2025-05-02";

/**
 * Función principal para probar la creación de reservas con formato de formulario
 */
async function testFormFormat() {
  console.log('=== PROBANDO CREACIÓN DE RESERVAS CON FORMATO DE FORMULARIO ===');
  
  if (!API_KEY) {
    console.error('ERROR: No se ha proporcionado API_KEY. Configure la variable de entorno CLOUDBEDS_API_KEY.');
    return;
  }
  
  try {
    // Construir URL con parámetros requeridos
    const url = new URL(`${BASE_URL}/postReservation`);
    url.searchParams.append('propertyID', PROPERTY_ID);
    url.searchParams.append('startDate', START_DATE);
    url.searchParams.append('endDate', END_DATE);
    
    console.log('URL:', url.toString());
    
    // Crear datos del formulario
    const formData = new URLSearchParams();
    formData.append('propertyID', PROPERTY_ID);
    formData.append('startDate', START_DATE);
    formData.append('endDate', END_DATE);
    formData.append('guestFirstName', 'Test');
    formData.append('guestLastName', 'User');
    formData.append('guestEmail', '<EMAIL>');
    formData.append('guestPhone', '+1234567890');
    formData.append('numAdults', '1');
    formData.append('numChildren', '0');
    formData.append('roomTypeID', ROOM_TYPE_ID);
    formData.append('source', 's-2-1');
    formData.append('status', 'confirmed');
    formData.append('thirdPartyIdentifier', `test-form-${Date.now()}`);
    formData.append('sendEmailConfirmation', 'false');
    
    console.log('Datos del formulario:', formData.toString());
    
    // Cabeceras para formulario
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    // Realizar la petición
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: headers,
      body: formData
    });
    
    // Obtener respuesta
    const responseText = await response.text();
    console.log('Código de estado:', response.status);
    console.log('Respuesta:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('Respuesta JSON:', JSON.stringify(jsonResponse, null, 2));
      
      if (jsonResponse.success) {
        console.log('¡Reserva creada exitosamente!');
        console.log('ID de reserva:', jsonResponse.reservationID);
        console.log('Código de confirmación:', jsonResponse.confirmationCode);
      } else {
        console.error('Error al crear la reserva:', jsonResponse.message);
      }
    } catch (e) {
      console.log('La respuesta no es un JSON válido');
    }
  } catch (error) {
    console.error('Error general:', error);
  } finally {
    console.log('=== PROCESO COMPLETADO ===');
  }
}

// Ejecutar la función principal
testFormFormat();
