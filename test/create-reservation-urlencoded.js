#!/usr/bin/env node

/**
 * Script para probar la creación de reservas usando URLSearchParams
 * Ejecutar con: node test/create-reservation-urlencoded.js
 */

// Importar fetch
const fetch = require('node-fetch');

// Cargar variables de entorno
require('dotenv').config();

// Constantes
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Generar fechas futuras (1 mes a partir de hoy)
const today = new Date();
const futureDate = new Date(today);
futureDate.setMonth(today.getMonth() + 1);
const nextDay = new Date(futureDate);
nextDay.setDate(futureDate.getDate() + 1);

// Formatear fechas como YYYY-MM-DD
const START_DATE = futureDate.toISOString().split('T')[0];
const END_DATE = nextDay.toISOString().split('T')[0];

/**
 * Función principal para probar la creación de reservas con URLSearchParams
 */
async function testURLEncodedReservation() {
  console.log('=== PROBANDO CREACIÓN DE RESERVAS CON URLSEARCHPARAMS ===');
  
  if (!API_KEY) {
    console.error('ERROR: No se ha proporcionado API_KEY. Configure la variable de entorno CLOUDBEDS_API_KEY.');
    return;
  }
  
  try {
    // Construir URL con propertyID
    const url = new URL(`${BASE_URL}/postReservation`);
    url.searchParams.append('propertyID', PROPERTY_ID);
    
    console.log('URL:', url.toString());
    console.log('Fechas de reserva:', START_DATE, 'a', END_DATE);
    console.log('ID de propiedad:', PROPERTY_ID);
    console.log('ID de tipo de habitación:', ROOM_TYPE_ID);
    
    // Crear URLSearchParams con datos completos y correctos
    const params = new URLSearchParams();
    
    // Información de la propiedad
    params.append('propertyID', PROPERTY_ID);
    params.append('startDate', START_DATE);
    params.append('endDate', END_DATE);
    
    // Información del huésped
    params.append('guestFirstName', 'John');
    params.append('guestLastName', 'Doe');
    params.append('guestEmail', '<EMAIL>');
    params.append('guestPhone', '+1234567890');
    params.append('guestCountry', 'US');
    params.append('guestZip', '12345');
    params.append('guestAddress', '123 Test St');
    params.append('guestCity', 'Test City');
    
    // Información de pago
    params.append('paymentMethod', 'credit');
    
    // Datos de habitación anidados con ID real
    params.append('rooms[0][roomTypeID]', ROOM_TYPE_ID);
    params.append('rooms[0][quantity]', '1');
    
    // Adultos y niños
    params.append('adults[0][roomTypeID]', ROOM_TYPE_ID);
    params.append('adults[0][quantity]', '1');
    params.append('children[0][roomTypeID]', ROOM_TYPE_ID);
    params.append('children[0][quantity]', '0');
    
    // Identificador único
    params.append('thirdPartyIdentifier', `test-urlencoded-${Date.now()}`);
    
    console.log('Datos URLSearchParams creados con valores reales y completos');
    console.log('Datos enviados:', params.toString());
    
    // Cabeceras
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    // Realizar la petición
    console.log('Enviando solicitud...');
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: headers,
      body: params
    });
    
    // Obtener respuesta
    const responseText = await response.text();
    console.log('Código de estado:', response.status);
    console.log('Respuesta completa:', responseText);
    console.log('Headers de respuesta:', response.headers);
    console.log('Content-Type de respuesta:', response.headers.get('content-type'));
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('Respuesta JSON:', JSON.stringify(jsonResponse, null, 2));
      
      if (jsonResponse.success) {
        console.log('¡Reserva creada exitosamente!');
        console.log('ID de reserva:', jsonResponse.reservationID);
        console.log('Código de confirmación:', jsonResponse.confirmationCode);
      } else {
        console.error('Error al crear la reserva:', jsonResponse.message);
      }
    } catch (e) {
      console.log('La respuesta no es un JSON válido');
    }
  } catch (error) {
    console.error('Error general:', error);
  } finally {
    console.log('=== PROCESO COMPLETADO ===');
  }
}

// Ejecutar la función principal
testURLEncodedReservation();
