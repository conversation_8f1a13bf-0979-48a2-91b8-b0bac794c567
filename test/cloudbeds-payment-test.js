#!/usr/bin/env node

/**
 * Final test script for Cloudbeds reservation API with payment method
 * Run with: node cloudbeds-payment-test.js
 */

// Import node's built-in fetch
const fetch = require('node-fetch');

// Try to load dotenv
try {
  require('dotenv').config({ path: './.env' });
  console.log("Loaded .env file");
} catch (e) {
  console.log('dotenv error:', e);
}

// Constants
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

/**
 * Test with payment method parameter added for Cloudbeds reservation API
 */
async function testWithPaymentMethod() {
  console.log('=== FINAL TEST WITH PAYMENT METHOD FOR CLOUDBEDS RESERVATION API ===');
  
  try {
    const url = `${BASE_URL}/postReservation`;
    
    // Create form data with all parameters including payment method
    const formData = new URLSearchParams();
    // Property
    formData.append('propertyID', PROPERTY_ID);
    // Guest
    formData.append('guestFirstName', 'Test');
    formData.append('guestLastName', 'User');
    formData.append('guestEmail', '<EMAIL>');
    formData.append('guestPhone', '+1234567890');
    // Dates
    formData.append('startDate', '2025-05-01');
    formData.append('endDate', '2025-05-02');
    // Room
    formData.append('roomTypeID', ROOM_TYPE_ID);
    // Required parameters
    formData.append('adults', '1');
    formData.append('children', '0');
    // Room array 
    formData.append('rooms[0][roomTypeID]', ROOM_TYPE_ID);
    formData.append('rooms[0][startDate]', '2025-05-01');
    formData.append('rooms[0][endDate]', '2025-05-02');
    formData.append('rooms[0][adults]', '1');
    formData.append('rooms[0][children]', '0');
    // Payment
    formData.append('paymentMethod', 'noPayment');  // No payment, cash on arrival, credit card
    // Other
    formData.append('source', 's-2-1');
    formData.append('status', 'confirmed');
    formData.append('thirdPartyIdentifier', `test-payment-${Date.now()}`);
    formData.append('sendEmailConfirmation', 'false');
    
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    console.log('Form data:', formData.toString());
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: formData
    });
    
    const responseText = await response.text();
    console.log('Status:', response.status);
    console.log('Response:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error in payment method test:', error);
  }
  
  // Test with both numAdults and rooms
  console.log('\n=== TEST 2: With numAdults and rooms ===');
  try {
    const url = `${BASE_URL}/postReservation`;
    
    // Basic form data with both numAdults and rooms
    const formData = new URLSearchParams();
    formData.append('propertyID', PROPERTY_ID);
    formData.append('startDate', '2025-05-01');
    formData.append('endDate', '2025-05-02');
    formData.append('roomTypeID', ROOM_TYPE_ID);
    formData.append('guestFirstName', 'Test');
    formData.append('guestLastName', 'User');
    formData.append('guestEmail', '<EMAIL>');
    // Both formats for adults/children
    formData.append('numAdults', '1');
    formData.append('numChildren', '0');
    formData.append('adults', '1');
    formData.append('children', '0');
    // Rooms parameter
    formData.append('rooms[0][roomTypeID]', ROOM_TYPE_ID);
    formData.append('rooms[0][startDate]', '2025-05-01');
    formData.append('rooms[0][endDate]', '2025-05-02');
    formData.append('rooms[0][adults]', '1');
    formData.append('rooms[0][children]', '0');
    // Payment
    formData.append('paymentMethod', 'noPayment');
    // Other
    formData.append('thirdPartyIdentifier', `test-both-${Date.now()}`);
    formData.append('status', 'confirmed');
    formData.append('source', 's-2-1');
    formData.append('sendEmailConfirmation', 'false');
    
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    console.log('Form data:', formData.toString());
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: formData
    });
    
    const responseText = await response.text();
    console.log('Status:', response.status);
    console.log('Response:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error in both formats test:', error);
  }
  
  // Try OAuth if possible
  if (process.env.CLOUDBEDS_CLIENT_ID && process.env.CLOUDBEDS_CLIENT_SECRET) {
    console.log('\n=== TEST 3: With OAuth ===');
    try {
      // First get the OAuth token
      const tokenUrl = 'https://hotels.cloudbeds.com/api/v1.1/oauth/token';
      const tokenBody = new URLSearchParams();
      tokenBody.append('grant_type', 'client_credentials');
      tokenBody.append('client_id', process.env.CLOUDBEDS_CLIENT_ID);
      tokenBody.append('client_secret', process.env.CLOUDBEDS_CLIENT_SECRET);
      
      const tokenResponse = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: tokenBody
      });
      
      const tokenData = await tokenResponse.json();
      console.log('Token response:', JSON.stringify(tokenData, null, 2));
      
      if (tokenData.access_token) {
        const url = `${BASE_URL}/postReservation`;
        
        // Form data with OAuth
        const formData = new URLSearchParams();
        formData.append('propertyID', PROPERTY_ID);
        formData.append('startDate', '2025-05-01');
        formData.append('endDate', '2025-05-02');
        formData.append('roomTypeID', ROOM_TYPE_ID);
        formData.append('guestFirstName', 'Test');
        formData.append('guestLastName', 'User');
        formData.append('guestEmail', '<EMAIL>');
        formData.append('adults', '1');
        formData.append('children', '0');
        formData.append('rooms[0][roomTypeID]', ROOM_TYPE_ID);
        formData.append('rooms[0][startDate]', '2025-05-01');
        formData.append('rooms[0][endDate]', '2025-05-02');
        formData.append('rooms[0][adults]', '1');
        formData.append('rooms[0][children]', '0');
        formData.append('paymentMethod', 'noPayment');
        formData.append('thirdPartyIdentifier', `test-oauth-${Date.now()}`);
        formData.append('status', 'confirmed');
        formData.append('source', 's-2-1');
        formData.append('sendEmailConfirmation', 'false');
        
        const headers = {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
          'Authorization': `Bearer ${tokenData.access_token}`
        };
        
        console.log('Form data:', formData.toString());
        
        const response = await fetch(url, {
          method: 'POST',
          headers,
          body: formData
        });
        
        const responseText = await response.text();
        console.log('Status:', response.status);
        console.log('Response:', responseText);
        
        try {
          const jsonResponse = JSON.parse(responseText);
          console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
        } catch (e) {
          console.log('Response is not valid JSON');
        }
      } else {
        console.log('Could not obtain OAuth token');
      }
    } catch (error) {
      console.error('Error in OAuth test:', error);
    }
  } else {
    console.log('\nSkipping OAuth test - no client credentials in .env');
  }
}

// Run the test
testWithPaymentMethod(); 