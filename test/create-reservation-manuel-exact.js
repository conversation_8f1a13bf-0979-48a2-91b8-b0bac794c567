#!/usr/bin/env node

/**
 * Script para probar la creación de reservas usando EXACTAMENTE el ejemplo de Manuel Arbelo
 * Ejecutar con: node test/create-reservation-manuel-exact.js
 */

// Importar fetch y form-data
const fetch = require('node-fetch');
const FormData = require('form-data');

// Cargar variables de entorno
require('dotenv').config();

// Constantes
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

/**
 * Función principal para probar la creación de reservas usando EXACTAMENTE el ejemplo de Manuel
 */
async function testManuelExact() {
  console.log('=== PROBANDO CREACIÓN DE RESERVAS USANDO EXACTAMENTE EL EJEMPLO DE MANUEL ARBELO ===');
  
  if (!API_KEY) {
    console.error('ERROR: No se ha proporcionado API_KEY. Configure la variable de entorno CLOUDBEDS_API_KEY.');
    return;
  }
  
  try {
    // Construir URL con solo propertyID como parámetro (formato sugerido por Manuel)
    const url = new URL(`${BASE_URL}/postReservation`);
    url.searchParams.append('propertyID', PROPERTY_ID);
    
    console.log('URL:', url.toString());
    
    // Crear FormData exactamente como en el ejemplo de Manuel
    const formData = new FormData();
    
    // Usar EXACTAMENTE los mismos valores que en el ejemplo de Manuel
    formData.append('propertyID', '');
    formData.append('startDate', '2025-02-17');
    formData.append('endDate', '2025-02-18');
    formData.append('guestFirstName', 'John');
    formData.append('guestLastName', 'Doe');
    formData.append('guestCountry', 'US');
    formData.append('guestZip', '1234');
    formData.append('guestEmail', '<EMAIL>');
    formData.append('guestPhone', '4567');
    formData.append('paymentMethod', 'credit');
    formData.append('rooms[0][roomTypeID]', '');
    formData.append('rooms[0][quantity]', '1');
    formData.append('adults[0][roomTypeID]', '');
    formData.append('adults[0][quantity]', '1');
    formData.append('children[0][roomTypeID]', '');
    formData.append('children[0][quantity]', '0');
    
    // Añadir un identificador único para evitar duplicados
    formData.append('thirdPartyIdentifier', `test-manuel-exact-${Date.now()}`);
    
    console.log('Datos del formulario creados EXACTAMENTE según el ejemplo de Manuel');
    
    // Cabeceras
    const headers = {
      // FormData establece automáticamente el Content-Type como multipart/form-data con el boundary correcto
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    // Realizar la petición
    console.log('Enviando solicitud...');
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: headers,
      body: formData
    });
    
    // Obtener respuesta
    const responseText = await response.text();
    console.log('Código de estado:', response.status);
    console.log('Respuesta:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('Respuesta JSON:', JSON.stringify(jsonResponse, null, 2));
      
      if (jsonResponse.success) {
        console.log('¡Reserva creada exitosamente!');
        console.log('ID de reserva:', jsonResponse.reservationID);
        console.log('Código de confirmación:', jsonResponse.confirmationCode);
      } else {
        console.error('Error al crear la reserva:', jsonResponse.message);
      }
    } catch (e) {
      console.log('La respuesta no es un JSON válido');
    }
  } catch (error) {
    console.error('Error general:', error);
  } finally {
    console.log('=== PROCESO COMPLETADO ===');
  }
}

// Ejecutar la función principal
testManuelExact();
