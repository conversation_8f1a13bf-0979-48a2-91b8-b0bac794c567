#!/usr/bin/env node

/**
 * Script para probar múltiples formatos de creación de reservas en Cloudbeds
 * Ejecutar con: node test/create-reservation-multi.js
 */

// Importar fetch
const fetch = require('node-fetch');

// Cargar variables de entorno
require('dotenv').config();

// Constantes
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Fechas para la reserva (formato YYYY-MM-DD)
const START_DATE = "2025-05-01";
const END_DATE = "2025-05-02";

/**
 * Función principal para probar múltiples formatos
 */
async function testMultipleFormats() {
  console.log('=== PROBANDO MÚLTIPLES FORMATOS DE CREACIÓN DE RESERVAS ===');
  
  if (!API_KEY) {
    console.error('ERROR: No se ha proporcionado API_KEY. Configure la variable de entorno CLOUDBEDS_API_KEY.');
    return;
  }
  
  try {
    // Formato 1: JSON con parámetros en URL
    await testFormat({
      name: "JSON con parámetros en URL",
      url: `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}&startDate=${START_DATE}&endDate=${END_DATE}`,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-api-key': API_KEY
      },
      body: JSON.stringify({
        propertyID: PROPERTY_ID,
        guestFirstName: "Test",
        guestLastName: "User",
        guestEmail: "<EMAIL>",
        guestPhone: "+1234567890",
        startDate: START_DATE,
        endDate: END_DATE,
        numAdults: 1,
        numChildren: 0,
        roomTypeID: ROOM_TYPE_ID,
        source: "s-2-1",
        status: "confirmed",
        thirdPartyIdentifier: `test-format1-${Date.now()}`,
        sendEmailConfirmation: false
      }),
      contentType: 'application/json'
    });
    
    // Formato 2: Form URL Encoded con parámetros en URL
    const formData = new URLSearchParams();
    formData.append('propertyID', PROPERTY_ID);
    formData.append('guestFirstName', 'Test');
    formData.append('guestLastName', 'User');
    formData.append('guestEmail', '<EMAIL>');
    formData.append('guestPhone', '+1234567890');
    formData.append('startDate', START_DATE);
    formData.append('endDate', END_DATE);
    formData.append('numAdults', '1');
    formData.append('numChildren', '0');
    formData.append('roomTypeID', ROOM_TYPE_ID);
    formData.append('source', 's-2-1');
    formData.append('status', 'confirmed');
    formData.append('thirdPartyIdentifier', `test-format2-${Date.now()}`);
    formData.append('sendEmailConfirmation', 'false');
    
    await testFormat({
      name: "Form URL Encoded con parámetros en URL",
      url: `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}&startDate=${START_DATE}&endDate=${END_DATE}`,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'x-api-key': API_KEY
      },
      body: formData,
      contentType: 'application/x-www-form-urlencoded'
    });
    
    // Formato 3: JSON con estructura roomsData
    await testFormat({
      name: "JSON con estructura roomsData",
      url: `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}&startDate=${START_DATE}&endDate=${END_DATE}`,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-api-key': API_KEY
      },
      body: JSON.stringify({
        propertyID: PROPERTY_ID,
        guestData: {
          firstName: "Test",
          lastName: "User",
          email: "<EMAIL>",
          phone: "+1234567890"
        },
        roomsData: [{
          roomTypeID: ROOM_TYPE_ID,
          startDate: START_DATE,
          endDate: END_DATE,
          adults: 1,
          children: 0
        }],
        startDate: START_DATE,
        endDate: END_DATE,
        status: "confirmed",
        sourceID: "s-2-1",
        thirdPartyIdentifier: `test-format3-${Date.now()}`,
        sendEmailConfirmation: false
      }),
      contentType: 'application/json'
    });
    
    // Formato 4: JSON con estructura rooms
    await testFormat({
      name: "JSON con estructura rooms",
      url: `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}&startDate=${START_DATE}&endDate=${END_DATE}`,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-api-key': API_KEY
      },
      body: JSON.stringify({
        propertyID: PROPERTY_ID,
        guestFirstName: "Test",
        guestLastName: "User",
        guestEmail: "<EMAIL>",
        guestPhone: "+1234567890",
        startDate: START_DATE,
        endDate: END_DATE,
        rooms: [{
          roomTypeID: ROOM_TYPE_ID,
          startDate: START_DATE,
          endDate: END_DATE,
          adults: 1,
          children: 0
        }],
        source: "s-2-1",
        status: "confirmed",
        thirdPartyIdentifier: `test-format4-${Date.now()}`,
        sendEmailConfirmation: false
      }),
      contentType: 'application/json'
    });
    
  } catch (error) {
    console.error('Error general:', error);
  } finally {
    console.log('=== TODAS LAS PRUEBAS COMPLETADAS ===');
  }
}

/**
 * Función para probar un formato específico
 */
async function testFormat({ name, url, headers, body, contentType }) {
  console.log(`\n=== PROBANDO FORMATO: ${name} ===`);
  console.log('URL:', url);
  console.log('Headers:', JSON.stringify(headers, (key, value) => 
    key === 'x-api-key' ? '[HIDDEN]' : value
  ));
  
  if (contentType === 'application/json') {
    console.log('Body:', body);
  } else {
    console.log('Body: [Form data]');
  }
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: body
    });
    
    const responseText = await response.text();
    console.log('Código de estado:', response.status);
    console.log('Respuesta:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('Respuesta JSON:', JSON.stringify(jsonResponse, null, 2));
      
      if (jsonResponse.success) {
        console.log('¡ÉXITO! Reserva creada con formato:', name);
        console.log('ID de reserva:', jsonResponse.reservationID);
        console.log('Código de confirmación:', jsonResponse.confirmationCode);
      } else {
        console.error('Error con formato', name, ':', jsonResponse.message);
      }
    } catch (e) {
      console.log('La respuesta no es un JSON válido');
    }
  } catch (error) {
    console.error(`Error probando formato ${name}:`, error);
  }
}

// Ejecutar la función principal
testMultipleFormats();
