#!/bin/bash

# Script para probar la creación de reservas usando curl exactamente como en el ejemplo de Manuel
# Ejecutar con: bash test/create-reservation-curl.sh

# Cargar variables de entorno
source .env

# Verificar que la API key esté configurada
if [ -z "$CLOUDBEDS_API_KEY" ]; then
  echo "ERROR: No se ha proporcionado CLOUDBEDS_API_KEY. Configure la variable de entorno."
  exit 1
fi

# Constantes
PROPERTY_ID=${CLOUDBEDS_PROPERTY_ID:-317353}
THIRD_PARTY_ID="test-curl-$(date +%s)"

echo "=== PROBANDO CREACIÓN DE RESERVAS USANDO CURL EXACTAMENTE COMO EN EL EJEMPLO DE MANUEL ==="
echo "URL: https://api.cloudbeds.com/api/v1.2/postReservation?propertyID=$PROPERTY_ID"

# Ejecutar curl exactamente como en el ejemplo de Manuel
curl --location "https://api.cloudbeds.com/api/v1.2/postReservation?propertyID=$PROPERTY_ID" \
--header "x-api-key: $CLOUDBEDS_API_KEY" \
--form 'propertyID=""' \
--form 'startDate="2025-02-17"' \
--form 'endDate="2025-02-18"' \
--form 'guestFirstName="John"' \
--form 'guestLastName="Doe"' \
--form 'guestCountry="US"' \
--form 'guestZip="1234"' \
--form 'guestEmail="<EMAIL>"' \
--form 'guestPhone="4567"' \
--form 'paymentMethod="credit"' \
--form 'rooms[0][roomTypeID]=""' \
--form 'rooms[0][quantity]="1"' \
--form 'adults[0][roomTypeID]=""' \
--form 'adults[0][quantity]="1"' \
--form 'children[0][roomTypeID]=""' \
--form 'children[0][quantity]="0"' \
--form "thirdPartyIdentifier=$THIRD_PARTY_ID"

echo ""
echo "=== PROCESO COMPLETADO ==="
