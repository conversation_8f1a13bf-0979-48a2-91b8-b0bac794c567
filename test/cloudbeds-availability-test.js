/**
 * Test script for Cloudbeds availability API
 * 
 * This script tests the Cloudbeds availability API directly to diagnose issues
 * with empty data arrays in the response.
 * 
 * Usage:
 * node test/cloudbeds-availability-test.js
 */

// Load environment variables from .env file
require('dotenv').config();

// Check if required environment variables are set
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '653496';

// Validate environment variables
if (!API_KEY) {
  console.error('ERROR: No API key provided. Set CLOUDBEDS_API_KEY environment variable.');
  process.exit(1);
}

// Main function
async function testAvailability() {
  console.log('=== TESTING CLOUDBEDS AVAILABILITY API ===');
  console.log('Environment variables:');
  console.log(`- PROPERTY_ID: ${PROPERTY_ID}`);
  console.log(`- ROOM_TYPE_ID: ${ROOM_TYPE_ID}`);
  console.log(`- API_KEY present: ${API_KEY ? 'YES' : 'NO'}`);
  
  // Calculate date range for availability check (next 30 days)
  const today = new Date();
  const endDate = new Date(today);
  endDate.setDate(today.getDate() + 30);
  
  const startDateStr = formatDate(today);
  const endDateStr = formatDate(endDate);
  
  console.log(`\nChecking availability for date range: ${startDateStr} to ${endDateStr}`);
  
  // Test 1: Get availability for all room types
  console.log('\n=== TEST 1: Get availability for all room types ===');
  await testGetAvailability(startDateStr, endDateStr);
  
  // Test 2: Get availability for specific room type
  console.log('\n=== TEST 2: Get availability for specific room type ===');
  await testGetAvailability(startDateStr, endDateStr, ROOM_TYPE_ID);
  
  // Test 3: Get room types to verify API access
  console.log('\n=== TEST 3: Get room types to verify API access ===');
  await testGetRoomTypes();
  
  console.log('\n=== TESTS COMPLETED ===');
}

// Test function for getAvailableRoomTypes endpoint
async function testGetAvailability(startDate, endDate, roomTypeId = null) {
  try {
    // Construct URL
    const baseUrl = 'https://api.cloudbeds.com/api/v1.2';
    const url = new URL(`${baseUrl}/getAvailableRoomTypes`);
    
    // Add query parameters
    url.searchParams.append('startDate', startDate);
    url.searchParams.append('endDate', endDate);
    url.searchParams.append('propertyID', PROPERTY_ID);
    
    if (roomTypeId) {
      url.searchParams.append('roomTypeId', roomTypeId);
    }
    
    console.log(`Request URL: ${url.toString()}`);
    
    // Prepare headers
    const headers = {
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    console.log('Sending request...');
    const startTime = Date.now();
    
    // Send request
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers
    });
    
    const endTime = Date.now();
    console.log(`Response received in ${endTime - startTime}ms`);
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    // Parse response
    const responseText = await response.text();
    
    try {
      const data = JSON.parse(responseText);
      console.log('Response data:');
      console.log(JSON.stringify(data, null, 2));
      
      // Analyze response
      if (data.success) {
        console.log('Success: true');
        
        if (data.data && data.data.length > 0) {
          console.log(`Found ${data.data.length} room types with availability data`);
          
          // Log first room type details
          const firstRoom = data.data[0];
          console.log('First room type details:');
          console.log(`- Room Type ID: ${firstRoom.roomTypeId || 'N/A'}`);
          console.log(`- Property ID: ${firstRoom.propertyID || 'N/A'}`);
          console.log(`- Dates count: ${firstRoom.dates ? firstRoom.dates.length : 0}`);
          
          if (firstRoom.dates && firstRoom.dates.length > 0) {
            console.log('First date availability:');
            console.log(JSON.stringify(firstRoom.dates[0], null, 2));
          }
        } else {
          console.log('WARNING: Empty data array returned');
          console.log('This indicates the room type might not be configured for public booking');
        }
      } else {
        console.log('API returned error:');
        console.log(JSON.stringify(data.error, null, 2));
      }
    } catch (error) {
      console.error('Error parsing response as JSON:', error);
      console.log('Raw response:', responseText);
    }
  } catch (error) {
    console.error('Error making API request:', error);
  }
}

// Test function for getRoomTypes endpoint
async function testGetRoomTypes() {
  try {
    // Construct URL
    const baseUrl = 'https://api.cloudbeds.com/api/v1.2';
    const url = new URL(`${baseUrl}/getRoomTypes`);
    
    // Add query parameters
    url.searchParams.append('propertyID', PROPERTY_ID);
    url.searchParams.append('includePhotos', '1');
    url.searchParams.append('includeAmenities', '1');
    
    console.log(`Request URL: ${url.toString()}`);
    
    // Prepare headers
    const headers = {
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    console.log('Sending request...');
    const startTime = Date.now();
    
    // Send request
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers
    });
    
    const endTime = Date.now();
    console.log(`Response received in ${endTime - startTime}ms`);
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    // Parse response
    const responseText = await response.text();
    
    try {
      const data = JSON.parse(responseText);
      
      // Analyze response
      if (data.success) {
        console.log('Success: true');
        
        if (data.data && data.data.length > 0) {
          console.log(`Found ${data.data.length} room types`);
          
          // Log room types summary
          console.log('Room types summary:');
          data.data.forEach((room, index) => {
            console.log(`${index + 1}. ${room.roomTypeName || 'Unnamed'} (ID: ${room.roomTypeID || 'N/A'})`);
            console.log(`   - Max Occupancy: ${room.maxGuests || 'N/A'}`);
            console.log(`   - Is Active: ${room.isActive !== undefined ? room.isActive : 'N/A'}`);
            console.log(`   - Is Virtual: ${room.isVirtual !== undefined ? room.isVirtual : 'N/A'}`);
          });
          
          // Check if our target room type exists
          const targetRoom = data.data.find(room => room.roomTypeID === ROOM_TYPE_ID);
          if (targetRoom) {
            console.log(`\nTarget room type (${ROOM_TYPE_ID}) found:`);
            console.log(JSON.stringify(targetRoom, null, 2));
          } else {
            console.log(`\nWARNING: Target room type (${ROOM_TYPE_ID}) not found in the list!`);
          }
        } else {
          console.log('WARNING: No room types returned');
        }
      } else {
        console.log('API returned error:');
        console.log(JSON.stringify(data.error, null, 2));
      }
    } catch (error) {
      console.error('Error parsing response as JSON:', error);
      console.log('Raw response:', responseText);
    }
  } catch (error) {
    console.error('Error making API request:', error);
  }
}

// Helper function to format date as YYYY-MM-DD
function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// Run the tests
testAvailability().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
