{"name": "cloudbeds-api-test", "version": "1.0.0", "description": "Simple test script for Cloudbeds API", "main": "cloudbeds-reservation-test.js", "scripts": {"test": "node cloudbeds-reservation-test.js", "test:comprehensive": "node cloudbeds-comprehensive-test.js", "test:create": "node create-reservation.js", "test:create-alt": "node create-reservation-alt.js", "test:create-multi": "node create-reservation-multi.js", "test:create-startdate": "node create-reservation-startdate.js", "test:create-local": "node create-reservation-local.js", "test:create-direct": "node create-reservation-direct.js", "test:create-form": "node create-reservation-form.js", "test:create-rooms": "node create-reservation-rooms.js", "test:create-nested": "node create-reservation-nested.js", "test:create-local-nested": "node create-reservation-local-nested.js", "test:create-flat": "node create-reservation-flat.js", "test:create-rooms-array": "node create-reservation-rooms-array.js", "test:create-form-rooms": "node create-reservation-form-rooms.js", "test:create-simple": "node create-reservation-simple.js", "test:create-oauth": "node create-reservation-oauth.js", "test:create-manuel": "node create-reservation-manuel.js", "test:create-manuel-form": "node create-reservation-manuel-form.js", "test:create-manuel-form-rooms": "node create-reservation-manuel-form-rooms.js", "test:create-manuel-form-rooms-alt": "node create-reservation-manuel-form-rooms-alt.js", "test:create-manuel-form-simple": "node create-reservation-manuel-form-simple.js", "test:create-manuel-example": "node create-reservation-manuel-example.js", "test:create-manuel-exact": "node create-reservation-manuel-exact.js", "test:create-complete": "node create-reservation-complete.js", "test:create-json": "node create-reservation-json.js", "test:create-form-browser": "node create-reservation-form-browser.js", "test:create-urlencoded": "node create-reservation-urlencoded.js", "test:all": "node run-all-tests.js"}, "dependencies": {"dotenv": "^16.4.7", "formdata-node": "^6.0.3", "node-fetch": "^2.7.0"}}