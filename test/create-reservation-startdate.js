#!/usr/bin/env node

/**
 * Script para probar específicamente el problema del parámetro startDate
 * Ejecutar con: node test/create-reservation-startdate.js
 */

// Importar fetch
const fetch = require('node-fetch');

// Cargar variables de entorno
require('dotenv').config();

// Constantes
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Fechas para la reserva (formato YYYY-MM-DD)
const START_DATE = "2025-05-01";
const END_DATE = "2025-05-02";

/**
 * Función principal para probar el problema del parámetro startDate
 */
async function testStartDateParameter() {
  console.log('=== PROBANDO PROBLEMA DEL PARÁMETRO startDate ===');
  
  if (!API_KEY) {
    console.error('ERROR: No se ha proporcionado API_KEY. Configure la variable de entorno CLOUDBEDS_API_KEY.');
    return;
  }
  
  try {
    // Prueba 1: startDate solo en URL
    await testStartDateFormat({
      name: "startDate solo en URL",
      url: `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}&startDate=${START_DATE}&endDate=${END_DATE}`,
      includeInBody: false
    });
    
    // Prueba 2: startDate solo en cuerpo
    await testStartDateFormat({
      name: "startDate solo en cuerpo",
      url: `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}`,
      includeInBody: true
    });
    
    // Prueba 3: startDate en URL y cuerpo
    await testStartDateFormat({
      name: "startDate en URL y cuerpo",
      url: `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}&startDate=${START_DATE}&endDate=${END_DATE}`,
      includeInBody: true
    });
    
    // Prueba 4: startDate en URL y cuerpo (con formato diferente)
    await testStartDateFormat({
      name: "startDate en URL y cuerpo (con formato diferente)",
      url: `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}&startDate=${START_DATE}&endDate=${END_DATE}`,
      includeInBody: true,
      useAlternativeFormat: true
    });
    
  } catch (error) {
    console.error('Error general:', error);
  } finally {
    console.log('=== TODAS LAS PRUEBAS COMPLETADAS ===');
  }
}

/**
 * Función para probar un formato específico del parámetro startDate
 */
async function testStartDateFormat({ name, url, includeInBody, useAlternativeFormat = false }) {
  console.log(`\n=== PROBANDO FORMATO: ${name} ===`);
  console.log('URL:', url);
  
  // Crear cuerpo de la solicitud
  let body;
  
  if (useAlternativeFormat) {
    // Formato alternativo (con roomsData)
    body = {
      propertyID: PROPERTY_ID,
      guestData: {
        firstName: "Test",
        lastName: "User",
        email: "<EMAIL>",
        phone: "+1234567890"
      },
      roomsData: [{
        roomTypeID: ROOM_TYPE_ID,
        startDate: START_DATE,
        endDate: END_DATE,
        adults: 1,
        children: 0
      }],
      status: "confirmed",
      sourceID: "s-2-1",
      thirdPartyIdentifier: `test-startdate-${Date.now()}`,
      sendEmailConfirmation: false
    };
    
    // Añadir startDate y endDate a nivel de reserva si se solicita
    if (includeInBody) {
      body.startDate = START_DATE;
      body.endDate = END_DATE;
    }
  } else {
    // Formato estándar
    body = {
      propertyID: PROPERTY_ID,
      guestFirstName: "Test",
      guestLastName: "User",
      guestEmail: "<EMAIL>",
      guestPhone: "+1234567890",
      numAdults: 1,
      numChildren: 0,
      roomTypeID: ROOM_TYPE_ID,
      source: "s-2-1",
      status: "confirmed",
      thirdPartyIdentifier: `test-startdate-${Date.now()}`,
      sendEmailConfirmation: false
    };
    
    // Añadir startDate y endDate a nivel de reserva si se solicita
    if (includeInBody) {
      body.startDate = START_DATE;
      body.endDate = END_DATE;
    }
  }
  
  console.log('Body:', JSON.stringify(body, null, 2));
  
  // Cabeceras
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'x-api-key': API_KEY
  };
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(body)
    });
    
    const responseText = await response.text();
    console.log('Código de estado:', response.status);
    console.log('Respuesta:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('Respuesta JSON:', JSON.stringify(jsonResponse, null, 2));
      
      if (jsonResponse.success) {
        console.log('¡ÉXITO! Reserva creada con formato:', name);
        console.log('ID de reserva:', jsonResponse.reservationID);
        console.log('Código de confirmación:', jsonResponse.confirmationCode);
      } else {
        console.error('Error con formato', name, ':', jsonResponse.message);
      }
    } catch (e) {
      console.log('La respuesta no es un JSON válido');
    }
  } catch (error) {
    console.error(`Error probando formato ${name}:`, error);
  }
}

// Ejecutar la función principal
testStartDateParameter();
