#!/usr/bin/env node

/**
 * Complete test script for Cloudbeds reservation API
 * Run with: node cloudbeds-complete-test.js
 */

// Import node's built-in fetch
const fetch = require('node-fetch');

// Try to load dotenv
try {
  require('dotenv').config({ path: './.env' });
  console.log("Loaded .env file");
} catch (e) {
  console.log('dotenv error:', e);
}

// Constants
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

/**
 * Test with all required parameters for Cloudbeds reservation API
 */
async function testCompleteReservation() {
  console.log('=== COMPLETE TEST FOR CLOUDBEDS RESERVATION API ===');
  
  try {
    const url = `${BASE_URL}/postReservation`;
    
    // Create form data with ALL identified required parameters
    const formData = new URLSearchParams();
    // Property
    formData.append('propertyID', PROPERTY_ID);
    // Guest info
    formData.append('guestFirstName', 'Test');
    formData.append('guestLastName', 'User');
    formData.append('guestEmail', '<EMAIL>');
    formData.append('guestPhone', '+1234567890');
    formData.append('guestCountry', 'ES'); // Spain - ISO country code
    formData.append('guestAddress', 'Test Address');
    formData.append('guestCity', 'Madrid');
    formData.append('guestZip', '28001');
    // Dates
    formData.append('startDate', '2025-05-01');
    formData.append('endDate', '2025-05-02');
    // Room
    formData.append('roomTypeID', ROOM_TYPE_ID);
    // Required parameters
    formData.append('adults', '1');
    formData.append('children', '0');
    // Room array 
    formData.append('rooms[0][roomTypeID]', ROOM_TYPE_ID);
    formData.append('rooms[0][startDate]', '2025-05-01');
    formData.append('rooms[0][endDate]', '2025-05-02');
    formData.append('rooms[0][adults]', '1');
    formData.append('rooms[0][children]', '0');
    // Payment
    formData.append('paymentMethod', 'noPayment');  // No payment, cash on arrival, credit card
    // Other
    formData.append('source', 's-2-1');
    formData.append('status', 'confirmed');
    formData.append('thirdPartyIdentifier', `test-complete-${Date.now()}`);
    formData.append('sendEmailConfirmation', 'false');
    
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    console.log('Form data:', formData.toString());
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: formData
    });
    
    const responseText = await response.text();
    console.log('Status:', response.status);
    console.log('Response:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error in complete test:', error);
  }
  
  // Test with property ID in header
  console.log('\n=== TEST 2: With Property ID in Header ===');
  try {
    const url = `${BASE_URL}/postReservation`;
    
    // Create form data with ALL identified required parameters
    const formData = new URLSearchParams();
    // Guest info
    formData.append('guestFirstName', 'Test');
    formData.append('guestLastName', 'User');
    formData.append('guestEmail', '<EMAIL>');
    formData.append('guestPhone', '+1234567890');
    formData.append('guestCountry', 'ES'); // Spain - ISO country code
    formData.append('guestAddress', 'Test Address');
    formData.append('guestCity', 'Madrid');
    formData.append('guestZip', '28001');
    // Dates
    formData.append('startDate', '2025-05-01');
    formData.append('endDate', '2025-05-02');
    // Room
    formData.append('roomTypeID', ROOM_TYPE_ID);
    // Required parameters
    formData.append('adults', '1');
    formData.append('children', '0');
    // Room array 
    formData.append('rooms[0][roomTypeID]', ROOM_TYPE_ID);
    formData.append('rooms[0][startDate]', '2025-05-01');
    formData.append('rooms[0][endDate]', '2025-05-02');
    formData.append('rooms[0][adults]', '1');
    formData.append('rooms[0][children]', '0');
    // Payment
    formData.append('paymentMethod', 'noPayment');
    // Other
    formData.append('source', 's-2-1');
    formData.append('status', 'confirmed');
    formData.append('thirdPartyIdentifier', `test-header-${Date.now()}`);
    formData.append('sendEmailConfirmation', 'false');
    
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json',
      'x-api-key': API_KEY,
      'X-PROPERTY-ID': PROPERTY_ID
    };
    
    console.log('Form data:', formData.toString());
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: formData
    });
    
    const responseText = await response.text();
    console.log('Status:', response.status);
    console.log('Response:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error in header test:', error);
  }
}

// Run the test
testCompleteReservation(); 