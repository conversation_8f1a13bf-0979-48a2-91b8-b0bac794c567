#!/bin/bash

# Script para probar la creación de reservas usando el ejemplo exacto de <PERSON> Arbelo
# Ejecutar con: bash test/cloudbeds-manuel-curl-example.sh

# Cargar variables de entorno
if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
fi

# Verificar que la API key esté configurada
if [ -z "$CLOUDBEDS_API_KEY" ]; then
  echo "ERROR: No se ha configurado la variable de entorno CLOUDBEDS_API_KEY"
  exit 1
fi

# Constantes
PROPERTY_ID=${CLOUDBEDS_PROPERTY_ID:-317353}
ROOM_TYPE_ID=${CLOUDBEDS_ROOM_TYPE_ID:-650743}

# Generar fechas futuras (1 mes a partir de hoy)
START_DATE=$(date -v+1m +%Y-%m-%d)
END_DATE=$(date -v+1m -v+1d +%Y-%m-%d)
THIRD_PARTY_ID="test-manuel-curl-$(date +%s)"

echo "=== PROBANDO CREACIÓN DE RESERVAS CON EJEMPLO EXACTO DE MANUEL ARBELO ==="
echo "URL: https://api.cloudbeds.com/api/v1.2/postReservation"
echo "Fechas de reserva: $START_DATE a $END_DATE"
echo "ID de propiedad: $PROPERTY_ID"
echo "ID de tipo de habitación: $ROOM_TYPE_ID"

# Ejecutar el comando curl exactamente como en el ejemplo de Manuel
curl --location 'https://api.cloudbeds.com/api/v1.2/postReservation' \
--header "Authorization: Bearer $CLOUDBEDS_API_KEY" \
--form "propertyID=\"$PROPERTY_ID\"" \
--form "sourceID=\"s-1-1\"" \
--form "startDate=\"$START_DATE\"" \
--form "endDate=\"$END_DATE\"" \
--form 'guestFirstName="test"' \
--form 'guestLastName="test"' \
--form 'guestCountry="FR"' \
--form 'guestZip="1234"' \
--form 'guestEmail="<EMAIL>"' \
--form 'guestPhone="4567"' \
--form 'paymentMethod="credit"' \
--form "rooms[0][roomTypeID]=\"$ROOM_TYPE_ID\"" \
--form 'rooms[0][quantity]="1"' \
--form "adults[0][roomTypeID]=\"$ROOM_TYPE_ID\"" \
--form 'adults[0][quantity]="1"' \
--form "children[0][roomTypeID]=\"$ROOM_TYPE_ID\"" \
--form 'children[0][quantity]="0"'

echo ""
echo "Comando curl ejecutado. Verifica la respuesta arriba."
echo "Si la respuesta es exitosa, deberías ver un JSON con success=true y un reservationID."
