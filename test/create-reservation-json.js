#!/usr/bin/env node

/**
 * Script para probar la creación de reservas usando JSON
 * Ejecutar con: node test/create-reservation-json.js
 */

// Importar fetch
const fetch = require('node-fetch');

// Cargar variables de entorno
require('dotenv').config();

// Constantes
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Generar fechas futuras (1 mes a partir de hoy)
const today = new Date();
const futureDate = new Date(today);
futureDate.setMonth(today.getMonth() + 1);
const nextDay = new Date(futureDate);
nextDay.setDate(futureDate.getDate() + 1);

// Formatear fechas como YYYY-MM-DD
const START_DATE = futureDate.toISOString().split('T')[0];
const END_DATE = nextDay.toISOString().split('T')[0];

/**
 * Función principal para probar la creación de reservas con JSON
 */
async function testJsonReservation() {
  console.log('=== PROBANDO CREACIÓN DE RESERVAS CON JSON ===');
  
  if (!API_KEY) {
    console.error('ERROR: No se ha proporcionado API_KEY. Configure la variable de entorno CLOUDBEDS_API_KEY.');
    return;
  }
  
  try {
    // Construir URL con propertyID
    const url = new URL(`${BASE_URL}/postReservation`);
    url.searchParams.append('propertyID', PROPERTY_ID);
    
    console.log('URL:', url.toString());
    console.log('Fechas de reserva:', START_DATE, 'a', END_DATE);
    console.log('ID de propiedad:', PROPERTY_ID);
    console.log('ID de tipo de habitación:', ROOM_TYPE_ID);
    
    // Crear objeto JSON
    const jsonData = {
      propertyID: PROPERTY_ID,
      startDate: START_DATE,
      endDate: END_DATE,
      guestFirstName: 'John',
      guestLastName: 'Doe',
      guestEmail: '<EMAIL>',
      guestPhone: '+1234567890',
      guestAddress: '123 Test St',
      guestCity: 'Test City',
      guestCountry: 'US',
      guestZip: '12345',
      roomTypeID: ROOM_TYPE_ID,
      adults: 1,
      children: 0,
      rooms: [
        {
          roomTypeID: ROOM_TYPE_ID,
          quantity: 1
        }
      ],
      paymentMethod: 'credit',
      source: 's-2-1',
      status: 'confirmed',
      thirdPartyIdentifier: `test-json-${Date.now()}`,
      sendEmailConfirmation: false
    };
    
    console.log('Datos JSON creados:', JSON.stringify(jsonData, null, 2));
    
    // Cabeceras
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    // Realizar la petición
    console.log('Enviando solicitud...');
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(jsonData)
    });
    
    // Obtener respuesta
    const responseText = await response.text();
    console.log('Código de estado:', response.status);
    console.log('Respuesta:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('Respuesta JSON:', JSON.stringify(jsonResponse, null, 2));
      
      if (jsonResponse.success) {
        console.log('¡Reserva creada exitosamente!');
        console.log('ID de reserva:', jsonResponse.reservationID);
        console.log('Código de confirmación:', jsonResponse.confirmationCode);
      } else {
        console.error('Error al crear la reserva:', jsonResponse.message);
      }
    } catch (e) {
      console.log('La respuesta no es un JSON válido');
    }
  } catch (error) {
    console.error('Error general:', error);
  } finally {
    console.log('=== PROCESO COMPLETADO ===');
  }
}

// Ejecutar la función principal
testJsonReservation();
