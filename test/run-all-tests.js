#!/usr/bin/env node

/**
 * Script para ejecutar todos los tests de reservas
 * Ejecutar con: node test/run-all-tests.js
 */

const { execSync } = require('child_process');

console.log('=== EJECUTANDO TODOS LOS TESTS DE RESERVAS ===');

const tests = [
  'create-reservation.js',
  'create-reservation-alt.js',
  'create-reservation-multi.js',
  'create-reservation-startdate.js',
  'create-reservation-local.js',
  'create-reservation-direct.js'
];

for (const test of tests) {
  console.log(`\n\n=== EJECUTANDO TEST: ${test} ===\n`);
  try {
    execSync(`node test/${test}`, { stdio: 'inherit' });
    console.log(`\n=== TEST ${test} COMPLETADO ===`);
  } catch (error) {
    console.error(`\n=== ERROR EN TEST ${test} ===`);
  }
}

console.log('\n=== TODOS LOS TESTS COMPLETADOS ===');
