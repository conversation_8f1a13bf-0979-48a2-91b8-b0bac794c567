# Scripts de Prueba para Reservas en Cloudbeds

Este directorio contiene varios scripts para probar la creación de reservas en Cloudbeds utilizando diferentes formatos y métodos.

## Configuración

1. Instala las dependencias:
   ```
   cd test
   npm install
   ```

2. Crea un archivo `.env` basado en el ejemplo:
   ```
   cp .env.sample .env
   ```

3. Edita el archivo `.env` para añadir tu API key de Cloudbeds y otros valores necesarios.

## Scripts Disponibles

### Pruebas Básicas

- `npm test` - Ejecuta la prueba básica de reservas
- `npm run test:comprehensive` - Ejecuta pruebas más completas

### Pruebas Específicas para el Problema de startDate

- `npm run test:create` - Prueba básica de creación de reserva
- `npm run test:create-alt` - Prueba alternativa usando formato de formulario
- `npm run test:create-multi` - Prueba múltiples formatos de creación de reservas
- `npm run test:create-startdate` - Prueba específicamente el problema del parámetro startDate
- `npm run test:create-local` - Prueba la API local de tu aplicación
- `npm run test:create-direct` - Prueba el método directo de creación de reservas
- `npm run test:all` - Ejecuta todas las pruebas anteriores

## Solución del Problema "Parameter startDate is required"

El problema "Parameter startDate is required" puede ocurrir por varias razones:

1. **Parámetros en URL vs. Cuerpo**: La API de Cloudbeds puede requerir que el parámetro `startDate` esté presente tanto en la URL como en el cuerpo de la solicitud.

2. **Formato de Fecha**: El formato de fecha debe ser exactamente `YYYY-MM-DD`.

3. **Estructura de Datos**: La API puede esperar una estructura específica donde `startDate` debe estar a nivel raíz del objeto de reserva, no solo dentro de `roomsData`.

Los scripts en este directorio prueban diferentes combinaciones para identificar la solución correcta.

## Uso en Producción

Una vez que identifiques qué formato funciona, actualiza tu código de producción para usar ese formato específico. Los cambios necesarios probablemente incluirán:

1. Asegurarte de que `startDate` y `endDate` estén presentes a nivel raíz del objeto de reserva.
2. Incluir estos parámetros en la URL de la solicitud.
3. Usar el formato de fecha correcto.

## Depuración

Si sigues teniendo problemas, revisa los logs detallados que generan estos scripts para identificar exactamente qué formato acepta la API de Cloudbeds.
