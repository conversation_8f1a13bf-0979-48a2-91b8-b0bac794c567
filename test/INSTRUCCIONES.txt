INSTRUCCIONES PARA PROBAR LA API DE CLOUDBEDS

Para solucionar los problemas con la API de Cloudbeds, he creado scripts de prueba simples
que intentan diferentes formatos y configuraciones para crear reservas.

PASOS:

1. Instala las dependencias:
   cd test
   npm install

2. Crea y configura tu archivo .env:
   cp .env.sample .env
   # Edita el archivo .env y añade tu API key de Cloudbeds

3. Ejecuta la prueba comprensiva:
   npm run test:comprehensive

4. Analiza los resultados:
   - Si alguna de las pruebas tiene éxito, sabremos qué formato funciona
   - Si todas fallan con el mismo error, puede ser un problema de permisos general

5. Envía los resultados:
   - Después de ejecutar las pruebas, envía los resultados a Cloudbeds Support
   - Incluye el timestamp exacto de las pruebas para que puedan revisar sus logs

La prueba más importante es la que usa exactamente el formato sugerido por Manuel de Cloudbeds:
https://api.cloudbeds.com/api/v1.2/postReservation?propertyID=317353

Si esto sigue fallando, el problema es muy probablemente relacionado con permisos
y no con el formato de la solicitud. 