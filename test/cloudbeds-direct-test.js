#!/usr/bin/env node

/**
 * Direct test script for Cloudbeds reservation API
 * Run with: node test/cloudbeds-direct-test.js
 */

// Import node's built-in fetch
const fetch = require('node-fetch');

// Try to load dotenv
try {
  require('dotenv').config({ path: './.env' });
  console.log("Loaded .env file");
} catch (e) {
  console.log('dotenv error:', e);
}

// Constants
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

/**
 * Test with different field structures using form-urlencoded format
 */
async function testWithRoomsParameter() {
  console.log('=== TESTING WITH ROOMS PARAMETER (FORM-URLENCODED) ===');
  
  try {
    const url = `${BASE_URL}/postReservation`;
    
    // Create form data with rooms parameter
    const formData = new URLSearchParams();
    formData.append('propertyID', PROPERTY_ID);
    formData.append('startDate', '2025-05-01');
    formData.append('endDate', '2025-05-02');
    formData.append('guestFirstName', 'Test');
    formData.append('guestLastName', 'User');
    formData.append('guestEmail', '<EMAIL>');
    formData.append('rooms[0][roomTypeID]', ROOM_TYPE_ID);
    formData.append('rooms[0][startDate]', '2025-05-01');
    formData.append('rooms[0][endDate]', '2025-05-02');
    formData.append('rooms[0][adults]', '1');
    formData.append('rooms[0][children]', '0');
    formData.append('source', 's-2-1');
    formData.append('status', 'confirmed');
    formData.append('thirdPartyIdentifier', `test-rooms-form-${Date.now()}`);
    formData.append('sendEmailConfirmation', 'false');
    
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    console.log('Form data:', formData.toString());
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: formData
    });
    
    const responseText = await response.text();
    console.log('Status:', response.status);
    console.log('Response:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error in rooms parameter test:', error);
  }
}

/**
 * Test with different field structures using JSON format
 */
async function testWithRoomsJSON() {
  console.log('\n=== TESTING WITH ROOMS PARAMETER (JSON) ===');
  
  try {
    const url = `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}`;
    
    // Create JSON with rooms parameter
    const jsonBody = {
      propertyID: PROPERTY_ID,
      startDate: "2025-05-01",
      endDate: "2025-05-02",
      guestFirstName: "Test",
      guestLastName: "User",
      guestEmail: "<EMAIL>",
      rooms: [
        {
          roomTypeID: ROOM_TYPE_ID,
          startDate: "2025-05-01",
          endDate: "2025-05-02",
          adults: 1,
          children: 0
        }
      ],
      source: "s-2-1",
      status: "confirmed",
      thirdPartyIdentifier: `test-rooms-json-${Date.now()}`,
      sendEmailConfirmation: false
    };
    
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    console.log('JSON body:', JSON.stringify(jsonBody, null, 2));
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(jsonBody)
    });
    
    const responseText = await response.text();
    console.log('Status:', response.status);
    console.log('Response:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error in rooms JSON test:', error);
  }
}

/**
 * Test with OAuth token instead of API key
 */
async function testWithOAuth() {
  console.log('\n=== TESTING WITH OAUTH TOKEN ===');
  
  try {
    const clientId = process.env.CLOUDBEDS_CLIENT_ID;
    const clientSecret = process.env.CLOUDBEDS_CLIENT_SECRET;
    
    if (!clientId || !clientSecret) {
      console.log('Skipping OAuth test - client ID or secret not provided');
      return;
    }
    
    // First, get an OAuth token
    console.log('Obtaining OAuth token...');
    const tokenUrl = `${BASE_URL}/access_token`;
    const tokenFormData = new URLSearchParams();
    tokenFormData.append('grant_type', 'client_credentials');
    tokenFormData.append('client_id', clientId);
    tokenFormData.append('client_secret', clientSecret);
    
    const tokenResponse = await fetch(tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      body: tokenFormData
    });
    
    const tokenData = await tokenResponse.json();
    console.log('Token response:', tokenData.access_token ? 'Received token' : 'Failed to get token');
    
    if (!tokenData.access_token) {
      console.log('Skipping OAuth test - failed to obtain token');
      return;
    }
    
    // Now try the reservation with OAuth token
    const url = `${BASE_URL}/postReservation?propertyID=${PROPERTY_ID}`;
    
    const jsonBody = {
      propertyID: PROPERTY_ID,
      startDate: "2025-05-01",
      endDate: "2025-05-02",
      guestFirstName: "Test",
      guestLastName: "User",
      guestEmail: "<EMAIL>",
      rooms: [
        {
          roomTypeID: ROOM_TYPE_ID,
          startDate: "2025-05-01",
          endDate: "2025-05-02",
          adults: 1,
          children: 0
        }
      ],
      source: "s-2-1",
      status: "confirmed",
      thirdPartyIdentifier: `test-oauth-${Date.now()}`,
      sendEmailConfirmation: false
    };
    
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${tokenData.access_token}`
    };
    
    console.log('Using OAuth token for reservation request');
    
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(jsonBody)
    });
    
    const responseText = await response.text();
    console.log('Status:', response.status);
    console.log('Response:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('JSON Response:', JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log('Response is not valid JSON');
    }
  } catch (error) {
    console.error('Error in OAuth test:', error);
  }
}

// Run the tests
(async () => {
  await testWithRoomsParameter();
  await testWithRoomsJSON();
  await testWithOAuth();
})(); 