#!/usr/bin/env node

/**
 * Script para probar la creación de reservas con array de rooms
 * Ejecutar con: node test/create-reservation-rooms-array.js
 */

// Importar fetch
const fetch = require('node-fetch');

// Cargar variables de entorno
require('dotenv').config();

// Constantes
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';
const ROOM_TYPE_ID = process.env.CLOUDBEDS_ROOM_TYPE_ID || '650743';
const BASE_URL = 'https://api.cloudbeds.com/api/v1.2';

// Fechas para la reserva (formato YYYY-MM-DD)
const START_DATE = "2025-05-01";
const END_DATE = "2025-05-02";

/**
 * Función principal para probar la creación de reservas con array de rooms
 */
async function testRoomsArray() {
  console.log('=== PROBANDO CREACIÓN DE RESERVAS CON ARRAY DE ROOMS ===');
  
  if (!API_KEY) {
    console.error('ERROR: No se ha proporcionado API_KEY. Configure la variable de entorno CLOUDBEDS_API_KEY.');
    return;
  }
  
  try {
    // Construir URL con parámetros requeridos
    const url = new URL(`${BASE_URL}/postReservation`);
    url.searchParams.append('propertyID', PROPERTY_ID);
    url.searchParams.append('startDate', START_DATE);
    url.searchParams.append('endDate', END_DATE);
    
    console.log('URL:', url.toString());
    
    // Crear datos de la reserva con array de rooms
    const reservationData = {
      propertyID: PROPERTY_ID,
      guestFirstName: "Test",
      guestLastName: "User",
      guestEmail: "<EMAIL>",
      guestPhone: "+1234567890",
      guestCountry: "ES",
      guestAddress: "Calle de Prueba 123",
      guestCity: "Madrid",
      guestZip: "28001",
      startDate: START_DATE,
      endDate: END_DATE,
      adults: 1,
      children: 0,
      rooms: [
        {
          roomTypeID: ROOM_TYPE_ID,
          startDate: START_DATE,
          endDate: END_DATE,
          adults: 1,
          children: 0
        }
      ],
      paymentMethod: "noPayment",
      source: "s-2-1",
      status: "confirmed",
      thirdPartyIdentifier: `test-rooms-array-${Date.now()}`,
      sendEmailConfirmation: false
    };
    
    console.log('Datos de la reserva:', JSON.stringify(reservationData, null, 2));
    
    // Cabeceras
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    // Realizar la petición
    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(reservationData)
    });
    
    // Obtener respuesta
    const responseText = await response.text();
    console.log('Código de estado:', response.status);
    console.log('Respuesta:', responseText);
    
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('Respuesta JSON:', JSON.stringify(jsonResponse, null, 2));
      
      if (jsonResponse.success) {
        console.log('¡Reserva creada exitosamente!');
        console.log('ID de reserva:', jsonResponse.reservationID);
        console.log('Código de confirmación:', jsonResponse.confirmationCode);
      } else {
        console.error('Error al crear la reserva:', jsonResponse.message);
      }
    } catch (e) {
      console.log('La respuesta no es un JSON válido');
    }
  } catch (error) {
    console.error('Error general:', error);
  } finally {
    console.log('=== PROCESO COMPLETADO ===');
  }
}

// Ejecutar la función principal
testRoomsArray();
