#!/bin/bash

# Script para probar la creación de reservas con datos completos y correctos usando curl
# Ejecutar con: bash test/create-reservation-complete.sh

# Cargar variables de entorno
source .env

# Verificar que la API key esté configurada
if [ -z "$CLOUDBEDS_API_KEY" ]; then
  echo "ERROR: No se ha proporcionado CLOUDBEDS_API_KEY. Configure la variable de entorno."
  exit 1
fi

# Constantes
PROPERTY_ID=${CLOUDBEDS_PROPERTY_ID:-317353}
ROOM_TYPE_ID=${CLOUDBEDS_ROOM_TYPE_ID:-650743}

# Generar fechas futuras (1 mes a partir de hoy)
START_DATE=$(date -v+1m +%Y-%m-%d)
END_DATE=$(date -v+1m -v+1d +%Y-%m-%d)
THIRD_PARTY_ID="test-curl-complete-$(date +%s)"

echo "=== PROBANDO CREACIÓN DE RESERVAS CON DATOS COMPLETOS Y CORRECTOS USANDO CURL ==="
echo "URL: https://api.cloudbeds.com/api/v1.2/postReservation?propertyID=$PROPERTY_ID"
echo "Fechas de reserva: $START_DATE a $END_DATE"
echo "ID de propiedad: $PROPERTY_ID"
echo "ID de tipo de habitación: $ROOM_TYPE_ID"

# Ejecutar curl con datos completos y correctos
curl --location "https://api.cloudbeds.com/api/v1.2/postReservation?propertyID=$PROPERTY_ID" \
--header "x-api-key: $CLOUDBEDS_API_KEY" \
--form "propertyID=$PROPERTY_ID" \
--form "startDate=$START_DATE" \
--form "endDate=$END_DATE" \
--form 'guestFirstName="John"' \
--form 'guestLastName="Doe"' \
--form 'guestEmail="<EMAIL>"' \
--form 'guestPhone="+1234567890"' \
--form 'guestCountry="US"' \
--form 'guestZip="12345"' \
--form 'guestAddress="123 Test St"' \
--form 'guestCity="Test City"' \
--form 'paymentMethod="credit"' \
--form "rooms[0][roomTypeID]=$ROOM_TYPE_ID" \
--form 'rooms[0][quantity]="1"' \
--form "adults[0][roomTypeID]=$ROOM_TYPE_ID" \
--form 'adults[0][quantity]="1"' \
--form "children[0][roomTypeID]=$ROOM_TYPE_ID" \
--form 'children[0][quantity]="0"' \
--form "thirdPartyIdentifier=$THIRD_PARTY_ID"

echo ""
echo "=== PROCESO COMPLETADO ==="
