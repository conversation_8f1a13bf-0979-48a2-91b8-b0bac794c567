# Baberrih Hotel Website

Este proyecto es el sitio web oficial del Hotel Baberrih, un lujoso hotel ubicado en Essaouira, Marruecos. El sitio está construido con SvelteKit y ofrece una experiencia de usuario moderna y atractiva.

![Baberrih Hotel](static/images/hero.jpg)

## 🌟 Características

- **Diseño Responsivo**: Experiencia perfecta en dispositivos móviles, tablets y escritorio
- **Multilingüe**: Soporte completo para múltiples idiomas con sistema de internacionalización avanzado
- **Reservas en Línea**: Integración con Cloudbeds para reservas en tiempo real
- **Blog**: Sistema de blog con contenido dinámico
- **Optimizado para SEO**: Estructura y metadatos optimizados para motores de búsqueda
- **Rendimiento Optimizado**: Carga rápida y experiencia fluida

## 🔧 Tecnologías

- **Frontend**: SvelteKit 2.x, Svelte 5.x
- **Estilos**: Tailwind CSS 4.x, DaisyUI
- **Reservas**: API de Cloudbeds
- **Pagos**: Stripe (para servicios adicionales)
- **Iconos**: Lucide Icons
- **Internacionalización**: Sistema personalizado con soporte para traducciones locales y remotas

## 🌐 Soporte de Idiomas

El sitio actualmente soporta los siguientes idiomas:

- 🇬🇧 Inglés (predeterminado)
- 🇪🇸 Español
- 🇫🇷 Francés
- 🇩🇪 Alemán
- 🇸🇦 Árabe
- 🇷🇺 Ruso

El sistema de internacionalización permite añadir fácilmente nuevos idiomas sin modificar el código base.

## 📂 Estructura del Proyecto

```
baberrih-web/
├── src/                    # Código fuente
│   ├── lib/                # Bibliotecas y componentes reutilizables
│   │   ├── cloudbeds/      # Integración con Cloudbeds
│   │   ├── components/     # Componentes de UI
│   │   ├── i18n/           # Sistema de internacionalización
│   │   └── stripe/         # Integración con Stripe
│   ├── routes/             # Rutas y páginas de la aplicación
│   └── app.css             # Estilos globales
├── static/                 # Archivos estáticos
│   ├── images/             # Imágenes
│   │   └── flags/          # Banderas para selector de idiomas
│   └── translations/       # Archivos de traducción JSON
├── scripts/                # Scripts de utilidad
└── docs/                   # Documentación
```

## 🚀 Desarrollo

### Requisitos Previos

- Node.js 18.x o superior
- pnpm 8.x o superior
- Cuenta en Supabase

### Instalación

```bash
# Clonar el repositorio
git clone https://github.com/mkiradani/dao-boilerplate.git baberrih-web
cd baberrih-web

# Instalar dependencias
pnpm install
```

### Variables de Entorno

Crea un archivo `.env` en la raíz del proyecto con las siguientes variables:

```
PUBLIC_SUPABASE_URL=your-supabase-url
PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
PUBLIC_BLOG_API_URL=your-blog-api-url
CLOUDBEDS_CLIENT_ID=your-cloudbeds-client-id
CLOUDBEDS_CLIENT_SECRET=your-cloudbeds-client-secret
STRIPE_SECRET_KEY=your-stripe-secret-key
PUBLIC_STRIPE_KEY=your-stripe-publishable-key
```

### Desarrollo Local

```bash
# Iniciar servidor de desarrollo
pnpm dev

# Iniciar servidor y abrir en el navegador
pnpm dev -- --open
```

### Construcción para Producción

```bash
# Crear versión de producción
pnpm build

# Previsualizar la versión de producción
pnpm preview
```

## 🌍 Internacionalización

El proyecto utiliza un sistema de internacionalización avanzado que permite:

- Traducciones basadas en archivos JSON
- Soporte para añadir nuevos idiomas dinámicamente
- Detección automática del idioma del navegador
- Persistencia de la selección de idioma
- Interfaz visual con banderas

### Añadir un Nuevo Idioma

```bash
# Usando el script de línea de comandos
node scripts/add-language.js it "Italiano"
```

O programáticamente:

```javascript
import { addLanguage } from '$lib/i18n';
addLanguage('it', 'Italiano');
```

También puedes usar la interfaz de administración en `/admin/languages`.

## 🏢 Gestión de Propiedades

El sistema está diseñado para soportar múltiples propiedades (hoteles, sitios, etc.) en una sola instalación. Cada propiedad tiene sus propios usuarios, suites y configuraciones.

### Estructura de Propiedades

- Cada propiedad se representa como un "site" en la base de datos
- Los usuarios pueden tener acceso a una o más propiedades
- Las suites y otros recursos pertenecen a una propiedad específica
- Los permisos se gestionan a nivel de propiedad

### Configuración de una Nueva Propiedad

Para configurar una nueva propiedad en el sistema:

1. **Crear la Propiedad en Supabase**:

```sql
INSERT INTO sites (name, domain, status)
VALUES ('Nombre del Hotel', 'dominio.com', 'active');
```

2. **Vincular Usuarios a la Propiedad**:

```sql
-- Primero, obtener el ID de la propiedad
SELECT id FROM sites WHERE name = 'Nombre del Hotel';

-- Luego, vincular un usuario a la propiedad con permisos
INSERT INTO site_users (user_id, site_id)
VALUES ('id-del-usuario', 'id-de-la-propiedad');

-- Finalmente, asignar permisos al usuario para esta propiedad
INSERT INTO user_site_permissions (user_id, site_id, permission)
VALUES
('id-del-usuario', 'id-de-la-propiedad', 'admin'),
('id-del-usuario', 'id-de-la-propiedad', 'manage_suites'),
('id-del-usuario', 'id-de-la-propiedad', 'view_reservations');
```

3. **Restricciones Importantes**:

- Los usuarios solo pueden crear y gestionar suites para las propiedades a las que tienen acceso
- La asociación entre una suite y su propiedad se establece automáticamente y no puede ser modificada por usuarios regulares
- Solo los desarrolladores pueden cambiar la propiedad a la que pertenece una suite mediante operaciones directas en la base de datos

### Permisos Disponibles

- `admin`: Acceso completo a la propiedad (incluye todos los demás permisos)
- `manage_suites`: Crear, editar y eliminar suites
- `view_suites`: Ver suites sin poder modificarlas
- `manage_reservations`: Gestionar reservas
- `view_reservations`: Ver reservas sin poder modificarlas

**Nota importante**: El permiso `admin` es un super-permiso que otorga automáticamente acceso a todas las demás funcionalidades. Si un usuario tiene el permiso `admin`, no es necesario asignarle permisos adicionales.

## 🐳 Docker

El proyecto incluye configuración para Docker. Consulta `README.docker.md` para más detalles.

## 📝 Licencia

Este proyecto es propiedad de Baberrih Hotel. Todos los derechos reservados.

---

Desarrollado con ❤️ para Baberrih Hotel
