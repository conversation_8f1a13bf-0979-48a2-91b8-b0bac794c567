Response received in 454ms
Status: 200 OK
Success: true
WARNING: Empty data array returned for public availability
This confirms that the room types are not configured for public booking
Check if the room types are marked as private in Cloudbeds

=== TEST 5: Get Public Availability for Specific Room Type (Commercial Context) ===
Testing public availability for room type 653496...
DEBUG: Request URL: https://api.cloudbeds.com/api/v1.2/getAvailableRoomTypes?startDate=2025-05-18&endDate=2025-06-17&propertyID=317353&roomTypeId=653496&publicOnly=true
Sending request...
Response received in 523ms
Status: 200 OK
Success: true
WARNING: Empty data array returned for public availability
This confirms that the room types are not configured for public booking
Check if the room types are marked as private in Cloudbeds

=== TEST 6: Check Room Type Configuration ===
Checking configuration for room type 653496...
DEBUG: Request URL: https://api.cloudbeds.com/api/v1.2/getRoomTypes?propertyID=317353&roomTypeID=653496
Sending request...
