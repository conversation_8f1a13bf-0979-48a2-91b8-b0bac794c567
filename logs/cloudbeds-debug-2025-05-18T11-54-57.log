Response received in 459ms
Status: 200 OK
Success: true
WARNING: Empty data array returned
This indicates the room type might not be configured for public booking
Check if the room type is marked as private in Cloudbeds

=== TEST 3: Get Availability for Specific Room Type (Admin Context) ===
Testing getAvailableRoomTypes endpoint for room type 653496...
DEBUG: Request URL: https://api.cloudbeds.com/api/v1.2/getAvailableRoomTypes?startDate=2025-05-18&endDate=2025-06-17&propertyID=317353&roomTypeId=653496
Sending request...
Response received in 516ms
Status: 200 OK
Success: true
WARNING: Empty data array returned
This indicates the room type might not be configured for public booking
Check if the room type is marked as private in Cloudbeds

=== TEST 4: Get Public Availability (Commercial Context) ===
Testing public availability for all room types...
DEBUG: Request URL: https://api.cloudbeds.com/api/v1.2/getAvailableRoomTypes?startDate=2025-05-18&endDate=2025-06-17&propertyID=317353&publicOnly=true
Sending request...
