{"timestamp": "2025-05-18T12:35:39.899Z", "tests": [{"name": "All-availability endpoint - Current dates", "passed": false, "timestamp": "2025-05-18T12:35:39.947Z", "details": {"error": "invalid json response body at http://localhost:5173/api/cloudbeds/all-availability?startDate=2025-05-18&endDate=2025-05-19 reason: Unexpected token '<', \"<!doctype \"... is not valid JSON", "dateRange": {"name": "Current dates", "startDate": "2025-05-18", "endDate": "2025-05-19"}}}, {"name": "All-availability endpoint - Near future", "passed": false, "timestamp": "2025-05-18T12:35:39.988Z", "details": {"error": "invalid json response body at http://localhost:5173/api/cloudbeds/all-availability?startDate=2025-05-25&endDate=2025-05-27 reason: Unexpected token '<', \"<!doctype \"... is not valid JSON", "dateRange": {"name": "Near future", "startDate": "2025-05-25", "endDate": "2025-05-27"}}}, {"name": "All-availability endpoint - Far future", "passed": false, "timestamp": "2025-05-18T12:35:40.019Z", "details": {"error": "invalid json response body at http://localhost:5173/api/cloudbeds/all-availability?startDate=2025-06-17&endDate=2025-06-20 reason: Unexpected token '<', \"<!doctype \"... is not valid JSON", "dateRange": {"name": "Far future", "startDate": "2025-06-17", "endDate": "2025-06-20"}}}, {"name": "All-availability endpoint - Peak season", "passed": false, "timestamp": "2025-05-18T12:35:40.047Z", "details": {"error": "invalid json response body at http://localhost:5173/api/cloudbeds/all-availability?startDate=2025-07-15&endDate=2025-07-20 reason: Unexpected token '<', \"<!doctype \"... is not valid JSON", "dateRange": {"name": "Peak season", "startDate": "2025-07-15", "endDate": "2025-07-20"}}}, {"name": "Room types endpoint", "passed": false, "timestamp": "2025-05-18T12:35:40.076Z", "details": {"error": "invalid json response body at http://localhost:5173/api/cloudbeds/room-types?includePhotos=1&includeAmenities=1 reason: Unexpected token '<', \"<!doctype \"... is not valid JSON"}}, {"name": "Room type availability", "passed": false, "timestamp": "2025-05-18T12:35:40.104Z", "details": {"error": "invalid json response body at http://localhost:5173/api/cloudbeds/room-types reason: Unexpected token '<', \"<!doctype \"... is not valid JSON"}}], "summary": {"total": 6, "passed": 0, "failed": 6}}