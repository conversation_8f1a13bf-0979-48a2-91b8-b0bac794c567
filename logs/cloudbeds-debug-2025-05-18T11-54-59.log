Response received in 470ms
Status: 200 OK
Success: true
Room type details:
- Name: Garden Deluxe
- ID: 653496
- Max Guests: 5
- Is Private: YES
- Is Active: N/A

DIAGNOSIS: This room type is marked as PRIVATE (isPrivate: true)
This is why it's not showing up in the commercial section.

SOLUTION: To fix this issue, you need to change the room type configuration in Cloudbeds:
1. Log in to the Cloudbeds dashboard
2. Navigate to the Property Settings or Room Types section
3. Find the room type "Garden Deluxe" (ID: 653496)
4. Change the "isPrivate" setting from "true" to "false"
5. Save the changes

=== TESTS COMPLETED ===
Check the log file for detailed results: /Users/<USER>/Documents/developer/baberrih-web/logs/cloudbeds-debug-2025-05-18T11-54-55.log
