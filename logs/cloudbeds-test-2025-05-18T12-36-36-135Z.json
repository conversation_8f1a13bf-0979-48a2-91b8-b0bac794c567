{"timestamp": "2025-05-18T12:36:36.136Z", "tests": [{"name": "All-availability endpoint - Current dates", "passed": false, "timestamp": "2025-05-18T12:36:37.496Z", "details": {"url": "http://localhost:5174/api/cloudbeds/all-availability?startDate=2025-05-18&endDate=2025-05-19", "status": 200, "success": true, "dataLength": 0, "data": [], "error": null, "dateRange": {"name": "Current dates", "startDate": "2025-05-18", "endDate": "2025-05-19"}}}, {"name": "All-availability endpoint - Near future", "passed": true, "timestamp": "2025-05-18T12:36:38.001Z", "details": {"url": "http://localhost:5174/api/cloudbeds/all-availability?startDate=2025-05-25&endDate=2025-05-27", "status": 200, "success": true, "dataLength": 1, "data": [{"propertyID": "317353", "propertyCurrency": {"currencyCode": "USD", "currencySymbol": "$", "currencyPosition": "before"}, "propertyRooms": [{"roomTypeID": "653496", "roomTypeName": "Garden Deluxe", "roomTypeNameShort": "GD", "roomTypeDescription": "Test", "maxGuests": "5", "adultsIncluded": "1", "childrenIncluded": 0, "roomTypePhotos": [{"thumb": "", "image": ""}], "roomTypeFeatures": [], "roomRateID": "2653370", "roomRate": 1000, "ratePlanNamePublic": "default", "ratePlanNamePrivate": "default", "roomsAvailable": 5, "adultsExtraCharge": {"2": 0, "3": 0, "4": 70, "5": 140}, "childrenExtraCharge": {"1": 0, "2": 0, "3": 50, "4": 100}}, {"roomTypeID": "653497", "roomTypeName": "Ocean Deluxe", "roomTypeNameShort": "OD", "roomTypeDescription": "Test", "maxGuests": "5", "adultsIncluded": "1", "childrenIncluded": 0, "roomTypePhotos": [{"thumb": "", "image": ""}], "roomTypeFeatures": [], "roomRateID": "2653371", "roomRate": 1000, "ratePlanNamePublic": "default", "ratePlanNamePrivate": "default", "roomsAvailable": 5, "adultsExtraCharge": {"2": 0, "3": 0, "4": 70, "5": 140}, "childrenExtraCharge": {"1": 0, "2": 0, "3": 50, "4": 100}}, {"roomTypeID": "653498", "roomTypeName": "Garden Junior", "roomTypeNameShort": "GJ", "roomTypeDescription": "Test", "maxGuests": "2", "adultsIncluded": "1", "childrenIncluded": 0, "roomTypePhotos": [{"thumb": "", "image": ""}], "roomTypeFeatures": [], "roomRateID": "2653372", "roomRate": 1000, "ratePlanNamePublic": "default", "ratePlanNamePrivate": "default", "roomsAvailable": 5, "adultsExtraCharge": {"2": 0}, "childrenExtraCharge": {"1": 0}}, {"roomTypeID": "653499", "roomTypeName": "Ocean Junior", "roomTypeNameShort": "OJ", "roomTypeDescription": "Test", "maxGuests": "2", "adultsIncluded": "1", "childrenIncluded": 0, "roomTypePhotos": [{"thumb": "", "image": ""}], "roomTypeFeatures": [], "roomRateID": "2653373", "roomRate": 1000, "ratePlanNamePublic": "default", "ratePlanNamePrivate": "default", "roomsAvailable": 5, "adultsExtraCharge": {"2": 0}, "childrenExtraCharge": {"1": 0}}]}], "error": null, "dateRange": {"name": "Near future", "startDate": "2025-05-25", "endDate": "2025-05-27"}}}, {"name": "All-availability endpoint - Far future", "passed": true, "timestamp": "2025-05-18T12:36:38.624Z", "details": {"url": "http://localhost:5174/api/cloudbeds/all-availability?startDate=2025-06-17&endDate=2025-06-20", "status": 200, "success": true, "dataLength": 1, "data": [{"propertyID": "317353", "propertyCurrency": {"currencyCode": "USD", "currencySymbol": "$", "currencyPosition": "before"}, "propertyRooms": [{"roomTypeID": "653496", "roomTypeName": "Garden Deluxe", "roomTypeNameShort": "GD", "roomTypeDescription": "Test", "maxGuests": "5", "adultsIncluded": "1", "childrenIncluded": 0, "roomTypePhotos": [{"thumb": "", "image": ""}], "roomTypeFeatures": [], "roomRateID": "2653370", "roomRate": 1500, "ratePlanNamePublic": "default", "ratePlanNamePrivate": "default", "roomsAvailable": 5, "adultsExtraCharge": {"2": 0, "3": 0, "4": 105, "5": 210}, "childrenExtraCharge": {"1": 0, "2": 0, "3": 75, "4": 150}}, {"roomTypeID": "653497", "roomTypeName": "Ocean Deluxe", "roomTypeNameShort": "OD", "roomTypeDescription": "Test", "maxGuests": "5", "adultsIncluded": "1", "childrenIncluded": 0, "roomTypePhotos": [{"thumb": "", "image": ""}], "roomTypeFeatures": [], "roomRateID": "2653371", "roomRate": 1500, "ratePlanNamePublic": "default", "ratePlanNamePrivate": "default", "roomsAvailable": 5, "adultsExtraCharge": {"2": 0, "3": 0, "4": 105, "5": 210}, "childrenExtraCharge": {"1": 0, "2": 0, "3": 75, "4": 150}}, {"roomTypeID": "653498", "roomTypeName": "Garden Junior", "roomTypeNameShort": "GJ", "roomTypeDescription": "Test", "maxGuests": "2", "adultsIncluded": "1", "childrenIncluded": 0, "roomTypePhotos": [{"thumb": "", "image": ""}], "roomTypeFeatures": [], "roomRateID": "2653372", "roomRate": 1500, "ratePlanNamePublic": "default", "ratePlanNamePrivate": "default", "roomsAvailable": 5, "adultsExtraCharge": {"2": 0}, "childrenExtraCharge": {"1": 0}}, {"roomTypeID": "653499", "roomTypeName": "Ocean Junior", "roomTypeNameShort": "OJ", "roomTypeDescription": "Test", "maxGuests": "2", "adultsIncluded": "1", "childrenIncluded": 0, "roomTypePhotos": [{"thumb": "", "image": ""}], "roomTypeFeatures": [], "roomRateID": "2653373", "roomRate": 1500, "ratePlanNamePublic": "default", "ratePlanNamePrivate": "default", "roomsAvailable": 5, "adultsExtraCharge": {"2": 0}, "childrenExtraCharge": {"1": 0}}]}], "error": null, "dateRange": {"name": "Far future", "startDate": "2025-06-17", "endDate": "2025-06-20"}}}, {"name": "All-availability endpoint - Peak season", "passed": true, "timestamp": "2025-05-18T12:36:39.253Z", "details": {"url": "http://localhost:5174/api/cloudbeds/all-availability?startDate=2025-07-15&endDate=2025-07-20", "status": 200, "success": true, "dataLength": 1, "data": [{"propertyID": "317353", "propertyCurrency": {"currencyCode": "USD", "currencySymbol": "$", "currencyPosition": "before"}, "propertyRooms": [{"roomTypeID": "653496", "roomTypeName": "Garden Deluxe", "roomTypeNameShort": "GD", "roomTypeDescription": "Test", "maxGuests": "5", "adultsIncluded": "1", "childrenIncluded": 0, "roomTypePhotos": [{"thumb": "", "image": ""}], "roomTypeFeatures": [], "roomRateID": "2653370", "roomRate": 2500, "ratePlanNamePublic": "default", "ratePlanNamePrivate": "default", "roomsAvailable": 5, "adultsExtraCharge": {"2": 0, "3": 0, "4": 175, "5": 350}, "childrenExtraCharge": {"1": 0, "2": 0, "3": 125, "4": 250}}, {"roomTypeID": "653497", "roomTypeName": "Ocean Deluxe", "roomTypeNameShort": "OD", "roomTypeDescription": "Test", "maxGuests": "5", "adultsIncluded": "1", "childrenIncluded": 0, "roomTypePhotos": [{"thumb": "", "image": ""}], "roomTypeFeatures": [], "roomRateID": "2653371", "roomRate": 2500, "ratePlanNamePublic": "default", "ratePlanNamePrivate": "default", "roomsAvailable": 5, "adultsExtraCharge": {"2": 0, "3": 0, "4": 175, "5": 350}, "childrenExtraCharge": {"1": 0, "2": 0, "3": 125, "4": 250}}, {"roomTypeID": "653498", "roomTypeName": "Garden Junior", "roomTypeNameShort": "GJ", "roomTypeDescription": "Test", "maxGuests": "2", "adultsIncluded": "1", "childrenIncluded": 0, "roomTypePhotos": [{"thumb": "", "image": ""}], "roomTypeFeatures": [], "roomRateID": "2653372", "roomRate": 2500, "ratePlanNamePublic": "default", "ratePlanNamePrivate": "default", "roomsAvailable": 5, "adultsExtraCharge": {"2": 0}, "childrenExtraCharge": {"1": 0}}, {"roomTypeID": "653499", "roomTypeName": "Ocean Junior", "roomTypeNameShort": "OJ", "roomTypeDescription": "Test", "maxGuests": "2", "adultsIncluded": "1", "childrenIncluded": 0, "roomTypePhotos": [{"thumb": "", "image": ""}], "roomTypeFeatures": [], "roomRateID": "2653373", "roomRate": 2500, "ratePlanNamePublic": "default", "ratePlanNamePrivate": "default", "roomsAvailable": 5, "adultsExtraCharge": {"2": 0}, "childrenExtraCharge": {"1": 0}}]}], "error": null, "dateRange": {"name": "Peak season", "startDate": "2025-07-15", "endDate": "2025-07-20"}}}, {"name": "Room types endpoint", "passed": true, "timestamp": "2025-05-18T12:36:42.108Z", "details": {"url": "http://localhost:5174/api/cloudbeds/room-types?includePhotos=1&includeAmenities=1", "status": 200, "success": true, "dataLength": 4, "roomTypes": [{"id": "653496", "name": "Garden Deluxe", "isPrivate": true, "maxOccupancy": 5}, {"id": "653497", "name": "Ocean Deluxe", "isPrivate": true, "maxOccupancy": 5}, {"id": "653498", "name": "Garden Junior", "isPrivate": true, "maxOccupancy": 2}, {"id": "653499", "name": "Ocean Junior", "isPrivate": true, "maxOccupancy": 2}], "error": null}}, {"name": "Room type details - Garden Deluxe", "passed": true, "timestamp": "2025-05-18T12:36:42.767Z", "details": {"url": "http://localhost:5174/api/cloudbeds/room-type-details?roomTypeId=653496&includePhotos=1&includeAmenities=1", "status": 200, "success": true, "roomTypeId": "653496", "roomTypeName": "Garden Deluxe", "hasPhotos": false, "hasAmenities": true, "isPrivate": true, "error": null}}, {"name": "Room type availability - Garden Deluxe", "passed": true, "timestamp": "2025-05-18T12:36:44.185Z", "details": {"url": "http://localhost:5174/api/cloudbeds/all-availability?startDate=2025-05-25&endDate=2025-05-27&roomTypeId=653496", "status": 200, "success": true, "dataLength": 1, "roomTypeId": "653496", "roomTypeName": "Garden Deluxe", "dateRange": {"name": "Near future", "startDate": "2025-05-25", "endDate": "2025-05-27"}, "hasDates": false, "error": null}}], "summary": {"total": 7, "passed": 6, "failed": 1}}