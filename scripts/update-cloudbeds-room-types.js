/**
 * <PERSON><PERSON><PERSON> to update Cloudbeds room type configuration
 * 
 * This script provides instructions on how to update the room type configuration
 * in Cloudbeds to make room types available for public booking.
 * 
 * Usage:
 * node scripts/update-cloudbeds-room-types.js
 */

// Load environment variables from .env file
require('dotenv').config();

// Check if required environment variables are set
const API_KEY = process.env.CLOUDBEDS_API_KEY;
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID || '317353';

// Validate environment variables
if (!API_KEY) {
  console.error('ERROR: No API key provided. Set CLOUDBEDS_API_KEY environment variable.');
  process.exit(1);
}

// Main function
async function main() {
  console.log('=== CLOUDBEDS ROOM TYPE CONFIGURATION GUIDE ===');
  console.log('This script provides instructions on how to update the room type configuration');
  console.log('in Cloudbeds to make room types available for public booking.\n');

  console.log('Environment variables:');
  console.log(`- PROPERTY_ID: ${PROPERTY_ID}`);
  console.log(`- API_KEY present: ${API_KEY ? 'YES' : 'NO'}\n`);

  // Get room types
  console.log('Fetching room types from Cloudbeds API...');
  const roomTypes = await getRoomTypes();

  if (!roomTypes.success) {
    console.error('ERROR: Failed to fetch room types from Cloudbeds API.');
    console.error('Error message:', roomTypes.error?.message || 'Unknown error');
    process.exit(1);
  }

  if (!roomTypes.data || roomTypes.data.length === 0) {
    console.error('ERROR: No room types found for this property.');
    process.exit(1);
  }

  // Display room types
  console.log(`\nFound ${roomTypes.data.length} room types:\n`);
  
  const privateRoomTypes = [];
  const publicRoomTypes = [];

  roomTypes.data.forEach((roomType, index) => {
    console.log(`${index + 1}. ${roomType.roomTypeName || 'Unnamed'} (ID: ${roomType.roomTypeID || 'N/A'})`);
    console.log(`   - Max Occupancy: ${roomType.maxGuests || 'N/A'}`);
    console.log(`   - Is Private: ${roomType.isPrivate !== undefined ? roomType.isPrivate : 'N/A'}`);
    console.log(`   - Is Virtual: ${roomType.isVirtual !== undefined ? roomType.isVirtual : 'N/A'}`);
    console.log('');

    if (roomType.isPrivate) {
      privateRoomTypes.push(roomType);
    } else {
      publicRoomTypes.push(roomType);
    }
  });

  // Display summary
  console.log('\n=== SUMMARY ===');
  console.log(`Total room types: ${roomTypes.data.length}`);
  console.log(`Private room types: ${privateRoomTypes.length}`);
  console.log(`Public room types: ${publicRoomTypes.length}`);

  // Display instructions
  if (privateRoomTypes.length > 0) {
    console.log('\n=== INSTRUCTIONS ===');
    console.log('The following room types are marked as private and need to be updated:');
    
    privateRoomTypes.forEach((roomType, index) => {
      console.log(`${index + 1}. ${roomType.roomTypeName} (ID: ${roomType.roomTypeID})`);
    });

    console.log('\nTo make these room types available for public booking, follow these steps:');
    console.log('1. Log in to the Cloudbeds dashboard');
    console.log('2. Navigate to the Property Settings or Room Types section');
    console.log('3. Find each room type listed above');
    console.log('4. Change the "isPrivate" setting from "true" to "false"');
    console.log('5. Save the changes');
    console.log('\nAfter updating the room type configuration, verify that the room types are visible in the public booking engine:');
    console.log('1. Go to the Cloudbeds dashboard');
    console.log('2. Navigate to the Booking Engine section');
    console.log('3. Preview the booking engine');
    console.log('4. Check if the room types are visible and available for booking');
  } else {
    console.log('\nAll room types are already configured for public booking. No action needed.');
  }

  console.log('\n=== COMPLETED ===');
}

// Function to get room types from Cloudbeds API
async function getRoomTypes() {
  try {
    // Construct URL
    const baseUrl = 'https://api.cloudbeds.com/api/v1.2';
    const url = new URL(`${baseUrl}/getRoomTypes`);
    
    // Add query parameters
    url.searchParams.append('propertyID', PROPERTY_ID);
    url.searchParams.append('includePhotos', '1');
    url.searchParams.append('includeAmenities', '1');
    
    console.log(`Request URL: ${url.toString()}`);
    
    // Prepare headers
    const headers = {
      'Accept': 'application/json',
      'x-api-key': API_KEY
    };
    
    // Send request
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers
    });
    
    // Parse response
    const responseText = await response.text();
    
    try {
      return JSON.parse(responseText);
    } catch (error) {
      console.error('Error parsing response as JSON:', error);
      return {
        success: false,
        error: {
          message: 'Error parsing response as JSON'
        }
      };
    }
  } catch (error) {
    console.error('Error making API request:', error);
    return {
      success: false,
      error: {
        message: error.message || 'Error making API request'
      }
    };
  }
}

// Run the main function
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
