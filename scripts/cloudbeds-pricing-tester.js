#!/usr/bin/env node

/**
 * Cloudbeds Pricing Tester
 *
 * A specialized tool for testing Cloudbeds pricing data.
 * This script focuses on retrieving and analyzing pricing information
 * for room types in different date ranges.
 *
 * Features:
 * - Tests pricing data for specific room types
 * - Checks multiple date ranges (current, next month, peak season)
 * - Compares pricing between admin and commercial contexts
 * - Detailed price analysis and reporting
 *
 * Usage:
 * node scripts/cloudbeds-pricing-tester.js [options]
 *
 * Options:
 *   --room-type-id=ID    Specify a room type ID to test
 *   --property-id=ID     Specify a property ID to test
 *   --verbose            Enable verbose logging
 *   --help               Show help
 */

// Import required modules
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Parse command line arguments
const args = parseArgs(process.argv.slice(2));

// Show help if requested
if (args.help) {
  showHelp();
  process.exit(0);
}

// Configuration
const config = {
  apiKey: process.env.CLOUDBEDS_API_KEY,
  propertyId: args['property-id'] || process.env.CLOUDBEDS_PROPERTY_ID || '317353',
  roomTypeId: args['room-type-id'] || process.env.CLOUDBEDS_ROOM_TYPE_ID || '',
  verbose: args.verbose || false,
  baseUrl: 'https://api.cloudbeds.com/api/v1.2',
  outputDir: path.join(__dirname, '../logs')
};

// Create logs directory if it doesn't exist
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Validate configuration
if (!config.apiKey) {
  console.error('ERROR: No API key provided. Set CLOUDBEDS_API_KEY environment variable.');
  process.exit(1);
}

// Logger setup
const logger = {
  log: (message) => {
    console.log(message);
    appendToLog(message);
  },
  error: (message) => {
    console.error(message);
    appendToLog(`ERROR: ${message}`);
  },
  info: (message) => {
    console.info(message);
    appendToLog(message);
  },
  debug: (message) => {
    if (config.verbose) {
      console.debug(message);
      appendToLog(`DEBUG: ${message}`);
    }
  },
  json: (data, label = '') => {
    const prefix = label ? `${label}: ` : '';
    const formatted = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
    console.log(`${prefix}${formatted}`);
    appendToLog(`${prefix}${formatted}`);
  }
};

// Main function
async function main() {
  const logFilePath = path.join(config.outputDir, `cloudbeds-pricing-${formatDateForFilename(new Date())}.log`);
  logger.log(`=== CLOUDBEDS PRICING TESTER ===`);
  logger.log(`Started at: ${new Date().toISOString()}`);
  logger.log(`Log file: ${logFilePath}`);
  logger.log(`Configuration:`);
  logger.log(`- Property ID: ${config.propertyId}`);
  logger.log(`- Room Type ID: ${config.roomTypeId || '(not specified)'}`);
  logger.log(`- API Key present: ${config.apiKey ? 'YES' : 'NO'}`);
  logger.log(`- Verbose logging: ${config.verbose ? 'YES' : 'NO'}`);
  logger.log(`\n`);

  try {
    // First, get room types to verify API access and check configuration
    logger.log(`\n=== Getting Room Types ===`);
    const roomTypesResult = await testGetRoomTypes();

    // If we didn't specify a room type ID but got results, use the first one
    if (!config.roomTypeId && roomTypesResult.success && roomTypesResult.data.length > 0) {
      config.roomTypeId = roomTypesResult.data[0].roomTypeID;
      logger.log(`Auto-selected Room Type ID: ${config.roomTypeId}`);
    }

    // If we still don't have a room type ID, we can't continue
    if (!config.roomTypeId) {
      logger.error(`No room type ID specified or found. Cannot continue.`);
      return;
    }

    // Define date ranges to test
    const dateRanges = [
      {
        name: 'Current Week',
        startDate: getDateOffset(0),
        endDate: getDateOffset(7)
      },
      {
        name: 'Next Month',
        startDate: getDateOffset(30),
        endDate: getDateOffset(37)
      },
      {
        name: 'Three Months Out',
        startDate: getDateOffset(90),
        endDate: getDateOffset(97)
      }
    ];

    // Test pricing for each date range
    for (const range of dateRanges) {
      logger.log(`\n=== Testing Pricing for ${range.name} ===`);
      logger.log(`Date range: ${range.startDate} to ${range.endDate}`);

      // Test admin context
      logger.log(`\n--- Admin Context ---`);
      await testPricing(range.startDate, range.endDate, config.roomTypeId, false);

      // Test commercial context
      logger.log(`\n--- Commercial Context ---`);
      await testPricing(range.startDate, range.endDate, config.roomTypeId, true);
    }

    logger.log(`\n=== TESTS COMPLETED ===`);
    logger.log(`Check the log file for detailed results: ${logFilePath}`);

  } catch (error) {
    logger.error(`Unhandled error: ${error.message}`);
    logger.error(error.stack);
  }
}

// Test function for getRoomTypes endpoint
async function testGetRoomTypes() {
  logger.log(`Testing getRoomTypes endpoint...`);

  try {
    // Construct URL
    const url = new URL(`${config.baseUrl}/getRoomTypes`);

    // Add query parameters
    url.searchParams.append('propertyID', config.propertyId);
    url.searchParams.append('includePhotos', '0');
    url.searchParams.append('includeAmenities', '0');

    logger.debug(`Request URL: ${url.toString()}`);

    // Prepare headers
    const headers = {
      'Accept': 'application/json',
      'x-api-key': config.apiKey
    };

    logger.log(`Sending request to getRoomTypes...`);
    const startTime = Date.now();

    // Send request
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers
    });

    const endTime = Date.now();
    logger.log(`Response received in ${endTime - startTime}ms`);
    logger.log(`Status: ${response.status} ${response.statusText}`);

    // Parse response
    const responseText = await response.text();

    try {
      const data = JSON.parse(responseText);

      // Analyze response
      if (data.success) {
        logger.log(`Success: true`);

        if (data.data && data.data.length > 0) {
          logger.log(`Found ${data.data.length} room types`);

          // Log room types summary
          logger.log(`Room types summary:`);
          data.data.forEach((room, index) => {
            logger.log(`${index + 1}. ${room.roomTypeName || 'Unnamed'} (ID: ${room.roomTypeID || 'N/A'})`);
            logger.log(`   - Is Private: ${room.isPrivate !== undefined ? (room.isPrivate ? 'YES' : 'NO') : 'N/A'}`);
          });

          // If we have a target room type, check if it exists
          if (config.roomTypeId) {
            const targetRoom = data.data.find(room => room.roomTypeID === config.roomTypeId);
            if (targetRoom) {
              logger.log(`\nTarget room type (${config.roomTypeId}) found: ${targetRoom.roomTypeName}`);

              // Check if the room type is private
              if (targetRoom.isPrivate) {
                logger.log(`WARNING: This room type is marked as PRIVATE (isPrivate: true)`);
                logger.log(`This is likely why pricing data is not available in the commercial section.`);
              }
            } else {
              logger.log(`WARNING: Target room type (${config.roomTypeId}) not found in the list!`);
            }
          }
        } else {
          logger.log(`WARNING: No room types returned`);
        }
      } else {
        logger.error(`API returned error:`);
        logger.json(data.error);
      }

      return data;
    } catch (error) {
      logger.error(`Error parsing response as JSON: ${error.message}`);
      logger.debug(`Raw response: ${responseText}`);
      return { success: false, error: { message: 'Failed to parse JSON response' } };
    }
  } catch (error) {
    logger.error(`Error making API request: ${error.message}`);
    return { success: false, error: { message: error.message } };
  }
}

// Test function for pricing data
async function testPricing(startDate, endDate, roomTypeId, isPublic = false) {
  const contextLabel = isPublic ? 'public (commercial)' : 'admin';
  logger.log(`Testing pricing data for ${contextLabel} context...`);

  try {
    // Construct URL
    const url = new URL(`${config.baseUrl}/getAvailableRoomTypes`);

    // Add query parameters
    url.searchParams.append('startDate', startDate);
    url.searchParams.append('endDate', endDate);
    url.searchParams.append('propertyID', config.propertyId);
    url.searchParams.append('roomTypeId', roomTypeId);

    // Add a parameter to indicate this is for public booking if needed
    if (isPublic) {
      url.searchParams.append('publicOnly', 'true');
    }

    logger.debug(`Request URL: ${url.toString()}`);

    // Prepare headers
    const headers = {
      'Accept': 'application/json',
      'x-api-key': config.apiKey
    };

    logger.log(`Sending request...`);
    const startTime = Date.now();

    // Send request
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers
    });

    const endTime = Date.now();
    logger.log(`Response received in ${endTime - startTime}ms`);
    logger.log(`Status: ${response.status} ${response.statusText}`);

    // Parse response
    const responseText = await response.text();

    try {
      const data = JSON.parse(responseText);

      // Analyze response
      if (data.success) {
        logger.log(`Success: true`);

        if (data.data && data.data.length > 0) {
          logger.log(`Found ${data.data.length} room types with availability data`);

          // Find our target room type
          const roomData = data.data.find(room => room.roomTypeId === roomTypeId);

          if (roomData) {
            logger.log(`Found pricing data for room type ${roomTypeId}`);

            // Check if there are dates with pricing
            if (roomData.dates && roomData.dates.length > 0) {
              logger.log(`Found ${roomData.dates.length} dates with availability data`);

              // Count dates with pricing
              const datesWithPricing = roomData.dates.filter(date => date.price !== undefined && date.price !== null);
              logger.log(`Dates with pricing: ${datesWithPricing.length} out of ${roomData.dates.length}`);

              if (datesWithPricing.length > 0) {
                // Calculate price statistics
                const prices = datesWithPricing.map(date => date.price);
                const minPrice = Math.min(...prices);
                const maxPrice = Math.max(...prices);
                const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;

                logger.log(`Price statistics:`);
                logger.log(`- Minimum price: ${minPrice}`);
                logger.log(`- Maximum price: ${maxPrice}`);
                logger.log(`- Average price: ${avgPrice.toFixed(2)}`);

                // Log some sample prices
                logger.log(`Sample prices:`);
                datesWithPricing.slice(0, 5).forEach(date => {
                  logger.log(`- ${date.date}: ${date.price}`);
                });
              } else {
                logger.log(`WARNING: No pricing data found for any dates`);
                logger.log(`This could be due to the room type being marked as private or not having rates configured.`);
              }

              // Check availability
              const availableDates = roomData.dates.filter(date => date.available > 0);
              logger.log(`Available dates: ${availableDates.length} out of ${roomData.dates.length}`);

              if (availableDates.length === 0) {
                logger.log(`WARNING: No available dates found`);
                logger.log(`This could be due to the room type being fully booked or not configured for booking.`);
              }
            } else {
              logger.log(`WARNING: No dates found in the response`);
            }
          } else {
            logger.log(`WARNING: Room type ${roomTypeId} not found in the response`);
          }
        } else {
          logger.log(`WARNING: Empty data array returned`);

          if (isPublic) {
            logger.log(`This confirms that the room type is not configured for public booking`);
            logger.log(`Check if the room type is marked as private in Cloudbeds`);
          } else {
            logger.log(`This is unexpected for admin context. Check the room type configuration.`);
          }
        }
      } else {
        logger.error(`API returned error:`);
        logger.json(data.error);
      }

      return data;
    } catch (error) {
      logger.error(`Error parsing response as JSON: ${error.message}`);
      logger.debug(`Raw response: ${responseText}`);
      return { success: false, error: { message: 'Failed to parse JSON response' } };
    }
  } catch (error) {
    logger.error(`Error making API request: ${error.message}`);
    return { success: false, error: { message: error.message } };
  }
}

// Helper function to get date with offset
function getDateOffset(days) {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return formatDate(date);
}

// Helper function to format date as YYYY-MM-DD
function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// Helper function to format date for filename
function formatDateForFilename(date) {
  return date.toISOString().replace(/:/g, '-').replace(/\..+/, '');
}

// Helper function to append to log file
function appendToLog(message) {
  const logFile = path.join(config.outputDir, `cloudbeds-pricing-${formatDateForFilename(new Date())}.log`);
  fs.appendFileSync(logFile, `${message}\n`);
}

// Helper function to parse command line arguments
function parseArgs(args) {
  const result = {};

  args.forEach(arg => {
    if (arg === '--help' || arg === '-h') {
      result.help = true;
    } else if (arg === '--verbose' || arg === '-v') {
      result.verbose = true;
    } else if (arg.startsWith('--')) {
      const [key, value] = arg.substring(2).split('=');
      if (value !== undefined) {
        result[key] = value;
      } else {
        result[key] = true;
      }
    }
  });

  return result;
}

// Helper function to show help
function showHelp() {
  console.log(`
Cloudbeds Pricing Tester

Usage:
  node scripts/cloudbeds-pricing-tester.js [options]

Options:
  --room-type-id=ID    Specify a room type ID to test
  --property-id=ID     Specify a property ID to test
  --verbose            Enable verbose logging
  --help               Show this help
  `);
}

// Run the main function
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
