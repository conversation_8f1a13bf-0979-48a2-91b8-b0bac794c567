#!/usr/bin/env node

/**
 * Cloudbeds API Integration Test Script
 *
 * This script tests the Cloudbeds API integration with our new all-availability endpoint.
 * It verifies that our workaround for the isPrivate flag issue is functioning correctly.
 *
 * The script performs the following tests:
 * 1. Fetch room availability data using our new all-availability endpoint
 * 2. Retrieve pricing information for all room types
 * 3. Get room characteristics and amenities
 * 4. Test with specific date ranges (current dates and future dates)
 * 5. Log all API responses in a structured format
 *
 * Usage:
 * node scripts/test-cloudbeds-integration.js
 */

import fetch from 'node-fetch';
import { config } from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
config();

// Constants
const BASE_URL = process.env.BASE_URL || 'http://localhost:5174';
const LOG_DIR = 'logs';
const LOG_FILE = `cloudbeds-test-${new Date().toISOString().replace(/[:.]/g, '-')}.json`;

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Create logs directory if it doesn't exist
const logDirPath = path.join(__dirname, '..', LOG_DIR);
if (!fs.existsSync(logDirPath)) {
  fs.mkdirSync(logDirPath, { recursive: true });
}

// Initialize log object
const testLog = {
  timestamp: new Date().toISOString(),
  tests: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0
  }
};

/**
 * Format date as YYYY-MM-DD
 * @param {Date} date - Date to format
 * @returns {string} Formatted date
 */
function formatDate(date) {
  return date.toISOString().split('T')[0];
}

/**
 * Generate date ranges for testing
 * @returns {Array} Array of date range objects
 */
function generateDateRanges() {
  const today = new Date();

  // Current dates (today to tomorrow)
  const currentStart = formatDate(today);
  const currentEnd = formatDate(new Date(today.getTime() + 24 * 60 * 60 * 1000));

  // Near future (7 days from now, 2-day stay)
  const nearFutureStart = formatDate(new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000));
  const nearFutureEnd = formatDate(new Date(today.getTime() + 9 * 24 * 60 * 60 * 1000));

  // Far future (30 days from now, 3-day stay)
  const farFutureStart = formatDate(new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000));
  const farFutureEnd = formatDate(new Date(today.getTime() + 33 * 24 * 60 * 60 * 1000));

  // Peak season (assuming summer is peak - July 15 to July 20)
  const peakYear = today.getMonth() > 6 ? today.getFullYear() + 1 : today.getFullYear();
  const peakStart = `${peakYear}-07-15`;
  const peakEnd = `${peakYear}-07-20`;

  return [
    { name: 'Current dates', startDate: currentStart, endDate: currentEnd },
    { name: 'Near future', startDate: nearFutureStart, endDate: nearFutureEnd },
    { name: 'Far future', startDate: farFutureStart, endDate: farFutureEnd },
    { name: 'Peak season', startDate: peakStart, endDate: peakEnd }
  ];
}

/**
 * Log test result
 * @param {string} testName - Name of the test
 * @param {boolean} passed - Whether the test passed
 * @param {Object} details - Test details
 */
function logTestResult(testName, passed, details) {
  const result = {
    name: testName,
    passed,
    timestamp: new Date().toISOString(),
    details
  };

  testLog.tests.push(result);
  testLog.summary.total++;
  if (passed) {
    testLog.summary.passed++;
  } else {
    testLog.summary.failed++;
  }

  // Log to console
  console.log(`\n${passed ? '✅' : '❌'} ${testName}`);
  if (!passed) {
    console.log('Error details:', details.error || 'Unknown error');
  }
}

/**
 * Save test log to file
 */
function saveTestLog() {
  const logFilePath = path.join(logDirPath, LOG_FILE);
  fs.writeFileSync(logFilePath, JSON.stringify(testLog, null, 2));
  console.log(`\nTest log saved to ${logFilePath}`);
}

/**
 * Test the all-availability endpoint with different date ranges
 */
async function testAllAvailabilityEndpoint() {
  console.log('\n=== Testing all-availability endpoint ===');

  const dateRanges = generateDateRanges();

  for (const range of dateRanges) {
    console.log(`\nTesting date range: ${range.name} (${range.startDate} to ${range.endDate})`);

    try {
      const url = `${BASE_URL}/api/cloudbeds/all-availability?startDate=${range.startDate}&endDate=${range.endDate}`;
      console.log(`Fetching from URL: ${url}`);

      const response = await fetch(url);
      const data = await response.json();

      console.log(`Response status: ${response.status}`);
      console.log(`Success: ${data.success}`);
      console.log(`Data length: ${data.data?.length || 0}`);

      const passed = data.success && data.data && data.data.length > 0;

      logTestResult(
        `All-availability endpoint - ${range.name}`,
        passed,
        {
          url,
          status: response.status,
          success: data.success,
          dataLength: data.data?.length || 0,
          data: data.data || [],
          error: data.error || null,
          dateRange: range
        }
      );
    } catch (error) {
      console.error(`Error testing all-availability endpoint for ${range.name}:`, error);

      logTestResult(
        `All-availability endpoint - ${range.name}`,
        false,
        {
          error: error.message,
          dateRange: range
        }
      );
    }
  }
}

/**
 * Test room type details endpoint
 */
async function testRoomTypeDetails() {
  console.log('\n=== Testing room type details ===');

  try {
    // First, get all room types
    const url = `${BASE_URL}/api/cloudbeds/room-types?includePhotos=1&includeAmenities=1`;
    console.log(`Fetching from URL: ${url}`);

    const response = await fetch(url);
    const data = await response.json();

    console.log(`Response status: ${response.status}`);
    console.log(`Success: ${data.success}`);
    console.log(`Room types found: ${data.data?.length || 0}`);

    const passed = data.success && data.data && data.data.length > 0;

    logTestResult(
      'Room types endpoint',
      passed,
      {
        url,
        status: response.status,
        success: data.success,
        dataLength: data.data?.length || 0,
        roomTypes: data.data?.map(room => ({
          id: room.id || room.roomTypeID,
          name: room.name || room.roomTypeName,
          isPrivate: room.isPrivate,
          maxOccupancy: room.maxOccupancy || room.maxGuests
        })) || [],
        error: data.error || null
      }
    );

    // If we have room types, test getting details for the first one
    if (passed && data.data && data.data.length > 0) {
      const roomType = data.data[0];
      const roomTypeId = roomType.id || roomType.roomTypeID;

      if (roomTypeId) {
        await testSpecificRoomTypeDetails(roomTypeId, roomType.name || roomType.roomTypeName);
      }
    }
  } catch (error) {
    console.error('Error testing room type details:', error);

    logTestResult(
      'Room types endpoint',
      false,
      {
        error: error.message
      }
    );
  }
}

/**
 * Test specific room type details
 * @param {string} roomTypeId - Room type ID
 * @param {string} roomTypeName - Room type name
 */
async function testSpecificRoomTypeDetails(roomTypeId, roomTypeName) {
  console.log(`\nTesting details for room type: ${roomTypeName} (ID: ${roomTypeId})`);

  try {
    const url = `${BASE_URL}/api/cloudbeds/room-type-details?roomTypeId=${roomTypeId}&includePhotos=1&includeAmenities=1`;
    console.log(`Fetching from URL: ${url}`);

    const response = await fetch(url);
    const data = await response.json();

    console.log(`Response status: ${response.status}`);
    console.log(`Success: ${data.success}`);
    console.log(`Data found: ${data.data?.length > 0}`);

    const passed = data.success && data.data && data.data.length > 0;

    logTestResult(
      `Room type details - ${roomTypeName}`,
      passed,
      {
        url,
        status: response.status,
        success: data.success,
        roomTypeId,
        roomTypeName,
        hasPhotos: data.data?.[0]?.photos?.length > 0 || data.data?.[0]?.roomTypePhotos?.length > 0,
        hasAmenities: data.data?.[0]?.amenities?.length > 0 || data.data?.[0]?.roomTypeFeatures != null,
        isPrivate: data.data?.[0]?.isPrivate,
        error: data.error || null
      }
    );
  } catch (error) {
    console.error(`Error testing room type details for ${roomTypeName}:`, error);

    logTestResult(
      `Room type details - ${roomTypeName}`,
      false,
      {
        error: error.message,
        roomTypeId,
        roomTypeName
      }
    );
  }
}

/**
 * Test availability for specific room type
 */
async function testRoomTypeAvailability() {
  console.log('\n=== Testing room type availability ===');

  try {
    // First, get all room types
    const roomTypesUrl = `${BASE_URL}/api/cloudbeds/room-types`;
    console.log(`Fetching from URL: ${roomTypesUrl}`);

    const roomTypesResponse = await fetch(roomTypesUrl);
    const roomTypesData = await roomTypesResponse.json();

    if (roomTypesData.success && roomTypesData.data && roomTypesData.data.length > 0) {
      const roomType = roomTypesData.data[0];
      const roomTypeId = roomType.id || roomType.roomTypeID;
      const roomTypeName = roomType.name || roomType.roomTypeName;

      if (roomTypeId) {
        // Get date ranges
        const dateRanges = generateDateRanges();
        const range = dateRanges[1]; // Use near future date range

        console.log(`\nTesting availability for room type: ${roomTypeName} (ID: ${roomTypeId})`);
        console.log(`Date range: ${range.name} (${range.startDate} to ${range.endDate})`);

        const url = `${BASE_URL}/api/cloudbeds/all-availability?startDate=${range.startDate}&endDate=${range.endDate}&roomTypeId=${roomTypeId}`;
        console.log(`Fetching from URL: ${url}`);

        const response = await fetch(url);
        const data = await response.json();

        console.log(`Response status: ${response.status}`);
        console.log(`Success: ${data.success}`);
        console.log(`Data length: ${data.data?.length || 0}`);

        const passed = data.success && data.data && data.data.length > 0;

        logTestResult(
          `Room type availability - ${roomTypeName}`,
          passed,
          {
            url,
            status: response.status,
            success: data.success,
            dataLength: data.data?.length || 0,
            roomTypeId,
            roomTypeName,
            dateRange: range,
            hasDates: data.data?.[0]?.dates?.length > 0,
            hasPrices: data.data?.[0]?.dates?.some(date => date.price !== undefined),
            error: data.error || null
          }
        );
      }
    }
  } catch (error) {
    console.error('Error testing room type availability:', error);

    logTestResult(
      'Room type availability',
      false,
      {
        error: error.message
      }
    );
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('=== Starting Cloudbeds API Integration Tests ===');
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Timestamp: ${new Date().toISOString()}`);

  try {
    // Test all-availability endpoint
    await testAllAvailabilityEndpoint();

    // Test room type details
    await testRoomTypeDetails();

    // Test room type availability
    await testRoomTypeAvailability();

    // Save test log
    saveTestLog();

    // Print summary
    console.log('\n=== Test Summary ===');
    console.log(`Total tests: ${testLog.summary.total}`);
    console.log(`Passed: ${testLog.summary.passed}`);
    console.log(`Failed: ${testLog.summary.failed}`);
    console.log(`Success rate: ${Math.round((testLog.summary.passed / testLog.summary.total) * 100)}%`);

    console.log('\n=== Tests completed ===');
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

// Run the tests
runTests().catch(error => {
  console.error('Unhandled error during test execution:', error);
  process.exit(1);
});
