#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Verificar argumentos
if (process.argv.length < 4) {
  console.error('Uso: node add-language.js <código-idioma> <nombre-idioma>');
  console.error('Ejemplo: node add-language.js it "Italiano"');
  process.exit(1);
}

const langCode = process.argv[2];
const langName = process.argv[3];

// Verificar que el código de idioma sea válido
if (!/^[a-z]{2}$/.test(langCode)) {
  console.error('El código de idioma debe ser de 2 letras (ej: es, en, fr)');
  process.exit(1);
}

// Rutas
const translationsDir = path.join(__dirname, '..', 'static', 'translations');
const flagsDir = path.join(__dirname, '..', 'static', 'images', 'flags');
const enTranslationsPath = path.join(translationsDir, 'en.json');

// Verificar que exista el directorio de traducciones
if (!fs.existsSync(translationsDir)) {
  console.log('Creando directorio de traducciones...');
  fs.mkdirSync(translationsDir, { recursive: true });
}

// Verificar que exista el directorio de banderas
if (!fs.existsSync(flagsDir)) {
  console.log('Creando directorio de banderas...');
  fs.mkdirSync(flagsDir, { recursive: true });
}

// Verificar si ya existe el archivo de traducciones para este idioma
const langTranslationsPath = path.join(translationsDir, `${langCode}.json`);
if (fs.existsSync(langTranslationsPath)) {
  console.log(`El archivo de traducciones para ${langCode} ya existe.`);
} else {
  // Crear archivo de traducciones a partir del inglés
  console.log(`Creando archivo de traducciones para ${langCode}...`);
  
  if (!fs.existsSync(enTranslationsPath)) {
    console.error('No se encontró el archivo de traducciones en inglés. Asegúrate de que exista static/translations/en.json');
    process.exit(1);
  }
  
  // Copiar el archivo de inglés
  const enTranslations = JSON.parse(fs.readFileSync(enTranslationsPath, 'utf8'));
  
  // Guardar el archivo para el nuevo idioma
  fs.writeFileSync(langTranslationsPath, JSON.stringify(enTranslations, null, 2));
  console.log(`Archivo de traducciones creado: ${langTranslationsPath}`);
}

// Descargar bandera si no existe
const flagPath = path.join(flagsDir, `${langCode}.svg`);
if (!fs.existsSync(flagPath)) {
  console.log(`Descargando bandera para ${langCode}...`);
  try {
    execSync(`wget -O ${flagPath} https://flagcdn.com/${langCode}.svg`);
    console.log(`Bandera descargada: ${flagPath}`);
  } catch (error) {
    console.error(`Error al descargar la bandera: ${error.message}`);
    console.log('Puedes añadir manualmente la bandera en static/images/flags/');
  }
}

console.log(`\nIdioma ${langName} (${langCode}) añadido correctamente.`);
console.log('\nPara usar este idioma en tu aplicación:');
console.log(`1. Edita el archivo de traducciones: ${langTranslationsPath}`);
console.log('2. Añade el idioma dinámicamente en tu aplicación:');
console.log(`   import { addLanguage } from '$lib/i18n';`);
console.log(`   addLanguage('${langCode}', '${langName}');`);
console.log('\n¡Listo! Tu aplicación ahora soporta un nuevo idioma.');
