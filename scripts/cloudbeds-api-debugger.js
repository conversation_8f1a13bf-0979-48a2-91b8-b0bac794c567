#!/usr/bin/env node

/**
 * Cloudbeds API Debugger
 *
 * A comprehensive debugging tool for Cloudbeds API integration.
 * This script tests various Cloudbeds API endpoints with different parameters
 * to help diagnose issues with pricing and availability data.
 *
 * Features:
 * - Tests both regular and public availability endpoints
 * - Compares admin vs. commercial context
 * - Checks room type configuration (isPrivate flag)
 * - Detailed error reporting and logging
 * - Parameter testing (dates, room types, property IDs)
 *
 * Usage:
 * node scripts/cloudbeds-api-debugger.js [options]
 *
 * Options:
 *   --room-type-id=ID    Specify a room type ID to test
 *   --property-id=ID     Specify a property ID to test
 *   --days=N             Number of days to check (default: 30)
 *   --verbose            Enable verbose logging
 *   --help               Show help
 */

// Import required modules
import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Parse command line arguments
const args = parseArgs(process.argv.slice(2));

// Show help if requested
if (args.help) {
  showHelp();
  process.exit(0);
}

// Configuration
const config = {
  apiKey: process.env.CLOUDBEDS_API_KEY,
  propertyId: args['property-id'] || process.env.CLOUDBEDS_PROPERTY_ID || '317353',
  roomTypeId: args['room-type-id'] || process.env.CLOUDBEDS_ROOM_TYPE_ID || '',
  days: parseInt(args.days || '30', 10),
  verbose: args.verbose || false,
  baseUrl: 'https://api.cloudbeds.com/api/v1.2',
  outputDir: path.join(__dirname, '../logs')
};

// Create logs directory if it doesn't exist
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Validate configuration
if (!config.apiKey) {
  console.error('ERROR: No API key provided. Set CLOUDBEDS_API_KEY environment variable.');
  process.exit(1);
}

// Logger setup
const logger = {
  log: (message) => {
    console.log(message);
    appendToLog(message);
  },
  error: (message) => {
    console.error(message);
    appendToLog(`ERROR: ${message}`);
  },
  info: (message) => {
    console.info(message);
    appendToLog(message);
  },
  debug: (message) => {
    if (config.verbose) {
      console.debug(message);
      appendToLog(`DEBUG: ${message}`);
    }
  },
  json: (data, label = '') => {
    const prefix = label ? `${label}: ` : '';
    const formatted = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
    console.log(`${prefix}${formatted}`);
    appendToLog(`${prefix}${formatted}`);
  }
};

// Main function
async function main() {
  const logFilePath = path.join(config.outputDir, `cloudbeds-debug-${formatDateForFilename(new Date())}.log`);
  logger.log(`=== CLOUDBEDS API DEBUGGER ===`);
  logger.log(`Started at: ${new Date().toISOString()}`);
  logger.log(`Log file: ${logFilePath}`);
  logger.log(`Configuration:`);
  logger.log(`- Property ID: ${config.propertyId}`);
  logger.log(`- Room Type ID: ${config.roomTypeId || '(not specified)'}`);
  logger.log(`- Days to check: ${config.days}`);
  logger.log(`- API Key present: ${config.apiKey ? 'YES' : 'NO'}`);
  logger.log(`- Verbose logging: ${config.verbose ? 'YES' : 'NO'}`);
  logger.log(`\n`);

  // Calculate date range
  const today = new Date();
  const endDate = new Date(today);
  endDate.setDate(today.getDate() + config.days);

  const startDateStr = formatDate(today);
  const endDateStr = formatDate(endDate);

  logger.log(`Date range for testing: ${startDateStr} to ${endDateStr}`);

  try {
    // Test 1: Get room types to verify API access and check configuration
    logger.log(`\n=== TEST 1: Get Room Types ===`);
    const roomTypesResult = await testGetRoomTypes();

    // If we didn't specify a room type ID but got results, use the first one
    if (!config.roomTypeId && roomTypesResult.success && roomTypesResult.data.length > 0) {
      config.roomTypeId = roomTypesResult.data[0].roomTypeID;
      logger.log(`Auto-selected Room Type ID: ${config.roomTypeId}`);
    }

    // Test 2: Get availability for all room types (admin context)
    logger.log(`\n=== TEST 2: Get Availability (Admin Context) ===`);
    await testGetAvailability(startDateStr, endDateStr);

    // Test 3: Get availability for specific room type (admin context)
    if (config.roomTypeId) {
      logger.log(`\n=== TEST 3: Get Availability for Specific Room Type (Admin Context) ===`);
      await testGetAvailability(startDateStr, endDateStr, config.roomTypeId);
    }

    // Test 4: Get public availability (commercial context)
    logger.log(`\n=== TEST 4: Get Public Availability (Commercial Context) ===`);
    await testGetPublicAvailability(startDateStr, endDateStr);

    // Test 5: Get public availability for specific room type (commercial context)
    if (config.roomTypeId) {
      logger.log(`\n=== TEST 5: Get Public Availability for Specific Room Type (Commercial Context) ===`);
      await testGetPublicAvailability(startDateStr, endDateStr, config.roomTypeId);
    }

    // Test 6: Check room type configuration
    if (config.roomTypeId) {
      logger.log(`\n=== TEST 6: Check Room Type Configuration ===`);
      await testRoomTypeConfiguration(config.roomTypeId);
    }

    logger.log(`\n=== TESTS COMPLETED ===`);
    logger.log(`Check the log file for detailed results: ${logFilePath}`);

  } catch (error) {
    logger.error(`Unhandled error: ${error.message}`);
    logger.error(error.stack);
  }
}

// Test function for getRoomTypes endpoint
async function testGetRoomTypes() {
  logger.log(`Testing getRoomTypes endpoint...`);

  try {
    // Construct URL
    const url = new URL(`${config.baseUrl}/getRoomTypes`);

    // Add query parameters
    url.searchParams.append('propertyID', config.propertyId);
    url.searchParams.append('includePhotos', '1');
    url.searchParams.append('includeAmenities', '1');

    logger.debug(`Request URL: ${url.toString()}`);

    // Prepare headers
    const headers = {
      'Accept': 'application/json',
      'x-api-key': config.apiKey
    };

    logger.log(`Sending request to getRoomTypes...`);
    const startTime = Date.now();

    // Send request
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers
    });

    const endTime = Date.now();
    logger.log(`Response received in ${endTime - startTime}ms`);
    logger.log(`Status: ${response.status} ${response.statusText}`);

    // Parse response
    const responseText = await response.text();

    try {
      const data = JSON.parse(responseText);

      // Analyze response
      if (data.success) {
        logger.log(`Success: true`);

        if (data.data && data.data.length > 0) {
          logger.log(`Found ${data.data.length} room types`);

          // Log room types summary
          logger.log(`Room types summary:`);
          data.data.forEach((room, index) => {
            logger.log(`${index + 1}. ${room.roomTypeName || 'Unnamed'} (ID: ${room.roomTypeID || 'N/A'})`);
            logger.log(`   - Max Occupancy: ${room.maxGuests || 'N/A'}`);
            logger.log(`   - Is Private: ${room.isPrivate !== undefined ? (room.isPrivate ? 'YES' : 'NO') : 'N/A'}`);
            logger.log(`   - Is Active: ${room.isActive !== undefined ? (room.isActive ? 'YES' : 'NO') : 'N/A'}`);
          });

          // If we have a target room type, check if it exists
          if (config.roomTypeId) {
            const targetRoom = data.data.find(room => room.roomTypeID === config.roomTypeId);
            if (targetRoom) {
              logger.log(`\nTarget room type (${config.roomTypeId}) found:`);
              logger.debug(JSON.stringify(targetRoom, null, 2));

              // Check if the room type is private
              if (targetRoom.isPrivate) {
                logger.log(`WARNING: This room type is marked as PRIVATE (isPrivate: true)`);
                logger.log(`This is likely why it's not showing up in the commercial section.`);
                logger.log(`To fix this, you need to change the room type configuration in Cloudbeds.`);
              }
            } else {
              logger.log(`WARNING: Target room type (${config.roomTypeId}) not found in the list!`);
            }
          }
        } else {
          logger.log(`WARNING: No room types returned`);
        }
      } else {
        logger.error(`API returned error:`);
        logger.json(data.error);
      }

      return data;
    } catch (error) {
      logger.error(`Error parsing response as JSON: ${error.message}`);
      logger.debug(`Raw response: ${responseText}`);
      return { success: false, error: { message: 'Failed to parse JSON response' } };
    }
  } catch (error) {
    logger.error(`Error making API request: ${error.message}`);
    return { success: false, error: { message: error.message } };
  }
}

// Test function for getAvailableRoomTypes endpoint (admin context)
async function testGetAvailability(startDate, endDate, roomTypeId = null) {
  const roomTypeLabel = roomTypeId ? `for room type ${roomTypeId}` : 'for all room types';
  logger.log(`Testing getAvailableRoomTypes endpoint ${roomTypeLabel}...`);

  try {
    // Construct URL
    const url = new URL(`${config.baseUrl}/getAvailableRoomTypes`);

    // Add query parameters
    url.searchParams.append('startDate', startDate);
    url.searchParams.append('endDate', endDate);
    url.searchParams.append('propertyID', config.propertyId);

    if (roomTypeId) {
      url.searchParams.append('roomTypeId', roomTypeId);
    }

    logger.debug(`Request URL: ${url.toString()}`);

    // Prepare headers
    const headers = {
      'Accept': 'application/json',
      'x-api-key': config.apiKey
    };

    logger.log(`Sending request...`);
    const startTime = Date.now();

    // Send request
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers
    });

    const endTime = Date.now();
    logger.log(`Response received in ${endTime - startTime}ms`);
    logger.log(`Status: ${response.status} ${response.statusText}`);

    // Parse response
    const responseText = await response.text();

    try {
      const data = JSON.parse(responseText);

      // Analyze response
      if (data.success) {
        logger.log(`Success: true`);

        if (data.data && data.data.length > 0) {
          logger.log(`Found ${data.data.length} room types with availability data`);

          // Log first room type details
          const firstRoom = data.data[0];
          logger.log(`First room type details:`);
          logger.log(`- Room Type ID: ${firstRoom.roomTypeId || 'N/A'}`);
          logger.log(`- Property ID: ${firstRoom.propertyID || 'N/A'}`);
          logger.log(`- Dates count: ${firstRoom.dates ? firstRoom.dates.length : 0}`);

          if (firstRoom.dates && firstRoom.dates.length > 0) {
            logger.log(`First date availability:`);
            logger.debug(JSON.stringify(firstRoom.dates[0], null, 2));

            // Check if there's pricing data
            if (firstRoom.dates[0].price !== undefined) {
              logger.log(`Price data found: ${firstRoom.dates[0].price}`);
            } else {
              logger.log(`WARNING: No price data found for this date`);
            }
          }
        } else {
          logger.log(`WARNING: Empty data array returned`);
          logger.log(`This indicates the room type might not be configured for public booking`);
          logger.log(`Check if the room type is marked as private in Cloudbeds`);
        }
      } else {
        logger.error(`API returned error:`);
        logger.json(data.error);
      }

      return data;
    } catch (error) {
      logger.error(`Error parsing response as JSON: ${error.message}`);
      logger.debug(`Raw response: ${responseText}`);
      return { success: false, error: { message: 'Failed to parse JSON response' } };
    }
  } catch (error) {
    logger.error(`Error making API request: ${error.message}`);
    return { success: false, error: { message: error.message } };
  }
}

// Test function for public availability (commercial context)
async function testGetPublicAvailability(startDate, endDate, roomTypeId = null) {
  const roomTypeLabel = roomTypeId ? `for room type ${roomTypeId}` : 'for all room types';
  logger.log(`Testing public availability ${roomTypeLabel}...`);

  try {
    // Construct URL - this simulates the public-availability endpoint
    const url = new URL(`${config.baseUrl}/getAvailableRoomTypes`);

    // Add query parameters
    url.searchParams.append('startDate', startDate);
    url.searchParams.append('endDate', endDate);
    url.searchParams.append('propertyID', config.propertyId);

    if (roomTypeId) {
      url.searchParams.append('roomTypeId', roomTypeId);
    }

    // Add a parameter to indicate this is for public booking
    url.searchParams.append('publicOnly', 'true');

    logger.debug(`Request URL: ${url.toString()}`);

    // Prepare headers
    const headers = {
      'Accept': 'application/json',
      'x-api-key': config.apiKey
    };

    logger.log(`Sending request...`);
    const startTime = Date.now();

    // Send request
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers
    });

    const endTime = Date.now();
    logger.log(`Response received in ${endTime - startTime}ms`);
    logger.log(`Status: ${response.status} ${response.statusText}`);

    // Parse response
    const responseText = await response.text();

    try {
      const data = JSON.parse(responseText);

      // Analyze response
      if (data.success) {
        logger.log(`Success: true`);

        if (data.data && data.data.length > 0) {
          logger.log(`Found ${data.data.length} public room types with availability data`);

          // Log first room type details
          const firstRoom = data.data[0];
          logger.log(`First room type details:`);
          logger.log(`- Room Type ID: ${firstRoom.roomTypeId || 'N/A'}`);
          logger.log(`- Property ID: ${firstRoom.propertyID || 'N/A'}`);
          logger.log(`- Dates count: ${firstRoom.dates ? firstRoom.dates.length : 0}`);

          if (firstRoom.dates && firstRoom.dates.length > 0) {
            logger.log(`First date availability:`);
            logger.debug(JSON.stringify(firstRoom.dates[0], null, 2));
          }
        } else {
          logger.log(`WARNING: Empty data array returned for public availability`);
          logger.log(`This confirms that the room types are not configured for public booking`);
          logger.log(`Check if the room types are marked as private in Cloudbeds`);
        }
      } else {
        logger.error(`API returned error:`);
        logger.json(data.error);
      }

      return data;
    } catch (error) {
      logger.error(`Error parsing response as JSON: ${error.message}`);
      logger.debug(`Raw response: ${responseText}`);
      return { success: false, error: { message: 'Failed to parse JSON response' } };
    }
  } catch (error) {
    logger.error(`Error making API request: ${error.message}`);
    return { success: false, error: { message: error.message } };
  }
}

// Test function to check room type configuration
async function testRoomTypeConfiguration(roomTypeId) {
  logger.log(`Checking configuration for room type ${roomTypeId}...`);

  try {
    // Construct URL
    const url = new URL(`${config.baseUrl}/getRoomTypes`);

    // Add query parameters
    url.searchParams.append('propertyID', config.propertyId);
    url.searchParams.append('roomTypeID', roomTypeId);

    logger.debug(`Request URL: ${url.toString()}`);

    // Prepare headers
    const headers = {
      'Accept': 'application/json',
      'x-api-key': config.apiKey
    };

    logger.log(`Sending request...`);
    const startTime = Date.now();

    // Send request
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers
    });

    const endTime = Date.now();
    logger.log(`Response received in ${endTime - startTime}ms`);
    logger.log(`Status: ${response.status} ${response.statusText}`);

    // Parse response
    const responseText = await response.text();

    try {
      const data = JSON.parse(responseText);

      // Analyze response
      if (data.success) {
        logger.log(`Success: true`);

        if (data.data && data.data.length > 0) {
          const roomType = data.data[0];
          logger.log(`Room type details:`);
          logger.log(`- Name: ${roomType.roomTypeName || 'N/A'}`);
          logger.log(`- ID: ${roomType.roomTypeID || 'N/A'}`);
          logger.log(`- Max Guests: ${roomType.maxGuests || 'N/A'}`);
          logger.log(`- Is Private: ${roomType.isPrivate !== undefined ? (roomType.isPrivate ? 'YES' : 'NO') : 'N/A'}`);
          logger.log(`- Is Active: ${roomType.isActive !== undefined ? (roomType.isActive ? 'YES' : 'NO') : 'N/A'}`);

          // Check if the room type is private
          if (roomType.isPrivate) {
            logger.log(`\nDIAGNOSIS: This room type is marked as PRIVATE (isPrivate: true)`);
            logger.log(`This is why it's not showing up in the commercial section.`);
            logger.log(`\nSOLUTION: To fix this issue, you need to change the room type configuration in Cloudbeds:`);
            logger.log(`1. Log in to the Cloudbeds dashboard`);
            logger.log(`2. Navigate to the Property Settings or Room Types section`);
            logger.log(`3. Find the room type "${roomType.roomTypeName}" (ID: ${roomType.roomTypeID})`);
            logger.log(`4. Change the "isPrivate" setting from "true" to "false"`);
            logger.log(`5. Save the changes`);
          } else {
            logger.log(`\nDIAGNOSIS: This room type is correctly marked as PUBLIC (isPrivate: false)`);
            logger.log(`If you're still experiencing issues, check other configuration settings.`);
          }
        } else {
          logger.log(`WARNING: Room type not found`);
        }
      } else {
        logger.error(`API returned error:`);
        logger.json(data.error);
      }

      return data;
    } catch (error) {
      logger.error(`Error parsing response as JSON: ${error.message}`);
      logger.debug(`Raw response: ${responseText}`);
      return { success: false, error: { message: 'Failed to parse JSON response' } };
    }
  } catch (error) {
    logger.error(`Error making API request: ${error.message}`);
    return { success: false, error: { message: error.message } };
  }
}

// Helper function to format date as YYYY-MM-DD
function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// Helper function to format date for filename
function formatDateForFilename(date) {
  return date.toISOString().replace(/:/g, '-').replace(/\..+/, '');
}

// Helper function to append to log file
function appendToLog(message) {
  const logFile = path.join(config.outputDir, `cloudbeds-debug-${formatDateForFilename(new Date())}.log`);
  fs.appendFileSync(logFile, `${message}\n`);
}

// Helper function to parse command line arguments
function parseArgs(args) {
  const result = {};

  args.forEach(arg => {
    if (arg === '--help' || arg === '-h') {
      result.help = true;
    } else if (arg === '--verbose' || arg === '-v') {
      result.verbose = true;
    } else if (arg.startsWith('--')) {
      const [key, value] = arg.substring(2).split('=');
      if (value !== undefined) {
        result[key] = value;
      } else {
        result[key] = true;
      }
    }
  });

  return result;
}

// Helper function to show help
function showHelp() {
  console.log(`
Cloudbeds API Debugger

Usage:
  node scripts/cloudbeds-api-debugger.js [options]

Options:
  --room-type-id=ID    Specify a room type ID to test
  --property-id=ID     Specify a property ID to test
  --days=N             Number of days to check (default: 30)
  --verbose            Enable verbose logging
  --help               Show this help
  `);
}

// Run the main function
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
