/**
 * Script de prueba para verificar la configuración de Stripe
 * 
 * Este script realiza pruebas básicas para verificar que la integración de Stripe
 * está configurada correctamente.
 * 
 * Uso:
 * ```
 * pnpm tsx scripts/test-stripe.ts
 * ```
 */
import Stripe from 'stripe';
import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import chalk from 'chalk';

// Cargar variables de entorno
config();

// Verificar que las variables de entorno necesarias están definidas
const requiredEnvVars = [
  'STRIPE_SECRET_KEY',
  'PUBLIC_STRIPE_PUBLISHABLE_KEY',
  'PUBLIC_SUPABASE_URL',
  'PUBLIC_SUPABASE_ANON_KEY'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error(chalk.red('Error: Faltan las siguientes variables de entorno:'));
  missingEnvVars.forEach(envVar => console.error(chalk.red(`- ${envVar}`)));
  process.exit(1);
}

// Crear cliente de Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia',
});

// Crear cliente de Supabase
const supabase = createClient(
  process.env.PUBLIC_SUPABASE_URL!,
  process.env.PUBLIC_SUPABASE_ANON_KEY!
);

// Función principal
async function main() {
  console.log(chalk.blue('=== Prueba de Configuración de Stripe ==='));

  // Prueba 1: Verificar la conexión con Stripe
  try {
    console.log(chalk.yellow('\nPrueba 1: Verificar la conexión con Stripe'));
    const account = await stripe.accounts.retrieve();
    console.log(chalk.green('✓ Conexión con Stripe establecida correctamente'));
    console.log(`  ID de cuenta: ${account.id}`);
    console.log(`  Email: ${account.email}`);
  } catch (error) {
    console.error(chalk.red('✗ Error al conectar con Stripe:'));
    console.error(error);
    process.exit(1);
  }

  // Prueba 2: Verificar la conexión con Supabase
  try {
    console.log(chalk.yellow('\nPrueba 2: Verificar la conexión con Supabase'));
    const { data, error } = await supabase.rpc('select_stripe_products', {}, {
      count: 'exact'
    });

    if (error) {
      throw error;
    }

    console.log(chalk.green('✓ Conexión con Supabase establecida correctamente'));
    console.log(`  Número de productos: ${data?.length || 0}`);
  } catch (error) {
    console.error(chalk.red('✗ Error al conectar con Supabase:'));
    console.error(error);
    console.log(chalk.yellow('  Nota: Asegúrate de que la extensión de Stripe está configurada correctamente en Supabase'));
    console.log(chalk.yellow('  Sigue las instrucciones en la documentación para configurar la extensión: https://supabase.com/docs/guides/database/extensions/wrappers/stripe'));
  }

  // Prueba 3: Verificar productos en Stripe
  try {
    console.log(chalk.yellow('\nPrueba 3: Verificar productos en Stripe'));
    const products = await stripe.products.list({ limit: 5, active: true });

    console.log(chalk.green(`✓ Se encontraron ${products.data.length} productos activos en Stripe`));

    if (products.data.length > 0) {
      console.log('\n  Productos:');
      products.data.forEach(product => {
        console.log(`  - ${product.name} (${product.id})`);
      });
    } else {
      console.log(chalk.yellow('  No se encontraron productos activos. Considera crear algunos productos de prueba.'));
    }
  } catch (error) {
    console.error(chalk.red('✗ Error al obtener productos de Stripe:'));
    console.error(error);
  }

  // Prueba 4: Verificar precios en Stripe
  try {
    console.log(chalk.yellow('\nPrueba 4: Verificar precios en Stripe'));
    const prices = await stripe.prices.list({ limit: 5, active: true });

    console.log(chalk.green(`✓ Se encontraron ${prices.data.length} precios activos en Stripe`));

    if (prices.data.length > 0) {
      console.log('\n  Precios:');
      prices.data.forEach(price => {
        const amount = price.unit_amount ? price.unit_amount / 100 : 0;
        const currency = price.currency.toUpperCase();
        const recurring = price.recurring
          ? `${price.recurring.interval_count} ${price.recurring.interval}(s)`
          : 'one-time';

        console.log(`  - ${amount} ${currency} (${recurring}) - ${price.id}`);
      });
    } else {
      console.log(chalk.yellow('  No se encontraron precios activos. Considera crear algunos precios de prueba.'));
    }
  } catch (error) {
    console.error(chalk.red('✗ Error al obtener precios de Stripe:'));
    console.error(error);
  }

  // Prueba 5: Crear una sesión de checkout de prueba
  try {
    console.log(chalk.yellow('\nPrueba 5: Crear una sesión de checkout de prueba'));

    // Obtener un precio activo
    const prices = await stripe.prices.list({ limit: 1, active: true });

    if (prices.data.length === 0) {
      console.log(chalk.yellow('  No se encontraron precios activos. Omitiendo esta prueba.'));
    } else {
      const price = prices.data[0];

      // Crear una sesión de checkout
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [
          {
            price: price.id,
            quantity: 1,
          },
        ],
        mode: price.recurring ? 'subscription' : 'payment',
        success_url: 'https://example.com/success?session_id={CHECKOUT_SESSION_ID}',
        cancel_url: 'https://example.com/cancel',
      });

      console.log(chalk.green('✓ Sesión de checkout creada correctamente'));
      console.log(`  ID de sesión: ${session.id}`);
      console.log(`  URL: ${session.url}`);
    }
  } catch (error) {
    console.error(chalk.red('✗ Error al crear una sesión de checkout:'));
    console.error(error);
  }

  console.log(chalk.blue('\n=== Fin de las Pruebas ==='));
}

// Ejecutar la función principal
main().catch(error => {
  console.error(chalk.red('Error inesperado:'));
  console.error(error);
  process.exit(1);
}); 