# Cloudbeds API Debugging Tools

This directory contains specialized scripts for debugging Cloudbeds API integration issues, particularly focusing on pricing and availability data problems.

## Overview

The debugging tools are designed to help diagnose and fix issues with the Cloudbeds API integration, such as:

- Empty data arrays in API responses
- Missing pricing information
- Availability data not showing up
- Differences between admin and commercial contexts

## Prerequisites

Before using these scripts, make sure you have:

1. Node.js installed (v14 or higher recommended)
2. A valid Cloudbeds API key
3. Your Cloudbeds property ID
4. (Optional) Room type IDs you want to test

## Setup

1. Create a `.env` file in the project root with your Cloudbeds API credentials:

```
CLOUDBEDS_API_KEY=your_api_key_here
CLOUDBEDS_PROPERTY_ID=your_property_id_here
CLOUDBEDS_ROOM_TYPE_ID=optional_room_type_id_here
```

2. Install required dependencies:

```bash
npm install node-fetch dotenv
```

## Available Scripts

### 1. Cloudbeds API Debugger

A comprehensive debugging tool that tests various Cloudbeds API endpoints with different parameters.

**Usage:**
```bash
node scripts/cloudbeds-api-debugger.js [options]
```

**Options:**
- `--room-type-id=ID` - Specify a room type ID to test
- `--property-id=ID` - Specify a property ID to test
- `--days=N` - Number of days to check (default: 30)
- `--verbose` - Enable verbose logging
- `--help` - Show help

**What it tests:**
- Room type configuration (isPrivate flag)
- Availability data in admin context
- Availability data in commercial context
- API access and authentication

**Example:**
```bash
node scripts/cloudbeds-api-debugger.js --room-type-id=653496 --verbose
```

### 2. Cloudbeds Pricing Tester

A specialized tool for testing Cloudbeds pricing data across different date ranges.

**Usage:**
```bash
node scripts/cloudbeds-pricing-tester.js [options]
```

**Options:**
- `--room-type-id=ID` - Specify a room type ID to test
- `--property-id=ID` - Specify a property ID to test
- `--verbose` - Enable verbose logging
- `--help` - Show help

**What it tests:**
- Pricing data for specific room types
- Multiple date ranges (current, next month, peak season)
- Pricing in both admin and commercial contexts
- Price statistics and availability

**Example:**
```bash
node scripts/cloudbeds-pricing-tester.js --room-type-id=653496
```

## Interpreting Results

The scripts generate detailed logs in the `logs` directory. Here's how to interpret the results:

### Common Issues and Solutions

#### 1. Empty Data Arrays

**Symptom:** API returns `"success": true` but with empty data arrays (`"data": []`).

**Possible Causes:**
- Room types are marked as private (`isPrivate: true`)
- No availability for the specified date range
- Room types not configured for online booking

**Solution:**
- Check if the room type is marked as private in Cloudbeds
- Change the `isPrivate` setting from `true` to `false`
- Verify that rates are configured for the date range

#### 2. Missing Pricing Data

**Symptom:** Availability data is present, but pricing information is missing.

**Possible Causes:**
- Rates not configured in Cloudbeds
- Rate restrictions in place
- Rate plan issues

**Solution:**
- Check rate configuration in Cloudbeds
- Ensure rate plans are properly set up
- Verify that the room type is associated with active rate plans

#### 3. API Authentication Issues

**Symptom:** API returns authentication errors.

**Possible Causes:**
- Invalid API key
- Expired API key
- Insufficient permissions

**Solution:**
- Verify that the API key is correct
- Generate a new API key if necessary
- Check API key permissions in Cloudbeds

## Fixing the isPrivate Flag

The most common issue is that room types are marked as private (`isPrivate: true`), which prevents them from appearing in the commercial section. To fix this:

1. Log in to the Cloudbeds dashboard
2. Navigate to the Property Settings or Room Types section
3. Find the room type you want to make public
4. Change the "isPrivate" setting from "true" to "false"
5. Save the changes

After making this change, run the debugging scripts again to verify that the issue is resolved.

## Additional Resources

- [Cloudbeds API Documentation](https://api.cloudbeds.com/docs/)
- [Cloudbeds Knowledge Base](https://support.cloudbeds.com/hc/en-us)
- [Cloudbeds Developer Portal](https://developers.cloudbeds.com/)

## Troubleshooting

If you encounter issues with the debugging scripts:

1. Verify that your API key and property ID are correct
2. Check that the room type ID exists and is associated with your property
3. Ensure that you have the necessary permissions to access the Cloudbeds API
4. Check the logs for detailed error messages

For persistent issues, contact Cloudbeds support:
- Email: <EMAIL>
- Phone: +****************
- Website: https://www.cloudbeds.com/contact-us/
