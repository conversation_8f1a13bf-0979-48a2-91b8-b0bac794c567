<script lang="ts">
  import { onMount } from "svelte";
  import { goto } from "$app/navigation";

  let authUrl: string = "";
  let loading: boolean = true;
  let error: string | null = null;
  let code: string | null = null;
  let tokenData: any = null;
  let tokenProcessing: boolean = false;
  let tokenError: string | null = null;

  // Obtiene código de la URL si existe
  onMount(async () => {
    try {
      // Verificar si hay un código en la URL (redireccionamiento de Cloudbeds)
      const urlParams = new URLSearchParams(window.location.search);
      code = urlParams.get("code");

      if (code) {
        // Si tenemos código, estamos en el callback de OAuth
        console.log("Código OAuth detectado:", code);
        await processOAuthCode(code);
      } else {
        // Si no hay código, obtener URL de autorización
        await fetchAuthUrl();
      }
    } catch (err) {
      error =
        err instanceof Error ? err.message : "Error desconocido al inicializar";
      console.error("Error en onMount:", err);
    } finally {
      loading = false;
    }
  });

  async function fetchAuthUrl() {
    try {
      const response = await fetch("/api/cloudbeds/oauth");
      const data = await response.json();

      if (data.success && data.authUrl) {
        authUrl = data.authUrl;
      } else {
        error = data.message || "No se pudo obtener la URL de autorización";
        console.error("Error al obtener URL de autorización:", data);
      }
    } catch (err) {
      error =
        err instanceof Error ? err.message : "Error desconocido al obtener URL";
      console.error("Error al obtener URL de autorización:", err);
    }
  }

  async function processOAuthCode(code: string) {
    try {
      tokenProcessing = true;

      const response = await fetch("/api/cloudbeds/oauth", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ code }),
      });

      const data = await response.json();

      if (data.success && data.tokenData) {
        tokenData = data.tokenData;
        console.log("Token obtenido correctamente:", tokenData);

        // Guardar token en localStorage para uso posterior
        localStorage.setItem("cloudbeds_tokens", JSON.stringify(tokenData));

        // Enviar el token al servidor para usarlo en futuras llamadas API
        try {
          const serverResponse = await fetch("/api/cloudbeds/token", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(tokenData),
          });

          const serverData = await serverResponse.json();

          if (serverData.success) {
            console.log("Token establecido en el servidor:", serverData);
          } else {
            console.warn(
              "El token se obtuvo pero no se pudo establecer en el servidor:",
              serverData
            );
          }
        } catch (serverError) {
          console.error("Error al enviar token al servidor:", serverError);
        }

        // Redirigir al usuario a la página de inicio después de 3 segundos
        setTimeout(() => {
          goto("/");
        }, 3000);
      } else {
        tokenError = data.message || "Error al procesar código de autorización";
        console.error("Error al procesar código:", data);
      }
    } catch (err) {
      tokenError =
        err instanceof Error
          ? err.message
          : "Error desconocido al procesar código";
      console.error("Error al procesar código:", err);
    } finally {
      tokenProcessing = false;
    }
  }

  function startOAuthFlow() {
    if (authUrl) {
      window.location.href = authUrl;
    }
  }
</script>

<div class="mx-auto p-4 max-w-2xl container">
  <h1 class="mb-6 font-bold text-3xl">Autorización de Cloudbeds</h1>

  {#if loading}
    <div class="flex justify-center my-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
  {:else if error}
    <div class="mb-6 alert alert-error">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="stroke-current w-6 h-6 shrink-0"
        fill="none"
        viewBox="0 0 24 24"
        ><path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
        /></svg
      >
      <span>{error}</span>
    </div>
  {:else if code}
    <div class="bg-base-200 shadow-xl mb-6 card">
      <div class="card-body">
        <h2 class="card-title">Procesando autorización</h2>

        {#if tokenProcessing}
          <div class="flex justify-center my-4">
            <span class="loading loading-spinner loading-md"></span>
          </div>
          <p>Obteniendo token de acceso...</p>
        {:else if tokenError}
          <div class="alert alert-error">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="stroke-current w-6 h-6 shrink-0"
              fill="none"
              viewBox="0 0 24 24"
              ><path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
              /></svg
            >
            <span>{tokenError}</span>
          </div>
          <div class="justify-end mt-4 card-actions">
            <button class="btn btn-primary" on:click={fetchAuthUrl}
              >Reintentar</button
            >
          </div>
        {:else if tokenData}
          <div class="alert alert-success">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="stroke-current w-6 h-6 shrink-0"
              fill="none"
              viewBox="0 0 24 24"
              ><path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              /></svg
            >
            <span>¡Autenticación exitosa! Redirigiendo...</span>
          </div>
        {/if}
      </div>
    </div>
  {:else}
    <div class="bg-base-200 shadow-xl card">
      <div class="card-body">
        <h2 class="card-title">Conectar con Cloudbeds</h2>
        <p class="mb-4">
          Para poder crear reservas, es necesario autorizar la aplicación en
          Cloudbeds.
        </p>
        <p class="mb-4">
          Al hacer clic en el botón, serás redirigido al sitio de Cloudbeds para
          completar la autorización.
        </p>

        <div class="justify-end card-actions">
          <button
            class="btn btn-primary"
            on:click={startOAuthFlow}
            disabled={!authUrl}
          >
            Conectar con Cloudbeds
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>
