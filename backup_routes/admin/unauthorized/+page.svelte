<script lang="ts">
  import { LogOut } from "lucide-svelte";
  import { enhance } from "$app/forms";
  import { supabase } from "$lib/supabase.client";
  import { clearBrowserAuthCookies } from "$lib/auth/cookie-utils";
</script>

<svelte:head>
  <title>Acceso no autorizado - Baberrih</title>
</svelte:head>

<div class="flex justify-center items-center bg-primary-50 min-h-screen">
  <div class="bg-white shadow-lg p-8 rounded-sm w-full max-w-md">
    <div class="mb-8 text-center">
      <img
        src="/baberrih-logo.svg"
        alt="Baberrih Logo"
        class="mx-auto mb-4 w-auto h-12"
      />
      <h1
        class="font-montserrat font-light text-primary-900 text-xl uppercase tracking-wider"
      >
        Acceso no autorizado
      </h1>
      <div class="bg-primary-300 mx-auto mt-4 mb-6 w-16 h-0.5"></div>
    </div>

    <div
      class="bg-red-50 mb-8 p-4 border border-red-200 rounded-sm text-red-700"
    >
      <p class="mb-2 font-medium">
        No tienes permisos para acceder al panel de administración.
      </p>
      <p>
        Si crees que deberías tener acceso, contacta al administrador del
        sistema.
      </p>
    </div>

    <div class="flex flex-col space-y-4">
      <form
        action="/admin/login?/logout"
        method="POST"
        class="w-full"
        use:enhance={async () => {
          console.log(
            "[CLIENT DEBUG /admin/unauthorized] Botón de logout presionado, iniciando use:enhance"
          );
          try {
            console.log(
              "[CLIENT DEBUG /admin/unauthorized] Estado de localStorage ANTES de supabase.auth.signOut():",
              JSON.stringify(localStorage)
            );

            // Step 1: Client-side sign out
            const { error: signOutError } = await supabase.auth.signOut();
            if (signOutError) {
              console.error(
                "[CLIENT DEBUG /admin/unauthorized] Error en supabase.auth.signOut():",
                signOutError
              );
            } else {
              console.log(
                "[CLIENT DEBUG /admin/unauthorized] supabase.auth.signOut() completado en cliente."
              );
            }

            // Step 2: Manually clear all auth cookies and localStorage
            clearBrowserAuthCookies();

            console.log(
              "[CLIENT DEBUG /admin/unauthorized] Estado de localStorage DESPUÉS de limpieza:",
              JSON.stringify(localStorage)
            );
            console.log(
              "[CLIENT DEBUG /admin/unauthorized] Cookies después de limpieza:",
              document.cookie
            );
          } catch (e) {
            console.error(
              "[CLIENT DEBUG /admin/unauthorized] Excepción durante supabase.auth.signOut() en cliente:",
              e
            );
          }

          return async ({ result }) => {
            console.log(
              "[CLIENT DEBUG /admin/unauthorized] use:enhance callback - resultado del servidor:",
              result
            );
            // Invalidar la sesión en el cliente también
            if (result.type === "redirect") {
              console.log(
                `[CLIENT DEBUG /admin/unauthorized] Redirigiendo a: ${result.location}`
              );
              // Forzar una recarga completa para asegurar que todas las cookies se limpien
              window.location.href = result.location;
            }
          };
        }}
      >
        <button
          type="submit"
          class="flex justify-center items-center bg-primary-700 hover:bg-primary-800 px-4 py-3 rounded-sm w-full font-montserrat text-white text-sm uppercase tracking-wider"
        >
          <LogOut class="mr-2 w-4 h-4" />
          Cerrar sesión
        </button>
      </form>

      <a
        href="/"
        class="flex justify-center items-center bg-white hover:bg-gray-100 px-4 py-3 border border-primary-200 rounded-sm font-montserrat text-primary-700 text-sm uppercase tracking-wider"
      >
        Volver al sitio principal
      </a>
    </div>
  </div>
</div>
