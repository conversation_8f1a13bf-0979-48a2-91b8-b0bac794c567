<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { supabase } from '$lib';
  import SuiteForm from '$lib/components/admin/SuiteForm.svelte';
  
  // State
  let sites = $state([]);
  let loading = $state(true);
  let error = $state(null);
  
  // Load sites
  async function loadSites() {
    try {
      loading = true;
      error = null;
      
      const { data, error: sitesError } = await supabase
        .from('sites')
        .select('id, name, domain')
        .order('name');
      
      if (sitesError) throw sitesError;
      
      sites = data || [];
    } catch (err) {
      console.error('Error loading sites:', err);
      error = err.message || 'Failed to load sites';
    } finally {
      loading = false;
    }
  }
  
  // Handle form submission
  function handleSaved(event) {
    const { id } = event.detail;
    goto(`/admin/suites/${id}`);
  }
  
  onMount(() => {
    loadSites();
  });
</script>

<svelte:head>
  <title>Add New Suite - <PERSON><PERSON>h Admin</title>
</svelte:head>

<div>
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-semibold text-gray-900">Add New Suite</h1>
    <a href="/admin/suites" class="text-blue-600 hover:text-blue-800">
      &larr; Back to Suites
    </a>
  </div>
  
  {#if loading}
    <div class="bg-white rounded-lg shadow p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-blue-600"></div>
      <p class="mt-2 text-gray-600">Loading...</p>
    </div>
  {:else if error}
    <div class="bg-white rounded-lg shadow p-8 text-center">
      <p class="text-red-600">{error}</p>
      <button 
        on:click={loadSites}
        class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
      >
        Try Again
      </button>
    </div>
  {:else}
    <SuiteForm {sites} on:saved={handleSaved} />
  {/if}
</div>
