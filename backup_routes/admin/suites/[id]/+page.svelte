<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { supabase } from '$lib';
  import SuiteForm from '$lib/components/admin/SuiteForm.svelte';
  
  // Get suite ID from URL
  const suiteId = $page.params.id;
  
  // State
  let suite = $state(null);
  let sites = $state([]);
  let loading = $state(true);
  let error = $state(null);
  
  // Load suite data
  async function loadSuite() {
    try {
      loading = true;
      error = null;
      
      // Load suite data
      const { data, error: suiteError } = await supabase
        .from('suites')
        .select('*')
        .eq('id', suiteId)
        .single();
      
      if (suiteError) throw suiteError;
      
      if (!data) {
        throw new Error('Suite not found');
      }
      
      suite = data;
      
      // Load sites
      await loadSites();
    } catch (err) {
      console.error('Error loading suite:', err);
      error = err.message || 'Failed to load suite';
    } finally {
      loading = false;
    }
  }
  
  // Load sites
  async function loadSites() {
    try {
      const { data, error: sitesError } = await supabase
        .from('sites')
        .select('id, name, domain')
        .order('name');
      
      if (sitesError) throw sitesError;
      
      sites = data || [];
    } catch (err) {
      console.error('Error loading sites:', err);
      throw err;
    }
  }
  
  // Handle form submission
  function handleSaved() {
    // Reload the page to show updated data
    window.location.reload();
  }
  
  onMount(() => {
    loadSuite();
  });
</script>

<svelte:head>
  <title>Edit Suite - Baberrih Admin</title>
</svelte:head>

<div>
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-semibold text-gray-900">
      {#if suite}
        Edit Suite: {suite.name}
      {:else}
        Edit Suite
      {/if}
    </h1>
    <a href="/admin/suites" class="text-blue-600 hover:text-blue-800">
      &larr; Back to Suites
    </a>
  </div>
  
  {#if loading}
    <div class="bg-white rounded-lg shadow p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-blue-600"></div>
      <p class="mt-2 text-gray-600">Loading suite data...</p>
    </div>
  {:else if error}
    <div class="bg-white rounded-lg shadow p-8 text-center">
      <p class="text-red-600">{error}</p>
      <button 
        on:click={loadSuite}
        class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
      >
        Try Again
      </button>
    </div>
  {:else if suite}
    <SuiteForm suite={suite} {sites} isEdit={true} on:saved={handleSaved} />
  {/if}
</div>
