<script lang="ts">
  import { LanguageManager } from '$lib/i18n';
</script>

<div class="container mx-auto px-4 py-8">
  <h1 class="text-2xl font-bold mb-6">Administración de Idiomas</h1>
  
  <div class="bg-white p-6 rounded-lg shadow-md">
    <LanguageManager />
  </div>
  
  <div class="mt-8 bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-xl font-bold mb-4">Instrucciones</h2>
    
    <div class="space-y-4">
      <div>
        <h3 class="text-lg font-medium">Añadir un nuevo idioma</h3>
        <p class="text-gray-700">
          Para añadir un nuevo idioma, introduce el código de 2 letras (ej: it para italiano) y el nombre del idioma.
          El sistema creará automáticamente los archivos necesarios.
        </p>
      </div>
      
      <div>
        <h3 class="text-lg font-medium">Editar traducciones</h3>
        <p class="text-gray-700">
          Después de añadir un idioma, puedes editar las traducciones en el archivo:
          <code class="bg-gray-100 px-2 py-1 rounded">static/translations/[código].json</code>
        </p>
      </div>
      
      <div>
        <h3 class="text-lg font-medium">Añadir idiomas programáticamente</h3>
        <p class="text-gray-700">
          También puedes añadir idiomas desde cualquier parte de la aplicación usando:
        </p>
        <pre class="bg-gray-100 p-3 rounded mt-2 overflow-x-auto">
import { addLanguage } from '$lib/i18n';

// Añadir un nuevo idioma
addLanguage('it', 'Italiano');
        </pre>
      </div>
      
      <div>
        <h3 class="text-lg font-medium">Usar el script de línea de comandos</h3>
        <p class="text-gray-700">
          Para añadir un idioma desde la línea de comandos:
        </p>
        <pre class="bg-gray-100 p-3 rounded mt-2 overflow-x-auto">
node scripts/add-language.js it "Italiano"
        </pre>
      </div>
    </div>
  </div>
</div>
