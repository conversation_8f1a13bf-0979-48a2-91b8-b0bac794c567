<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { supabase } from '$lib';
  import SiteUserForm from '$lib/components/admin/SiteUserForm.svelte';
  
  // Get site user ID from URL
  const siteUserId = $page.params.id;
  
  // State
  let siteUser = $state(null);
  let sites = $state([]);
  let loading = $state(true);
  let error = $state(null);
  
  // Load site user data
  async function loadSiteUser() {
    try {
      loading = true;
      error = null;
      
      // Load site user data
      const { data, error: siteUserError } = await supabase
        .from('site_users')
        .select('*')
        .eq('id', siteUserId)
        .single();
      
      if (siteUserError) throw siteUserError;
      
      if (!data) {
        throw new Error('Site user not found');
      }
      
      siteUser = data;
      
      // Load sites
      await loadSites();
    } catch (err) {
      console.error('Error loading site user:', err);
      error = err.message || 'Failed to load site user';
    } finally {
      loading = false;
    }
  }
  
  // Load sites
  async function loadSites() {
    try {
      const { data, error: sitesError } = await supabase
        .from('sites')
        .select('id, name, domain')
        .order('name');
      
      if (sitesError) throw sitesError;
      
      sites = data || [];
    } catch (err) {
      console.error('Error loading sites:', err);
      throw err;
    }
  }
  
  // Handle form submission
  function handleSaved() {
    // Reload the page to show updated data
    window.location.reload();
  }
  
  onMount(() => {
    loadSiteUser();
  });
</script>

<svelte:head>
  <title>Edit Site User - Baberrih Admin</title>
</svelte:head>

<div>
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-semibold text-gray-900">
      {#if siteUser}
        Edit User: {siteUser.full_name || siteUser.email}
      {:else}
        Edit Site User
      {/if}
    </h1>
    <a href="/admin/site-users" class="text-blue-600 hover:text-blue-800">
      &larr; Back to Users
    </a>
  </div>
  
  {#if loading}
    <div class="bg-white rounded-lg shadow p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-blue-600"></div>
      <p class="mt-2 text-gray-600">Loading user data...</p>
    </div>
  {:else if error}
    <div class="bg-white rounded-lg shadow p-8 text-center">
      <p class="text-red-600">{error}</p>
      <button 
        on:click={loadSiteUser}
        class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
      >
        Try Again
      </button>
    </div>
  {:else if siteUser}
    <SiteUserForm siteUser={siteUser} {sites} isEdit={true} on:saved={handleSaved} />
  {/if}
</div>
