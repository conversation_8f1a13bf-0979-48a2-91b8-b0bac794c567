<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { supabase } from '$lib';
  import SiteUserForm from '$lib/components/admin/SiteUserForm.svelte';
  
  // State
  let sites = $state([]);
  let loading = $state(true);
  let error = $state(null);
  
  // Load sites
  async function loadSites() {
    try {
      loading = true;
      error = null;
      
      const { data, error: sitesError } = await supabase
        .from('sites')
        .select('id, name, domain')
        .order('name');
      
      if (sitesError) throw sitesError;
      
      sites = data || [];
    } catch (err) {
      console.error('Error loading sites:', err);
      error = err.message || 'Failed to load sites';
    } finally {
      loading = false;
    }
  }
  
  // Handle form submission
  function handleSaved(event) {
    const { id } = event.detail;
    goto(`/admin/site-users/${id}`);
  }
  
  onMount(() => {
    loadSites();
  });
</script>

<svelte:head>
  <title>Add New Site User - Baberrih Admin</title>
</svelte:head>

<div>
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-semibold text-gray-900">Add New Site User</h1>
    <a href="/admin/site-users" class="text-blue-600 hover:text-blue-800">
      &larr; Back to Users
    </a>
  </div>
  
  {#if loading}
    <div class="bg-white rounded-lg shadow p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-blue-600"></div>
      <p class="mt-2 text-gray-600">Loading...</p>
    </div>
  {:else if error}
    <div class="bg-white rounded-lg shadow p-8 text-center">
      <p class="text-red-600">{error}</p>
      <button 
        on:click={loadSites}
        class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
      >
        Try Again
      </button>
    </div>
  {:else}
    <SiteUserForm {sites} on:saved={handleSaved} />
  {/if}
</div>
