import { redirect, fail } from '@sveltejs/kit';
import { z } from 'zod';
import type { Actions, PageServerLoad } from './$types';
import { hasAdminPermission } from '$lib/auth';
import { clearSupabaseAuthCookies } from '$lib/auth/cookie-utils';

// Función de depuración
function logDebug(message: string, data?: any) {
  console.log(`[ADMIN AUTH DEBUG] ${message}`, data || '');
}

// Esquema de validación para el formulario de inicio de sesión
const loginSchema = z.object({
  email: z.string().email('El correo electrónico no es válido'),
  password: z.string().min(6, 'La contraseña debe tener al menos 6 caracteres')
});

// Mensajes de error traducidos
const errorMessages: Record<string, string> = {
  'Invalid login credentials': 'Credenciales de inicio de sesión inválidas',
  'Email not confirmed': 'Correo electrónico no confirmado',
  'Invalid email or password': 'Correo electrónico o contraseña inválidos',
  'Email already in use': 'El correo electrónico ya está en uso',
  'User not found': 'Usuario no encontrado',
  'New password should be different from the old password': 'La nueva contraseña debe ser diferente a la anterior'
};

// Función para traducir mensajes de error de Supabase
function translateErrorMessage(message: string): string {
  return errorMessages[message] || 'Ha ocurrido un error. Por favor, intenta de nuevo';
}



// Cargar datos de la página
export const load: PageServerLoad = async ({ locals }) => {
  // Si el usuario ya está autenticado, verificar si tiene permisos de administración
  if (locals.session && locals.user) {
    const isAdmin = await hasAdminPermission(locals.supabase, locals.user.id);

    // Si tiene permisos de administración, redirigir al panel de administración
    if (isAdmin) {
      throw redirect(303, '/admin');
    }

    // Si no tiene permisos de administración, redirigir a la página de error
    throw redirect(303, '/admin/login?error=unauthorized');
  }

  // Si no está autenticado, mostrar la página de login
  return {};
};

// Acciones del formulario
export const actions: Actions = {
  // Acción para cerrar sesión
  logout: async ({ locals, cookies }) => {
    logDebug("Iniciando proceso de logout para admin");

    const allCookiesBefore = cookies.getAll();
    logDebug("Cookies ANTES de locals.supabase.auth.signOut() (admin):", allCookiesBefore.map(c => ({ name: c.name, value: c.value.substring(0, 30) + "..." })));

    // Cerrar sesión en Supabase. El método signOut() de @supabase/ssr debería manejar la eliminación de cookies.
    logDebug("Intentando cerrar sesión en Supabase (servidor, admin) usando locals.supabase.auth.signOut()");
    const { error } = await locals.supabase.auth.signOut();

    if (error) {
      logDebug("Error de Supabase al cerrar sesión (servidor, admin):", error);
      console.error('Error al cerrar sesión en Supabase (admin):', error);
      // Incluso con error, intentamos limpiar las cookies manualmente.
    } else {
      logDebug("Cierre de sesión en Supabase (servidor, admin) aparentemente exitoso (sin error devuelto por signOut)");
    }

    // Eliminar manualmente todas las cookies relacionadas con Supabase
    logDebug("Eliminando manualmente todas las cookies relacionadas con Supabase");
    clearSupabaseAuthCookies(cookies);

    const allCookiesAfter = cookies.getAll();
    logDebug("Cookies DESPUÉS de la eliminación manual (admin):", allCookiesAfter.map(c => ({ name: c.name, value: c.value.substring(0, 30) + "..." })));

    // Redirigir al usuario a la página de login de administración
    logDebug("Redirigiendo a /admin/login");
    throw redirect(303, '/admin/login');
  },

  // Acción para iniciar sesión
  login: async ({ request, locals }) => {
    logDebug("Procesando solicitud de inicio de sesión para admin");
    const formData = await request.formData();
    const email = formData.get('email')?.toString() || '';
    const password = formData.get('password')?.toString() || '';

    try {
      // Validar los datos del formulario
      logDebug("Validando datos del formulario");
      const result = loginSchema.parse({ email, password });

      // Iniciar sesión con Supabase
      logDebug("Intentando iniciar sesión con Supabase");
      const { data, error } = await locals.supabase.auth.signInWithPassword({
        email: result.email,
        password: result.password
      });

      logDebug("Respuesta de Supabase:", { data, error });

      // Si hay un error, devolver un mensaje de error traducido
      if (error) {
        logDebug("Error de autenticación:", error);
        return fail(400, {
          error: translateErrorMessage(error.message),
          email,
          fieldErrors: {
            // Si el error es de credenciales inválidas, marcamos ambos campos como erróneos
            ...(error.message === 'Invalid login credentials' && {
              email: ['Verifica tus credenciales'],
              password: ['Verifica tus credenciales']
            })
          }
        });
      }

      // Verificar si el usuario tiene permisos de administración
      const isAdmin = await hasAdminPermission(locals.supabase, data.user.id);

      // Si no tiene permisos de administración, cerrar sesión y devolver un error
      if (!isAdmin) {
        logDebug("Usuario no tiene permisos de administración:", data.user.id);
        await locals.supabase.auth.signOut();
        return fail(403, {
          error: 'No tienes permisos para acceder al panel de administración',
          email
        });
      }

      // Si todo está bien, devolver un resultado exitoso
      logDebug("Inicio de sesión exitoso, redirigiendo al panel de administración");
      return {
        success: true,
        message: 'Inicio de sesión exitoso',
        redirectTo: '/admin'
      };
    } catch (error) {
      // Si hay un error de validación, devolver un mensaje de error
      if (error instanceof z.ZodError) {
        logDebug("Error de validación:", error);
        const fieldErrors = error.flatten().fieldErrors;
        return fail(400, {
          error: 'Por favor, corrige los errores en el formulario',
          fieldErrors,
          email
        });
      }

      // Si es un error de redirección, propagarlo
      if (error instanceof Response) {
        logDebug("Propagando redirección:", error);
        throw error;
      }

      // Para cualquier otro error, devolver un mensaje genérico
      logDebug("Error inesperado:", error);
      console.error('Error de inicio de sesión:', error);
      return fail(500, {
        error: 'Ha ocurrido un error al procesar tu solicitud. Por favor, intenta de nuevo más tarde',
        email
      });
    }
  }
};
