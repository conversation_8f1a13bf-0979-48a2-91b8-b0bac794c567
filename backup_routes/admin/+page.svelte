<script lang="ts">
  import { onMount } from "svelte";
  import { supabase } from "$lib";
  import { Bed, Users, Calendar, Building } from "lucide-svelte";

  // Dashboard stats
  let stats = $state({
    suites: 0,
    users: 0,
    reservations: 0,
    sites: 0,
  });

  // Load dashboard data
  async function loadDashboardData() {
    try {
      // Get suites count
      const { count: suitesCount, error: suitesError } = await supabase
        .from("suites")
        .select("*", { count: "exact", head: true });

      if (suitesError) throw suitesError;
      stats.suites = suitesCount || 0;

      // Get site users count
      const { count: usersCount, error: usersError } = await supabase
        .from("site_users")
        .select("*", { count: "exact", head: true });

      if (usersError) throw usersError;
      stats.users = usersCount || 0;

      // Get reservations count
      const { count: reservationsCount, error: reservationsError } =
        await supabase
          .from("reservations")
          .select("*", { count: "exact", head: true });

      if (reservationsError) throw reservationsError;
      stats.reservations = reservationsCount || 0;

      // Get sites count
      const { count: sitesCount, error: sitesError } = await supabase
        .from("sites")
        .select("*", { count: "exact", head: true });

      if (sitesError) throw sitesError;
      stats.sites = sitesCount || 0;
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    }
  }

  onMount(() => {
    loadDashboardData();
  });
</script>

<svelte:head>
  <title>Admin Dashboard - Baberrih</title>
</svelte:head>

<div>
  <div class="mb-8">
    <h1
      class="mb-2 font-montserrat font-light text-primary-900 text-2xl uppercase tracking-wider"
    >
      Dashboard
    </h1>
    <div class="bg-primary-300 mb-6 w-24 h-0.5"></div>
    <p class="mb-6 font-montserrat text-primary-700 text-sm">
      Welcome to the Baberrih Hotel administration panel.
    </p>
  </div>

  <!-- Stats Cards -->
  <div class="gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-12">
    <!-- Suites Card -->
    <div
      class="bg-primary-50/80 shadow-sm hover:shadow-md p-6 border border-primary-200 transition-shadow"
    >
      <div class="flex items-center">
        <div class="bg-primary-200/60 p-3 rounded-sm text-primary-800">
          <Bed class="w-5 h-5" />
        </div>
        <div class="ml-4">
          <h2
            class="font-montserrat text-primary-700 text-xs uppercase tracking-wider"
          >
            Suites
          </h2>
          <p class="mt-1 font-montserrat font-light text-primary-900 text-xl">
            {stats.suites}
          </p>
        </div>
      </div>
      <div class="mt-4">
        <a
          href="/admin/suites"
          class="font-montserrat text-primary-700 hover:text-primary-800 text-xs uppercase tracking-wider"
          >View all suites →</a
        >
      </div>
    </div>

    <!-- Users Card -->
    <div
      class="bg-primary-100/70 shadow-sm hover:shadow-md p-6 border border-primary-200 transition-shadow"
    >
      <div class="flex items-center">
        <div class="bg-primary-200/70 p-3 rounded-sm text-primary-800">
          <Users class="w-5 h-5" />
        </div>
        <div class="ml-4">
          <h2
            class="font-montserrat text-primary-700 text-xs uppercase tracking-wider"
          >
            Users
          </h2>
          <p class="mt-1 font-montserrat font-light text-primary-900 text-xl">
            {stats.users}
          </p>
        </div>
      </div>
      <div class="mt-4">
        <a
          href="/admin/site-users"
          class="font-montserrat text-primary-700 hover:text-primary-800 text-xs uppercase tracking-wider"
          >Manage users →</a
        >
      </div>
    </div>

    <!-- Reservations Card -->
    <div
      class="bg-primary-50/90 shadow-sm hover:shadow-md p-6 border border-primary-200 transition-shadow"
    >
      <div class="flex items-center">
        <div class="bg-primary-200/80 p-3 rounded-sm text-primary-800">
          <Calendar class="w-5 h-5" />
        </div>
        <div class="ml-4">
          <h2
            class="font-montserrat text-primary-700 text-xs uppercase tracking-wider"
          >
            Reservations
          </h2>
          <p class="mt-1 font-montserrat font-light text-primary-900 text-xl">
            {stats.reservations}
          </p>
        </div>
      </div>
      <div class="mt-4">
        <a
          href="/admin/reservations"
          class="font-montserrat text-primary-700 hover:text-primary-800 text-xs uppercase tracking-wider"
          >View reservations →</a
        >
      </div>
    </div>

    <!-- Sites Card -->
    <div
      class="bg-primary-100/60 shadow-sm hover:shadow-md p-6 border border-primary-200 transition-shadow"
    >
      <div class="flex items-center">
        <div class="bg-primary-200/90 p-3 rounded-sm text-primary-800">
          <Building class="w-5 h-5" />
        </div>
        <div class="ml-4">
          <h2
            class="font-montserrat text-primary-700 text-xs uppercase tracking-wider"
          >
            Sites
          </h2>
          <p class="mt-1 font-montserrat font-light text-primary-900 text-xl">
            {stats.sites}
          </p>
        </div>
      </div>
      <div class="mt-4">
        <a
          href="/admin/settings"
          class="font-montserrat text-primary-700 hover:text-primary-800 text-xs uppercase tracking-wider"
          >Manage sites →</a
        >
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="bg-primary-100/40 shadow-sm mb-12 p-6 border border-primary-200">
    <h2
      class="mb-6 font-montserrat text-primary-800 text-sm uppercase tracking-wider"
    >
      Quick Actions
    </h2>
    <div class="gap-6 grid grid-cols-1 md:grid-cols-3">
      <a
        href="/admin/suites/new"
        class="flex justify-center items-center bg-primary-50/90 hover:bg-primary-200/50 shadow-sm hover:shadow-md p-4 border border-primary-200 text-primary-800 transition-all"
      >
        <Bed class="mr-2 w-4 h-4" />
        <span class="font-montserrat text-xs uppercase tracking-wider"
          >Add New Suite</span
        >
      </a>
      <a
        href="/admin/site-users/new"
        class="flex justify-center items-center bg-primary-50/80 hover:bg-primary-200/50 shadow-sm hover:shadow-md p-4 border border-primary-200 text-primary-800 transition-all"
      >
        <Users class="mr-2 w-4 h-4" />
        <span class="font-montserrat text-xs uppercase tracking-wider"
          >Add New User</span
        >
      </a>
      <a
        href="/admin/reservations/new"
        class="flex justify-center items-center bg-primary-50/70 hover:bg-primary-200/50 shadow-sm hover:shadow-md p-4 border border-primary-200 text-primary-800 transition-all"
      >
        <Calendar class="mr-2 w-4 h-4" />
        <span class="font-montserrat text-xs uppercase tracking-wider"
          >Create Reservation</span
        >
      </a>
    </div>
  </div>

  <!-- System Status -->
  <div class="bg-primary-50/60 shadow-sm p-6 border border-primary-200">
    <h2
      class="mb-6 font-montserrat text-primary-800 text-sm uppercase tracking-wider"
    >
      System Status
    </h2>
    <div class="space-y-4">
      <div
        class="flex justify-between items-center pb-3 border-primary-100 border-b"
      >
        <span class="font-montserrat text-primary-800 text-xs"
          >Database Connection</span
        >
        <span
          class="bg-primary-100/70 px-3 py-1 font-montserrat text-primary-800 text-xs uppercase"
          >Connected</span
        >
      </div>
      <div
        class="flex justify-between items-center pb-3 border-primary-100 border-b"
      >
        <span class="font-montserrat text-primary-800 text-xs"
          >Cloudbeds Integration</span
        >
        <span
          class="bg-primary-100/60 px-3 py-1 font-montserrat text-primary-800 text-xs uppercase"
          >Active</span
        >
      </div>
      <div class="flex justify-between items-center">
        <span class="font-montserrat text-primary-800 text-xs"
          >Last Data Sync</span
        >
        <span class="font-montserrat text-primary-700 text-xs"
          >Today at {new Date().toLocaleTimeString()}</span
        >
      </div>
    </div>
  </div>
</div>
