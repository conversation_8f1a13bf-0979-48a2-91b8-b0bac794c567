<script lang="ts">
  import { onMount } from "svelte";
  
  // Animation state
  let overviewVisible = $state(false);
  let facilitiesVisible = $state(false);
  
  // Facilities data
  const facilities = [
    {
      id: "garden",
      title: "Garden and Orchard",
      description: "Our lush garden and orchard are the heart of Baberrih, offering a peaceful retreat where you can connect with nature. Wander through aromatic herbs, flowering plants, and fruit trees.",
      image: "https://baberrih.ma/media/facilities/garden/1_f.webp",
      url: "/facilities/garden",
      features: ["Organic vegetable garden", "Fruit trees", "Aromatic herb garden", "Shaded seating areas"]
    },
    {
      id: "common-areas",
      title: "Common Areas",
      description: "The common areas at Baberrih are designed to foster connection and relaxation. From our welcoming reception to cozy lounges and outdoor terraces, these spaces blend traditional Moroccan design with modern comfort.",
      image: "https://baberrih.ma/media/facilities/common/1_f.webp",
      url: "/facilities/common-areas",
      features: ["Welcoming reception area", "Comfortable lounge with fireplace", "Library", "Outdoor terraces"]
    },
    {
      id: "yoga-room",
      title: "Yoga Room",
      description: "Our dedicated yoga room provides a serene space for practice and meditation. With natural light, beautiful views, and all necessary equipment, it's the perfect environment to nurture your body and mind.",
      image: "https://baberrih.ma/media/facilities/yoga/1_f.webp",
      url: "/facilities/yoga-room",
      features: ["Spacious practice area", "Natural light", "Yoga mats and props", "Sound system"]
    },
    {
      id: "stable",
      title: "Stable",
      description: "Our well-maintained stable houses our gentle horses, which are available for guided rides along the beach and surrounding countryside. The facility is designed with animal welfare as a priority.",
      image: "https://baberrih.ma/media/facilities/stable/1_f.webp",
      url: "/facilities/stable",
      features: ["Well-ventilated horse stalls", "Experienced stable staff", "Riding equipment", "Direct access to beach riding trails"]
    },
    {
      id: "pool",
      title: "Pool",
      description: "Our refreshing pool offers a perfect alternative to the ocean, with calm waters and comfortable lounging areas. Surrounded by palm trees and offering views of the garden, it's an ideal spot for relaxation.",
      image: "https://baberrih.ma/media/facilities/pool/1_f.webp",
      url: "/facilities/pool",
      features: ["Freshwater pool", "Comfortable sun loungers", "Poolside service", "Towel service"]
    },
    {
      id: "beach",
      title: "Beach",
      description: "Baberrih enjoys a privileged location with direct access to Tissa Beach, a pristine stretch of Atlantic coastline. Whether you want to swim, stroll, or simply relax with the sound of waves, the beach is just steps away.",
      image: "https://baberrih.ma/media/facilities/beach/1_f.webp",
      url: "/facilities/beach",
      features: ["Direct beach access", "Beach chairs and umbrellas", "Beach towel service", "Water sports nearby"]
    }
  ];
  
  // Intersection Observer for scroll animations
  function setupIntersectionObserver() {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };
    
    const overviewObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          overviewVisible = true;
          overviewObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    const facilitiesObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          facilitiesVisible = true;
          facilitiesObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    // Observe elements
    const overviewElement = document.getElementById('overview-section');
    if (overviewElement) overviewObserver.observe(overviewElement);
    
    const facilitiesElement = document.getElementById('facilities-section');
    if (facilitiesElement) facilitiesObserver.observe(facilitiesElement);
  }
  
  onMount(() => {
    setupIntersectionObserver();
  });
</script>

<svelte:head>
  <title>Facilities - Baberrih Hotel</title>
  <meta name="description" content="Explore the facilities at Baberrih Hotel in Essaouira, Morocco, including our garden, pool, beach access, yoga room, and more, designed for your comfort and enjoyment." />
</svelte:head>

<!-- Hero Section -->
<section class="relative">
  <img 
    src="https://baberrih.ma/media/facilities/1_f.webp" 
    alt="Baberrih Facilities" 
    class="w-full h-[50vh] md:h-[60vh] object-cover"
  />
  <div class="absolute inset-0 flex items-center justify-center bg-primary-950/50">
    <h1 class="font-montserrat font-light text-primary-50 text-3xl md:text-5xl uppercase">Facilities</h1>
  </div>
</section>

<!-- Overview Section -->
<section id="overview-section" class="py-12 px-4">
  <div class="container mx-auto max-w-4xl">
    <div class={`scroll-animation fade ${overviewVisible ? 'visible' : ''}`} style="--delay: 300ms;">
      <h2 class="font-montserrat font-light text-primary-900 text-xl uppercase mb-6">Our Facilities</h2>
      
      <p class="font-eb-garamond font-light text-primary-800 text-lg mb-6">
        At Baberrih, we've created a range of facilities designed to enhance your stay and connect you with the natural beauty and tranquility of our location. From our lush gardens to our direct beach access, each space is thoughtfully designed to provide comfort, relaxation, and authentic experiences.
      </p>
      
      <p class="font-eb-garamond font-light text-primary-800 text-lg mb-6">
        Our facilities blend traditional Moroccan design elements with modern amenities, creating spaces that are both beautiful and functional. Whether you're seeking active pursuits or peaceful relaxation, our facilities provide the perfect setting for your preferred experience.
      </p>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        <div class="border border-primary-100 p-6">
          <h3 class="font-montserrat font-light text-primary-900 text-base uppercase mb-2">Relaxation</h3>
          <p class="font-eb-garamond font-light text-primary-800">
            Unwind in our tranquil spaces, from the peaceful garden to our comfortable common areas. Our pool and direct beach access offer different ways to enjoy the Moroccan sunshine.
          </p>
        </div>
        
        <div class="border border-primary-100 p-6">
          <h3 class="font-montserrat font-light text-primary-900 text-base uppercase mb-2">Wellness</h3>
          <p class="font-eb-garamond font-light text-primary-800">
            Nurture your wellbeing in our dedicated yoga room, or enjoy the natural therapy of walks along the beach and through our garden. Our facilities support a holistic approach to wellness.
          </p>
        </div>
        
        <div class="border border-primary-100 p-6">
          <h3 class="font-montserrat font-light text-primary-900 text-base uppercase mb-2">Activities</h3>
          <p class="font-eb-garamond font-light text-primary-800">
            From horseback riding starting at our stable to water sports at the beach, our facilities provide the perfect base for a range of activities that allow you to experience the best of Essaouira.
          </p>
        </div>
        
        <div class="border border-primary-100 p-6">
          <h3 class="font-montserrat font-light text-primary-900 text-base uppercase mb-2">Dining</h3>
          <p class="font-eb-garamond font-light text-primary-800">
            Enjoy meals in various settings, from our restaurant to outdoor terraces, garden dining spots, and even beach picnics. Our organic garden provides fresh ingredients for our kitchen.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Facilities Section -->
<section id="facilities-section" class="py-12 px-4 bg-primary-50">
  <div class="container mx-auto max-w-6xl">
    <div class={`scroll-animation slide-up ${facilitiesVisible ? 'visible' : ''}`} style="--delay: 300ms;">
      <h2 class="font-montserrat font-light text-primary-900 text-xl uppercase mb-10 text-center">Explore Our Facilities</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {#each facilities as facility}
          <div class="bg-primary-50 border border-primary-100">
            <div class="aspect-ratio-container" style="--aspect-ratio:16/9">
              <img 
                src={facility.image} 
                alt={facility.title} 
                class="w-full h-full object-cover"
              />
            </div>
            <div class="p-6">
              <h3 class="font-montserrat font-medium text-primary-900 text-xl mb-2">{facility.title}</h3>
              <p class="font-eb-garamond font-light text-primary-800 mb-4">{facility.description}</p>
              
              <div class="flex flex-wrap gap-2 mb-6">
                {#each facility.features as feature}
                  <span class="bg-primary-100 px-3 py-1 text-xs font-montserrat font-light text-primary-800">
                    {feature}
                  </span>
                {/each}
              </div>
              
              <a href={facility.url} class="button-secondary">
                View Details
              </a>
            </div>
          </div>
        {/each}
      </div>
    </div>
  </div>
</section>

<!-- Additional Information Section -->
<section class="py-12 px-4">
  <div class="container mx-auto max-w-4xl text-center">
    <h2 class="font-montserrat font-light text-primary-900 text-xl uppercase mb-6">Additional Services</h2>
    <p class="font-eb-garamond font-light text-primary-800 text-lg mb-8 max-w-2xl mx-auto">
      Beyond our main facilities, we offer a range of services to enhance your stay, including airport transfers, excursion planning, in-room massage, and special event arrangements. Contact our concierge team for more information.
    </p>
    <a href="/contact" class="button">
      Contact Us
    </a>
  </div>
</section>
