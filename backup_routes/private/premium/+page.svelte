<script>
  let { data } = $props();
  let { user } = $derived(data);
</script>

<div class="space-y-4">
  <div class="alert alert-success">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="stroke-current w-6 h-6 shrink-0"
      fill="none"
      viewBox="0 0 24 24"
      ><path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      /></svg
    >
    <span>¡Contenido Premium! Tienes una suscripción activa.</span>
  </div>

  <div class="bg-base-200 card">
    <div class="card-body">
      <h3 class="card-title">Contenido exclusivo para suscriptores</h3>
      <p>Este contenido solo está disponible para usuarios con una suscripción activa.</p>
      <p>Grac<PERSON> por ser un suscriptor premium.</p>
    </div>
  </div>

  <div class="card-actions">
    <a href="/private" class="btn-outline btn">Volver a mi cuenta</a>
  </div>
</div>
