import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { getUserReservations } from '$lib/services/reservations';

export const load = (async ({ locals }) => {
  // Verificar si el usuario está autenticado
  if (!locals.session || !locals.user) {
    throw redirect(303, '/auth');
  }

  const userId = locals.user.id;
  
  // Obtener las reservas del usuario
  const { data: reservations, error } = await getUserReservations(locals.supabase, userId);
  
  return {
    reservations,
    error,
    user: locals.user
  };
}) satisfies PageServerLoad;
