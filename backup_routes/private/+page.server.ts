import type { PageServerLoad } from './$types';
import { redirect } from '@sveltejs/kit';

/**
 * Este archivo carga los datos necesarios para la página privada
 * y verifica que el usuario esté autenticado.
 */
export const load = (async (event) => {
  // Verificar si el usuario está autenticado
  if (!event.locals.session || !event.locals.user) {
    throw redirect(303, '/auth');
  }

  // Si llega aquí, el usuario está autenticado

  // Devolver los datos necesarios para la página
  // La verificación de suscripción se hará de forma asíncrona en el cliente
  return {
    session: event.locals.session,
    user: event.locals.user
  };
}) satisfies PageServerLoad;
