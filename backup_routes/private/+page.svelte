<script>
  import { onMount } from "svelte";
  import { supabase } from "$lib/supabase.client";
  import { enhance } from "$app/forms";
  import { clearBrowserAuthCookies } from "$lib/auth/cookie-utils";
  import {
    authStore,
    user,
    isAdmin,
    isLoading as authIsLoading,
    initialized as authInitialized,
  } from "$lib/auth";

  let { data } = $props();
  console.log(
    "[Private Page] Component instance created. Initial data prop:",
    data
  );

  // Estado para verificar si el usuario tiene una suscripción activa
  let hasSubscription = $state(false);
  let subscriptionLoading = $state(true);

  // $effect for when $user changes (Svelte 5 Runes)
  $effect(() => {
    const timestamp = new Date().toISOString();
    console.log(
      `[Private Page User Effect - ${timestamp}] Auth user store changed:`,
      $user?.id,
      "User object:",
      $user
    );
    if ($user && $user.id) {
      console.log(
        `[Private Page User Effect - ${timestamp}] User is present, calling checkSubscriptionStatus.`
      );
      checkSubscriptionStatus();
    } else {
      console.log(
        `[Private Page User Effect - ${timestamp}] User is not present or lacks ID. Resetting subscription state.`
      );
      hasSubscription = false;
      subscriptionLoading = true; // Reset if user logs out or becomes null
    }
  });

  // $effect for when $authIsLoading changes (Svelte 5 Runes)
  $effect(() => {
    const timestamp = new Date().toISOString();
    console.log(
      `[Private Page Auth Loading Effect - ${timestamp}] Auth isLoading store changed:`,
      $authIsLoading
    );
  });

  // $effect for when $authInitialized changes (Svelte 5 Runes)
  $effect(() => {
    const timestamp = new Date().toISOString();
    console.log(
      `[Private Page Auth Initialized Effect - ${timestamp}] Auth initialized store changed:`,
      $authInitialized
    );
  });

  // Verificar si el usuario tiene una suscripción activa de forma diferida
  async function checkSubscriptionStatus() {
    const timestamp = new Date().toISOString();
    if (!$user || !$user.id) {
      console.log(
        `[Private Page SubCheck - ${timestamp}] No user or user.id, cannot check subscription.`
      );
      subscriptionLoading = false;
      hasSubscription = false;
      return;
    }
    console.log(
      `[Private Page SubCheck - ${timestamp}] Starting subscription check for user:`,
      $user.id
    );
    subscriptionLoading = true;

    try {
      // Verificar si el usuario tiene una suscripción activa
      console.time(
        `[Private Page SubCheck - ${timestamp}] API call /api/subscription/check`
      );
      const response = await fetch("/api/subscription/check");
      const result = await response.json();
      console.timeEnd(
        `[Private Page SubCheck - ${timestamp}] API call /api/subscription/check`
      );
      console.log(
        `[Private Page SubCheck - ${timestamp}] Subscription API result:`,
        result
      );

      hasSubscription = result.hasActiveSubscription;
    } catch (error) {
      console.error(
        `[Private Page SubCheck - ${timestamp}] Error checking subscription:`,
        error
      );
      hasSubscription = false; // Assume no subscription on error
    } finally {
      console.log(
        `[Private Page SubCheck - ${timestamp}] Subscription check finished. HasSubscription: ${hasSubscription}`
      );
      subscriptionLoading = false;
    }
  }

  onMount(() => {
    const timestamp = new Date().toISOString();
    console.log(
      `[Private Page onMount - ${timestamp}] Component mounted. Initial $user:`,
      $user?.id,
      "Auth isLoading:",
      $authIsLoading,
      "Auth initialized:",
      $authInitialized
    );
    // Initial subscription check if user is already available from the store on mount
    if ($user && $user.id) {
      console.log(
        `[Private Page onMount - ${timestamp}] User already available on mount, calling checkSubscriptionStatus.`
      );
      checkSubscriptionStatus();
    }
  });
</script>

<div class="space-y-4">
  <div class="alert alert-success">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="stroke-current w-6 h-6 shrink-0"
      fill="none"
      viewBox="0 0 24 24"
      ><path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      /></svg
    >
    <span>¡Autenticación exitosa! Esta es tu área personal.</span>
  </div>

  <div class="bg-base-200 card">
    <div class="card-body">
      <h3 class="card-title">Información del usuario</h3>
      {#if $user}
        <div class="overflow-x-auto">
          <table class="table">
            <tbody>
              <tr>
                <th>ID</th>
                <td>{$user.id}</td>
              </tr>
              <tr>
                <th>Email</th>
                <td>{$user.email}</td>
              </tr>
              <tr>
                <th>Último inicio de sesión</th>
                <td>{new Date($user.last_sign_in_at || "").toLocaleString()}</td
                >
              </tr>
              <tr>
                <th>Estado de suscripción</th>
                <td>
                  {#if subscriptionLoading}
                    <span class="loading loading-spinner loading-xs"></span> Verificando...
                  {:else if hasSubscription}
                    <span class="badge badge-success">Activa</span>
                  {:else}
                    <span class="badge badge-warning">Inactiva</span>
                  {/if}
                </td>
              </tr>
              <tr>
                <th>Rol</th>
                <td>
                  {#if $isAdmin}
                    <span class="badge badge-primary">Administrador</span>
                  {:else}
                    <span class="badge badge-secondary">Usuario</span>
                  {/if}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      {:else}
        <div class="flex items-center space-x-2">
          <span class="loading loading-spinner loading-md"></span>
          <span>Cargando información del usuario...</span>
        </div>
      {/if}
    </div>
  </div>

  <div class="flex flex-wrap gap-2">
    {#if $user}
      <a href="/private/reservations" class="btn-primary btn">
        Ver mis reservas
      </a>

      {#if subscriptionLoading && !$user}
        <button class="btn-secondary btn" disabled>
          <span class="loading loading-spinner loading-xs"></span> Verificando...
        </button>
      {:else if hasSubscription}
        <a href="/private/premium" class="btn-primary btn">
          Acceder a contenido premium
        </a>
      {:else}
        <a href="/products" class="btn-secondary btn">
          Ver planes de suscripción
        </a>
      {/if}

      {#if $isAdmin}
        <a href="/admin" class="btn-accent btn"> Panel de administración </a>
      {/if}

      <form
        action="/auth?/logout"
        method="POST"
        use:enhance={async () => {
          console.log(
            "[CLIENT DEBUG /private] Botón de logout presionado, iniciando use:enhance"
          );
          try {
            console.log(
              "[CLIENT DEBUG /private] Estado de localStorage ANTES de supabase.auth.signOut():",
              JSON.stringify(localStorage)
            );
            // Step 1: Client-side sign out
            const { error: signOutError } = await supabase.auth.signOut();
            if (signOutError) {
              console.error(
                "[CLIENT DEBUG /private] Error en supabase.auth.signOut():",
                signOutError
              );
            } else {
              console.log(
                "[CLIENT DEBUG /private] supabase.auth.signOut() completado en cliente."
              );
            }

            // Step 2: Update the auth store
            authStore.clearSession();

            // Step 3: Manually clear all auth cookies and localStorage
            clearBrowserAuthCookies();

            console.log(
              "[CLIENT DEBUG /private] Estado de localStorage DESPUÉS de limpieza:",
              JSON.stringify(localStorage)
            );
            console.log(
              "[CLIENT DEBUG /private] Cookies después de limpieza:",
              document.cookie
            );
          } catch (e) {
            console.error(
              "[CLIENT DEBUG /private] Excepción durante supabase.auth.signOut() en cliente:",
              e
            );
          }

          return async ({ result }) => {
            console.log(
              "[CLIENT DEBUG /private] use:enhance callback - resultado del servidor:",
              result
            );
            // Invalidar la sesión en el cliente también
            if (result.type === "redirect") {
              console.log(
                `[CLIENT DEBUG /private] Redirigiendo a: ${result.location}`
              );
              // Forzar una recarga completa para asegurar que todas las cookies se limpien
              window.location.href = result.location;
            }
          };
        }}
      >
        <button type="submit" class="btn-outline btn-error btn">
          Cerrar sesión
        </button>
      </form>
    {:else}
      <!-- Fallback/disabled buttons if user is not loaded -->
      <button class="btn-primary btn" disabled>Ver mis reservas</button>
      <button class="btn-secondary btn" disabled>
        Ver planes de suscripción
      </button>
      <button class="btn-outline btn-error btn" disabled>Cerrar sesión</button>
    {/if}
  </div>

  <div class="card-actions">
    <a href="/" class="btn-outline btn">Volver al inicio</a>
  </div>
</div>
