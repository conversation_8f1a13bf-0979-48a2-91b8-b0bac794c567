<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { T } from '$lib/i18n';
  import ReservationCard from '$lib/components/reservations/ReservationCard.svelte';
  import type { Reservation } from '$lib/services/reservations';
  
  // Estado
  let reservation = $state<Reservation | null>(null);
  let error = $state<string | null>(null);
  let loading = $state(true);
  
  // Obtener token de la URL
  $: token = $page.url.searchParams.get('token');
  
  // Verificar token y obtener detalles de la reserva
  async function verifyTokenAndGetReservation(): Promise<void> {
    if (!token) {
      error = 'Se requiere un token de acceso válido';
      loading = false;
      return;
    }
    
    try {
      const response = await fetch(`/api/reservations/token?token=${token}`);
      const result = await response.json();
      
      if (!result.success) {
        error = result.error || 'Token inválido o expirado';
      } else if (result.data) {
        reservation = result.data;
      }
    } catch (err) {
      console.error('Error al verificar token:', err);
      error = 'Ha ocurrido un error al verificar el token. Por favor, inténtalo de nuevo.';
    } finally {
      loading = false;
    }
  }
  
  onMount(() => {
    verifyTokenAndGetReservation();
  });
</script>

<svelte:head>
  <title>Detalles de Reserva - Baberrih</title>
</svelte:head>

<div class="mx-auto max-w-3xl">
  <div class="mb-8">
    <h1 class="font-montserrat font-medium text-2xl text-primary-900 mb-2">
      <T key="reservations.reservationDetails" />
    </h1>
    <p class="text-primary-600">
      <T key="reservations.reservationDetailsDescription" />
    </p>
  </div>
  
  {#if loading}
    <div class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-700"></div>
    </div>
  {:else if error}
    <div class="bg-red-50 border border-red-200 text-red-700 p-6 rounded-sm">
      <h3 class="font-montserrat font-medium text-lg mb-2">
        <T key="reservations.accessError" />
      </h3>
      <p>{error}</p>
      <div class="mt-6">
        <a 
          href="/reservations/search" 
          class="inline-block bg-primary-700 hover:bg-primary-800 text-white font-montserrat py-2 px-4 rounded-sm transition-colors text-sm"
        >
          <T key="reservations.searchAgain" />
        </a>
      </div>
    </div>
  {:else if reservation}
    <div class="space-y-6">
      <ReservationCard reservation={reservation} />
      
      <div class="bg-primary-50 border border-primary-200 p-6 rounded-sm">
        <h3 class="font-montserrat font-medium text-lg text-primary-900 mb-4">
          <T key="reservations.saveForLater" />
        </h3>
        <p class="text-primary-600 mb-4">
          <T key="reservations.saveForLaterDescription" />
        </p>
        <div class="flex flex-wrap gap-3">
          <a 
            href="/auth?register=true" 
            class="inline-block bg-primary-700 hover:bg-primary-800 text-white font-montserrat py-2 px-4 rounded-sm transition-colors text-sm"
          >
            <T key="common.createAccount" />
          </a>
          <a 
            href="/reservations/search" 
            class="inline-block bg-white hover:bg-primary-50 text-primary-700 border border-primary-200 font-montserrat py-2 px-4 rounded-sm transition-colors text-sm"
          >
            <T key="reservations.searchAgain" />
          </a>
        </div>
      </div>
    </div>
  {:else}
    <div class="bg-primary-50 border border-primary-200 text-primary-700 p-6 rounded-sm">
      <h3 class="font-montserrat font-medium text-lg mb-2">
        <T key="reservations.noReservationFound" />
      </h3>
      <p>
        <T key="reservations.noReservationFoundDescription" />
      </p>
      <div class="mt-6">
        <a 
          href="/reservations/search" 
          class="inline-block bg-primary-700 hover:bg-primary-800 text-white font-montserrat py-2 px-4 rounded-sm transition-colors text-sm"
        >
          <T key="reservations.searchAgain" />
        </a>
      </div>
    </div>
  {/if}
</div>
