<script lang="ts">
  import { page } from "$app/stores";
  import { onMount } from "svelte";
  
  // Get the slug from the URL
  const slug = $derived($page.params.slug);
  
  // State for scroll animations
  let detailsVisible = $state(false);
  let infoVisible = $state(false);
  let bookingVisible = $state(false);
  
  // Experience data (in a real app, this would come from an API or CMS)
  const experiences = {
    "horseback-riding": {
      title: "Horseback Riding",
      description: "Experience the beauty of Essaouira's coastline on horseback. Our guided horseback riding tours take you along the pristine beaches and through the surrounding countryside, offering a unique perspective of the region's natural beauty.",
      images: [
        "https://baberrih.ma/media/experiences/horse/1_f.webp",
        "https://baberrih.ma/media/experiences/horse/2_f.webp",
        "https://baberrih.ma/media/experiences/horse/3_f.webp",
        "https://baberrih.ma/media/experiences/horse/4_f.webp"
      ],
      details: [
        "Duration: 1-3 hours",
        "Difficulty: Beginner to Advanced",
        "Includes: Professional guide, equipment, safety briefing",
        "Best time: Morning or late afternoon",
        "Location: Baberrih stables and beach"
      ],
      price: "From 500 MAD per person"
    },
    "quads": {
      title: "Quads",
      description: "Explore the diverse landscapes around Essaouira on a thrilling quad bike adventure. Navigate through sand dunes, forests, and along the coastline for an adrenaline-pumping experience that showcases the region's natural diversity.",
      images: [
        "https://baberrih.ma/media/experiences/quads/1_f.webp",
        "https://baberrih.ma/media/experiences/quads/2_f.webp",
        "https://baberrih.ma/media/experiences/quads/3_f.webp",
        "https://baberrih.ma/media/experiences/quads/4_f.webp"
      ],
      details: [
        "Duration: 2-4 hours",
        "Difficulty: Beginner to Intermediate",
        "Includes: Professional guide, equipment, safety briefing",
        "Best time: Morning or late afternoon",
        "Location: Surrounding areas of Essaouira"
      ],
      price: "From 700 MAD per person"
    },
    "land-sailing": {
      title: "Land Sailing",
      description: "Experience the thrill of land sailing on Essaouira's wide, flat beaches. Harness the power of the wind as you glide across the sand in these eco-friendly vehicles, combining the excitement of sailing with the accessibility of land travel.",
      images: [
        "https://baberrih.ma/media/experiences/sand_sailing/1_f.webp",
        "https://baberrih.ma/media/experiences/sand_sailing/2_f.webp",
        "https://baberrih.ma/media/experiences/sand_sailing/3_f.webp",
        "https://baberrih.ma/media/experiences/sand_sailing/4_f.webp"
      ],
      details: [
        "Duration: 1-2 hours",
        "Difficulty: Beginner to Intermediate",
        "Includes: Professional instructor, equipment, safety briefing",
        "Best time: Windy days, typically afternoon",
        "Location: Essaouira beach"
      ],
      price: "From 400 MAD per person"
    },
    "fatbikes": {
      title: "Fatbikes",
      description: "Discover the coastal landscapes of Essaouira on a fatbike adventure. These specialized bikes with wide tires are perfect for riding on sand, allowing you to explore remote beaches and coastal trails with ease and minimal environmental impact.",
      images: [
        "https://baberrih.ma/media/experiences/fatbikes/1_f.webp",
        "https://baberrih.ma/media/experiences/fatbikes/2_f.webp",
        "https://baberrih.ma/media/experiences/fatbikes/3_f.webp",
        "https://baberrih.ma/media/experiences/fatbikes/4_f.webp"
      ],
      details: [
        "Duration: 2-3 hours",
        "Difficulty: Beginner to Intermediate",
        "Includes: Guide, fatbike rental, helmet, water",
        "Best time: Morning or late afternoon",
        "Location: Coastal trails and beaches"
      ],
      price: "From 350 MAD per person"
    },
    "yoga": {
      title: "Yoga",
      description: "Find inner peace and balance with our yoga sessions at Baberrih. Practice in our dedicated yoga room or on the beach at sunrise or sunset, guided by experienced instructors who adapt sessions to all levels of experience.",
      images: [
        "https://baberrih.ma/media/experiences/yoga/1_f.webp",
        "https://baberrih.ma/media/experiences/yoga/2_f.webp",
        "https://baberrih.ma/media/experiences/yoga/3_f.webp",
        "https://baberrih.ma/media/experiences/yoga/4_f.webp"
      ],
      details: [
        "Duration: 60-90 minutes",
        "Difficulty: All levels",
        "Includes: Instructor, yoga mats, props",
        "Best time: Sunrise or sunset",
        "Location: Yoga room or beach"
      ],
      price: "From 200 MAD per person"
    },
    "kite-surf": {
      title: "Kite Surf",
      description: "Essaouira is world-renowned for its ideal kitesurfing conditions. Take advantage of the consistent winds and learn this exhilarating water sport with our experienced instructors, or rent equipment if you're already skilled.",
      images: [
        "https://baberrih.ma/media/experiences/kitesurf/1_f.webp",
        "https://baberrih.ma/media/experiences/kitesurf/2_f.webp",
        "https://baberrih.ma/media/experiences/kitesurf/3_f.webp",
        "https://baberrih.ma/media/experiences/kitesurf/4_f.webp"
      ],
      details: [
        "Duration: 2-3 hours (lessons), Full day (rental)",
        "Difficulty: Beginner to Advanced",
        "Includes: Instructor, equipment, safety briefing (for lessons)",
        "Best time: Afternoon when winds are strongest",
        "Location: Essaouira bay"
      ],
      price: "From 600 MAD per person (lessons), 400 MAD (equipment rental)"
    }
  };
  
  // Get the current experience
  const experience = $derived(experiences[slug as keyof typeof experiences]);
  
  // Carousel state
  let currentImageIndex = $state(0);
  
  function nextImage() {
    if (experience) {
      currentImageIndex = (currentImageIndex + 1) % experience.images.length;
    }
  }
  
  function prevImage() {
    if (experience) {
      currentImageIndex = (currentImageIndex - 1 + experience.images.length) % experience.images.length;
    }
  }
  
  // Intersection Observer for scroll animations
  function setupIntersectionObserver() {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };
    
    const detailsObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          detailsVisible = true;
          detailsObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    const infoObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          infoVisible = true;
          infoObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    const bookingObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          bookingVisible = true;
          bookingObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    // Observe elements
    const detailsElement = document.getElementById('details-section');
    if (detailsElement) detailsObserver.observe(detailsElement);
    
    const infoElement = document.getElementById('info-section');
    if (infoElement) infoObserver.observe(infoElement);
    
    const bookingElement = document.getElementById('booking-section');
    if (bookingElement) bookingObserver.observe(bookingElement);
  }
  
  onMount(() => {
    setupIntersectionObserver();
  });
</script>

<svelte:head>
  <title>{experience?.title || 'Experience'} - Baberrih Hotel</title>
  <meta name="description" content={experience?.description || 'Discover our unique experiences at Baberrih Hotel in Essaouira, Morocco.'} />
</svelte:head>

{#if experience}
  <!-- Hero Section with Carousel -->
  <section class="relative">
    <div class="carousel">
      <img 
        src={experience.images[currentImageIndex]} 
        alt={`${experience.title} - Image ${currentImageIndex + 1}`} 
        class="carousel-item"
      />
      <div class="carousel-nav">
        <button onclick={prevImage} class="carousel-button" aria-label="Previous image">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button onclick={nextImage} class="carousel-button" aria-label="Next image">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
      <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-primary-950/75 to-transparent">
        <h1 class="font-montserrat font-light text-primary-50 text-2xl md:text-4xl uppercase">{experience.title}</h1>
      </div>
    </div>
  </section>

  <!-- Details Section -->
  <section id="details-section" class="py-12 px-4">
    <div class="container mx-auto max-w-4xl">
      <div class={`scroll-animation fade ${detailsVisible ? 'visible' : ''}`} style="--delay: 300ms;">
        <h2 class="font-montserrat font-light text-primary-900 text-xl uppercase mb-6">About this experience</h2>
        <p class="font-eb-garamond font-light text-primary-800 text-lg mb-8">{experience.description}</p>
      </div>
    </div>
  </section>

  <!-- Info Section -->
  <section id="info-section" class="py-12 px-4 bg-primary-50">
    <div class="container mx-auto max-w-4xl">
      <div class={`scroll-animation slide-up ${infoVisible ? 'visible' : ''}`} style="--delay: 300ms;">
        <h2 class="font-montserrat font-light text-primary-900 text-xl uppercase mb-6">Experience Details</h2>
        <div class="grid grid-cols-1 gap-4">
          {#each experience.details as detail}
            <div class="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-5 h-5 text-primary-700">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <p class="font-montserrat font-light text-primary-800">{detail}</p>
            </div>
          {/each}
        </div>
        
        <div class="mt-8 p-6 border border-primary-200">
          <h3 class="font-montserrat font-light text-primary-900 text-base uppercase mb-2">Price</h3>
          <p class="font-eb-garamond font-light text-primary-800 text-lg">{experience.price}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Booking Section -->
  <section id="booking-section" class="py-12 px-4">
    <div class="container mx-auto max-w-4xl">
      <div class={`scroll-animation fade ${bookingVisible ? 'visible' : ''}`} style="--delay: 300ms;">
        <h2 class="font-montserrat font-light text-primary-900 text-xl uppercase mb-6">Book this experience</h2>
        <p class="font-eb-garamond font-light text-primary-800 text-lg mb-8">Interested in the {experience.title} experience? Contact our concierge to arrange your booking.</p>
        
        <div class="flex justify-center">
          <a href="/contact" class="button">
            Contact Us
          </a>
        </div>
      </div>
    </div>
  </section>
{:else}
  <div class="flex items-center justify-center min-h-[50vh]">
    <p class="font-montserrat font-light text-primary-900 text-xl">Experience not found</p>
  </div>
{/if}
