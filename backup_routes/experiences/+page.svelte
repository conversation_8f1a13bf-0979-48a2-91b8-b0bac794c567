<script lang="ts">
  import { onMount } from "svelte";
  
  // Animation state
  let overviewVisible = $state(false);
  let experiencesVisible = $state(false);
  
  // Experiences data
  const experiences = [
    {
      id: "horseback-riding",
      title: "Horseback Riding",
      description: "Experience the beauty of Essaouira's coastline on horseback. Our guided horseback riding tours take you along the pristine beaches and through the surrounding countryside.",
      image: "https://baberrih.ma/media/experiences/horse/1_f.webp",
      url: "/experiences/horseback-riding",
      details: ["Duration: 1-3 hours", "Difficulty: Beginner to Advanced", "Best time: Morning or late afternoon"]
    },
    {
      id: "quads",
      title: "Quads",
      description: "Explore the diverse landscapes around Essaouira on a thrilling quad bike adventure. Navigate through sand dunes, forests, and along the coastline for an adrenaline-pumping experience.",
      image: "https://baberrih.ma/media/experiences/quads/1_f.webp",
      url: "/experiences/quads",
      details: ["Duration: 2-4 hours", "Difficulty: Beginner to Intermediate", "Best time: Morning or late afternoon"]
    },
    {
      id: "land-sailing",
      title: "Land Sailing",
      description: "Experience the thrill of land sailing on Essaouira's wide, flat beaches. Harness the power of the wind as you glide across the sand in these eco-friendly vehicles.",
      image: "https://baberrih.ma/media/experiences/sand_sailing/1_f.webp",
      url: "/experiences/land-sailing",
      details: ["Duration: 1-2 hours", "Difficulty: Beginner to Intermediate", "Best time: Windy days, typically afternoon"]
    },
    {
      id: "fatbikes",
      title: "Fatbikes",
      description: "Discover the coastal landscapes of Essaouira on a fatbike adventure. These specialized bikes with wide tires are perfect for riding on sand, allowing you to explore remote beaches.",
      image: "https://baberrih.ma/media/experiences/fatbikes/1_f.webp",
      url: "/experiences/fatbikes",
      details: ["Duration: 2-3 hours", "Difficulty: Beginner to Intermediate", "Best time: Morning or late afternoon"]
    },
    {
      id: "yoga",
      title: "Yoga",
      description: "Find inner peace and balance with our yoga sessions at Baberrih. Practice in our dedicated yoga room or on the beach at sunrise or sunset, guided by experienced instructors.",
      image: "https://baberrih.ma/media/experiences/yoga/1_f.webp",
      url: "/experiences/yoga",
      details: ["Duration: 60-90 minutes", "Difficulty: All levels", "Best time: Sunrise or sunset"]
    },
    {
      id: "kite-surf",
      title: "Kite Surf",
      description: "Essaouira is world-renowned for its ideal kitesurfing conditions. Take advantage of the consistent winds and learn this exhilarating water sport with our experienced instructors.",
      image: "https://baberrih.ma/media/experiences/kitesurf/1_f.webp",
      url: "/experiences/kite-surf",
      details: ["Duration: 2-3 hours (lessons)", "Difficulty: Beginner to Advanced", "Best time: Afternoon when winds are strongest"]
    }
  ];
  
  // Intersection Observer for scroll animations
  function setupIntersectionObserver() {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };
    
    const overviewObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          overviewVisible = true;
          overviewObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    const experiencesObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          experiencesVisible = true;
          experiencesObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    // Observe elements
    const overviewElement = document.getElementById('overview-section');
    if (overviewElement) overviewObserver.observe(overviewElement);
    
    const experiencesElement = document.getElementById('experiences-section');
    if (experiencesElement) experiencesObserver.observe(experiencesElement);
  }
  
  onMount(() => {
    setupIntersectionObserver();
  });
</script>

<svelte:head>
  <title>Experiences - Baberrih Hotel</title>
  <meta name="description" content="Discover unique experiences at Baberrih Hotel in Essaouira, Morocco, from horseback riding on the beach to yoga sessions and thrilling water sports." />
</svelte:head>

<!-- Hero Section -->
<section class="relative">
  <img 
    src="https://baberrih.ma/media/experiences/1_f.webp" 
    alt="Baberrih Experiences" 
    class="w-full h-[50vh] md:h-[60vh] object-cover"
  />
  <div class="absolute inset-0 flex items-center justify-center bg-primary-950/50">
    <h1 class="font-montserrat font-light text-primary-50 text-3xl md:text-5xl uppercase">Experiences</h1>
  </div>
</section>

<!-- Overview Section -->
<section id="overview-section" class="py-12 px-4">
  <div class="container mx-auto max-w-4xl">
    <div class={`scroll-animation fade ${overviewVisible ? 'visible' : ''}`} style="--delay: 300ms;">
      <h2 class="font-montserrat font-light text-primary-900 text-xl uppercase mb-6">Discover Essaouira</h2>
      
      <p class="font-eb-garamond font-light text-primary-800 text-lg mb-6">
        At Baberrih, we believe that truly experiencing a destination goes beyond comfortable accommodation. That's why we offer a range of carefully curated experiences that allow you to connect with the natural beauty, culture, and spirit of Essaouira.
      </p>
      
      <p class="font-eb-garamond font-light text-primary-800 text-lg mb-6">
        From exhilarating outdoor adventures to peaceful wellness practices, our experiences cater to all interests and abilities. Each activity is led by experienced local guides who share their knowledge and passion for this unique region of Morocco.
      </p>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        <div class="border border-primary-100 p-6">
          <h3 class="font-montserrat font-light text-primary-900 text-base uppercase mb-2">Adventure</h3>
          <p class="font-eb-garamond font-light text-primary-800">
            Explore the diverse landscapes of Essaouira through horseback riding, quad biking, land sailing, and fatbike adventures. Experience the thrill of discovery in this unique coastal environment.
          </p>
        </div>
        
        <div class="border border-primary-100 p-6">
          <h3 class="font-montserrat font-light text-primary-900 text-base uppercase mb-2">Wellness</h3>
          <p class="font-eb-garamond font-light text-primary-800">
            Find balance and rejuvenation through our wellness offerings, including yoga sessions, meditation practices, and traditional hammam experiences that nurture body and mind.
          </p>
        </div>
        
        <div class="border border-primary-100 p-6">
          <h3 class="font-montserrat font-light text-primary-900 text-base uppercase mb-2">Water Sports</h3>
          <p class="font-eb-garamond font-light text-primary-800">
            Take advantage of Essaouira's world-renowned wind conditions for kitesurfing, windsurfing, and other water sports. Lessons are available for beginners, while experienced practitioners can rent equipment.
          </p>
        </div>
        
        <div class="border border-primary-100 p-6">
          <h3 class="font-montserrat font-light text-primary-900 text-base uppercase mb-2">Cultural Immersion</h3>
          <p class="font-eb-garamond font-light text-primary-800">
            Discover the rich cultural heritage of the region through cooking classes, traditional music performances, and visits to local artisan workshops and markets.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Experiences Section -->
<section id="experiences-section" class="py-12 px-4 bg-primary-50">
  <div class="container mx-auto max-w-6xl">
    <div class={`scroll-animation slide-up ${experiencesVisible ? 'visible' : ''}`} style="--delay: 300ms;">
      <h2 class="font-montserrat font-light text-primary-900 text-xl uppercase mb-10 text-center">Our Experiences</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {#each experiences as experience}
          <div class="bg-primary-50 border border-primary-100">
            <div class="aspect-ratio-container" style="--aspect-ratio:16/9">
              <img 
                src={experience.image} 
                alt={experience.title} 
                class="w-full h-full object-cover"
              />
            </div>
            <div class="p-6">
              <h3 class="font-montserrat font-medium text-primary-900 text-xl mb-2">{experience.title}</h3>
              <p class="font-eb-garamond font-light text-primary-800 mb-4">{experience.description}</p>
              
              <div class="space-y-1 mb-6">
                {#each experience.details as detail}
                  <p class="text-xs font-montserrat font-light text-primary-800">
                    {detail}
                  </p>
                {/each}
              </div>
              
              <a href={experience.url} class="button-secondary">
                View Details
              </a>
            </div>
          </div>
        {/each}
      </div>
    </div>
  </div>
</section>

<!-- Booking Section -->
<section class="py-12 px-4">
  <div class="container mx-auto max-w-4xl text-center">
    <h2 class="font-montserrat font-light text-primary-900 text-xl uppercase mb-6">Book Your Experience</h2>
    <p class="font-eb-garamond font-light text-primary-800 text-lg mb-8 max-w-2xl mx-auto">
      Ready to enhance your stay with one of our unique experiences? Contact our concierge team to arrange your booking or to create a custom itinerary tailored to your interests.
    </p>
    <a href="/contact" class="button">
      Contact Us
    </a>
  </div>
</section>
