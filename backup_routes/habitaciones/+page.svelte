<script lang="ts">
  import { goto } from "$app/navigation";
  import RoomList from "$lib/components/cloudbeds/room-list.svelte";
  import AvailabilityCalendar from "$lib/components/cloudbeds/availability-calendar.svelte";

  /**
   * Función para navegar a la página de reservas
   */
  function goToReservations(): void {
    goto("/cloudbeds/reservations");
  }
</script>

<div class="mx-auto p-4 container">
  <h1 class="mb-8 font-bold text-3xl">Nuestras Habitaciones</h1>

  <div class="mb-12">
    <RoomList />
  </div>

  <div class="mb-12">
    <h2 class="mb-4 font-bold text-2xl">Calendario de Disponibilidad</h2>
    <AvailabilityCalendar />
  </div>

  <div class="flex justify-center mb-8">
    <button class="btn btn-primary btn-lg" on:click={goToReservations}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="mr-2 w-6 h-6"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
        <line x1="16" y1="2" x2="16" y2="6"></line>
        <line x1="8" y1="2" x2="8" y2="6"></line>
        <line x1="3" y1="10" x2="21" y2="10"></line>
      </svg>
      Hacer una Reserva
    </button>
  </div>
</div>
