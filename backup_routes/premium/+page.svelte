<script lang="ts">
  import type { PageData } from "./$types";

  // Cambiado a una constante ya que no se utiliza como prop
  // Si se necesita acceder a los datos, usar $page.data en su lugar
</script>

<div class="mx-auto p-8 container">
  <div class="bg-base-100 shadow-xl card">
    <div class="card-body">
      <h1 class="font-bold text-3xl card-title">Contenido Premium</h1>
      <p class="py-4">
        ¡Felicidades! Tienes acceso a este contenido premium porque tienes una
        suscripción activa.
      </p>
      <p class="py-2">
        Este es un ejemplo de cómo proteger una ruta completa utilizando el hook
        <code>requireSubscription</code> en el archivo
        <code>+page.server.ts</code>.
      </p>
      <div class="mt-4 card-actions">
        <a href="/account" class="btn btn-primary">Ver mi cuenta</a>
      </div>
    </div>
  </div>
</div>
