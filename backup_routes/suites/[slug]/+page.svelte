<script lang="ts">
  import { page } from "$app/stores";
  import { onMount } from "svelte";
  import SuiteDisplay from "$lib/components/cloudbeds/SuiteDisplay.svelte";

  // Get the slug from the URL
  const slug = $derived($page.params.slug);
  const suiteSlug = $derived(slug);

  // State
  let siteId = $state("");
  let loading = $state(true);
  let fallbackToStaticData = $state(true); // Default to fallback since we're not implementing the database lookup yet

  // Static suite data (fallback if database is not set up yet)
  const staticSuites = {
    "ocean-view-deluxe": {
      id: "DVM",
      title: "Ocean View Deluxe",
      description:
        "Immerse yourself in the beauty of the Atlantic Ocean from our Ocean View Deluxe suite. This spacious accommodation offers panoramic views of the coastline, allowing you to wake up to the sound of waves and fall asleep under the starry sky.",
      images: [
        "https://baberrih.ma/media/suites/sea_deluxe/1_f.webp",
        "https://baberrih.ma/media/suites/sea_deluxe/2_f.webp",
        "https://baberrih.ma/media/suites/sea_deluxe/3_f.webp",
        "https://baberrih.ma/media/suites/sea_deluxe/4_f.webp",
      ],
      amenities: [
        "King-size bed",
        "Private terrace with ocean view",
        "En-suite bathroom with rain shower",
        "Air conditioning",
        "Free Wi-Fi",
        "Mini-bar",
        "Coffee and tea facilities",
        "Safe",
        "Hairdryer",
        "Organic toiletries",
      ],
      size: "45 m²",
      capacity: "2 adults",
      bookUrl:
        "https://hotels.cloudbeds.com/en/reservas/lmKzDQ#checkin=2025-05-21&checkout=2025-05-28&currency=eur&room_type=DVM",
    },
    "garden-view-deluxe": {
      id: "DVJ",
      title: "Garden View Deluxe",
      description:
        "Our Garden View Deluxe suite offers a serene retreat overlooking the lush gardens of Baberrih. Enjoy the tranquility of nature from your private terrace, surrounded by the scents and colors of our carefully maintained garden.",
      images: [
        "https://baberrih.ma/media/suites/garden_deluxe/1_f.webp",
        "https://baberrih.ma/media/suites/garden_deluxe/2_f.webp",
        "https://baberrih.ma/media/suites/garden_deluxe/3_f.webp",
        "https://baberrih.ma/media/suites/garden_deluxe/4_f.webp",
      ],
      amenities: [
        "King-size bed",
        "Private terrace with garden view",
        "En-suite bathroom with rain shower",
        "Air conditioning",
        "Free Wi-Fi",
        "Mini-bar",
        "Coffee and tea facilities",
        "Safe",
        "Hairdryer",
        "Organic toiletries",
      ],
      size: "45 m²",
      capacity: "2 adults",
      bookUrl:
        "https://hotels.cloudbeds.com/en/reservas/lmKzDQ#checkin=2025-05-21&checkout=2025-05-28&currency=eur&room_type=DVJ",
    },
    "ocean-view-junior": {
      id: "JVM",
      title: "Ocean View Junior",
      description:
        "Experience the beauty of the Atlantic Ocean from our Ocean View Junior suite. This cozy accommodation offers stunning views of the coastline, perfect for a romantic getaway or a peaceful retreat.",
      images: [
        "https://baberrih.ma/media/suites/junior_sea/1_f.webp",
        "https://baberrih.ma/media/suites/junior_sea/2_f.webp",
        "https://baberrih.ma/media/suites/junior_sea/3_f.webp",
        "https://baberrih.ma/media/suites/junior_sea/4_f.webp",
      ],
      amenities: [
        "Queen-size bed",
        "Private balcony with ocean view",
        "En-suite bathroom with shower",
        "Air conditioning",
        "Free Wi-Fi",
        "Mini-bar",
        "Coffee and tea facilities",
        "Safe",
        "Hairdryer",
        "Organic toiletries",
      ],
      size: "35 m²",
      capacity: "2 adults",
      bookUrl:
        "https://hotels.cloudbeds.com/en/reservas/lmKzDQ#checkin=2025-05-21&checkout=2025-05-28&currency=eur&room_type=JVM",
    },
    "garden-view-junior": {
      id: "JVJ",
      title: "Garden View Junior",
      description:
        "Our Garden View Junior suite provides a peaceful retreat with views of our beautiful gardens. Enjoy the tranquility of nature from your private balcony, surrounded by the scents and colors of our carefully maintained garden.",
      images: [
        "https://baberrih.ma/media/suites/junior_garden/1_f.webp",
        "https://baberrih.ma/media/suites/junior_garden/2_f.webp",
        "https://baberrih.ma/media/suites/junior_garden/3_f.webp",
        "https://baberrih.ma/media/suites/junior_garden/4_f.webp",
      ],
      amenities: [
        "Queen-size bed",
        "Private balcony with garden view",
        "En-suite bathroom with shower",
        "Air conditioning",
        "Free Wi-Fi",
        "Mini-bar",
        "Coffee and tea facilities",
        "Safe",
        "Hairdryer",
        "Organic toiletries",
      ],
      size: "35 m²",
      capacity: "2 adults",
      bookUrl:
        "https://hotels.cloudbeds.com/en/reservas/lmKzDQ#checkin=2025-05-21&checkout=2025-05-28&currency=eur&room_type=JVJ",
    },
  };

  // Get the current static suite for fallback
  const staticSuite = $derived(staticSuites[slug as keyof typeof staticSuites]);

  // Simplified loadSiteId function - just sets loading to false
  // In a real implementation, this would query the database
  async function loadSiteId() {
    try {
      // Simulate loading
      await new Promise((resolve) => setTimeout(resolve, 500));

      // For now, we'll always use the fallback data
      fallbackToStaticData = true;

      // In a real implementation, we would set siteId here
      // siteId = "some-site-id";
    } catch (err) {
      console.error("Error in loadSiteId:", err);
      fallbackToStaticData = true;
    } finally {
      loading = false;
    }
  }

  onMount(() => {
    loadSiteId();
  });
</script>

<svelte:head>
  <title>{staticSuite?.title || "Suite"} - Baberrih Hotel</title>
  <meta
    name="description"
    content={staticSuite?.description ||
      "Discover our luxurious suites at Baberrih Hotel in Essaouira, Morocco."}
  />
</svelte:head>

<div class="mx-auto px-4 py-8 max-w-4xl container">
  {#if loading}
    <div class="p-8 text-center">
      <div
        class="inline-block border-4 border-gray-300 border-t-blue-600 rounded-full w-8 h-8 animate-spin"
      ></div>
      <p class="mt-2 text-gray-600">Loading suite information...</p>
    </div>
  {:else if siteId && !fallbackToStaticData}
    <!-- Use the new SuiteDisplay component with database data -->
    <SuiteDisplay {suiteSlug} {siteId} />
  {:else if staticSuite}
    <!-- Fallback to static data if database is not set up -->
    <div class="space-y-8">
      <!-- Suite Header -->
      <div>
        <h1 class="mb-2 font-semibold text-gray-900 text-2xl">
          {staticSuite.title}
        </h1>
      </div>

      <!-- Suite Images -->
      <div class="relative">
        <div class="carousel">
          <img
            src={staticSuite.images[0]}
            alt={`${staticSuite.title}`}
            class="rounded-lg w-full h-[50vh] object-cover"
          />
          <div
            class="right-0 bottom-0 left-0 absolute bg-gradient-to-t from-primary-950/75 to-transparent p-4"
          >
            <h1
              class="font-montserrat font-light text-primary-50 text-2xl md:text-4xl uppercase"
            >
              {staticSuite.title}
            </h1>
          </div>
        </div>
      </div>

      <!-- Suite Description -->
      <div>
        <h2 class="mb-2 font-medium text-gray-900 text-xl">Description</h2>
        <p class="text-gray-700">{staticSuite.description}</p>
      </div>

      <!-- Suite Details -->
      <div class="gap-6 grid grid-cols-1 md:grid-cols-2">
        <div>
          <h2 class="mb-2 font-medium text-gray-900 text-xl">Details</h2>
          <ul class="space-y-2">
            {#if staticSuite.size}
              <li class="flex items-center">
                <span class="w-24 text-gray-600">Size:</span>
                <span class="text-gray-900">{staticSuite.size}</span>
              </li>
            {/if}

            {#if staticSuite.capacity}
              <li class="flex items-center">
                <span class="w-24 text-gray-600">Capacity:</span>
                <span class="text-gray-900">{staticSuite.capacity}</span>
              </li>
            {/if}
          </ul>
        </div>
      </div>

      <!-- Suite Amenities -->
      {#if staticSuite.amenities && staticSuite.amenities.length > 0}
        <div>
          <h2 class="mb-2 font-medium text-gray-900 text-xl">Amenities</h2>
          <div class="gap-2 grid grid-cols-1 md:grid-cols-2">
            {#each staticSuite.amenities as amenity}
              <div class="flex items-center">
                <svg
                  class="mr-2 w-5 h-5 text-blue-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span>{amenity}</span>
              </div>
            {/each}
          </div>
        </div>
      {/if}

      <!-- Booking Button -->
      <div class="flex justify-center mt-8">
        <a
          href={staticSuite.bookUrl}
          class="inline-flex items-center bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-md text-white"
        >
          <svg
            class="mr-2 w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
            ></path>
          </svg>
          Book Now
        </a>
      </div>
    </div>
  {:else}
    <div class="flex justify-center items-center min-h-[50vh]">
      <p class="font-montserrat font-light text-primary-900 text-xl">
        Suite not found
      </p>
    </div>
  {/if}
</div>
