<script lang="ts">
  import { onMount } from "svelte";

  // Animation state
  let overviewVisible = $state(false);
  let suitesVisible = $state(false);

  // Suites data
  const suites = [
    {
      id: "ocean-view-deluxe",
      title: "Ocean View Deluxe",
      description:
        "Immerse yourself in the beauty of the Atlantic Ocean from our Ocean View Deluxe suite. This spacious accommodation offers panoramic views of the coastline.",
      image: "https://baberrih.ma/media/suites/sea_deluxe/1_f.webp",
      url: "/suites/ocean-view-deluxe",
      bookUrl: "/accommodation/reservations",
      features: ["45 m²", "King-size bed", "Ocean view", "Private terrace"],
    },
    {
      id: "garden-view-deluxe",
      title: "Garden View Deluxe",
      description:
        "Our Garden View Deluxe suite offers a serene retreat overlooking the lush gardens of Baberrih. Enjoy the tranquility of nature from your private terrace.",
      image: "https://baberrih.ma/media/suites/garden_deluxe/1_f.webp",
      url: "/suites/garden-view-deluxe",
      bookUrl: "/accommodation/reservations",
      features: ["45 m²", "King-size bed", "Garden view", "Private terrace"],
    },
    {
      id: "ocean-view-junior",
      title: "Ocean View Junior",
      description:
        "Experience the beauty of the Atlantic Ocean from our Ocean View Junior suite. This cozy accommodation offers stunning views of the coastline.",
      image: "https://baberrih.ma/media/suites/junior_sea/1_f.webp",
      url: "/suites/ocean-view-junior",
      bookUrl: "/accommodation/reservations",
      features: ["35 m²", "Queen-size bed", "Ocean view", "Private balcony"],
    },
    {
      id: "garden-view-junior",
      title: "Garden View Junior",
      description:
        "Our Garden View Junior suite provides a peaceful retreat with views of our beautiful gardens. Enjoy the tranquility of nature from your private balcony.",
      image: "https://baberrih.ma/media/suites/junior_garden/1_f.webp",
      url: "/suites/garden-view-junior",
      bookUrl: "/accommodation/reservations",
      features: ["35 m²", "Queen-size bed", "Garden view", "Private balcony"],
    },
  ];

  // Intersection Observer for scroll animations
  function setupIntersectionObserver() {
    const observerOptions = {
      root: null,
      rootMargin: "0px",
      threshold: 0.1,
    };

    const overviewObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          overviewVisible = true;
          overviewObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);

    const suitesObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          suitesVisible = true;
          suitesObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe elements
    const overviewElement = document.getElementById("overview-section");
    if (overviewElement) overviewObserver.observe(overviewElement);

    const suitesElement = document.getElementById("suites-section");
    if (suitesElement) suitesObserver.observe(suitesElement);
  }

  onMount(() => {
    setupIntersectionObserver();
  });
</script>

<svelte:head>
  <title>Accommodation - Baberrih Hotel</title>
  <meta
    name="description"
    content="Discover our luxurious suites at Baberrih Hotel in Essaouira, Morocco, offering ocean and garden views with modern amenities and traditional Moroccan design."
  />
</svelte:head>

<!-- Hero Section -->
<section class="relative">
  <img
    src="https://baberrih.ma/media/accommodation/1_f.webp"
    alt="Baberrih Accommodation"
    class="w-full h-[50vh] md:h-[60vh] object-cover"
  />
  <div
    class="absolute inset-0 flex justify-center items-center bg-primary-950/50"
  >
    <h1
      class="font-montserrat font-light text-primary-50 text-3xl md:text-5xl uppercase"
    >
      Accommodation
    </h1>
  </div>
</section>

<!-- Overview Section -->
<section id="overview-section" class="px-4 py-12">
  <div class="mx-auto max-w-4xl container">
    <div
      class={`scroll-animation fade ${overviewVisible ? "visible" : ""}`}
      style="--delay: 300ms;"
    >
      <h2
        class="mb-6 font-montserrat font-light text-primary-900 text-xl uppercase"
      >
        Our Suites
      </h2>

      <p class="mb-6 font-eb-garamond font-light text-primary-800 text-lg">
        At Baberrih, we offer a selection of beautifully appointed suites
        designed to provide comfort, privacy, and a connection to the natural
        beauty that surrounds us. Each suite features a blend of traditional
        Moroccan design elements and modern amenities to ensure a memorable
        stay.
      </p>

      <p class="mb-6 font-eb-garamond font-light text-primary-800 text-lg">
        Choose between our ocean-facing suites with panoramic views of the
        Atlantic or our garden-view suites overlooking our lush gardens. All
        accommodations include private outdoor spaces, en-suite bathrooms, and
        thoughtful amenities to enhance your experience.
      </p>

      <div class="gap-6 grid grid-cols-1 md:grid-cols-2 mt-8">
        <div class="p-6 border border-primary-100">
          <h3
            class="mb-2 font-montserrat font-light text-primary-900 text-base uppercase"
          >
            Standard Amenities
          </h3>
          <ul class="space-y-2 font-eb-garamond font-light text-primary-800">
            <li>• En-suite bathroom with rain shower</li>
            <li>• Air conditioning</li>
            <li>• Free Wi-Fi</li>
            <li>• Mini-bar</li>
            <li>• Coffee and tea facilities</li>
            <li>• Safe</li>
            <li>• Organic toiletries</li>
          </ul>
        </div>

        <div class="p-6 border border-primary-100">
          <h3
            class="mb-2 font-montserrat font-light text-primary-900 text-base uppercase"
          >
            Services
          </h3>
          <ul class="space-y-2 font-eb-garamond font-light text-primary-800">
            <li>• Daily housekeeping</li>
            <li>• Turndown service</li>
            <li>• Room service</li>
            <li>• Laundry service</li>
            <li>• Concierge assistance</li>
            <li>• Beach towels and accessories</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Suites Section -->
<section id="suites-section" class="bg-primary-50 px-4 py-12">
  <div class="mx-auto max-w-6xl container">
    <div
      class={`scroll-animation slide-up ${suitesVisible ? "visible" : ""}`}
      style="--delay: 300ms;"
    >
      <h2
        class="mb-10 font-montserrat font-light text-primary-900 text-xl text-center uppercase"
      >
        Our Suite Collection
      </h2>

      <div class="gap-8 grid grid-cols-1 md:grid-cols-2">
        {#each suites as suite}
          <div class="bg-primary-50 border border-primary-100">
            <div class="aspect-ratio-container" style="--aspect-ratio:16/9">
              <img
                src={suite.image}
                alt={suite.title}
                class="w-full h-full object-cover"
              />
            </div>
            <div class="p-6">
              <h3
                class="mb-2 font-montserrat font-medium text-primary-900 text-xl"
              >
                {suite.title}
              </h3>
              <p class="mb-4 font-eb-garamond font-light text-primary-800">
                {suite.description}
              </p>

              <div class="flex flex-wrap gap-2 mb-6">
                {#each suite.features as feature}
                  <span
                    class="bg-primary-100 px-3 py-1 font-montserrat font-light text-primary-800 text-xs"
                  >
                    {feature}
                  </span>
                {/each}
              </div>

              <div class="flex flex-wrap gap-3">
                <a href={suite.url} class="button-secondary"> View Details </a>
                <a href={suite.bookUrl} class="button"> Book Now </a>
              </div>
            </div>
          </div>
        {/each}
      </div>
    </div>
  </div>
</section>

<!-- Booking Section -->
<section class="px-4 py-12">
  <div class="mx-auto max-w-4xl text-center container">
    <h2
      class="mb-6 font-montserrat font-light text-primary-900 text-xl uppercase"
    >
      Book Your Stay
    </h2>
    <p
      class="mx-auto mb-8 max-w-2xl font-eb-garamond font-light text-primary-800 text-lg"
    >
      Ready to experience the tranquility and beauty of Baberrih? Book your stay
      directly through our online reservation system for the best rates and
      special offers.
    </p>
    <div class="flex justify-center gap-4">
      <a href="/accommodation/reservations" class="button-secondary">
        Check Availability
      </a>
      <a href="/accommodation/reservations" class="button"> Book Now </a>
    </div>
  </div>
</section>
