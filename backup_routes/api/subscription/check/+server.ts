/**
 * Endpoint para verificar el estado de la suscripción de un usuario
 */
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { checkSubscription } from '$lib/stripe/subscription.server';

// Cache para almacenar los resultados de verificación de suscripciones por sesión
interface SubscriptionCacheEntry {
  hasActiveSubscription: boolean;
  timestamp: number;
}
const sessionSubscriptionCache = new Map<string, SubscriptionCacheEntry>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos en milisegundos

export const GET: RequestHandler = async ({ locals }) => {
  // Verificar si el usuario está autenticado
  if (!locals.session || !locals.user) {
    return json({ hasActiveSubscription: false, error: 'No autenticado' }, { status: 401 });
  }

  const userId = locals.user.id;
  const userEmail = locals.user.email;
  const sessionId = locals.session.access_token;

  // Verificar si hay datos en caché
  const now = Date.now();
  const cachedData = sessionSubscriptionCache.get(sessionId);

  // Si hay datos en caché y no han expirado, usarlos
  if (cachedData && (now - cachedData.timestamp < CACHE_DURATION)) {
    return json({
      hasActiveSubscription: cachedData.hasActiveSubscription,
      error: null,
      fromCache: true
    });
  }

  // Verificar si el usuario tiene una suscripción activa
  const { hasActiveSubscription, error } = await checkSubscription(userId, userEmail);

  // Guardar en caché
  if (!error) {
    sessionSubscriptionCache.set(sessionId, {
      hasActiveSubscription,
      timestamp: now
    });
  }

  return json({ hasActiveSubscription, error, fromCache: false });
};
