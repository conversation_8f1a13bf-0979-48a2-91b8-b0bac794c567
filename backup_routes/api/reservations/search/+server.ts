/**
 * Endpoint para buscar reservas por correo electrónico y código de confirmación
 */
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { findReservationByEmailAndCode } from '$lib/services/reservations';

export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    const { email, confirmationCode } = await request.json();
    
    // Validar datos de entrada
    if (!email || !confirmationCode) {
      return json({ 
        success: false, 
        error: 'Se requieren el correo electrónico y el código de confirmación' 
      }, { status: 400 });
    }
    
    // Buscar reservas
    const { data, error } = await findReservationByEmailAndCode(
      locals.supabase,
      email,
      confirmationCode
    );
    
    if (error) {
      return json({ success: false, error }, { status: 400 });
    }
    
    return json({ success: true, data });
  } catch (error) {
    console.error('Error al buscar reservas:', error);
    return json({ 
      success: false, 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
};
