/**
 * Endpoint para generar y verificar tokens de acceso a reservas
 */
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { 
  generateReservationAccessToken, 
  verifyReservationAccessToken,
  getReservationById
} from '$lib/services/reservations';

// Generar token de acceso
export const POST: RequestHandler = async ({ request, locals }) => {
  try {
    const { reservationId } = await request.json();
    
    // Validar datos de entrada
    if (!reservationId) {
      return json({ 
        success: false, 
        error: 'Se requiere el ID de la reserva' 
      }, { status: 400 });
    }
    
    // Generar token
    const { token, error } = await generateReservationAccessToken(
      locals.supabase,
      reservationId
    );
    
    if (error) {
      return json({ success: false, error }, { status: 400 });
    }
    
    return json({ 
      success: true, 
      token,
      url: `/reservations/view?token=${token}`
    });
  } catch (error) {
    console.error('Error al generar token de acceso:', error);
    return json({ 
      success: false, 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
};

// Verificar token de acceso
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    const token = url.searchParams.get('token');
    
    // Validar datos de entrada
    if (!token) {
      return json({ 
        success: false, 
        error: 'Se requiere el token de acceso' 
      }, { status: 400 });
    }
    
    // Verificar token
    const { reservationId, error } = await verifyReservationAccessToken(
      locals.supabase,
      token
    );
    
    if (error) {
      return json({ success: false, error }, { status: 400 });
    }
    
    // Obtener detalles de la reserva
    const { data, error: reservationError } = await getReservationById(
      locals.supabase,
      reservationId!
    );
    
    if (reservationError) {
      return json({ success: false, error: reservationError }, { status: 400 });
    }
    
    return json({ success: true, data });
  } catch (error) {
    console.error('Error al verificar token de acceso:', error);
    return json({ 
      success: false, 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
};
