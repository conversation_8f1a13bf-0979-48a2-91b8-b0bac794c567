/**
 * Endpoint para obtener productos y precios de Stripe
 */
import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';
import type { StripeProduct, ProductsAndPricesResult } from '$lib/stripe/types.d';

/**
 * Maneja la solicitud GET para obtener productos y precios
 */
export async function GET({ locals }: RequestEvent) {
  try {
    // Obtener productos y precios usando el servicio de Stripe
    const result = await locals.stripe.getProductsAndPrices();

    // Verificar si la operación fue exitosa
    if (!result.success) {
      console.error('Error al obtener productos y precios:', result.error);
      return json({ success: false, error: 'Error al obtener productos y precios' }, { status: 500 });
    }

    // Transformar los productos para incluir solo la información necesaria
    const products: StripeProduct[] = result.products?.map(product => {
      const defaultPrice = product.default_price as any;

      return {
        id: product.id,
        name: product.name,
        description: product.description,
        active: product.active,
        images: product.images,
        metadata: product.metadata,
        price: defaultPrice ? {
          id: defaultPrice.id,
          currency: defaultPrice.currency,
          unit_amount: defaultPrice.unit_amount,
          type: defaultPrice.type,
          product: product.id,
          active: defaultPrice.active,
          recurring: defaultPrice.recurring
        } : undefined
      };
    }) || [];

    // Devolver los productos
    const response: ProductsAndPricesResult = {
      success: true,
      products
    };

    return json(response);
  } catch (error) {
    // Manejar errores
    console.error('Error al obtener productos y precios:', error);
    return json({ success: false, error: 'Error interno del servidor' }, { status: 500 });
  }
} 