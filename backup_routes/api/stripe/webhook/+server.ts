/**
 * Endpoint para manejar webhooks de Stripe
 */
import { json } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';
import { StripeService } from '$lib/stripe.server';
import type Stripe from 'stripe';
import type { RequestEvent } from '@sveltejs/kit';

/**
 * Maneja la solicitud POST para los webhooks de Stripe
 */
export async function POST({ request, locals }: RequestEvent) {
  try {
    // Obtener el cuerpo de la solicitud como texto sin procesar
    const payload = await request.text();

    // Obtener la firma de Stripe del encabezado
    const signature = request.headers.get('stripe-signature');

    // Verificar que la firma existe
    if (!signature) {
      console.error('Falta la firma de Stripe');
      return json({ error: 'Falta la firma de Stripe' }, { status: 400 });
    }

    // Verificar el evento usando el servicio de Stripe
    const result = locals.stripe.verifyWebhookEvent({
      payload,
      signature,
      webhookSecret: env.STRIPE_WEBHOOK_SECRET
    });

    // Verificar si la verificación fue exitosa
    if (!result.success || !result.event) {
      console.error('Error al verificar el webhook:', result.error);
      return json({ error: 'Error al verificar el webhook' }, { status: 400 });
    }

    const event = result.event;

    // Manejar diferentes tipos de eventos
    switch (event.type) {
      case 'checkout.session.completed':
        // Manejar la finalización de una sesión de checkout
        await handleCheckoutSessionCompleted(event, locals);
        break;

      case 'customer.subscription.created':
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        // Manejar eventos de suscripción
        await handleSubscriptionEvent(event, locals);
        break;

      case 'invoice.paid':
      case 'invoice.payment_failed':
        // Manejar eventos de factura
        await handleInvoiceEvent(event, locals);
        break;

      // Añadir más casos según sea necesario

      default:
        console.log(`Evento no manejado: ${event.type}`);
    }

    // Responder con éxito para confirmar la recepción
    return json({ received: true });
  } catch (error) {
    // Manejar errores
    console.error('Error al procesar el webhook:', error);
    return json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

/**
 * Maneja el evento checkout.session.completed
 */
async function handleCheckoutSessionCompleted(event: Stripe.Event, locals: App.Locals) {
  const session = event.data.object as Stripe.Checkout.Session;

  console.log('Sesión de checkout completada:', session.id);

  // Aquí puedes implementar la lógica para:
  // - Actualizar el estado del pedido en tu base de datos
  // - Enviar un correo electrónico de confirmación
  // - Activar una suscripción
  // - etc.
}

/**
 * Maneja eventos relacionados con suscripciones
 */
async function handleSubscriptionEvent(event: Stripe.Event, locals: App.Locals) {
  const subscription = event.data.object as Stripe.Subscription;

  console.log(`Evento de suscripción ${event.type}:`, subscription.id);

  // Aquí puedes implementar la lógica para:
  // - Actualizar el estado de la suscripción en tu base de datos
  // - Enviar notificaciones al usuario
  // - Activar o desactivar funcionalidades
  // - etc.
}

/**
 * Maneja eventos relacionados con facturas
 */
async function handleInvoiceEvent(event: Stripe.Event, locals: App.Locals) {
  const invoice = event.data.object as Stripe.Invoice;

  console.log(`Evento de factura ${event.type}:`, invoice.id);

  // Aquí puedes implementar la lógica para:
  // - Actualizar el estado de la factura en tu base de datos
  // - Enviar notificaciones al usuario
  // - Manejar pagos fallidos
  // - etc.
}