/**
 * Endpoint para obtener información de un cliente de Stripe
 */
import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';

/**
 * Maneja la solicitud GET para obtener información de un cliente
 */
export async function GET({ params, locals }: RequestEvent) {
  try {
    // Verificar si el usuario está autenticado
    if (!locals.session) {
      return json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener el ID del cliente de los parámetros de la ruta
    const customerId = params.id;

    // Verificar que el ID del cliente existe
    if (!customerId) {
      return json({ error: 'ID de cliente no proporcionado' }, { status: 400 });
    }

    // Obtener información del cliente usando el servicio de Stripe
    const result = await locals.stripe.getCustomer(customerId);

    // Verificar si la operación fue exitosa
    if (!result.success) {
      console.error('Error al obtener información del cliente:', result.error);
      return json({ error: 'Error al obtener información del cliente' }, { status: 500 });
    }

    // Transformar el cliente para incluir solo la información necesaria
    const customer = result.customer ? {
      id: result.customer.id,
      email: result.customer.email,
      name: result.customer.name,
      phone: result.customer.phone,
      metadata: result.customer.metadata,
      created: result.customer.created,
      default_source: result.customer.default_source
    } : null;

    // Devolver el cliente
    return json({ customer });
  } catch (error) {
    // Manejar errores
    console.error('Error al obtener información del cliente:', error);
    return json({ error: 'Error interno del servidor' }, { status: 500 });
  }
} 