/**
 * Endpoint para obtener las suscripciones de un cliente de Stripe
 */
import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';
import type { StripeSubscription, CustomerSubscriptionsResult } from '$lib/stripe/types.d';

/**
 * Maneja la solicitud GET para obtener las suscripciones de un cliente
 */
export async function GET({ params, locals }: RequestEvent) {
  try {
    // Verificar si el usuario está autenticado
    if (!locals.session) {
      return json({ success: false, error: 'No autorizado' }, { status: 401 });
    }

    // Obtener el ID del cliente de los parámetros de la ruta
    const customerId = params.id;

    // Verificar que el ID del cliente existe
    if (!customerId) {
      return json({ success: false, error: 'ID de cliente no proporcionado' }, { status: 400 });
    }

    // Obtener las suscripciones del cliente usando el servicio de Stripe
    const result = await locals.stripe.getCustomerSubscriptions(customerId);

    // Verificar si la operación fue exitosa
    if (!result.success) {
      console.error('Error al obtener las suscripciones del cliente:', result.error);
      return json({ success: false, error: 'Error al obtener las suscripciones del cliente' }, { status: 500 });
    }

    // Transformar las suscripciones para incluir solo la información necesaria
    const subscriptions = result.subscriptions?.map(subscription => {
      // Asegurarse de que customer sea un string
      const customerString = typeof subscription.customer === 'string'
        ? subscription.customer
        : subscription.customer.id;

      return {
        id: subscription.id,
        customer: customerString,
        status: subscription.status as StripeSubscription['status'],
        current_period_start: subscription.current_period_start,
        current_period_end: subscription.current_period_end,
        cancel_at_period_end: subscription.cancel_at_period_end,
        items: {
          data: subscription.items.data.map(item => {
            // Asegurarse de que product sea un string
            const productString = typeof item.price.product === 'string'
              ? item.price.product
              : item.price.product.id;

            return {
              id: item.id,
              price: {
                id: item.price.id,
                product: productString,
                currency: item.price.currency,
                unit_amount: item.price.unit_amount || 0,
                type: item.price.type,
                active: item.price.active,
                recurring: item.price.recurring
              }
            };
          })
        },
        default_payment_method: subscription.default_payment_method,
        latest_invoice: subscription.latest_invoice
      };
    }) || [];

    // Devolver las suscripciones
    const response: CustomerSubscriptionsResult = {
      success: true,
      subscriptions: subscriptions as StripeSubscription[]
    };

    return json(response);
  } catch (error) {
    // Manejar errores
    console.error('Error al obtener las suscripciones del cliente:', error);
    return json({ success: false, error: 'Error interno del servidor' }, { status: 500 });
  }
} 