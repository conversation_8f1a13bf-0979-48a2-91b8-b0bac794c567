/**
 * Endpoint para crear una sesión de checkout de Stripe
 */
import { json } from '@sveltejs/kit';
import { createCheckoutSessionSchema } from '$lib/stripe/schemas';
import { z } from 'zod';
import type { CreateCheckoutSessionParams, CheckoutSessionResult } from '$lib/stripe/types.d';
import type { RequestEvent } from '@sveltejs/kit';

/**
 * Maneja la solicitud POST para crear una sesión de checkout
 */
export async function POST({ request, locals }: RequestEvent) {
  try {
    // Verificar si el usuario está autenticado
    if (!locals.session) {
      return json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener el ID del usuario de Supabase
    const userId = locals.session.user.id;
    const userEmail = locals.session.user.email;

    // Obtener los datos de la solicitud
    const body = await request.json();

    // Validar los datos con Zod
    const validatedData = createCheckoutSessionSchema.parse(body) as CreateCheckoutSessionParams;

    // Verificar si el usuario ya tiene un cliente en Stripe
    const customerResult = await locals.stripe.getCustomerByEmail(userEmail);

    let customerId: string;

    if (customerResult.success && customerResult.customer) {
      // Si encontramos un cliente, usamos su ID
      customerId = typeof customerResult.customer === 'string'
        ? customerResult.customer
        : customerResult.customer.id;

      // Actualizamos los metadatos para asegurarnos de que tenga el userId
      await locals.stripe.updateCustomer({
        customerId,
        metadata: { userId }
      });
    } else {
      // Si no encontramos un cliente, creamos uno nuevo
      const createResult = await locals.stripe.createCustomer({
        email: userEmail,
        metadata: { userId }
      });

      if (!createResult.success || !createResult.customer) {
        throw new Error('Error al crear el cliente en Stripe');
      }

      customerId = typeof createResult.customer === 'string'
        ? createResult.customer
        : createResult.customer.id;
    }

    // Crear la sesión de checkout usando el servicio de Stripe
    const result = await locals.stripe.createCheckoutSession({
      priceId: validatedData.priceId,
      customerId,
      successUrl: validatedData.successUrl,
      cancelUrl: validatedData.cancelUrl
    });

    return json(result);
  } catch (error) {
    // Manejar errores de validación de Zod
    if (error instanceof z.ZodError) {
      return json({ error: error.errors }, { status: 400 });
    }

    // Manejar otros errores
    console.error('Error al crear la sesión de checkout:', error);
    return json({ success: false, error: error instanceof Error ? error.message : 'Error interno del servidor' }, { status: 500 });
  }
}