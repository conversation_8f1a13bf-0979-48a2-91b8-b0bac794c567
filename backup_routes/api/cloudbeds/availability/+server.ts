import { json } from '@sveltejs/kit';
import { cloudbedsApiService } from '$lib/cloudbeds/api.server';
import type { RequestHandler } from './$types';
import { z } from 'zod';

export const GET: RequestHandler = async ({ url }) => {
  try {
    // Validar y extraer parámetros
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const roomTypeId = url.searchParams.get('roomTypeId') || undefined;

    // Validar parámetros requeridos
    if (!startDate || !endDate) {
      return json({
        success: false,
        error: {
          code: 'MISSING_PARAMETERS',
          message: 'Se requieren los parámetros startDate y endDate'
        }
      }, { status: 400 });
    }

    // Validar formato de fechas
    const dateSchema = z.string().regex(/^\d{4}-\d{2}-\d{2}$/);
    try {
      dateSchema.parse(startDate);
      dateSchema.parse(endDate);
    } catch (validationError) {
      return json({
        success: false,
        error: {
          code: 'INVALID_DATE_FORMAT',
          message: 'El formato de fecha debe ser YYYY-MM-DD'
        }
      }, { status: 400 });
    }

    const availability = await cloudbedsApiService.getAvailability(startDate, endDate, roomTypeId);
    return json(availability);
  } catch (error) {
    console.error('Error obteniendo disponibilidad:', error);
    return json({
      success: false,
      error: {
        code: 'CLOUDBEDS_API_ERROR',
        message: error instanceof Error ? error.message : 'Error desconocido'
      }
    }, { status: 500 });
  }
}; 