import { json } from '@sveltejs/kit';
import { cloudbedsApiService } from '$lib/cloudbeds/api.server';
import type { RequestEvent } from '@sveltejs/kit';

/**
 * Endpoint para obtener la configuración de Cloudbeds
 * Proporciona información necesaria para que el cliente funcione correctamente
 */
export const GET = async ({ request }: RequestEvent) => {
  try {
    console.log('[API:Config] Solicitando configuración de Cloudbeds');

    // Obtener el Property ID desde el servidor
    let propertyID: string;
    try {
      propertyID = await cloudbedsApiService.fetchPropertyId();
      console.log('[API:Config] PropertyID obtenido:', propertyID);
    } catch (error) {
      console.error('[API:Config] Error al obtener PropertyID:', error);
      // Establecer un valor predeterminado si hay error
      propertyID = '317353'; // Usar el ID que aparece en los logs
      console.log('[API:Config] Usando PropertyID predeterminado:', propertyID);
    }

    // Devolver la configuración
    return json({
      success: true,
      propertyID,
      // Aquí se pueden añadir más valores de configuración si son necesarios
    });
  } catch (error) {
    console.error('[API:Config] Error general al obtener configuración:', error);
    return json({
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Error desconocido al obtener configuración'
      }
    }, { status: 500 });
  }
}; 