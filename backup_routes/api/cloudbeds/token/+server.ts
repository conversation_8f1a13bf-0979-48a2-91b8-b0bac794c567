import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';
import { cloudbedsApiService } from '$lib/cloudbeds/api.server';

/**
 * Guarda el token OAuth en el servicio de Cloudbeds para su uso en llamadas posteriores
 */
export const POST = async ({ request }: RequestEvent) => {
  try {
    const { accessToken, refreshToken, expiresIn, tokenType } = await request.json();

    if (!accessToken || !refreshToken || !expiresIn || !tokenType) {
      return json({
        success: false,
        error: 'MISSING_TOKEN_DATA',
        message: 'Faltan datos del token'
      }, { status: 400 });
    }

    // Establecer el token en el servicio de API
    cloudbedsApiService.setOAuthToken(accessToken, refreshToken, expiresIn, tokenType);

    // Probar el token realizando una consulta básica
    try {
      const propertyId = await cloudbedsApiService.fetchPropertyId();

      return json({
        success: true,
        message: 'Token establecido y verificado correctamente',
        propertyId
      });
    } catch (verificationError) {
      return json({
        success: false,
        error: 'TOKEN_VERIFICATION_FAILED',
        message: verificationError instanceof Error ? verificationError.message : 'Error al verificar el token'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('[API:Token] Error al procesar token:', error);
    return json({
      success: false,
      error: 'TOKEN_PROCESSING_ERROR',
      message: error instanceof Error ? error.message : 'Error desconocido al procesar el token'
    }, { status: 500 });
  }
}; 