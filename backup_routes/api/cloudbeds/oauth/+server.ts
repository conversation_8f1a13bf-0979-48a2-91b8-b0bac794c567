import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';

/**
 * Inicia el flujo de autenticación OAuth 2.0
 * Redirecciona al usuario a la página de autorización de Cloudbeds
 */
export const GET = async ({ url }: RequestEvent) => {
  try {
    const clientId = env.CLOUDBEDS_CLIENT_ID;
    const redirectUri = 'https://localhost';

    if (!clientId) {
      return json({
        success: false,
        error: 'MISSING_CLIENT_ID',
        message: 'CLOUDBEDS_CLIENT_ID no está configurado en variables de entorno'
      }, { status: 500 });
    }

    // Construir la URL de autorización de Cloudbeds
    const authUrl = `https://hotels.cloudbeds.com/api/v1.2/oauth?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code`;

    return json({
      success: true,
      authUrl
    });
  } catch (error) {
    console.error('[API:OAuth] Error al generar URL de autorización:', error);
    return json({
      success: false,
      error: 'AUTH_URL_ERROR',
      message: error instanceof Error ? error.message : 'Error desconocido al generar URL de autorización'
    }, { status: 500 });
  }
};

/**
 * Procesa el código de autorización y lo intercambia por un token de acceso
 */
export const POST = async ({ request }: RequestEvent) => {
  try {
    const { code } = await request.json();

    if (!code) {
      return json({
        success: false,
        error: 'MISSING_CODE',
        message: 'Se requiere el código de autorización'
      }, { status: 400 });
    }

    const clientId = env.CLOUDBEDS_CLIENT_ID;
    const clientSecret = env.CLOUDBEDS_CLIENT_SECRET;
    const redirectUri = 'https://localhost';

    if (!clientId || !clientSecret) {
      return json({
        success: false,
        error: 'MISSING_CREDENTIALS',
        message: 'Faltan credenciales de Cloudbeds en variables de entorno'
      }, { status: 500 });
    }

    // Crear payload para solicitar el token
    const formData = new URLSearchParams();
    formData.append('grant_type', 'authorization_code');
    formData.append('client_id', clientId);
    formData.append('client_secret', clientSecret);
    formData.append('redirect_uri', redirectUri);
    formData.append('code', code);

    // Solicitar el token de acceso
    const response = await fetch('https://api.cloudbeds.com/api/v1.2/access_token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      body: formData
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('[API:OAuth] Error al obtener token:', response.status, errorText);
      return json({
        success: false,
        error: 'TOKEN_REQUEST_FAILED',
        message: `Error al obtener token: ${response.status} ${response.statusText}`,
        details: errorText
      }, { status: response.status });
    }

    const tokenData = await response.json();
    console.log('[API:OAuth] Token obtenido correctamente');

    return json({
      success: true,
      tokenData: {
        accessToken: tokenData.access_token,
        refreshToken: tokenData.refresh_token,
        expiresIn: tokenData.expires_in,
        tokenType: tokenData.token_type
      }
    });
  } catch (error) {
    console.error('[API:OAuth] Error al procesar solicitud de token:', error);
    return json({
      success: false,
      error: 'TOKEN_PROCESSING_ERROR',
      message: error instanceof Error ? error.message : 'Error desconocido al procesar solicitud de token'
    }, { status: 500 });
  }
}; 