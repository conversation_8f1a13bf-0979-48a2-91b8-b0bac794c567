import { json } from '@sveltejs/kit';
import { cloudbedsApiService } from '$lib/cloudbeds/api.server';
import type { RequestEvent } from '@sveltejs/kit';

export const GET = async ({ url }: RequestEvent) => {
  try {
    // Obtener el ID del tipo de habitación
    const roomTypeId = url.searchParams.get('roomTypeId');

    if (!roomTypeId) {
      return json({
        success: false,
        error: {
          code: 'MISSING_PARAMETER',
          message: 'El parámetro roomTypeId es obligatorio'
        }
      }, { status: 400 });
    }

    // Obtener parámetros de la URL
    const includePhotos = url.searchParams.get('includePhotos') === '1';
    const includeAmenities = url.searchParams.get('includeAmenities') === '1';

    // Obtener detalles del tipo de habitación con los parámetros solicitados
    const roomTypeDetails = await cloudbedsApiService.getRoomTypeDetails(
      roomTypeId,
      includePhotos,
      includeAmenities
    );

    return json(roomTypeDetails);
  } catch (error) {
    console.error('Error obteniendo detalles del tipo de habitación:', error);
    return json({
      success: false,
      error: {
        code: 'CLOUDBEDS_API_ERROR',
        message: error instanceof Error ? error.message : 'Error desconocido'
      }
    }, { status: 500 });
  }
}; 