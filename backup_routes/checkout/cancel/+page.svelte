<!--
  Página de cancelación de checkout
  Esta página se muestra cuando el pago ha sido cancelado.
-->
<script lang="ts">
  import { XCircle } from "lucide-svelte";
</script>

<svelte:head>
  <title>Pago cancelado - Mi SaaS</title>
</svelte:head>

<div class="mx-auto px-4 py-16 container">
  <div class="mx-auto max-w-lg text-center">
    <div class="flex justify-center mb-6">
      <XCircle class="w-16 h-16 text-error" />
    </div>

    <h1 class="mb-4 font-bold text-3xl">Pago cancelado</h1>

    <p class="mb-8 text-lg">
      Has cancelado el proceso de pago. No te preocupes, no se ha realizado
      ningún cargo.
    </p>

    <div class="flex justify-center gap-4">
      <a href="/products" class="btn btn-primary">Volver a los productos</a>
      <a href="/" class="btn-outline btn">Volver al inicio</a>
    </div>
  </div>
</div>
