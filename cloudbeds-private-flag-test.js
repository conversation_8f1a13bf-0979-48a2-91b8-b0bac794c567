/**
 * Cloudbeds isPrivate Flag Test
 *
 * Este script está diseñado específicamente para probar cómo la configuración
 * "Private accommodation" vs "Shared dorm room" afecta al flag isPrivate en la API
 * y cómo esto impacta la disponibilidad de las habitaciones.
 */

import fetch from 'node-fetch';
import { config } from 'dotenv';

// Cargar variables de entorno
config();

// Configuración - Usar variables de entorno
const API_KEY = process.env.CLOUDBEDS_API_KEY; // Usar la API key del .env
const PROPERTY_ID = process.env.CLOUDBEDS_PROPERTY_ID; // Usar el property ID del .env

// Función para formatear fechas como YYYY-MM-DD
const formatDate = (date) => {
  return date.toISOString().split('T')[0];
};

/**
 * Prueba el endpoint getRoomTypes para verificar el flag isPrivate
 * Este endpoint muestra todos los tipos de habitación configurados
 */
async function testRoomTypesPrivateFlag() {
  console.log('\n=== Analizando flag isPrivate en tipos de habitación ===');

  const url = new URL('https://api.cloudbeds.com/api/v1.2/getRoomTypes');
  url.searchParams.append('propertyID', PROPERTY_ID);

  console.log(`URL: ${url.toString()}`);

  try {
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'x-api-key': API_KEY
      }
    });

    const data = await response.json();

    if (data.success && data.data && data.data.length > 0) {
      console.log(`\nSe encontraron ${data.data.length} tipos de habitación configurados`);

      // Contar habitaciones privadas vs no privadas
      const privateRooms = data.data.filter(room => room.isPrivate === true);
      const publicRooms = data.data.filter(room => room.isPrivate === false);

      console.log(`\n=== RESUMEN DE CONFIGURACIÓN ===`);
      console.log(`Total de tipos de habitación: ${data.data.length}`);
      console.log(`Habitaciones con isPrivate=true: ${privateRooms.length}`);
      console.log(`Habitaciones con isPrivate=false: ${publicRooms.length}`);

      // Mostrar detalles de cada tipo de habitación
      console.log('\n=== DETALLES DE TIPOS DE HABITACIÓN ===');
      data.data.forEach(room => {
        console.log(`\n- ${room.roomTypeName || 'Sin nombre'} (ID: ${room.roomTypeId || 'N/A'})`);
        console.log(`  Tipo: ${room.roomTypeClass || 'N/A'}`);
        console.log(`  isPrivate: ${room.isPrivate ? 'TRUE' : 'FALSE'}`);
        console.log(`  Ocupación máxima: ${room.maxOccupancy || 'N/A'}`);

        // Analizar si es dorm o private según la API
        if (room.roomTypeClass) {
          if (room.roomTypeClass.toLowerCase().includes('dorm')) {
            console.log(`  Configuración en Cloudbeds: Parece ser "Shared dorm room"`);
          } else {
            console.log(`  Configuración en Cloudbeds: Parece ser "Private accommodation"`);
          }
        }

        // Analizar el impacto en la disponibilidad
        console.log(`  Impacto en disponibilidad: ${room.isPrivate ?
          'NO aparecerá en respuestas de disponibilidad pública' :
          'SÍ aparecerá en respuestas de disponibilidad pública'}`);
      });

      // Conclusión y recomendaciones
      console.log('\n=== CONCLUSIÓN Y RECOMENDACIONES ===');
      if (privateRooms.length > 0 && publicRooms.length === 0) {
        console.log('PROBLEMA DETECTADO: Todos tus tipos de habitación tienen isPrivate=true');
        console.log('Esto explica por qué no se muestran datos de disponibilidad en la API pública.');
        console.log('\nRECOMENDACIÓN:');
        console.log('1. Contacta con soporte de Cloudbeds para confirmar si la configuración es correcta');
        console.log('2. Si necesitas que las habitaciones aparezcan en la API pública, pide que cambien');
        console.log('   la configuración para que isPrivate=false en los tipos de habitación que deseas mostrar');
      } else if (privateRooms.length > 0) {
        console.log('ATENCIÓN: Algunos tipos de habitación tienen isPrivate=true');
        console.log('Estos tipos de habitación no aparecerán en las respuestas de disponibilidad pública.');
        console.log('\nRECOMENDACIÓN:');
        console.log('Verifica si esto es intencional. Si no lo es, contacta con soporte de Cloudbeds.');
      } else {
        console.log('La configuración parece correcta. Todos los tipos de habitación tienen isPrivate=false');
        console.log('Deberían aparecer en las respuestas de disponibilidad pública.');
        console.log('\nSi sigues sin ver datos de disponibilidad, el problema podría ser:');
        console.log('1. No hay tarifas configuradas para las fechas consultadas');
        console.log('2. No hay disponibilidad para las fechas consultadas');
        console.log('3. Problemas con los parámetros de la API o la autenticación');
      }
    } else {
      console.log('Error o no se encontraron tipos de habitación:', data.message || 'Sin mensaje de error');
    }
  } catch (error) {
    console.error('Error al realizar la solicitud:', error.message);
  }
}

/**
 * Prueba el endpoint getAvailableRoomTypes para verificar qué habitaciones aparecen
 */
async function testAvailabilityWithPrivateFlag() {
  console.log('\n=== Probando disponibilidad con relación al flag isPrivate ===');

  // Generar fechas para la prueba (30 días en el futuro)
  const today = new Date();
  const startDate = formatDate(new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000));
  const endDate = formatDate(new Date(today.getTime() + 31 * 24 * 60 * 60 * 1000));

  console.log(`Fechas de prueba: ${startDate} a ${endDate}`);

  const url = new URL('https://api.cloudbeds.com/api/v1.2/getAvailableRoomTypes');
  url.searchParams.append('propertyID', PROPERTY_ID);
  url.searchParams.append('startDate', startDate);
  url.searchParams.append('endDate', endDate);

  console.log(`URL: ${url.toString()}`);

  try {
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'x-api-key': API_KEY
      }
    });

    const data = await response.json();

    if (data.success) {
      if (data.data && data.data.length > 0) {
        console.log(`\nSe encontraron ${data.data.length} tipos de habitación disponibles`);
        console.log('\nHabitaciones que aparecen en la respuesta de disponibilidad:');
        data.data.forEach(room => {
          console.log(`- ${room.roomTypeName || 'Sin nombre'} (ID: ${room.roomTypeId || 'N/A'})`);
          console.log(`  isPrivate: ${room.isPrivate ? 'TRUE' : 'FALSE'}`);
          if (room.dates && room.dates.length > 0) {
            console.log(`  Precio: ${room.dates[0].price || 'N/A'}`);
          }
        });
      } else {
        console.log('\nNo se encontraron habitaciones disponibles');
        console.log('Esto puede deberse a:');
        console.log('1. Todas las habitaciones tienen isPrivate=true');
        console.log('2. No hay tarifas configuradas para estas fechas');
        console.log('3. No hay disponibilidad para estas fechas');
      }
    } else {
      console.log('Error en la respuesta:', data.message || 'Sin mensaje de error');
    }
  } catch (error) {
    console.error('Error al realizar la solicitud:', error.message);
  }
}

/**
 * Función principal para ejecutar todas las pruebas
 */
async function runTests() {
  console.log('=== Iniciando pruebas del flag isPrivate en Cloudbeds ===');
  console.log(`Propiedad ID: ${PROPERTY_ID}`);

  // Probar el flag isPrivate en los tipos de habitación
  await testRoomTypesPrivateFlag();

  // Probar cómo afecta a la disponibilidad
  await testAvailabilityWithPrivateFlag();

  console.log('\n=== Pruebas completadas ===');
}

// Ejecutar todas las pruebas
runTests().catch(error => {
  console.error('Error al ejecutar las pruebas:', error);
});
