/**
 * Cloudbeds Reservation API Test
 * 
 * Este script prueba la creación de reservas en Cloudbeds basado en el ejemplo
 * proporcionado por Manuel A<PERSON>elo en el correo electrónico.
 */

const fetch = require('node-fetch');
const FormData = require('form-data');

// Configuración - Reemplaza con tus credenciales reales
const API_KEY = 'TU_API_KEY'; // Reemplaza con tu API key
const PROPERTY_ID = '317353'; // Tu ID de propiedad según los emails

// Función para formatear fechas como YYYY-MM-DD
const formatDate = (date) => {
  return date.toISOString().split('T')[0];
};

/**
 * Prueba la creación de una reserva usando el ejemplo de Manuel
 */
async function testCreateReservation() {
  console.log('\n=== Probando creación de reserva con el ejemplo de Manuel ===');
  
  // Generar fechas para la reserva (30 días en el futuro)
  const today = new Date();
  const startDate = formatDate(new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000));
  const endDate = formatDate(new Date(today.getTime() + 31 * 24 * 60 * 60 * 1000));
  
  console.log(`Fechas de reserva: ${startDate} a ${endDate}`);
  
  // Crear FormData según el ejemplo de Manuel
  const formData = new FormData();
  formData.append('propertyID', PROPERTY_ID);
  formData.append('startDate', startDate);
  formData.append('endDate', endDate);
  formData.append('guestFirstName', 'John');
  formData.append('guestLastName', 'Doe');
  formData.append('guestCountry', 'US');
  formData.append('guestZip', '1234');
  formData.append('guestEmail', '<EMAIL>');
  formData.append('guestPhone', '4567');
  formData.append('paymentMethod', 'credit');
  formData.append('thirdPartyIdentifier', `test-${Date.now()}`);
  
  // Estos campos necesitan ser completados con IDs reales
  // Reemplaza ROOM_TYPE_ID con un ID real de tipo de habitación
  const ROOM_TYPE_ID = 'ROOM_TYPE_ID'; // Reemplaza con un ID real
  
  formData.append('rooms[0][roomTypeID]', ROOM_TYPE_ID);
  formData.append('rooms[0][quantity]', '1');
  formData.append('adults[0][roomTypeID]', ROOM_TYPE_ID);
  formData.append('adults[0][quantity]', '1');
  formData.append('children[0][roomTypeID]', ROOM_TYPE_ID);
  formData.append('children[0][quantity]', '0');
  
  const url = `https://api.cloudbeds.com/api/v1.2/postReservation`;
  
  console.log(`URL: ${url}`);
  console.log('Enviando solicitud con los siguientes datos:');
  console.log(`- Property ID: ${PROPERTY_ID}`);
  console.log(`- Room Type ID: ${ROOM_TYPE_ID}`);
  console.log(`- Fechas: ${startDate} a ${endDate}`);
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'x-api-key': API_KEY
        // FormData establece automáticamente el Content-Type como multipart/form-data
      },
      body: formData
    });
    
    const data = await response.json();
    
    console.log('Código de respuesta:', response.status);
    
    if (data.success) {
      console.log('¡Éxito! Reserva creada correctamente');
      console.log('ID de reserva:', data.data.reservationID);
      console.log('Detalles de la reserva:', data.data);
    } else {
      console.log('Error al crear la reserva:', data.message || 'Sin mensaje de error');
      
      // Analizar posibles causas del error
      if (data.message && data.message.includes('Invalid Parameter Format')) {
        console.log('\nPosibles causas del error "Invalid Parameter Format":');
        console.log('1. El ID de tipo de habitación no es válido o está vacío');
        console.log('2. El formato de las fechas no es correcto');
        console.log('3. Faltan campos obligatorios');
        console.log('4. El formato de los arrays (rooms, adults, children) no es correcto');
      }
    }
  } catch (error) {
    console.error('Error al realizar la solicitud:', error.message);
  }
}

/**
 * Obtiene los IDs de los tipos de habitación para usar en la creación de reservas
 */
async function getRoomTypeIDs() {
  console.log('\n=== Obteniendo IDs de tipos de habitación ===');
  
  const url = new URL('https://api.cloudbeds.com/api/v1.2/getRoomTypes');
  url.searchParams.append('propertyID', PROPERTY_ID);
  
  try {
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'x-api-key': API_KEY
      }
    });
    
    const data = await response.json();
    
    if (data.success && data.data && data.data.length > 0) {
      console.log('IDs de tipos de habitación disponibles:');
      data.data.forEach(room => {
        console.log(`- ${room.roomTypeName}: ${room.roomTypeId}`);
      });
      
      return data.data.map(room => ({
        id: room.roomTypeId,
        name: room.roomTypeName
      }));
    } else {
      console.log('No se pudieron obtener los tipos de habitación');
      return [];
    }
  } catch (error) {
    console.error('Error al obtener los tipos de habitación:', error.message);
    return [];
  }
}

/**
 * Función principal para ejecutar todas las pruebas
 */
async function runTests() {
  console.log('=== Iniciando pruebas de creación de reservas en Cloudbeds ===');
  console.log(`Propiedad ID: ${PROPERTY_ID}`);
  
  // Obtener IDs de tipos de habitación
  const roomTypes = await getRoomTypeIDs();
  
  if (roomTypes.length > 0) {
    console.log('\nPara probar la creación de reservas, edita este script y reemplaza');
    console.log('ROOM_TYPE_ID con uno de los IDs mostrados arriba.');
  }
  
  // Probar creación de reserva
  // Nota: Esta función está comentada para evitar crear reservas accidentalmente
  // Descomenta la siguiente línea cuando estés listo para probar
  // await testCreateReservation();
  
  console.log('\n=== Pruebas completadas ===');
  console.log('Para probar la creación de reservas, edita el script y descomenta la llamada a testCreateReservation()');
}

// Ejecutar todas las pruebas
runTests().catch(error => {
  console.error('Error al ejecutar las pruebas:', error);
});
