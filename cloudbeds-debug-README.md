# Scripts de Depuración para la API de Cloudbeds

Este conjunto de scripts está diseñado para ayudar a diagnosticar problemas con la integración de la API de Cloudbeds, específicamente el problema de "No availability data returned from Cloudbeds" que estás experimentando.

## Requisitos previos

Antes de ejecutar estos scripts, necesitarás:

1. Node.js instalado en tu sistema
2. Las siguientes dependencias de npm:
   - `node-fetch`
   - `form-data` (solo para el script de reservas)

Puedes instalar estas dependencias con:

```bash
npm install node-fetch form-data
```

## Configuración

Antes de ejecutar cualquier script, debes editar cada archivo y reemplazar:

- `TU_API_KEY` con tu clave de API de Cloudbeds
- Verificar que el `PROPERTY_ID` sea correcto (actualmente configurado como '317353' según tus correos)
- Para el script de reservas, reemplazar `ROOM_TYPE_ID` con un ID real de tipo de habitación

## Scripts disponibles

### 1. cloudbeds-debug.js

Este script realiza pruebas generales de la API de Cloudbeds, incluyendo:
- Obtener todos los tipos de habitación
- Probar la disponibilidad con diferentes rangos de fechas
- Verificar las tarifas configuradas

```bash
node cloudbeds-debug.js
```

### 2. cloudbeds-private-flag-test.js

Este script está específicamente diseñado para probar cómo la configuración "Private accommodation" vs "Shared dorm room" afecta al flag `isPrivate` en la API y cómo esto impacta la disponibilidad de las habitaciones.

```bash
node cloudbeds-private-flag-test.js
```

### 3. cloudbeds-parameter-test.js

Este script prueba diferentes combinaciones de parámetros para identificar cuáles son necesarios para obtener datos de disponibilidad correctamente.

```bash
node cloudbeds-parameter-test.js
```

### 4. cloudbeds-reservation-test.js

Este script prueba la creación de reservas en Cloudbeds basado en el ejemplo proporcionado por Manuel Arbelo en el correo electrónico.

**Nota**: Por defecto, la función de creación de reservas está comentada para evitar crear reservas accidentalmente. Debes editar el script y descomentar la llamada a `testCreateReservation()` cuando estés listo para probar.

```bash
node cloudbeds-reservation-test.js
```

## Interpretación de resultados

Cada script generará resultados detallados en la consola. Presta especial atención a:

1. **Flag isPrivate**: Si todos tus tipos de habitación tienen `isPrivate: true`, esto podría explicar por qué no se muestran datos de disponibilidad.

2. **Parámetros de la API**: Diferentes combinaciones de parámetros pueden producir diferentes resultados. Identifica qué combinación funciona mejor.

3. **Configuración de tarifas**: Verifica si hay tarifas configuradas para las fechas que estás consultando.

4. **Errores específicos**: Analiza los mensajes de error para identificar problemas específicos.

## Próximos pasos

Después de ejecutar estos scripts y analizar los resultados:

1. Si todos tus tipos de habitación tienen `isPrivate: true` y esto no es lo que deseas, contacta con soporte de Cloudbeds para que te ayuden a cambiar esta configuración.

2. Si identificas una combinación de parámetros que funciona, actualiza tus componentes para usar esa combinación.

3. Si no hay tarifas configuradas para las fechas que estás consultando, configúralas en el panel de Cloudbeds.

4. Si sigues teniendo problemas, comparte los resultados de estos scripts con soporte de Cloudbeds para obtener ayuda adicional.

## Contacto

Si tienes alguna pregunta o necesitas ayuda adicional, no dudes en contactarme.

---

Espero que estos scripts te ayuden a resolver el problema. ¡Buena suerte!
