# Etapa de construcción
FROM node:20-alpine AS build

# Instalar una versión específica de pnpm
# Usando una versión más antigua que es más compatible con diferentes formatos de lockfile
RUN npm install -g pnpm@7.33.6

# Establecer directorio de trabajo
WORKDIR /app

# Copiar archivos de configuración
COPY package.json pnpm-lock.yaml .npmrc ./
COPY svelte.config.js vite.config.ts tsconfig.json ./

# Instalar dependencias
# Si sigue fallando, podemos usar --no-frozen-lockfile como alternativa
RUN pnpm install --frozen-lockfile || pnpm install --no-frozen-lockfile

# Copiar el resto de los archivos
COPY . .

# Copiar el archivo .env (asegúrate de que no esté en .dockerignore)
COPY .env ./

# Construir la aplicación
RUN pnpm build

# Etapa de producción
FROM node:20-alpine AS production

# Establecer directorio de trabajo
WORKDIR /app

# Copiar archivos de construcción y dependencias necesarias
COPY --from=build /app/package.json /app/pnpm-lock.yaml ./
COPY --from=build /app/build ./build
COPY --from=build /app/node_modules ./node_modules
COPY --from=build /app/.env ./

# Configurar variables de entorno
ENV NODE_ENV=production
ENV PORT=3000

# Cambiar a usuario node por seguridad
USER node

# Exponer el puerto
EXPOSE 3000

# Comando para ejecutar la aplicación
CMD ["node", "build/index.js"] 