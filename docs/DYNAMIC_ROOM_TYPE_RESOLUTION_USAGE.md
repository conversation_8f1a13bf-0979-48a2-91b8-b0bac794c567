# Dynamic Room Type Resolution System Usage Guide

This guide explains how to use the dynamic room type resolution system in your application.

## Overview

The dynamic room type resolution system eliminates the need for hardcoded room type IDs by implementing intelligent caching, pattern matching, and fallback mechanisms to ensure reliable room type resolution across different properties.

## Key Components

1. **RoomTypeCache**: Manages the caching of room types with intelligent refresh strategies
2. **RoomTypeResolver**: Implements multiple strategies to resolve room type IDs
3. **WebhookHandler**: Handles webhook events to keep the cache up-to-date
4. **CloudbedsApiService**: Integrates with the Cloudbeds API and uses the resolver

## How to Use

### Creating Reservations

When creating a reservation, you no longer need to know the exact room type ID. You can provide either:

1. A room type ID (which will be validated)
2. A room name (which will be resolved to an ID)
3. Both (ID will be used if valid, otherwise name will be used)

```typescript
// Example: Creating a reservation with room name instead of ID
const reservationData = {
  propertyID: 'your-property-id',
  guestData: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+1234567890'
  },
  roomsData: [
    {
      // No need for exact room type ID
      roomTypeID: '', // Can be empty or omitted
      roomName: 'Garden Deluxe', // Will be resolved to the correct ID
      startDate: '2023-06-01',
      endDate: '2023-06-03',
      adults: 2,
      children: 0
    }
  ],
  status: 'confirmed',
  thirdPartyIdentifier: 'test-123',
  sendEmailConfirmation: true
};

// The system will automatically resolve the room type ID
const result = await cloudbedsApiService.createReservationDirect(reservationData);
```

### Resolution Strategies

The system uses the following strategies to resolve room type IDs:

1. **Direct ID Validation**: If a valid room type ID is provided, it will be used
2. **Exact Name Matching**: If the exact room name is found in the cache
3. **Pattern Matching**: If the room name contains patterns like "Garden Deluxe" or "Ocean Junior"
4. **Fuzzy Matching**: For similar but not exact matches
5. **Default Room Type**: As a last resort, the first available room type is used

### Webhook Integration

To keep the room type cache up-to-date, you can set up webhooks to receive notifications when room types change:

1. Register your webhook endpoint with Cloudbeds
2. Configure the webhook to send events to `/api/cloudbeds/webhook`
3. The system will automatically refresh the cache when relevant events are received

## Testing

You can test the dynamic room type resolution system using the provided test script:

```bash
node test/cloudbeds-dynamic-room-type-test.js
```

This script will:
1. Fetch room types from the Cloudbeds API
2. Test various resolution strategies
3. Verify that room type IDs are correctly resolved

## Troubleshooting

### Room Type Resolution Failures

If the system fails to resolve room type IDs:

1. Check that the Cloudbeds API credentials are valid
2. Verify that the property ID is correct
3. Ensure the room types exist in the Cloudbeds account
4. Check the room name patterns for typos or unexpected formats
5. Implement more detailed logging to identify the failure point

### API Rate Limiting

If you're hitting Cloudbeds API rate limits:

1. The cache system should help reduce API calls
2. Increase cache duration if needed
3. Implement exponential backoff for API retries
4. Contact Cloudbeds support to request higher rate limits

## Advanced Usage

### Custom Resolution Strategies

You can extend the `RoomTypeResolver` class to implement custom resolution strategies:

```typescript
class CustomRoomTypeResolver extends RoomTypeResolver {
  async resolveRoomTypeId(roomTypeId, roomName, propertyId) {
    // Try custom strategy first
    const customId = await this.customStrategy(roomName, propertyId);
    if (customId) {
      return customId;
    }
    
    // Fall back to default strategies
    return super.resolveRoomTypeId(roomTypeId, roomName, propertyId);
  }
  
  async customStrategy(roomName, propertyId) {
    // Your custom resolution logic here
  }
}
```

### Multi-Property Support

The system is designed to work with multiple properties:

```typescript
// Get room types for a specific property
const roomTypes = await roomTypeCache.getRoomTypes('property-id-1');

// Resolve room type ID for a specific property
const roomTypeId = await roomTypeResolver.resolveRoomTypeId(
  null, 
  'Ocean Suite', 
  'property-id-1'
);
```

## Conclusion

The dynamic room type resolution system provides a robust and flexible way to work with Cloudbeds room types without hardcoding IDs. This makes your application more maintainable and portable across different Cloudbeds accounts.
