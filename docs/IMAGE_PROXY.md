# Image Proxy Solution for Baberrih Web

This document explains the image proxy solution implemented to solve the persistent issue with images from the baberrih.ma domain in the Baberrih Web application.

## Problem

Images hosted on the baberrih.ma domain were not loading correctly in the application due to:

1. Domain accessibility issues - the domain may not be responding to HTTP requests
2. Potential CORS restrictions
3. Network connectivity problems specific to that domain

## Solution

We've implemented a server-side image proxy that:

1. Fetches images from the baberrih.ma domain on the server-side
2. Serves them to the client from our own domain
3. Implements proper caching for performance
4. Provides fallback mechanisms when images can't be retrieved

This approach solves the issue by:
- Bypassing CORS restrictions
- Handling network connectivity issues
- Providing a consistent solution that works in all environments
- Maintaining the original URLs as the primary source

## Implementation

### 1. Server-Side Image Proxy Endpoint

We've created a server endpoint at `/api/image-proxy` that:
- Takes a URL parameter for the image to proxy
- Optionally takes a fallback URL parameter
- Fetches the image on the server side
- Returns it to the client with appropriate caching headers

```typescript
// src/routes/api/image-proxy/+server.ts
export const GET: RequestHandler = async ({ url, fetch, setHeaders }) => {
  // Get the image URL from the query parameter
  const imageUrl = url.searchParams.get('url');
  const fallbackUrl = url.searchParams.get('fallback');
  
  // Fetch the image and return it to the client
  // ...
}
```

### 2. OptimizedImage Component Integration

The `OptimizedImage` component has been updated to automatically use the proxy for baberrih.ma domain images:

```typescript
// Special handling for baberrih.ma domain which is known to have issues
if (src.includes('baberrih.ma')) {
  // Create a proxied URL that will fetch the image on the server side
  const proxyUrl = createProxiedUrl(src, fallbackSrc || defaultFallback);
  
  // Use the proxied URL instead of the original
  currentSrc = proxyUrl;
}
```

### 3. Branded Fallback Image

We've created a branded fallback image specifically for the Baberrih website:
- Located at `/static/images/baberrih-placeholder.svg`
- Used as the default fallback for all images
- Matches the Baberrih brand aesthetics

## Usage

The solution is transparent to developers - simply use the `OptimizedImage` component as before:

```svelte
<OptimizedImage 
  src="https://baberrih.ma/media/restaurant/1_f.webp" 
  alt="Restaurant Image" 
  aspectRatio="16/9"
/>
```

The component will automatically:
1. Detect that the image is from the baberrih.ma domain
2. Use the image proxy to fetch it on the server side
3. Fall back to the branded placeholder if the image can't be retrieved

## Benefits

1. **Reliability**: Images load consistently across all environments
2. **Performance**: Server-side caching improves load times
3. **Maintainability**: No need to replace all image URLs in the codebase
4. **Transparency**: The solution is transparent to developers and users
5. **Fallback Handling**: Graceful degradation when images can't be retrieved

## Limitations

1. **Server Load**: The proxy increases server load slightly
2. **Cache Management**: Cache invalidation needs to be considered for long-term use
3. **External Dependency**: Still depends on the original domain for fresh content

## Future Improvements

1. **CDN Integration**: Offload proxied images to a CDN for better performance
2. **Image Optimization**: Add server-side image optimization (resizing, compression)
3. **Preloading**: Implement preloading for critical images
4. **Monitoring**: Add monitoring for proxy usage and performance
