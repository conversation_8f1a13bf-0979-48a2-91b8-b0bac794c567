# Integración de Stripe en la Aplicación SaaS

Este documento proporciona una visión general de la integración de Stripe en nuestra aplicación SaaS, incluyendo la configuración, los componentes principales y cómo probar la integración.

## Índice

1. [Visión General](#visión-general)
2. [Configuración](#configuración)
3. [Componentes Principales](#componentes-principales)
4. [Endpoints API](#endpoints-api)
5. [Pruebas](#pruebas)
6. [Recursos Adicionales](#recursos-adicionales)

## Visión General

La integración de Stripe en nuestra aplicación SaaS permite:

- Mostrar productos y precios desde Stripe
- Procesar pagos utilizando Stripe Checkout Embedded
- Gestionar suscripciones de usuarios
- Recibir notificaciones de eventos de Stripe mediante webhooks

La integración utiliza la arquitectura hexagonal para separar la lógica de negocio de las dependencias externas, lo que facilita el mantenimiento y las pruebas.

## Configuración

### Variables de Entorno

La integración de Stripe requiere las siguientes variables de entorno:

```
STRIPE_SECRET_KEY=sk_test_...
PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

### Configuración de Supabase

La integración utiliza la extensión de Stripe en Supabase para acceder a los datos de Stripe directamente desde la base de datos. Para configurar esta extensión, sigue los pasos descritos en [docs/stripe-testing.md](./stripe-testing.md).

## Componentes Principales

### Servicio de Stripe

El servicio de Stripe (`src/lib/stripe/service.ts`) proporciona una interfaz para interactuar con la API de Stripe. Utiliza la arquitectura hexagonal con:

- **Puertos**: Interfaces que definen las operaciones disponibles (`src/lib/stripe/port.ts`)
- **Adaptadores**: Implementaciones concretas de los puertos (`src/lib/stripe/adapter.ts`)
- **Tipos**: Definiciones de tipos para los datos de Stripe (`src/lib/stripe/types.d.ts`)
- **Esquemas**: Validación de datos con Zod (`src/lib/stripe/schemas.ts`)

### Componentes UI

Los componentes UI para la integración de Stripe incluyen:

- **ProductList**: Muestra los productos disponibles (`src/lib/components/stripe/ProductList.svelte`)
- **StripeCheckout**: Implementa Stripe Checkout Embedded (`src/lib/components/stripe/StripeCheckout.svelte`)
- **SubscriptionList**: Muestra las suscripciones del usuario (`src/lib/components/stripe/SubscriptionList.svelte`)

## Endpoints API

La integración incluye los siguientes endpoints API:

- **POST /api/stripe/checkout-sessions**: Crea una sesión de checkout
- **GET /api/stripe/products**: Obtiene los productos disponibles
- **GET /api/stripe/customers/[id]/subscriptions**: Obtiene las suscripciones de un cliente
- **POST /api/stripe/webhook**: Recibe eventos de Stripe

## Pruebas

### Pruebas Manuales

Para realizar pruebas manuales de la integración, sigue las instrucciones en [docs/stripe-testing.md](./stripe-testing.md).

### Pruebas Automatizadas

Para ejecutar pruebas automatizadas de la configuración de Stripe:

```bash
pnpm test:stripe
```

Este comando ejecuta el script `scripts/test-stripe.ts`, que verifica:

1. La conexión con Stripe
2. La conexión con Supabase
3. La disponibilidad de productos en Stripe
4. La disponibilidad de precios en Stripe
5. La capacidad de crear sesiones de checkout

## Recursos Adicionales

- [Documentación de Stripe](https://stripe.com/docs)
- [Documentación de Stripe Checkout](https://stripe.com/docs/checkout)
- [Documentación de Stripe Webhooks](https://stripe.com/docs/webhooks)
- [Documentación de Supabase](https://supabase.com/docs)
- [Documentación de Supabase Stripe Wrapper](https://supabase.com/docs/guides/database/extensions/wrappers/stripe) 