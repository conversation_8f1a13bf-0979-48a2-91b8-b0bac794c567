# Dynamic Cloudbeds Room Type Resolution System

## Table of Contents

1. [Introduction](#introduction)
2. [System Architecture](#system-architecture)
3. [Implementation Guide](#implementation-guide)
   - [Step 1: Room Type Cache Service](#step-1-room-type-cache-service)
   - [Step 2: Room Type Resolution Strategies](#step-2-room-type-resolution-strategies)
   - [Step 3: Webhook Integration](#step-3-webhook-integration)
   - [Step 4: API Integration](#step-4-api-integration)
4. [Testing and Validation](#testing-and-validation)
5. [Troubleshooting](#troubleshooting)
6. [Advanced Optimizations](#advanced-optimizations)
7. [References](#references)

## Introduction

This guide provides a comprehensive implementation approach for a dynamic Cloudbeds room type resolution system. The system eliminates the need for hardcoded room type IDs by implementing intelligent caching, pattern matching, and fallback mechanisms to ensure reliable room type resolution across different properties.

**Key Benefits:**
- Eliminates hardcoded room type IDs
- Works across multiple properties without code changes
- Handles room type changes automatically
- Provides robust fallback mechanisms
- Improves maintainability and reduces technical debt

## System Architecture

The dynamic room type resolution system consists of four main components:

1. **Room Type Cache Service**: Manages the caching of room types with intelligent refresh strategies
2. **Resolution Strategies**: Multiple approaches to resolve room type IDs from names or patterns
3. **Webhook Integration**: Real-time updates when room types change
4. **API Integration**: Integration with Cloudbeds API endpoints

![System Architecture](https://mermaid.ink/img/pako:eNqNkk1PwzAMhv9KlBMgdYceurJNQkgcEBLiNnGwmtQltfJRJe7KNP47aQsTH4LBLrH9-PXrODsUuWYUUbDKVLBW5RO8WV0aWMEWnLUGnHxFp6Gy8g2qrWQHK1BVCc7JCp7BkYON1AZKsOTgHpzMYQVGlqBIwkqWJZAqYVvCGp2sBDBZgJG5dKCkM1CQhILWUJKqYGO0JGj0FrZyC1ZVkqDQFdRkJWxkDmQkbNAZcPIBjFxLB1oW4GQBa_IFkLXQkIQXVJCTBD2Ckw9Qk5HwjBKMzKHQFVi0sBHCQUka1iihoAI2aOCZJGhVQE0SnlBBThJ0Dk4-QE1GwjNKMDKHQldg0cJGCAc1aXhECQUVsEEDTyRBqwJqkvCICnKSoHNw8gFqMhKeUYKRORS6AosWNkI4qEnDGiUUVMAGDTyShJIKqEnCGhXkJEHn4OQD1GQkPKMEI3ModAUWLWyEcFCThkeUUFABGzTwQBI0FVCThAeUkJMEnYOTD1CTkfCMEozModAVWLSwEcJBTRoeUEJBBWzQwD1JKKmAmiTco4ScJOgcnHyAmoyEZ5RgZA6FrsCihY0QDmrScI8SCipggwbuSEJJBdQk4Q4l5CRB5-DkA9RkJDyjBCNzKHQFFi1shHBQk4Y7lFBQARs0cEsSSiqgJgm3KCEnCToHJx-gJiPhGSUYmUOhK7BoYSOEg5o03KKEggrYoIEbklBSATVJuEEJOUnQOTj5ADUZCc8owcgcCl2BRQsbIRzUpOEGJRRUwAYNXJOEkgqoScI1SshJgs7ByQeoyUh4RglG5lDoCixa2AjhoCYN1yihoAI2aOCKJJRUQE0SrlBCThJ0Dk4-QE1GwjNKMDKHQldg0cJGCAc1abhCCQUVsEEDlyShpAJqknCJEnKSoHNw8gFqMhKeUYKRORS6AosWNkI4qEnDJUooqIANGrgAKKmAmiRcoIScJOgcnHyAmoyEZ5RgZA6FrsCihY0QDmrScIESCipggwbOAUoqoCYJ5yghJwk6BycfoCYj4RklGJlDoSuwaGEjhIOaNJyjhIIK2KCBMwBNBdQk4Qwl5CRB5-DkA9RkJDyjBCNzKHQFFi1shHBQk4YzlFBQARs0cApQUgE1SThFCTlJ0Dk4-QA1GQnPKMHIHApdgUULGyEc1KThFCUUVMAGDZwAlFRATRJOUEJOEnQOTj5ATUbCM0owModCV2DRwkYIBzVpOEEJBRWwQQPHACUVUJOEY5SQkwSdg5MPUJORcIwSjMyh0BVYtLARwkFNGo5RQkEFbNDAEUBJBdQk4Qgl5CRB5-DkA9RkJByhBCNzKHQFFi1shHBQk4YjlFBQARs0cAhQUgE1SThECTlJ0Dk4-QA1GQmHKMHIHApdgUULGyEc1KThECUUVMAGDRwAlFRATRIOUEJOEnQOTj5ATUbCAUowModCV2DRwkYIBzVpOEAJBRWwQQP7ACUVUJOE_f9_QedQk5Gwj_8BnYOTD1CTkbCPEozModAVWLSwEcJBTRr2UUJBBWzQwB5ASQXUJGEPJRQ6ByMfoKZ_1JmXrw?type=png)

## Implementation Guide

### Step 1: Room Type Cache Service

First, create a `RoomTypeCache` class to manage the caching of room types:

```typescript
// room-type-cache.ts
import { CloudbedsApiService } from './cloudbeds-api.service';

export class RoomTypeCache {
  private cache: Map<string, {
    roomTypes: any[],
    lastUpdated: number,
    expiresAt: number
  }> = new Map();

  private readonly DEFAULT_CACHE_DURATION_MS = 3600000; // 1 hour

  constructor(
    private readonly apiService: CloudbedsApiService,
    private readonly cacheDuration: number = DEFAULT_CACHE_DURATION_MS
  ) {}

  /**
   * Get room types for a specific property
   * @param propertyId The property ID
   * @param forceRefresh Force a cache refresh
   * @returns Array of room types
   */
  async getRoomTypes(propertyId: string, forceRefresh: boolean = false): Promise<any[]> {
    const cacheKey = `property_${propertyId}`;
    const now = Date.now();

    // Check if we have a valid cache entry
    if (!forceRefresh && this.cache.has(cacheKey)) {
      const cacheEntry = this.cache.get(cacheKey);

      if (cacheEntry && now < cacheEntry.expiresAt) {
        console.log(`Using cached room types for property ${propertyId}`);
        return cacheEntry.roomTypes;
      }
    }

    // Fetch fresh data
    console.log(`Fetching room types for property ${propertyId}`);
    try {
      const response = await this.apiService.getRoomTypes();

      if (response.success && response.data && response.data.length > 0) {
        // Update cache
        this.cache.set(cacheKey, {
          roomTypes: response.data,
          lastUpdated: now,
          expiresAt: now + this.cacheDuration
        });

        return response.data;
      } else {
        console.warn(`No room types found for property ${propertyId}`);
        return [];
      }
    } catch (error) {
      console.error(`Error fetching room types for property ${propertyId}:`, error);

      // Return cached data if available, even if expired
      if (this.cache.has(cacheKey)) {
        const cacheEntry = this.cache.get(cacheKey);
        console.warn(`Using expired cache for property ${propertyId} due to API error`);
        return cacheEntry.roomTypes;
      }

      return [];
    }
  }

  /**
   * Build a mapping of room names to room type IDs
   * @param propertyId The property ID
   * @returns Map of room names to room type IDs
   */
  async buildRoomTypeMapping(propertyId: string): Promise<Map<string, string>> {
    const roomTypes = await this.getRoomTypes(propertyId);
    const mapping = new Map<string, string>();

    for (const roomType of roomTypes) {
      const id = roomType.roomTypeID || roomType.id;
      const name = roomType.roomTypeName || roomType.name || '';
      const nameShort = roomType.roomTypeNameShort || '';

      if (id && name) {
        // Store lowercase for case-insensitive matching
        mapping.set(name.toLowerCase(), id);

        if (nameShort) {
          mapping.set(nameShort.toLowerCase(), id);
        }
      }
    }

    return mapping;
  }

  /**
   * Handle webhook events to refresh cache when needed
   * @param event The webhook event
   * @param propertyId The property ID
   */
  async handleWebhookEvent(event: string, propertyId: string): Promise<void> {
    // Events that should trigger a cache refresh
    const refreshEvents = [
      'reservation/accommodation_type_changed',
      'night_audit/completed'
    ];

    if (refreshEvents.includes(event)) {
      console.log(`Refreshing room type cache for property ${propertyId} due to event: ${event}`);
      await this.getRoomTypes(propertyId, true);
    }
  }

  /**
   * Clear the cache for a specific property
   * @param propertyId The property ID
   */
  clearCache(propertyId: string): void {
    const cacheKey = `property_${propertyId}`;
    this.cache.delete(cacheKey);
  }

  /**
   * Clear the entire cache
   */
  clearAllCaches(): void {
    this.cache.clear();
  }
}
```

### Step 2: Room Type Resolution Strategies

Next, implement the room type resolution strategies:

```typescript
// room-type-resolver.ts
import { RoomTypeCache } from './room-type-cache';

export class RoomTypeResolver {
  constructor(private readonly roomTypeCache: RoomTypeCache) {}

  /**
   * Validate a room type ID
   * @param roomTypeId The room type ID to validate
   * @param propertyId The property ID
   * @returns True if valid, false otherwise
   */
  async validateRoomTypeId(roomTypeId: string, propertyId: string): Promise<boolean> {
    if (!roomTypeId) return false;

    const roomTypes = await this.roomTypeCache.getRoomTypes(propertyId);

    return roomTypes.some(roomType =>
      (roomType.roomTypeID === roomTypeId) || (roomType.id === roomTypeId)
    );
  }

  /**
   * Find a room type ID by name
   * @param roomName The room name
   * @param propertyId The property ID
   * @returns The room type ID if found, undefined otherwise
   */
  async findRoomTypeIdByName(roomName: string, propertyId: string): Promise<string | undefined> {
    if (!roomName) return undefined;

    const mapping = await this.roomTypeCache.buildRoomTypeMapping(propertyId);
    const normalizedName = roomName.toLowerCase();

    // Direct match
    if (mapping.has(normalizedName)) {
      return mapping.get(normalizedName);
    }

    // Try to match patterns
    for (const [mappedName, id] of mapping.entries()) {
      if (normalizedName.includes(mappedName) || mappedName.includes(normalizedName)) {
        return id;
      }
    }

    // Try pattern matching
    return this.findRoomTypeByPattern(roomName, propertyId);
  }

  /**
   * Find a room type by pattern matching
   * @param roomName The room name
   * @param propertyId The property ID
   * @returns The room type ID if found, undefined otherwise
   */
  async findRoomTypeByPattern(roomName: string, propertyId: string): Promise<string | undefined> {
    if (!roomName) return undefined;

    const roomTypes = await this.roomTypeCache.getRoomTypes(propertyId);
    const normalizedName = roomName.toLowerCase();

    // Location patterns
    const locationPatterns = ['garden', 'ocean', 'mountain', 'pool', 'beach', 'sea', 'lake'];
    const roomTypePatterns = ['deluxe', 'junior', 'suite', 'standard', 'superior', 'executive', 'premium'];

    // Extract patterns from room name
    const foundPatterns: Record<string, string> = {};

    // Check for location patterns
    for (const pattern of locationPatterns) {
      if (normalizedName.includes(pattern)) {
        foundPatterns['location'] = pattern;
        break;
      }
    }

    // Check for room type patterns
    for (const pattern of roomTypePatterns) {
      if (normalizedName.includes(pattern)) {
        foundPatterns['roomType'] = pattern;
        break;
      }
    }

    // If we found both location and room type, search for a match
    if (foundPatterns.location && foundPatterns.roomType) {
      const patternToMatch = `${foundPatterns.location} ${foundPatterns.roomType}`;

      const matchedRoomType = roomTypes.find(rt => {
        const rtName = (rt.roomTypeName || rt.name || '').toLowerCase();
        return rtName.includes(patternToMatch);
      });

      if (matchedRoomType) {
        return matchedRoomType.roomTypeID || matchedRoomType.id;
      }
    }

    return undefined;
  }

  /**
   * Get the default room type ID for a property
   * @param propertyId The property ID
   * @returns The default room type ID if available, undefined otherwise
   */
  async getDefaultRoomTypeId(propertyId: string): Promise<string | undefined> {
    const roomTypes = await this.roomTypeCache.getRoomTypes(propertyId);

    if (roomTypes.length > 0) {
      const firstRoom = roomTypes[0];
      return firstRoom.roomTypeID || firstRoom.id;
    }

    return undefined;
  }

  /**
   * Resolve a room type ID using multiple strategies
   * @param roomTypeId Optional room type ID to validate
   * @param roomName Optional room name to use for resolution
   * @param propertyId The property ID
   * @returns A valid room type ID or undefined if not found
   */
  async resolveRoomTypeId(
    roomTypeId: string | undefined,
    roomName: string | undefined,
    propertyId: string
  ): Promise<string | undefined> {
    console.log(`Resolving room type ID: ${roomTypeId}, name: ${roomName}, property: ${propertyId}`);

    // Strategy 1: Validate the provided room type ID
    if (roomTypeId) {
      const isValid = await this.validateRoomTypeId(roomTypeId, propertyId);
      if (isValid) {
        console.log(`Room type ID ${roomTypeId} is valid`);
        return roomTypeId;
      }
    }

    // Strategy 2: Find by room name
    if (roomName) {
      const idByName = await this.findRoomTypeIdByName(roomName, propertyId);
      if (idByName) {
        console.log(`Found room type ID ${idByName} by name "${roomName}"`);
        return idByName;
      }

      // Strategy 3: Find by pattern
      const idByPattern = await this.findRoomTypeByPattern(roomName, propertyId);
      if (idByPattern) {
        console.log(`Found room type ID ${idByPattern} by pattern matching "${roomName}"`);
        return idByPattern;
      }
    }

    // Strategy 4: Use default room type
    const defaultId = await this.getDefaultRoomTypeId(propertyId);
    if (defaultId) {
      console.log(`Using default room type ID ${defaultId}`);
      return defaultId;
    }

    console.warn(`Could not resolve room type ID for property ${propertyId}`);
    return undefined;
  }
}
```

### Step 3: Webhook Integration

Implement webhook handlers to keep the room type cache up-to-date:

```typescript
// webhook-handler.ts
import { RoomTypeCache } from './room-type-cache';

export class WebhookHandler {
  constructor(private readonly roomTypeCache: RoomTypeCache) {}

  /**
   * Handle a Cloudbeds webhook event
   * @param event The webhook event data
   * @returns A response indicating success or failure
   */
  async handleWebhookEvent(event: any): Promise<{ success: boolean, message: string }> {
    try {
      // Extract event information
      const eventType = event.event;
      const propertyId = event.propertyId || event.propertyID || event.propertyID_str;

      if (!eventType || !propertyId) {
        return {
          success: false,
          message: 'Invalid webhook event: missing event type or property ID'
        };
      }

      console.log(`Received webhook event: ${eventType} for property ${propertyId}`);

      // Handle the event
      await this.roomTypeCache.handleWebhookEvent(eventType, propertyId);

      return {
        success: true,
        message: `Successfully processed webhook event: ${eventType}`
      };
    } catch (error) {
      console.error('Error handling webhook event:', error);
      return {
        success: false,
        message: `Error handling webhook event: ${error.message}`
      };
    }
  }

  /**
   * Register webhook subscriptions with Cloudbeds
   * @param apiService The Cloudbeds API service
   * @param endpointUrl Your webhook endpoint URL
   * @param propertyId The property ID
   * @returns A response indicating success or failure
   */
  async registerWebhooks(
    apiService: any,
    endpointUrl: string,
    propertyId: string
  ): Promise<{ success: boolean, message: string }> {
    try {
      // Events to subscribe to
      const events = [
        { object: 'reservation', action: 'accommodation_type_changed' },
        { object: 'reservation', action: 'accommodation_changed' },
        { object: 'night_audit', action: 'completed' }
      ];

      // Register each webhook
      for (const event of events) {
        await apiService.postWebhook({
          endpointUrl,
          object: event.object,
          action: event.action,
          propertyID: propertyId
        });

        console.log(`Registered webhook for ${event.object}/${event.action}`);
      }

      return {
        success: true,
        message: 'Successfully registered webhooks'
      };
    } catch (error) {
      console.error('Error registering webhooks:', error);
      return {
        success: false,
        message: `Error registering webhooks: ${error.message}`
      };
    }
  }
}
```

### Step 4: API Integration

Now, integrate the room type resolution system with your Cloudbeds API service:

```typescript
// cloudbeds-api.service.ts
import { RoomTypeResolver } from './room-type-resolver';

export class CloudbedsApiService {
  constructor(
    private readonly roomTypeResolver: RoomTypeResolver,
    private readonly apiKey: string,
    private readonly baseUrl: string = 'https://api.cloudbeds.com/api/v1.2'
  ) {}

  /**
   * Get room types from Cloudbeds API
   * @param includePhotos Whether to include photos
   * @param includeAmenities Whether to include amenities
   * @returns Room types response
   */
  async getRoomTypes(includePhotos: boolean = false, includeAmenities: boolean = false): Promise<any> {
    const params: Record<string, string | number> = {};

    if (includePhotos) {
      params.includePhotos = '1';
    }

    if (includeAmenities) {
      params.includeAmenities = '1';
    }

    return this.makeApiRequest('/getRoomTypes', 'GET', params);
  }

  /**
   * Create a reservation with dynamic room type resolution
   * @param reservationData The reservation data
   * @returns Reservation creation response
   */
  async createReservation(reservationData: any): Promise<any> {
    // Ensure we have valid room type IDs
    if (reservationData.roomsData && Array.isArray(reservationData.roomsData)) {
      for (let i = 0; i < reservationData.roomsData.length; i++) {
        const room = reservationData.roomsData[i];

        // Resolve room type ID
        const resolvedRoomTypeId = await this.roomTypeResolver.resolveRoomTypeId(
          room.roomTypeID,
          room.roomName,
          reservationData.propertyID
        );

        if (!resolvedRoomTypeId) {
          throw new Error(`Could not resolve room type ID for room ${i + 1}`);
        }

        // Update room type ID
        reservationData.roomsData[i] = {
          ...room,
          roomTypeID: resolvedRoomTypeId
        };
      }
    }

    return this.makeApiRequest('/postReservation', 'POST', reservationData);
  }

  /**
   * Make an API request to Cloudbeds
   * @param endpoint The API endpoint
   * @param method The HTTP method
   * @param params The request parameters
   * @returns API response
   */
  private async makeApiRequest(
    endpoint: string,
    method: 'GET' | 'POST' = 'GET',
    params: Record<string, any> = {}
  ): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;

    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Accept': 'application/json',
      'Content-Type': method === 'GET'
        ? 'application/x-www-form-urlencoded'
        : 'application/json'
    };

    try {
      let requestUrl = url;
      let body: string | FormData | null = null;

      if (method === 'GET') {
        // Add query parameters
        const queryParams = new URLSearchParams();
        for (const [key, value] of Object.entries(params)) {
          queryParams.append(key, String(value));
        }

        requestUrl = `${url}?${queryParams.toString()}`;
      } else {
        // For POST requests, use FormData for reservation creation
        if (endpoint === '/postReservation') {
          body = this.createFormDataForReservation(params);
        } else {
          body = JSON.stringify(params);
        }
      }

      const response = await fetch(requestUrl, {
        method,
        headers,
        body
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(`API error: ${data.message || response.statusText}`);
      }

      return data;
    } catch (error) {
      console.error(`Error making API request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Create FormData for reservation creation
   * @param reservationData The reservation data
   * @returns FormData object
   */
  private createFormDataForReservation(reservationData: any): FormData {
    const formData = new FormData();

    // Add property ID
    formData.append('propertyID', reservationData.propertyID);

    // Add guest data
    if (reservationData.guestData) {
      formData.append('guestFirstName', reservationData.guestData.firstName);
      formData.append('guestLastName', reservationData.guestData.lastName);
      formData.append('guestEmail', reservationData.guestData.email);

      if (reservationData.guestData.phone) {
        formData.append('guestPhone', reservationData.guestData.phone);
      }

      if (reservationData.guestData.country) {
        formData.append('guestCountry', reservationData.guestData.country);
      }

      if (reservationData.guestData.postalCode) {
        formData.append('guestZip', reservationData.guestData.postalCode);
      }
    }

    // Add room data
    if (reservationData.roomsData && reservationData.roomsData.length > 0) {
      const room = reservationData.roomsData[0];

      formData.append('rooms[0][roomTypeID]', room.roomTypeID);
      formData.append('rooms[0][quantity]', '1');

      formData.append('adults[0][roomTypeID]', room.roomTypeID);
      formData.append('adults[0][quantity]', room.adults.toString());

      formData.append('children[0][roomTypeID]', room.roomTypeID);
      formData.append('children[0][quantity]', (room.children || 0).toString());

      // Add dates
      formData.append('startDate', room.startDate);
      formData.append('endDate', room.endDate);
    }

    // Add source ID
    formData.append('sourceID', 's-1-1'); // As recommended by Cloudbeds

    // Add third-party identifier
    formData.append('thirdPartyIdentifier', reservationData.thirdPartyIdentifier || `web-${Date.now()}`);

    // Add status
    formData.append('status', reservationData.status || 'confirmed');

    // Add payment method
    formData.append('paymentMethod', 'credit');

    return formData;
  }
}
```

## Testing and Validation

To ensure your dynamic room type resolution system works correctly, implement comprehensive testing:

### Unit Tests

Create unit tests for each component of the system:

```typescript
// room-type-resolver.test.ts
import { RoomTypeResolver } from './room-type-resolver';
import { RoomTypeCache } from './room-type-cache';

describe('RoomTypeResolver', () => {
  let resolver: RoomTypeResolver;
  let cacheMock: jest.Mocked<RoomTypeCache>;

  const mockRoomTypes = [
    { roomTypeID: '123', roomTypeName: 'Garden Deluxe', maxGuests: 2 },
    { roomTypeID: '456', roomTypeName: 'Ocean Junior Suite', maxGuests: 4 },
    { roomTypeID: '789', roomTypeName: 'Mountain View', maxGuests: 2 }
  ];

  beforeEach(() => {
    cacheMock = {
      getRoomTypes: jest.fn().mockResolvedValue(mockRoomTypes),
      buildRoomTypeMapping: jest.fn().mockResolvedValue(new Map([
        ['garden deluxe', '123'],
        ['ocean junior suite', '456'],
        ['mountain view', '789']
      ])),
      handleWebhookEvent: jest.fn(),
      clearCache: jest.fn(),
      clearAllCaches: jest.fn()
    } as any;

    resolver = new RoomTypeResolver(cacheMock);
  });

  test('validateRoomTypeId should return true for valid ID', async () => {
    const result = await resolver.validateRoomTypeId('123', 'property1');
    expect(result).toBe(true);
    expect(cacheMock.getRoomTypes).toHaveBeenCalledWith('property1');
  });

  test('validateRoomTypeId should return false for invalid ID', async () => {
    const result = await resolver.validateRoomTypeId('999', 'property1');
    expect(result).toBe(false);
  });

  test('findRoomTypeIdByName should find exact match', async () => {
    const result = await resolver.findRoomTypeIdByName('Garden Deluxe', 'property1');
    expect(result).toBe('123');
    expect(cacheMock.buildRoomTypeMapping).toHaveBeenCalledWith('property1');
  });

  test('findRoomTypeIdByName should find partial match', async () => {
    const result = await resolver.findRoomTypeIdByName('Ocean Junior', 'property1');
    expect(result).toBe('456');
  });

  test('resolveRoomTypeId should use valid ID if provided', async () => {
    jest.spyOn(resolver, 'validateRoomTypeId').mockResolvedValue(true);

    const result = await resolver.resolveRoomTypeId('123', 'Some Room', 'property1');
    expect(result).toBe('123');
  });

  test('resolveRoomTypeId should fall back to name resolution if ID is invalid', async () => {
    jest.spyOn(resolver, 'validateRoomTypeId').mockResolvedValue(false);
    jest.spyOn(resolver, 'findRoomTypeIdByName').mockResolvedValue('456');

    const result = await resolver.resolveRoomTypeId('999', 'Ocean Junior', 'property1');
    expect(result).toBe('456');
  });

  test('resolveRoomTypeId should use default ID as last resort', async () => {
    jest.spyOn(resolver, 'validateRoomTypeId').mockResolvedValue(false);
    jest.spyOn(resolver, 'findRoomTypeIdByName').mockResolvedValue(undefined);
    jest.spyOn(resolver, 'getDefaultRoomTypeId').mockResolvedValue('123');

    const result = await resolver.resolveRoomTypeId('999', 'Unknown Room', 'property1');
    expect(result).toBe('123');
  });
});
```

### Integration Tests

Create integration tests to verify the system works with the Cloudbeds API:

```typescript
// cloudbeds-integration.test.ts
import { CloudbedsApiService } from './cloudbeds-api.service';
import { RoomTypeResolver } from './room-type-resolver';
import { RoomTypeCache } from './room-type-cache';

describe('Cloudbeds Integration', () => {
  let apiService: CloudbedsApiService;
  let resolver: RoomTypeResolver;
  let cache: RoomTypeCache;

  // Use a test API key or mock the API responses
  const testApiKey = process.env.TEST_API_KEY || 'test-api-key';

  beforeEach(() => {
    // For integration tests, you might want to mock the fetch calls
    // or use a test environment with a real API key
    global.fetch = jest.fn().mockImplementation((url, options) => {
      if (url.includes('/getRoomTypes')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: [
              { roomTypeID: '123', roomTypeName: 'Garden Deluxe', maxGuests: 2 },
              { roomTypeID: '456', roomTypeName: 'Ocean Junior Suite', maxGuests: 4 }
            ]
          })
        });
      }

      if (url.includes('/postReservation')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            reservationID: '12345',
            confirmationCode: 'ABC123'
          })
        });
      }

      return Promise.reject(new Error(`Unhandled URL: ${url}`));
    }) as any;

    cache = new RoomTypeCache(null as any);
    resolver = new RoomTypeResolver(cache);
    apiService = new CloudbedsApiService(resolver, testApiKey);

    // Replace the cache's API service with our mocked one
    (cache as any).apiService = apiService;
  });

  test('should fetch room types', async () => {
    const result = await apiService.getRoomTypes();

    expect(result).toEqual({
      success: true,
      data: [
        { roomTypeID: '123', roomTypeName: 'Garden Deluxe', maxGuests: 2 },
        { roomTypeID: '456', roomTypeName: 'Ocean Junior Suite', maxGuests: 4 }
      ]
    });

    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining('/getRoomTypes'),
      expect.objectContaining({
        method: 'GET',
        headers: expect.objectContaining({
          'Authorization': `Bearer ${testApiKey}`
        })
      })
    );
  });

  test('should create reservation with resolved room type ID', async () => {
    // Mock the resolver to return a specific ID
    jest.spyOn(resolver, 'resolveRoomTypeId').mockResolvedValue('123');

    const reservationData = {
      propertyID: 'property1',
      guestData: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      },
      roomsData: [
        {
          roomTypeID: 'unknown',
          roomName: 'Garden Room',
          startDate: '2023-01-01',
          endDate: '2023-01-03',
          adults: 2,
          children: 0
        }
      ],
      status: 'confirmed',
      thirdPartyIdentifier: 'test-123'
    };

    const result = await apiService.createReservation(reservationData);

    expect(result).toEqual({
      success: true,
      reservationID: '12345',
      confirmationCode: 'ABC123'
    });

    expect(resolver.resolveRoomTypeId).toHaveBeenCalledWith(
      'unknown',
      'Garden Room',
      'property1'
    );

    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining('/postReservation'),
      expect.objectContaining({
        method: 'POST',
        body: expect.any(FormData)
      })
    );
  });
});
```

### Manual Testing

For manual testing, create a test script that exercises the room type resolution system:

```typescript
// test-room-type-resolution.ts
import { CloudbedsApiService } from './cloudbeds-api.service';
import { RoomTypeResolver } from './room-type-resolver';
import { RoomTypeCache } from './room-type-cache';

async function testRoomTypeResolution() {
  // Initialize the components
  const apiService = new CloudbedsApiService(
    null as any, // Will be set after initialization
    process.env.CLOUDBEDS_API_KEY || '',
    'https://api.cloudbeds.com/api/v1.2'
  );

  const cache = new RoomTypeCache(apiService);
  const resolver = new RoomTypeResolver(cache);

  // Set the resolver in the API service
  (apiService as any).roomTypeResolver = resolver;

  // Test property ID
  const propertyId = process.env.TEST_PROPERTY_ID || '';

  console.log('Fetching room types...');
  const roomTypesResponse = await apiService.getRoomTypes();

  if (!roomTypesResponse.success) {
    console.error('Failed to fetch room types:', roomTypesResponse.message);
    return;
  }

  console.log(`Found ${roomTypesResponse.data.length} room types:`);
  for (const roomType of roomTypesResponse.data) {
    console.log(`- ${roomType.roomTypeName} (ID: ${roomType.roomTypeID})`);
  }

  // Test room type resolution
  const testCases = [
    { id: roomTypesResponse.data[0].roomTypeID, name: null },
    { id: 'invalid-id', name: roomTypesResponse.data[0].roomTypeName },
    { id: null, name: 'Garden Deluxe' },
    { id: null, name: 'Ocean Junior' },
    { id: null, name: 'Unknown Room' }
  ];

  console.log('\nTesting room type resolution:');
  for (const testCase of testCases) {
    console.log(`\nTest case: ID=${testCase.id}, Name=${testCase.name}`);

    try {
      const resolvedId = await resolver.resolveRoomTypeId(
        testCase.id,
        testCase.name,
        propertyId
      );

      console.log(`Resolved ID: ${resolvedId}`);

      if (resolvedId) {
        // Verify the resolved ID
        const isValid = await resolver.validateRoomTypeId(resolvedId, propertyId);
        console.log(`Is valid: ${isValid}`);
      }
    } catch (error) {
      console.error('Error resolving room type ID:', error);
    }
  }
}

// Run the test
testRoomTypeResolution().catch(console.error);
```

## Troubleshooting

Here are common issues and their solutions:

### Room Type Resolution Failures

**Issue**: The system fails to resolve room type IDs.

**Solutions**:
1. Check that the Cloudbeds API credentials are valid
2. Verify that the property ID is correct
3. Ensure the room types exist in the Cloudbeds account
4. Check the room name patterns for typos or unexpected formats
5. Implement more detailed logging to identify the failure point

### API Rate Limiting

**Issue**: Hitting Cloudbeds API rate limits.

**Solutions**:
1. Increase cache duration to reduce API calls
2. Implement exponential backoff for API retries
3. Batch API requests where possible
4. Contact Cloudbeds support to request higher rate limits

### Webhook Integration Issues

**Issue**: Webhooks not triggering cache updates.

**Solutions**:
1. Verify webhook registration with Cloudbeds
2. Check webhook endpoint accessibility from the internet
3. Implement webhook logging to track received events
4. Test webhook handling with simulated events

### Cache Consistency Issues

**Issue**: Cache becomes stale or inconsistent.

**Solutions**:
1. Implement periodic cache refresh (e.g., daily)
2. Add cache validation checks
3. Implement a cache invalidation strategy
4. Add monitoring for cache hit/miss rates

## Advanced Optimizations

### Fuzzy Matching

For more advanced name matching, implement fuzzy string matching:

```typescript
/**
 * Perform fuzzy matching on room names
 * @param roomName The room name to match
 * @param roomTypes The room types to match against
 * @returns The best matching room type ID or undefined
 */
function fuzzyMatchRoomType(roomName: string, roomTypes: any[]): string | undefined {
  if (!roomName || !roomTypes.length) return undefined;

  // Simple Levenshtein distance implementation
  function levenshteinDistance(a: string, b: string): number {
    const matrix: number[][] = [];

    // Initialize matrix
    for (let i = 0; i <= b.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= a.length; j++) {
      matrix[0][j] = j;
    }

    // Fill matrix
    for (let i = 1; i <= b.length; i++) {
      for (let j = 1; j <= a.length; j++) {
        if (b.charAt(i-1) === a.charAt(j-1)) {
          matrix[i][j] = matrix[i-1][j-1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i-1][j-1] + 1, // substitution
            matrix[i][j-1] + 1,   // insertion
            matrix[i-1][j] + 1    // deletion
          );
        }
      }
    }

    return matrix[b.length][a.length];
  }

  // Find the room type with the smallest Levenshtein distance
  let bestMatch = null;
  let bestDistance = Infinity;

  for (const roomType of roomTypes) {
    const rtName = roomType.roomTypeName || roomType.name || '';
    const distance = levenshteinDistance(
      roomName.toLowerCase(),
      rtName.toLowerCase()
    );

    // Normalize by the length of the longer string
    const normalizedDistance = distance / Math.max(roomName.length, rtName.length);

    if (normalizedDistance < bestDistance) {
      bestDistance = normalizedDistance;
      bestMatch = roomType;
    }
  }

  // Only return if the match is reasonably close (threshold of 0.3)
  if (bestDistance < 0.3) {
    return bestMatch.roomTypeID || bestMatch.id;
  }

  return undefined;
}
```

### Multi-Property Support

For applications that work with multiple Cloudbeds properties, implement property-specific caching:

```typescript
/**
 * Get a cache key for a property
 * @param propertyId The property ID
 * @param suffix Optional suffix for the cache key
 * @returns The cache key
 */
function getCacheKey(propertyId: string, suffix: string = ''): string {
  return `property_${propertyId}${suffix ? `_${suffix}` : ''}`;
}

/**
 * Clear cache for all properties
 * @param properties List of property IDs
 */
async function clearCacheForAllProperties(properties: string[]): Promise<void> {
  for (const propertyId of properties) {
    await clearCacheForProperty(propertyId);
  }
}
```

### Performance Monitoring

Implement performance monitoring to track resolution times and success rates:

```typescript
/**
 * Track room type resolution performance
 * @param startTime The start time of the resolution
 * @param success Whether the resolution was successful
 * @param strategy The resolution strategy used
 */
function trackResolutionPerformance(
  startTime: number,
  success: boolean,
  strategy: string
): void {
  const duration = Date.now() - startTime;

  console.log(`Room type resolution: ${success ? 'SUCCESS' : 'FAILURE'}, Strategy: ${strategy}, Duration: ${duration}ms`);

  // Here you could send metrics to your monitoring system
  // e.g., Prometheus, CloudWatch, etc.
}
```

## References

- [Cloudbeds API Documentation](https://hotels.cloudbeds.com/api/v1.2/docs/)
- [Cloudbeds Webhooks Documentation](https://developers.cloudbeds.com/docs/webhooks-1)
- [Cloudbeds getRoomTypes Endpoint](https://developers.cloudbeds.com/reference/get_getroomtypes)
- [Cloudbeds postReservation Endpoint](https://developers.cloudbeds.com/reference/post_postreservation)
- [Cloudbeds postWebhook Endpoint](https://developers.cloudbeds.com/reference/post_postwebhook)