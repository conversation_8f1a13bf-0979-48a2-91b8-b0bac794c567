# Image Loading in Baberrih Web

This document explains how image loading is handled in the Baberrih Web project, including how to deal with potential issues with external image sources.

## Components

### OptimizedImage

The `OptimizedImage` component is the recommended way to display images in the Baberrih Web project. It provides:

- Support for both local and remote images
- Automatic fallback handling when images fail to load
- Responsive image loading with srcset for supported CDNs
- Lazy loading for better performance
- Consistent aspect ratio handling

```svelte
<OptimizedImage 
  src="https://baberrih.ma/media/restaurant/1_f.webp" 
  alt="Restaurant Image" 
  fallbackSrc="/images/placeholder.svg"
  aspectRatio="16/9"
/>
```

### SimpleImage (Legacy)

The `SimpleImage` component is maintained for backward compatibility and now uses `OptimizedImage` under the hood.

## Known Issues with External Image Sources

### baberrih.ma Domain

Images from the `baberrih.ma` domain may not be accessible in certain environments. This could be due to:

1. Domain availability issues
2. CORS restrictions
3. Network connectivity problems

### Handling External Image Sources

When using images from external sources like `baberrih.ma`, always:

1. **Provide a fallback image**:
   ```svelte
   <OptimizedImage 
     src="https://baberrih.ma/media/restaurant/1_f.webp" 
     alt="Restaurant Image" 
     fallbackSrc="/images/placeholder.svg"
   />
   ```

2. **Consider hosting critical images locally** to avoid dependency on external sources

3. **Use the default fallback** provided by the OptimizedImage component as a last resort

## Best Practices

### 1. Always Use OptimizedImage

Use the `OptimizedImage` component for all images in the project:

```svelte
<OptimizedImage 
  src="/path/to/image.jpg" 
  alt="Description" 
/>
```

### 2. Always Provide Alt Text

For accessibility, always provide descriptive alt text:

```svelte
<OptimizedImage 
  src="/path/to/image.jpg" 
  alt="A person using the Baberrih hotel facilities" 
/>
```

### 3. Specify Aspect Ratios

To prevent layout shifts, always specify an aspect ratio:

```svelte
<OptimizedImage 
  src="/path/to/image.jpg" 
  alt="Description" 
  aspectRatio="16/9"
/>
```

### 4. Provide Fallbacks for External Images

Always provide fallbacks for external images:

```svelte
<OptimizedImage 
  src="https://external-domain.com/image.jpg" 
  alt="Description" 
  fallbackSrc="/images/placeholder.svg"
/>
```

### 5. Prioritize Above-the-Fold Images

For images that appear above the fold:

```svelte
<OptimizedImage 
  src="/path/to/hero-image.jpg" 
  alt="Hero image" 
  loading="eager"
  fetchpriority="high"
/>
```

## Debugging Image Loading Issues

### Using the ImageErrorDebug Component

For debugging image loading issues, you can use the `ImageErrorDebug` component:

```svelte
<script>
  import ImageErrorDebug from "$lib/components/debug/ImageErrorDebug.svelte";
</script>

<!-- Add this at the bottom of your page -->
<ImageErrorDebug position="bottom-right" showOnLoad={true} />
```

This will display a debug panel showing:
- Total number of image errors
- Breakdown by local vs. remote images
- Errors by domain
- Details of the most recent error

### Console Logging

The `OptimizedImage` component logs detailed information to the console when images fail to load:

1. Open your browser's developer tools (F12)
2. Go to the Console tab
3. Look for messages starting with "OptimizedImage:"

### Network Tab

You can also check the Network tab in your browser's developer tools to see if image requests are failing:

1. Open your browser's developer tools (F12)
2. Go to the Network tab
3. Filter by "Img" to see only image requests
4. Look for failed requests (red)

## Migrating from SimpleImage to OptimizedImage

If you're updating existing code that uses `SimpleImage`, you can either:

1. **Keep using SimpleImage** (which now uses OptimizedImage under the hood)
2. **Replace with OptimizedImage** for more control:

```svelte
<!-- Before -->
<SimpleImage 
  src="/path/to/image.jpg" 
  alt="Description" 
/>

<!-- After -->
<OptimizedImage 
  src="/path/to/image.jpg" 
  alt="Description" 
  aspectRatio="16/9"
  fallbackSrc="/images/placeholder.svg"
/>
```

## Conclusion

By following these guidelines, you can ensure that images in the Baberrih Web project load reliably and provide a good user experience, even when external image sources are unavailable.
