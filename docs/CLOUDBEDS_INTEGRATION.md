# Cloudbeds Integration Guide

This document provides information about the Cloudbeds integration in the Baberrih website, including how to configure room types for public booking and troubleshoot common issues.

## Current Status

- **Admin Section**: The Cloudbeds integration is working correctly in the admin section. API calls are successful and returning proper data.
- **Commercial Section**: The Cloudbeds integration in the commercial section is experiencing issues with empty data arrays in API responses.

## Root Cause Analysis

After thorough investigation, we've identified the root cause of the issue:

1. **Room Type Configuration**: The room types in Cloudbeds are marked as `isPrivate: true`, which means they are not available for public booking. This is why the availability and pricing data is empty in the commercial section.

2. **API Response**: The API is returning a successful response (`success: true`) but with empty data arrays (`data: []`), which is the expected behavior for private room types.

3. **Error Handling**: The components have proper error handling that displays appropriate messages when data is empty.

## How to Fix the Issue

We've implemented two solutions to address this issue:

### Solution 1: Update Room Type Configuration in Cloudbeds (Recommended)

The primary issue is that the room types are marked as private in Cloudbeds. This needs to be fixed in the Cloudbeds dashboard:

1. Run the helper script to identify private room types:
   ```
   node scripts/update-cloudbeds-room-types.js
   ```

2. Follow the instructions provided by the script:
   - Log in to the Cloudbeds dashboard
   - Navigate to the Property Settings or Room Types section
   - Find the room types (Garden Deluxe, Ocean Deluxe, etc.)
   - Change the "isPrivate" setting from "true" to "false"
   - Save the changes

3. Verify Room Type Visibility:
   - Go to the Cloudbeds dashboard
   - Navigate to the Booking Engine section
   - Preview the booking engine
   - Check if the room types are visible and available for booking

4. Test the Integration:
   - Run the test script: `node test/cloudbeds-availability-test.js`
   - Check if the API returns data for the room types
   - Verify that the availability and pricing components display the correct data

### Solution 2: Code Changes (Already Implemented)

We've implemented code changes to handle private room types properly:

1. **New API Endpoints**:
   - Created `/api/cloudbeds/public-room-types` endpoint that filters out private room types
   - Created `/api/cloudbeds/public-availability` endpoint that only shows availability for public room types

2. **Updated Components**:
   - Modified `PriceDisplay.svelte` and `AvailabilityIndicator.svelte` to use different endpoints for admin and commercial sections
   - Admin section continues to use the original endpoints to see all room types
   - Commercial section uses the new public endpoints to only show public room types

3. **Improved Error Handling**:
   - Added more user-friendly messages when room types are not available for public booking
   - Enhanced logging to help diagnose issues

## User Experience Improvements

We've made several improvements to enhance the user experience:

1. **User-Friendly Messages**:
   - Components now display more user-friendly messages that encourage visitors to contact the hotel directly for pricing and availability
   - Messages are more informative and provide clear next steps for users

2. **Contact Page Redirection**:
   - The "Quick Book" button redirects to the contact page when room types are not available for public booking
   - This ensures users can still inquire about rooms even if online booking is not available

3. **Intelligent Endpoint Selection**:
   - Components automatically detect whether they're being used in the admin or commercial section
   - Admin section uses endpoints that show all room types (including private ones)
   - Commercial section uses endpoints that only show public room types

## Debugging Tools

We've added several debugging tools to help diagnose Cloudbeds integration issues:

1. **Test Script**: The `test/cloudbeds-availability-test.js` script can be used to test the Cloudbeds API directly and verify if room types are configured correctly.

2. **Enhanced Logging**: The components and API endpoints now include enhanced logging that provides more detailed information about API calls and responses.

## API Response Analysis

The test script revealed the following information about the room types:

```json
{
  "roomTypeID": "653496",
  "propertyID": "317353",
  "roomTypeName": "Garden Deluxe",
  "roomTypeNameShort": "GD",
  "roomTypeDescription": "Test",
  "isPrivate": true,
  "maxGuests": 5,
  "adultsIncluded": 1,
  "childrenIncluded": 0,
  "roomTypePhotos": [],
  "roomTypeFeatures": [],
  "roomsAvailable": 0,
  "roomTypeUnits": 5
}
```

The key issue is the `"isPrivate": true` setting, which needs to be changed to `"isPrivate": false` for the room type to be available for public booking.

## Common Issues and Solutions

### Empty Data Arrays in API Response

**Issue**: The API returns `"success": true` but with empty data arrays (`"data": []`).

**Solution**: Check if the room types are marked as private in Cloudbeds. If they are, change the setting to make them public.

### Room Types Not Visible in Booking Engine

**Issue**: Room types are not visible in the Cloudbeds booking engine.

**Solution**: Check the room type configuration in Cloudbeds and ensure they are marked as public and available for booking.

### API Credentials Issues

**Issue**: API calls fail with authentication errors.

**Solution**: Verify that the API credentials (API key, client ID, client secret) are correct and have the necessary permissions.

## Contact Cloudbeds Support

If you continue to experience issues with the Cloudbeds integration, contact Cloudbeds support for assistance:

- Email: <EMAIL>
- Phone: +****************
- Website: https://www.cloudbeds.com/contact-us/

## Additional Resources

- [Cloudbeds API Documentation](https://api.cloudbeds.com/docs/)
- [Cloudbeds Knowledge Base](https://support.cloudbeds.com/hc/en-us)
- [Cloudbeds Developer Portal](https://developers.cloudbeds.com/)
