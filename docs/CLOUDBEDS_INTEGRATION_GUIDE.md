# Cloudbeds Integration Guide

This document provides detailed information about the Cloudbeds integration in our application, including the dynamic room type resolution system, source IDs, and other important configuration details.

## Dynamic Room Type Resolution

Our integration uses a dynamic room type resolution system that automatically fetches and maps room types from the Cloudbeds API. This approach eliminates the need for hardcoded room type IDs, making the integration portable across different Cloudbeds accounts.

### How It Works

1. **Initialization**: When the CloudbedsApiService is initialized, it automatically fetches room types from the Cloudbeds API and caches them.

2. **Mapping**: The system builds a mapping of room names to room type IDs, including variations and patterns like "Garden Deluxe" or "Ocean Junior".

3. **Resolution**: When creating a reservation, the system validates the room type ID and attempts to resolve it using:
   - Direct ID validation against cached room types
   - Name-based lookup using the room name
   - Pattern matching for partial names
   - Fallback to the first available room type if no match is found

4. **Caching**: Room types are cached for 1 hour to reduce API calls, with automatic refresh when needed.

### Benefits

- **Portability**: Works with any Cloudbeds account without code changes
- **Resilience**: Handles missing or invalid room type IDs gracefully
- **Flexibility**: Supports multiple lookup methods (ID, name, pattern)
- **Performance**: Reduces API calls through intelligent caching

### Previously Used Room Type IDs

For reference, these room type IDs were previously hardcoded for Baberrih Hotel (Property ID: 317353):

| Room Type ID | Room Name       | Description                                |
|--------------|----------------|--------------------------------------------|
| 653496       | Garden Deluxe  | Garden view deluxe room                    |
| 653497       | Ocean Deluxe   | Ocean view deluxe room                     |
| 653498       | Garden Junior  | Garden view junior suite                   |
| 653499       | Ocean Junior   | Ocean view junior suite                    |

**Note**: With the new dynamic system, these IDs no longer need to be hardcoded in the application.

## Source IDs

The following source IDs have been verified to work correctly:

| Source ID | Description                | Usage                                  |
|-----------|----------------------------|----------------------------------------|
| s-1-1     | Website/booking engine     | Recommended for direct website bookings |
| s-2-1     | Website (alternative)      | Alternative for website bookings        |
| s-5-1     | Default Corporate Client   | For corporate bookings                  |

**Recommended Source ID:** `s-1-1` (Website/booking engine) as confirmed by Manuel Arbelo from Cloudbeds.

## API Request Format

When creating reservations, always use:

1. **FormData** instead of URLSearchParams
2. **Bearer token authentication**
3. **Dynamic room type resolution** (no hardcoded IDs)
4. **Source ID** `s-1-1` for website bookings

Example request format:

```javascript
const formData = new FormData();

// Property information
formData.append('propertyID', propertyId);
formData.append('startDate', startDate);
formData.append('endDate', endDate);

// Guest information
formData.append('guestFirstName', 'Test');
formData.append('guestLastName', 'User');
formData.append('guestCountry', 'US');
formData.append('guestZip', '1234');
formData.append('guestEmail', '<EMAIL>');
formData.append('guestPhone', '4567');

// Payment information
formData.append('paymentMethod', 'credit');

// Room information with dynamically resolved room type ID
// The roomTypeID is validated and resolved by CloudbedsApiService.validateRoomTypeId()
const roomTypeID = await cloudbedsApiService.validateRoomTypeId(roomTypeId, roomName);
formData.append('rooms[0][roomTypeID]', roomTypeID);
formData.append('rooms[0][quantity]', '1');

// Guest counts
formData.append('adults[0][roomTypeID]', roomTypeID);
formData.append('adults[0][quantity]', '1');
formData.append('children[0][roomTypeID]', roomTypeID);
formData.append('children[0][quantity]', '0');

// Source ID
formData.append('sourceID', 's-1-1');

// Other parameters
formData.append('thirdPartyIdentifier', `test-${Date.now()}`);
```

### Room Type Resolution Process

When validating a room type ID, the system follows this process:

1. Check if the provided ID exists in the cached room types
2. If not found, try to find a room type by name
3. If still not found, use pattern matching to find a similar room
4. As a last resort, return the first available room type

### Error Handling

The system includes robust error handling:

1. Graceful degradation when room types can't be fetched
2. Fallback mechanisms when room type IDs can't be resolved
3. Detailed logging for troubleshooting
4. Automatic retry mechanisms for transient errors

## isPrivate Flag

The `isPrivate` flag in Cloudbeds is used to distinguish between private rooms and dorm rooms:

- `isPrivate: true` - Standard hotel rooms (private accommodation)
- `isPrivate: false` - Dorm beds (shared accommodation)

All our room types are correctly configured as `isPrivate: true` since they are standard hotel rooms. This is the expected behavior and does not affect the ability to create reservations.

## Troubleshooting

### "No rate found" Error

If you encounter a "No rate found" error when creating reservations:

1. Verify that rates are configured in the Cloudbeds dashboard for the room types
2. Check that the dates are in the future
3. Ensure all required fields are properly populated
4. Verify that the source ID is 's-1-1'
5. Check the room type resolution logs to see which room type ID was used

### Room Type Resolution Issues

If the system is having trouble resolving room type IDs:

1. Check the logs for any errors during room type fetching or mapping
2. Verify that the room names in your system match or contain patterns from Cloudbeds
3. Try providing both roomTypeID and roomName to improve matching
4. If necessary, manually refresh the room types cache by restarting the service

### API Authentication

Always use Bearer token authentication:

```javascript
headers['Authorization'] = `Bearer ${accessToken}`;
```

## Testing

Use the test scripts in the `test` directory to verify the integration:

- `test/cloudbeds-fixed-test.js` - Comprehensive test of room types and reservation creation
- `test/cloudbeds-source-id-test.js` - Test of source ID handling
- `test/cloudbeds-private-flag-verification.js` - Verification of isPrivate flag behavior

## References

- [Cloudbeds API Documentation](https://hotels.cloudbeds.com/api/docs/)
- [Manuel Arbelo's Email (May 21, 2025)](docs/CLOUDBEDS_RESPONSE_TO_MANUEL.md)
