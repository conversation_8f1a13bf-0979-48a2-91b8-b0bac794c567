# Guía de Pruebas para la Integración de Stripe

Este documento proporciona instrucciones detalladas para probar la integración de Stripe en nuestra aplicación SaaS.

## Índice

1. [Configuración del Entorno de Prueba](#configuración-del-entorno-de-prueba)
2. [Prueba de la Configuración de Stripe en Supabase](#prueba-de-la-configuración-de-stripe-en-supabase)
3. [Prueba de la Carga de Productos](#prueba-de-la-carga-de-productos)
4. [Prueba del Proceso de Pago](#prueba-del-proceso-de-pago)
5. [Prueba de la Gestión de Suscripciones](#prueba-de-la-gestión-de-suscripciones)
6. [Prueba de Webhooks](#prueba-de-webhooks)
7. [Pruebas Automatizadas](#pruebas-automatizadas)
8. [Solución de Problemas Comunes](#solución-de-problemas-comunes)

## Configuración del Entorno de Prueba

### Requisitos Previos

- Cuenta de Stripe en modo de prueba
- Claves API de Stripe (pública y secreta) para el modo de prueba
- Entorno de desarrollo local configurado

### Configuración de Variables de Entorno

Asegúrate de que las siguientes variables de entorno estén configuradas en tu archivo `.env`:

```
STRIPE_SECRET_KEY=sk_test_...
PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

### Datos de Prueba

Stripe proporciona varios tipos de datos de prueba para simular diferentes escenarios:

#### Tarjetas de Prueba

Puedes usar estas tarjetas de prueba para simular diferentes escenarios de pago:

- **Pago exitoso**: `4242 4242 4242 4242`
- **Pago fallido**: `4000 0000 0000 0002`
- **Autenticación requerida**: `4000 0025 0000 3155`
- **Fondos insuficientes**: `4000 0000 0000 9995`
- **Tarjeta robada**: `4000 0000 0000 9979`
- **Tarjeta expirada**: `4000 0000 0000 0069`

Para cualquier tarjeta de prueba, puedes usar:
- Cualquier fecha de caducidad futura
- Cualquier código CVC de 3 dígitos
- Cualquier código postal válido

#### Otros Datos de Prueba

- **Correo electrónico**: Puedes usar cualquier correo electrónico con formato válido
- **Nombre**: Puedes usar cualquier nombre
- **Dirección**: Puedes usar cualquier dirección válida

Para más datos de prueba, consulta la [documentación de Stripe](https://stripe.com/docs/testing).

## Prueba de la Configuración de Stripe en Supabase

### Verificar la Creación de Tablas Extranjeras

1. Accede a la consola SQL de Supabase.
2. Ejecuta la siguiente consulta para verificar que las tablas extranjeras se han creado correctamente:

```sql
SELECT * FROM information_schema.foreign_tables
WHERE foreign_table_schema = 'stripe';
```

Deberías ver una lista de tablas extranjeras, incluyendo `customers`, `products`, `prices`, `subscriptions`, etc.

### Consultar Datos de Prueba

1. Ejecuta las siguientes consultas para verificar que puedes acceder a los datos de Stripe:

```sql
-- Consultar productos
SELECT * FROM stripe.products LIMIT 5;

-- Consultar precios
SELECT * FROM stripe.prices LIMIT 5;

-- Consultar clientes
SELECT * FROM stripe.customers LIMIT 5;
```

## Prueba de la Carga de Productos

### Crear Productos de Prueba en Stripe

1. Accede al [panel de control de Stripe](https://dashboard.stripe.com/test/products).
2. Crea al menos 3 productos con diferentes precios y opciones de suscripción.
3. Asegúrate de incluir imágenes y descripciones detalladas.

### Verificar la Visualización de Productos

1. Inicia la aplicación en modo de desarrollo:

```bash
pnpm dev
```

2. Navega a la página de productos (`/products`).
3. Verifica que los productos creados en Stripe se muestran correctamente.
4. Comprueba que las imágenes, descripciones, precios y opciones de suscripción se muestran correctamente.

## Prueba del Proceso de Pago

### Iniciar el Proceso de Pago

1. En la página de productos, selecciona un producto y haz clic en el botón "Comprar ahora".
2. Verifica que se muestra el formulario de pago de Stripe Checkout Embedded.

### Completar el Proceso de Pago

1. Introduce los siguientes datos de prueba:
   - Correo electrónico: `<EMAIL>`
   - Número de tarjeta: `4242 4242 4242 4242`
   - Fecha de caducidad: Cualquier fecha futura
   - CVC: Cualquier número de 3 dígitos
   - Nombre: Cualquier nombre
   - País: España
   - Código postal: Cualquier código postal válido

2. Haz clic en "Pagar".
3. Verifica que se muestra la página de éxito (`/checkout/success`).
4. Comprueba que se ha creado correctamente la suscripción en Stripe.

### Probar Escenarios de Error

1. Repite el proceso de pago, pero esta vez utiliza la tarjeta `4000 0000 0000 0002` para simular un pago fallido.
2. Verifica que se muestra un mensaje de error adecuado.

## Prueba de la Gestión de Suscripciones

### Verificar Suscripciones Activas

1. Inicia sesión con un usuario que tenga suscripciones activas.
2. Navega a la página de suscripciones (`/account/subscriptions`).
3. Verifica que las suscripciones activas se muestran correctamente.
4. Comprueba que la información de la suscripción (precio, período, estado) es correcta.

### Cancelar una Suscripción

1. En la página de suscripciones, selecciona una suscripción activa y haz clic en "Cancelar".
2. Confirma la cancelación.
3. Verifica que la suscripción se marca como "Se cancelará al final del período actual".
4. Comprueba que la suscripción se ha actualizado correctamente en Stripe.

## Prueba de Webhooks

Los webhooks son esenciales para recibir notificaciones de eventos de Stripe, como pagos completados o suscripciones actualizadas. Probar webhooks puede ser complicado, especialmente en un entorno local. A continuación, se presentan varias opciones para probar webhooks.

### Opción 1: Usar la CLI de Stripe (Recomendado)

La CLI de Stripe es la forma más sencilla y recomendada para probar webhooks localmente.

#### Instalar la CLI de Stripe

1. Instala la CLI de Stripe siguiendo las instrucciones en la [documentación oficial](https://stripe.com/docs/stripe-cli).

   **macOS (con Homebrew):**
   ```bash
   brew install stripe/stripe-cli/stripe
   ```

   **Windows (con Scoop):**
   ```bash
   scoop install stripe
   ```

   **Linux:**
   ```bash
   # Descarga el binario adecuado para tu sistema desde GitHub
   # https://github.com/stripe/stripe-cli/releases
   ```

2. Inicia sesión en tu cuenta de Stripe:
   ```bash
   stripe login
   ```

#### Escuchar y Reenviar Eventos

1. Inicia el servidor de desarrollo local:
   ```bash
   pnpm dev
   ```

2. En otra terminal, ejecuta el siguiente comando para escuchar eventos de Stripe y reenviarlos a tu servidor local:
   ```bash
   stripe listen --forward-to http://localhost:5173/api/stripe/webhook
   ```

3. La CLI generará un secreto de webhook para firmar los eventos. Copia este secreto y configúralo en tu archivo `.env`:
   ```
   STRIPE_WEBHOOK_SECRET=whsec_...
   ```

#### Filtrar Eventos Específicos

Si solo quieres recibir ciertos tipos de eventos, puedes especificarlos con la opción `--events`:

```bash
stripe listen --events checkout.session.completed,customer.subscription.updated --forward-to http://localhost:5173/api/stripe/webhook
```

#### Generar Eventos de Prueba

Puedes generar eventos de prueba manualmente usando la CLI:

```bash
stripe trigger checkout.session.completed
```

Esto enviará un evento de prueba a tu endpoint local. Puedes ver la lista completa de eventos disponibles con:

```bash
stripe trigger --help
```

### Opción 2: Usar ngrok para Exponer tu Servidor Local

Si prefieres usar los webhooks reales de Stripe, puedes exponer tu servidor local a internet usando ngrok.

1. Instala ngrok desde [ngrok.com](https://ngrok.com/).

2. Inicia tu servidor local:
   ```bash
   pnpm dev
   ```

3. En otra terminal, ejecuta ngrok para exponer tu servidor:
   ```bash
   ngrok http 5173
   ```

4. Ngrok generará una URL pública (por ejemplo, `https://abc123.ngrok.io`).

5. Configura esta URL en el panel de control de Stripe:
   - Ve a [Webhooks en el Dashboard de Stripe](https://dashboard.stripe.com/test/webhooks).
   - Haz clic en "Añadir endpoint".
   - Introduce la URL completa: `https://abc123.ngrok.io/api/stripe/webhook`.
   - Selecciona los eventos que quieres recibir.
   - Guarda el endpoint.

6. Copia el secreto del webhook generado y configúralo en tu archivo `.env`.

### Verificar la Firma del Webhook

Es importante verificar la firma de los webhooks para garantizar que los eventos provienen realmente de Stripe. Aquí hay un ejemplo de cómo hacerlo:

```typescript
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

// En tu endpoint de webhook
export async function POST({ request }) {
  const payload = await request.text();
  const signature = request.headers.get('stripe-signature');

  try {
    // Verificar la firma
    const event = stripe.webhooks.constructEvent(
      payload,
      signature,
      webhookSecret
    );

    // Procesar el evento
    switch (event.type) {
      case 'checkout.session.completed':
        // Manejar el evento
        break;
      // Otros casos...
    }

    return new Response(JSON.stringify({ received: true }), {
      status: 200
    });
  } catch (err) {
    console.error('Error al verificar webhook:', err);
    return new Response(
      JSON.stringify({ error: 'Error al verificar webhook' }),
      { status: 400 }
    );
  }
}
```

### Depurar Webhooks

Si tienes problemas con los webhooks, puedes usar estas técnicas para depurarlos:

1. **Revisar los logs**: Asegúrate de registrar información detallada sobre los eventos recibidos.

2. **Verificar eventos en el Dashboard**: Puedes ver todos los intentos de entrega de webhooks en el [Dashboard de Stripe](https://dashboard.stripe.com/test/webhooks).

3. **Usar la CLI de Stripe para depurar**:
   ```bash
   stripe listen --debug
   ```

4. **Comprobar la respuesta del servidor**: Asegúrate de que tu servidor responde con un código 200 lo antes posible, incluso antes de procesar el evento.

## Pruebas Automatizadas

Además de las pruebas manuales, es recomendable implementar pruebas automatizadas para verificar la integración de Stripe. Este proyecto incluye un script de prueba que verifica varios aspectos de la configuración de Stripe.

### Ejecutar el Script de Prueba

Para ejecutar el script de prueba:

```bash
pnpm test:stripe
```

Este comando ejecuta el script `scripts/test-stripe.ts`, que realiza las siguientes pruebas:

1. Verifica la conexión con Stripe
2. Verifica la conexión con Supabase
3. Verifica la disponibilidad de productos en Stripe
4. Verifica la disponibilidad de precios en Stripe
5. Verifica la capacidad de crear sesiones de checkout

### Personalizar las Pruebas

Puedes personalizar el script de prueba según tus necesidades. El script se encuentra en `scripts/test-stripe.ts`.

### Integrar con CI/CD

Para integrar estas pruebas en tu pipeline de CI/CD, puedes añadir el comando `pnpm test:stripe` a tu configuración de CI/CD. Asegúrate de configurar las variables de entorno necesarias en tu entorno de CI/CD.

### Simular Respuestas de la API

Para pruebas unitarias, puedes simular las respuestas de la API de Stripe sin hacer llamadas reales a la API. Por ejemplo:

```typescript
// Ejemplo de simulación de una respuesta de la API de Stripe
const mockPaymentIntent = {
  id: 'pi_123456789',
  object: 'payment_intent',
  status: 'succeeded',
  amount: 2000,
  currency: 'eur',
  // ... otros campos
};

// Usar este objeto en tus pruebas
```

Para más información sobre pruebas automatizadas con Stripe, consulta la [documentación oficial](https://stripe.com/docs/automated-testing).

## Solución de Problemas Comunes

### Problemas de Configuración

- **Error al cargar productos**: Verifica que las claves API de Stripe están configuradas correctamente en tu archivo `.env`.
- **Error al crear sesiones de checkout**: Asegúrate de que la clave secreta de Stripe es válida y tiene los permisos necesarios.
- **Error al procesar webhooks**: Verifica que el secreto del webhook está configurado correctamente en tu archivo `.env`.

### Problemas de Pago

- **Pago rechazado**: Asegúrate de estar utilizando una tarjeta de prueba válida.
- **Error de autenticación**: Algunas tarjetas de prueba requieren autenticación adicional. Sigue las instrucciones en pantalla.
- **Error al crear suscripción**: Verifica que el producto tiene un precio de suscripción configurado correctamente.

### Problemas de Webhooks

- **Webhook no recibido**: Asegúrate de que la URL del webhook es accesible desde Internet. Puedes usar herramientas como [ngrok](https://ngrok.com/) para exponer tu servidor local.
- **Error de firma**: Verifica que el secreto del webhook está configurado correctamente en tu archivo `.env`.
- **Evento no procesado**: Asegúrate de que estás manejando correctamente el tipo de evento en tu código.

### Registro y Depuración

Para facilitar la depuración, asegúrate de registrar información detallada en los logs:

```typescript
// Ejemplo de registro detallado
console.log('Evento de Stripe recibido:', {
  type: event.type,
  id: event.id,
  object: event.data.object
});
``` 