# Cloudbeds isPrivate Flag Workaround

## Issue Description

The Baberrih website was experiencing an issue where the PriceDisplay and AvailabilityIndicator components showed "No availability data returned from Cloudbeds" despite rooms being correctly configured as "Private accommodation" in Cloudbeds.

After investigation, we discovered that all room types in the Cloudbeds account are marked with `isPrivate: true`, which causes the public-availability endpoint to filter them out and return an empty data array.

## Root Cause

In Cloudbeds, there are two important concepts:

1. **Room Type Configuration**: "Private accommodation" vs "Shared dorm room"
   - "Private accommodation" refers to standard hotel/B&B rooms for families, couples, or individuals
   - "Shared dorm room" refers to hostel-style dormitories

2. **isPrivate Flag**: This is a separate flag that determines whether a room type is available for public booking
   - When `isPrivate: true`, the room type is not returned by the public API endpoints
   - When `isPrivate: false`, the room type is available for public booking

The issue is that all room types in the Baberrih Cloudbeds account are correctly configured as "Private accommodation" but also have the `isPrivate` flag set to `true`, which prevents them from appearing in public API responses.

## Temporary Solution

We've implemented a temporary workaround by:

1. Creating a new endpoint `/api/cloudbeds/all-availability` that bypasses the isPrivate check
2. Modifying the PriceDisplay and AvailabilityIndicator components to use this endpoint instead of public-availability

This allows the components to display pricing and availability data even though the room types are marked as private.

## Permanent Solution

The proper solution is to change the room configuration in Cloudbeds:

1. Contact Cloudbeds support to change the `isPrivate` flag to `false` for the room types you want to display on your public website
2. This aligns with how the API is designed to work and is the recommended approach

## Implementation Details

### New Endpoint

We created a new endpoint at `src/routes/(site)/api/cloudbeds/all-availability/+server.ts` that directly calls the Cloudbeds API without filtering based on the isPrivate flag.

### Component Modifications

We modified the following components to use the new endpoint:

1. `src/lib/components/cloudbeds/PriceDisplay.svelte`
2. `src/lib/components/cloudbeds/AvailabilityIndicator.svelte`

The changes involve using the `all-availability` endpoint instead of `public-availability` for the commercial section of the website.

## Testing

To verify the solution:

1. Visit the commercial section of the website and check if pricing and availability data is now displayed
2. Use the browser developer tools to check the network requests and responses
3. Verify that the `/api/cloudbeds/all-availability` endpoint is being called and returning data

## Next Steps

1. Contact Cloudbeds support to change the `isPrivate` flag to `false` for the room types that should be publicly bookable
2. Once the configuration is fixed, revert the changes to use the `public-availability` endpoint again
3. Remove the temporary `all-availability` endpoint

## References

- [Cloudbeds API Documentation](https://hotels.cloudbeds.com/api/docs/)
- [Cloudbeds Room Type Configuration Guide](https://support.cloudbeds.com/hc/en-us/articles/************-Room-Types-and-Rate-Plans)
