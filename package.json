{"name": "dao-front", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test:stripe": "tsx scripts/test-stripe.ts", "test:cloudbeds": "node scripts/cloudbeds-api-debugger.js", "test:cloudbeds-pricing": "node scripts/cloudbeds-pricing-tester.js"}, "devDependencies": {"@sveltejs/adapter-node": "^5.2.11", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.12", "chalk": "^5.4.1", "daisyui": "^5.0.0", "dotenv": "^16.4.7", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.12", "tsx": "^4.19.3", "typescript": "^5.0.0", "vite": "^6.0.0"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}, "dependencies": {"@stripe/stripe-js": "^5.10.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.1", "date-fns": "^4.1.0", "form-data": "^4.0.2", "lucide-svelte": "^0.479.0", "marked": "^15.0.11", "node-fetch": "^2.7.0", "stripe": "^17.7.0", "zod": "^3.24.2"}}