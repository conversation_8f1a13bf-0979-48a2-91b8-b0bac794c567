/**
 * Cloudbeds API Debugging Script
 * 
 * Este script prueba diferentes endpoints de la API de Cloudbeds para identificar
 * problemas con los datos de disponibilidad y precios.
 */

const fetch = require('node-fetch');

// Configuración - Reemplaza con tus credenciales reales
const API_KEY = 'TU_API_KEY'; // Reemplaza con tu API key
const PROPERTY_ID = '317353'; // Tu ID de propiedad según los emails

// IDs de tipos de habitación - Reemplaza con los IDs reales de tus habitaciones
const ROOM_TYPES = [
  { id: 'ID_GARDEN_DELUXE', name: 'Garden Deluxe' },
  { id: 'ID_OCEAN_DELUXE', name: 'Ocean Deluxe' },
  { id: 'ID_GARDEN_JUNIOR', name: 'Garden Junior' },
  { id: 'ID_OCEAN_JUNIOR', name: 'Ocean Junior' }
];

// Función para formatear fechas como YYYY-MM-DD
const formatDate = (date) => {
  return date.toISOString().split('T')[0];
};

// Función para generar un rango de fechas para pruebas
const generateDateRanges = () => {
  const today = new Date();
  
  return [
    // Fechas cercanas (próxima semana)
    {
      startDate: formatDate(new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)),
      endDate: formatDate(new Date(today.getTime() + 8 * 24 * 60 * 60 * 1000)),
      description: 'Próxima semana'
    },
    // Fechas a un mes
    {
      startDate: formatDate(new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)),
      endDate: formatDate(new Date(today.getTime() + 31 * 24 * 60 * 60 * 1000)),
      description: 'Un mes adelante'
    },
    // Fechas a tres meses
    {
      startDate: formatDate(new Date(today.getTime() + 90 * 24 * 60 * 60 * 1000)),
      endDate: formatDate(new Date(today.getTime() + 91 * 24 * 60 * 60 * 1000)),
      description: 'Tres meses adelante'
    }
  ];
};

/**
 * Prueba el endpoint getAvailableRoomTypes
 * Este endpoint debería devolver los tipos de habitación disponibles para las fechas especificadas
 */
async function testAvailableRoomTypes(dateRange) {
  console.log(`\n=== Probando getAvailableRoomTypes para ${dateRange.description} ===`);
  console.log(`Fechas: ${dateRange.startDate} a ${dateRange.endDate}`);
  
  const url = new URL('https://api.cloudbeds.com/api/v1.2/getAvailableRoomTypes');
  url.searchParams.append('propertyID', PROPERTY_ID);
  url.searchParams.append('startDate', dateRange.startDate);
  url.searchParams.append('endDate', dateRange.endDate);
  
  console.log(`URL: ${url.toString()}`);
  
  try {
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'x-api-key': API_KEY
      }
    });
    
    const data = await response.json();
    
    console.log('Código de respuesta:', response.status);
    
    if (data.success) {
      console.log('Éxito! Datos recibidos:');
      if (data.data && data.data.length > 0) {
        console.log(`Se encontraron ${data.data.length} tipos de habitación disponibles`);
        data.data.forEach(room => {
          console.log(`- ${room.roomTypeName || 'Sin nombre'} (ID: ${room.roomTypeId || 'N/A'})`);
          console.log(`  Disponibilidad: ${room.available || 'N/A'}`);
          console.log(`  Es privado: ${room.isPrivate ? 'Sí' : 'No'}`);
          if (room.dates && room.dates.length > 0) {
            console.log(`  Primera fecha: ${room.dates[0].date}, Precio: ${room.dates[0].price || 'N/A'}`);
          }
        });
      } else {
        console.log('No se encontraron habitaciones disponibles');
      }
    } else {
      console.log('Error en la respuesta:', data.message || 'Sin mensaje de error');
    }
  } catch (error) {
    console.error('Error al realizar la solicitud:', error.message);
  }
}

/**
 * Prueba el endpoint getRoomTypes
 * Este endpoint debería devolver todos los tipos de habitación configurados en la propiedad
 */
async function testGetRoomTypes() {
  console.log('\n=== Probando getRoomTypes ===');
  
  const url = new URL('https://api.cloudbeds.com/api/v1.2/getRoomTypes');
  url.searchParams.append('propertyID', PROPERTY_ID);
  
  console.log(`URL: ${url.toString()}`);
  
  try {
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'x-api-key': API_KEY
      }
    });
    
    const data = await response.json();
    
    console.log('Código de respuesta:', response.status);
    
    if (data.success) {
      console.log('Éxito! Datos recibidos:');
      if (data.data && data.data.length > 0) {
        console.log(`Se encontraron ${data.data.length} tipos de habitación configurados`);
        data.data.forEach(room => {
          console.log(`- ${room.roomTypeName || 'Sin nombre'} (ID: ${room.roomTypeId || 'N/A'})`);
          console.log(`  Tipo: ${room.roomTypeClass || 'N/A'}`);
          console.log(`  Es privado: ${room.isPrivate ? 'Sí' : 'No'}`);
          console.log(`  Ocupación máxima: ${room.maxOccupancy || 'N/A'}`);
        });
      } else {
        console.log('No se encontraron tipos de habitación configurados');
      }
    } else {
      console.log('Error en la respuesta:', data.message || 'Sin mensaje de error');
    }
  } catch (error) {
    console.error('Error al realizar la solicitud:', error.message);
  }
}

/**
 * Prueba el endpoint getRate para verificar las tarifas configuradas
 */
async function testGetRate(dateRange) {
  console.log(`\n=== Probando getRate para ${dateRange.description} ===`);
  console.log(`Fechas: ${dateRange.startDate} a ${dateRange.endDate}`);
  
  const url = new URL('https://api.cloudbeds.com/api/v1.2/getRate');
  url.searchParams.append('propertyID', PROPERTY_ID);
  url.searchParams.append('startDate', dateRange.startDate);
  url.searchParams.append('endDate', dateRange.endDate);
  
  console.log(`URL: ${url.toString()}`);
  
  try {
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'x-api-key': API_KEY
      }
    });
    
    const data = await response.json();
    
    console.log('Código de respuesta:', response.status);
    
    if (data.success) {
      console.log('Éxito! Datos recibidos:');
      if (data.data && Object.keys(data.data).length > 0) {
        console.log('Tarifas encontradas:');
        for (const [roomTypeId, rates] of Object.entries(data.data)) {
          console.log(`- Tipo de habitación ID: ${roomTypeId}`);
          if (rates && rates.length > 0) {
            rates.forEach(rate => {
              console.log(`  Fecha: ${rate.date}, Precio: ${rate.rate || 'N/A'}`);
            });
          } else {
            console.log('  No hay tarifas configuradas para este tipo de habitación');
          }
        }
      } else {
        console.log('No se encontraron tarifas configuradas');
      }
    } else {
      console.log('Error en la respuesta:', data.message || 'Sin mensaje de error');
    }
  } catch (error) {
    console.error('Error al realizar la solicitud:', error.message);
  }
}

/**
 * Función principal para ejecutar todas las pruebas
 */
async function runTests() {
  console.log('=== Iniciando pruebas de la API de Cloudbeds ===');
  console.log(`Propiedad ID: ${PROPERTY_ID}`);
  
  // Obtener todos los tipos de habitación configurados
  await testGetRoomTypes();
  
  // Probar disponibilidad con diferentes rangos de fechas
  const dateRanges = generateDateRanges();
  for (const dateRange of dateRanges) {
    await testAvailableRoomTypes(dateRange);
    await testGetRate(dateRange);
  }
  
  console.log('\n=== Pruebas completadas ===');
  console.log('Revisa los resultados para identificar posibles problemas.');
}

// Ejecutar todas las pruebas
runTests().catch(error => {
  console.error('Error al ejecutar las pruebas:', error);
});
