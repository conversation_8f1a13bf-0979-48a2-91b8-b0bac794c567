# Despliegue con Docker

Este proyecto está configurado para ser desplegado utilizando Docker y Docker Compose.

## Requisitos previos

- Docker instalado en tu sistema
- Docker Compose instalado en tu sistema

## Configuración

1. Asegúrate de tener un archivo `.env` en la raíz del proyecto con las variables de entorno necesarias.

## Construcción y despliegue

### Construir la imagen

```bash
docker-compose build
```

### Iniciar el contenedor

```bash
docker-compose up -d
```

El parámetro `-d` ejecuta el contenedor en segundo plano (modo "detached").

### Ver los logs

```bash
docker-compose logs -f
```

El parámetro `-f` permite seguir los logs en tiempo real.

### Detener el contenedor

```bash
docker-compose down
```

### Reconstruir y reiniciar

Si has realizado cambios en el código y necesitas reconstruir la imagen:

```bash
docker-compose down
docker-compose build
docker-compose up -d
```

O en una sola línea:

```bash
docker-compose down && docker-compose build && docker-compose up -d
```

## Estructura de Docker

### Dockerfile

El Dockerfile utiliza un enfoque multi-etapa:

1. **Etapa de construcción**: Instala las dependencias y construye la aplicación.
2. **Etapa de producción**: Copia solo los archivos necesarios para ejecutar la aplicación.

### docker-compose.yml

El archivo docker-compose.yml configura:

- El servicio de la aplicación
- El mapeo de puertos (3000:3000)
- Las variables de entorno
- La carga del archivo .env
- Una comprobación de salud (healthcheck)
- La red

## Solución de problemas

### Verificar el estado del contenedor

```bash
docker-compose ps
```

### Entrar al contenedor

```bash
docker-compose exec app sh
```

### Ver los logs del contenedor

```bash
docker-compose logs -f app
``` 