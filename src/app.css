@import 'tailwindcss';
@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';
@plugin "daisyui";

/* Custom color palette for Baberrih */
:root {
  --primary-50: #f8f7f4;
  --primary-100: #f0ede7;
  --primary-200: #e2dcd0;
  --primary-300: #d3c9b9;
  --primary-400: #c4b6a2;
  --primary-500: #b5a38b;
  --primary-600: #a69073;
  --primary-700: #97825c;
  --primary-800: #886f45;
  --primary-900: #795c2e;
  --primary-950: #6a4917;
}

/* Base styles */
body {
  font-family: 'Montserrat', sans-serif;
  color: var(--primary-900);
  background-color: var(--primary-50);
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
  text-transform: uppercase;
}

.font-eb-garamond {
  font-family: '<PERSON><PERSON>', serif;
  font-style: italic;
}

.font-montserrat {
  font-family: 'Montserrat', sans-serif;
}

/* Button styles */
.button {
  background-color: var(--primary-500);
  border-color: var(--primary-400);
  color: var(--primary-50);
  @apply px-3 py-1.5 border font-light text-xs uppercase tracking-widest;
}

.button-secondary {
  border-color: var(--primary-200);
  color: var(--primary-900);
  @apply bg-transparent px-3 py-1.5 border font-light text-xs uppercase tracking-widest;
}

/* Card styles */
.card-container {
  @apply gap-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
}

.card {
  border-color: var(--primary-100);
  @apply flex flex-col border;
}

.card-image-container {
  @apply relative overflow-hidden;
}

.card-image {
  @apply w-full h-full object-cover transition-transform duration-500 ease-in-out;
}

.card-content {
  @apply flex flex-wrap items-start gap-y-2 bg-gradient-to-b from-[var(--primary-50)] to-[color:var(--primary-100)/.25] p-2 md:p-4 w-full;
}

.card-title {
  @apply flex-1 min-w-[200px] font-light text-xs md:text-base;
}

/* Hero section styles */
.hero-container {
  @apply relative h-screen; /* Full viewport height */
  /* No padding-top needed as we'll handle this with positioning */
}

.hero-image {
  @apply w-full h-full object-cover;
}

.hero-video {
  @apply w-full h-full object-cover;
  will-change: transform;
  transition: transform 700ms ease-out;
}

/* Disable hover scale effect for better performance and cleaner look */
.hero-video:hover {
  transform: none;
}

.hero-overlay {
  @apply absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-[color:var(--primary-950)/.75];
}

.hero-content {
  color: var(--primary-50);
  @apply z-10 absolute inset-0 flex flex-col justify-center items-center gap-4 px-8 w-full text-center;
}

/* Carousel styles */
.carousel {
  @apply relative overflow-hidden;
}

.carousel-item {
  @apply w-full h-full object-cover;
}

.carousel-nav {
  @apply absolute top-1/2 transform -translate-y-1/2 flex justify-between w-full px-4;
}

.carousel-button {
  color: var(--primary-50);
  @apply bg-[color:var(--primary-950)/.25] rounded-full w-10 h-10 flex items-center justify-center;
}

/* Aspect ratio container */
.aspect-ratio-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* Default 16:9 aspect ratio */
  overflow: hidden;
}

.aspect-ratio-container[style*="--aspect-ratio: 16/9"]::before {
  padding-top: 56.25%; /* 16:9 aspect ratio */
}

.aspect-ratio-container[style*="--aspect-ratio: 4/3"]::before {
  padding-top: 75%; /* 4:3 aspect ratio */
}

.aspect-ratio-container[style*="--aspect-ratio: 1/1"]::before {
  padding-top: 100%; /* 1:1 aspect ratio (square) */
}

.aspect-ratio-container > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Hide scrollbar but allow scrolling */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Animation classes - optimized for performance */
.scroll-animation {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity var(--duration, 300ms) cubic-bezier(0.33, 1, 0.68, 1) var(--delay, 0ms),
              transform var(--duration, 300ms) cubic-bezier(0.33, 1, 0.68, 1) var(--delay, 0ms);
  will-change: opacity, transform;
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
}

.scroll-animation.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Optimización para dispositivos con preferencia de movimiento reducido */
@media (prefers-reduced-motion: reduce) {
  .scroll-animation {
    transition: opacity 50ms ease 0ms;
    transform: none !important;
  }
}

.fade {
  transform: none;
}

.slide-up {
  transform: translateY(20px);
}

.slide-down {
  transform: translateY(-20px);
}

.slide-left {
  transform: translateX(20px);
}

.slide-right {
  transform: translateX(-20px);
}

/* Optimización de carga de imágenes - Eliminado para usar el componente SimpleImage */

/* Optimizaciones de rendimiento adicionales */
.content-visibility-auto {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px; /* Valor estimado para reservar espacio */
}

/* Blog styles */
.prose {
  max-width: 65ch;
  color: var(--primary-900);
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
  color: var(--primary-900);
  margin-top: 2em;
  margin-bottom: 1em;
  text-transform: uppercase;
}

.prose h1 {
  font-size: 2.25rem;
  line-height: 1.2;
}

.prose h2 {
  font-size: 1.875rem;
  line-height: 1.3;
}

.prose h3 {
  font-size: 1.5rem;
  line-height: 1.4;
}

.prose p, .prose ul, .prose ol {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
  line-height: 1.8;
}

.prose a {
  color: var(--primary-700);
  text-decoration: underline;
  text-decoration-thickness: 1px;
  text-underline-offset: 2px;
}

.prose a:hover {
  color: var(--primary-800);
}

.prose blockquote {
  font-style: italic;
  border-left: 3px solid var(--primary-300);
  padding-left: 1.5rem;
  margin-left: 0;
  color: var(--primary-800);
}

.prose code {
  font-family: monospace;
  background-color: var(--primary-100);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.9em;
}

.prose pre {
  background-color: var(--primary-100);
  padding: 1.25rem;
  border-radius: 0.375rem;
  overflow-x: auto;
}

.prose pre code {
  background-color: transparent;
  padding: 0;
  font-size: 0.9em;
  color: var(--primary-900);
}

.prose img {
  border-radius: 0.375rem;
}

.prose hr {
  border: 0;
  border-top: 1px solid var(--primary-200);
  margin: 3em 0;
}

.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 2em 0;
}

.prose table th {
  background-color: var(--primary-100);
  font-weight: 500;
  text-align: left;
  padding: 0.75em;
  border-bottom: 1px solid var(--primary-300);
}

.prose table td {
  padding: 0.75em;
  border-bottom: 1px solid var(--primary-200);
}

.prose table tr:last-child td {
  border-bottom: none;
}

/* Optimización para dispositivos móviles */
@media (max-width: 768px) {
  .scroll-animation {
    --duration: 200ms; /* Animaciones más rápidas en móvil */
  }

  /* Reducir complejidad visual en móvil */
  .hero-image, .hero-video, .testimonial-image {
    transform: none !important; /* Desactivar transformaciones en móvil */
  }

  .prose h1 {
    font-size: 1.875rem;
  }

  .prose h2 {
    font-size: 1.5rem;
  }

  .prose h3 {
    font-size: 1.25rem;
  }
}