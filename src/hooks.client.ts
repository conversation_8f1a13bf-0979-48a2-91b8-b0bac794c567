import { loadTranslations, language } from '$lib/i18n';
import type { HandleClientError } from '@sveltejs/kit';

// This runs on the client when the app starts
export async function handleError({ error, event }) {
  console.error('Client error:', error);
  return {
    message: 'An unexpected error occurred. Please try again later.'
  };
}

// Load translations when the app starts
if (typeof window !== 'undefined') {
  // This code only runs in the browser
  console.log('Initializing translations in hooks.client.ts');

  // Get the current language from the store
  let currentLang = 'en';
  const unsubscribe = language.subscribe(value => {
    currentLang = value;
  });
  unsubscribe();

  // Load translations for the current language
  console.log(`Initial language is: ${currentLang}`);
  loadTranslations(currentLang);

  // Subscribe to language changes to load new translations
  language.subscribe(async (lang) => {
    console.log(`Language changed to: ${lang}`);
    await loadTranslations(lang);
  });
}
