import { createServerSupabaseClient } from '$lib/supabase';
import { redirect, type Handle, type HandleServerError } from '@sveltejs/kit';
import { sequence } from '@sveltejs/kit/hooks';
import { stripeServerService } from '$lib/stripe.server';
import { hasAdminPermission } from '$lib/auth';

// Cache para sesiones (5 minutos de duración)
const sessionCache = new Map<string, { session: any, timestamp: number }>();
const SESSION_CACHE_DURATION = 5 * 60 * 1000; // 5 minutos en milisegundos

// Hook para configurar el cliente de Supabase en cada solicitud
const supabaseHook: Handle = async ({ event, resolve }) => {
  // Crear un cliente de Supabase específico para esta solicitud
  const supabase = createServerSupabaseClient(event.cookies);
  event.locals.supabase = supabase;

  // Initialize to null
  event.locals.session = null;
  event.locals.user = null;

  try {
    // Intentar obtener la sesión de la caché primero
    const cookieValue = event.cookies.get('sb-tddvejlfcchqxoyllser-auth-token');
    const cacheKey = cookieValue || 'no-session';
    const now = Date.now();
    const cachedData = sessionCache.get(cacheKey);

    // Si hay datos en caché y no han expirado, usarlos
    if (cachedData && (now - cachedData.timestamp < SESSION_CACHE_DURATION)) {
      if (cachedData.session) {
        event.locals.session = cachedData.session;
        event.locals.user = cachedData.session.user;
      }
    } else {
      // Si no hay datos en caché o han expirado, obtener la sesión de Supabase
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (!sessionError && session) {
        // Si hay una sesión válida, establecer session y user en locals
        event.locals.session = session;
        event.locals.user = session.user;

        // Guardar en caché
        sessionCache.set(cacheKey, { session, timestamp: now });
      } else if (!sessionError) {
        // Si no hay error pero tampoco hay sesión, guardar null en caché
        sessionCache.set(cacheKey, { session: null, timestamp: now });
      }
    }
  } catch (error) {
    console.error("[HOOKS] Error durante la verificación de autenticación:", error);
  }

  // Añadir el servicio de Stripe a event.locals
  event.locals.stripe = stripeServerService;

  // Continuar con la solicitud
  const response = await resolve(event, {
    filterSerializedResponseHeaders(name) {
      return name === 'content-range' || name === 'x-supabase-api-version';
    }
  });

  return response;
};

// Cache para permisos de administrador (10 minutos de duración)
const adminPermissionCache = new Map<string, { isAdmin: boolean, timestamp: number }>();
const ADMIN_CACHE_DURATION = 10 * 60 * 1000; // 10 minutos en milisegundos

// Hook para proteger rutas que requieren autenticación
const authGuard: Handle = async ({ event, resolve }) => {
  // Si el usuario no está autenticado y la ruta comienza con /private, redirigir a /auth
  if (!event.locals.session && event.url.pathname.startsWith('/private')) {
    throw redirect(303, '/auth');
  }

  // Si el usuario está autenticado y la ruta es /auth, redirigir a /private
  if (event.locals.session && event.url.pathname === '/auth') {
    throw redirect(303, '/private');
  }

  // Proteger rutas de administración
  if (event.url.pathname.startsWith('/admin') &&
    !event.url.pathname.startsWith('/admin/login') &&
    !event.url.pathname.startsWith('/admin/unauthorized')) {

    // Si el usuario no está autenticado, redirigir a la página de login de administración
    if (!event.locals.session || !event.locals.user) {
      throw redirect(303, '/admin/login');
    }

    // Verificar si el usuario tiene permisos de administración (usando caché)
    const userId = event.locals.user.id;
    const now = Date.now();
    const cachedPermission = adminPermissionCache.get(userId);

    let isAdmin = false;

    // Si hay datos en caché y no han expirado, usarlos
    if (cachedPermission && (now - cachedPermission.timestamp < ADMIN_CACHE_DURATION)) {
      isAdmin = cachedPermission.isAdmin;
    } else {
      // Si no hay datos en caché o han expirado, verificar permisos
      isAdmin = await hasAdminPermission(event.locals.supabase, userId);

      // Guardar en caché
      adminPermissionCache.set(userId, { isAdmin, timestamp: now });
    }

    // Si no tiene permisos de administración, redirigir a la página de error
    if (!isAdmin) {
      throw redirect(303, '/admin/unauthorized');
    }
  }

  // Continuar con la solicitud
  return resolve(event);
};

// Combinar los hooks en secuencia
export const handle: Handle = sequence(supabaseHook, authGuard);

// Manejador de errores del servidor
export const handleError: HandleServerError = ({ error }) => {
  console.error(error);
  return {
    message: 'Error interno del servidor',
    code: 'INTERNAL_SERVER_ERROR'
  };
};