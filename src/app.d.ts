// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
import type { SupabaseClient, Session, User } from '@supabase/supabase-js';
import type Stripe from 'stripe';
import type { StripeService } from '$lib/stripe/service';
import type {
	StripeProduct,
	StripePrice,
	StripeSubscription,
	StripeCustomer,
	StripeCheckoutSession
} from '$lib/stripe/types.d';

declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			supabase: SupabaseClient;
			session: Session | null;
			user: User | null;
			stripe: StripeService;
		}
		interface PageData {
			session: Session | null;
			user: User | null;
			stripeProducts?: StripeProduct[];
			stripeSubscriptions?: StripeSubscription[];
			stripeCustomer?: StripeCustomer;
			stripeCheckoutSession?: StripeCheckoutSession;
		}
		// interface PageState {}
		// interface Platform {}
	}
}

// Añadir tipos para las variables de entorno
declare namespace NodeJS {
	interface ProcessEnv {
		STRIPE_SECRET_KEY: string;
		PUBLIC_STRIPE_PUBLISHABLE_KEY: string;
		STRIPE_WEBHOOK_SECRET: string;
	}
}

export { };
