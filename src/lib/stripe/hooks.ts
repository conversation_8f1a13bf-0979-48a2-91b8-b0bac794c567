/**
 * Hooks para verificar suscripciones en el lado del servidor
 * Este archivo proporciona funciones para proteger rutas que requieren suscripción
 */
import { redirect, type RequestEvent } from '@sveltejs/kit';
import { checkSubscription, checkAccess } from './subscription.server';

/**
 * Hook para verificar si un usuario tiene una suscripción activa
 * Si no tiene una suscripción activa, redirige a la página de productos
 * @param event Evento de la solicitud
 * @param redirectTo Ruta a la que redirigir si no tiene suscripción
 */
export async function requireSubscription(
  event: RequestEvent,
  redirectTo = '/products'
): Promise<void> {
  // Verificar si el usuario está autenticado
  if (!event.locals.session || !event.locals.user) {
    throw redirect(303, '/auth');
  }

  const userId = event.locals.user.id;

  // Verificar si el usuario tiene una suscripción activa
  const { hasActiveSubscription, error } = await checkSubscription(userId);

  // Si hay un error o no tiene suscripción, redirigir
  if (error || !hasActiveSubscription) {
    throw redirect(303, redirectTo);
  }
}

/**
 * Hook para verificar si un usuario tiene acceso a un recurso específico
 * Si no tiene acceso, redirige a la página especificada
 * @param event Evento de la solicitud
 * @param requiredProductIds IDs de productos que dan acceso al recurso
 * @param redirectTo Ruta a la que redirigir si no tiene acceso
 */
export async function requireAccess(
  event: RequestEvent,
  requiredProductIds: string[],
  redirectTo = '/products'
): Promise<void> {
  // Verificar si el usuario está autenticado
  if (!event.locals.session || !event.locals.user) {
    throw redirect(303, '/auth');
  }

  const userId = event.locals.user.id;

  // Verificar si el usuario tiene acceso
  const { hasAccess, error } = await checkAccess(userId, requiredProductIds);

  // Si hay un error o no tiene acceso, redirigir
  if (error || !hasAccess) {
    throw redirect(303, redirectTo);
  }
}