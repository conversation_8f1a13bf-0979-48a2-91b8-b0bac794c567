/**
 * Servicio de suscripciones (Servidor)
 * Este archivo proporciona funciones para verificar y gestionar suscripciones
 * SOLO DEBE IMPORTARSE DESDE CÓDIGO DEL SERVIDOR
 */
import { createClient } from '$lib/supabase';
import type { SubscriptionWithProduct } from './types';
import Stripe from 'stripe';
import { env } from '$env/dynamic/private';

// Crear una instancia de Stripe con la clave secreta
const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-02-24.acacia',
});

// Cache para almacenar la relación entre usuarios de Supabase y clientes de Stripe
// Esto evita tener que buscar el cliente de Stripe cada vez que se verifica una suscripción
const customerCache = new Map<string, string>();

// Cache para almacenar los resultados de verificación de suscripciones
// Esto evita tener que consultar a Stripe cada vez que se verifica una suscripción
interface SubscriptionCacheEntry {
  hasActiveSubscription: boolean;
  subscriptions: any[];
  timestamp: number;
}
const subscriptionCache = new Map<string, SubscriptionCacheEntry>();
const SUBSCRIPTION_CACHE_DURATION = 5 * 60 * 1000; // 5 minutos en milisegundos

/**
 * Encuentra el ID de cliente de Stripe asociado con un usuario de Supabase
 * @param userId ID del usuario de Supabase
 * @param userEmail Email del usuario (opcional)
 * @returns ID del cliente de Stripe o null si no se encuentra
 */
async function findStripeCustomerId(userId: string, userEmail?: string): Promise<string | null> {
  if (customerCache.has(userId)) {
    return customerCache.get(userId) || null;
  }

  try {
    if (userId) {
      // Using stripe.customers.search for metadata queries
      const searchResults = await stripe.customers.search({
        query: `metadata['userId']:'${userId}'`,
        limit: 1,
      });

      if (searchResults.data.length > 0) {
        const customerId = searchResults.data[0].id;
        customerCache.set(userId, customerId);
        return customerId;
      }
    }

    if (userEmail) {
      const customers = await stripe.customers.list({
        email: userEmail,
        limit: 1,
      });

      if (customers.data.length > 0) {
        const customerId = customers.data[0].id;
        await stripe.customers.update(customerId, { metadata: { userId: userId } });
        customerCache.set(userId, customerId);
        return customerId;
      }
    }

    return null;
  } catch (error) {
    console.error('Error al buscar el cliente de Stripe por ID/Email:', error);
    return null;
  }
}

/**
 * Obtiene el cliente de Stripe con sus suscripciones (usado internamente por checkSubscription)
 * @param userId 
 * @param userEmail 
 * @returns Stripe Customer object with subscriptions or null
 */
async function getStripeCustomerWithSubscriptions(userId: string, userEmail?: string): Promise<Stripe.Customer | null> {
  try {
    let stripeCustomerId = customerCache.get(userId);

    if (!stripeCustomerId) {
      if (userId) {
        const searchResults = await stripe.customers.search({
          query: `metadata['userId']:'${userId}'`,
          limit: 1,
          expand: ['data.subscriptions'], // Request subscription data to be expanded
        });

        if (searchResults.data.length > 0) {
          const customer = searchResults.data[0]; // This should be a Stripe.Customer
          // Ensure the customer object from search is valid and subscriptions are potentially expanded
          if (customer.deleted) return null;
          // If customer.subscriptions is populated by the expand, we can use it directly.
          // The type Stripe.Customer should include an optional subscriptions field when expanded.
          customerCache.set(userId, customer.id);
          return customer as Stripe.Customer; // Assert type if confident from SDK docs/behavior
        }
      }

      if (userEmail) {
        const customersByEmail = await stripe.customers.list({
          email: userEmail,
          limit: 1,
          expand: ['data.subscriptions'] // List by email can expand subscriptions directly
        });

        if (customersByEmail.data.length > 0) {
          const customer = customersByEmail.data[0];
          if (customer.deleted) return null;
          await stripe.customers.update(customer.id, { metadata: { userId: userId } });
          customerCache.set(userId, customer.id);
          return customer as Stripe.Customer;
        }
      }
      return null;
    } else {
      const customer = await stripe.customers.retrieve(stripeCustomerId, {
        expand: ['subscriptions']
      });
      if (customer.deleted) return null;
      return customer as Stripe.Customer;
    }
  } catch (error) {
    console.error('Error al obtener el cliente de Stripe con suscripciones:', error);
    return null;
  }
}

/**
 * Verifica si un usuario tiene una suscripción activa
 * @param userId ID del usuario
 * @param userEmail Email del usuario (opcional)
 * @returns Objeto con información sobre la suscripción
 */
export async function checkSubscription(userId: string, userEmail?: string) {
  try {
    if (!userId) {
      return { hasActiveSubscription: false, subscriptions: [], error: 'Se requiere el ID de usuario' };
    }

    const now = Date.now();
    const cachedData = subscriptionCache.get(userId);

    if (cachedData && (now - cachedData.timestamp < SUBSCRIPTION_CACHE_DURATION)) {
      return {
        hasActiveSubscription: cachedData.hasActiveSubscription,
        subscriptions: cachedData.subscriptions,
        error: null
      };
    }

    const customer = await getStripeCustomerWithSubscriptions(userId, userEmail);

    if (!customer) {
      subscriptionCache.set(userId, {
        hasActiveSubscription: false,
        subscriptions: [],
        timestamp: now
      });
      return { hasActiveSubscription: false, subscriptions: [], error: null };
    }

    const activeSubscriptions = (customer.subscriptions?.data || []).filter(
      (sub) => sub.status === 'active' || sub.status === 'trialing'
    );

    const hasActiveSubscription = activeSubscriptions.length > 0;

    subscriptionCache.set(userId, {
      hasActiveSubscription,
      subscriptions: activeSubscriptions,
      timestamp: now
    });

    return {
      hasActiveSubscription,
      subscriptions: activeSubscriptions,
      error: null
    };
  } catch (error) {
    console.error('Error al verificar la suscripción:', error);
    // Consider whether to cache this error state or not
    return { hasActiveSubscription: false, subscriptions: [], error: 'Error interno del servidor al verificar suscripción' };
  }
}

/**
 * Verifica si un usuario tiene acceso a un recurso específico basado en su nivel de suscripción
 * @param userId ID del usuario
 * @param requiredProductIds IDs de productos que dan acceso al recurso
 * @param userEmail Email del usuario (opcional)
 * @returns Objeto con información sobre el acceso
 */
export async function checkAccess(userId: string, requiredProductIds: string[], userEmail?: string) {
  try {
    if (!userId) {
      return { hasAccess: false, error: 'Se requiere el ID de usuario' };
    }

    if (!requiredProductIds || requiredProductIds.length === 0) {
      return { hasAccess: false, error: 'Se requieren los IDs de productos' };
    }

    const { hasActiveSubscription, subscriptions, error } = await checkSubscription(userId, userEmail);

    if (error) {
      return { hasAccess: false, error };
    }

    if (!hasActiveSubscription) {
      return { hasAccess: false, error: null };
    }

    // Para verificar el acceso a productos específicos, necesitaríamos hacer consultas adicionales
    // Por ahora, simplemente asumimos que cualquier suscripción activa da acceso
    const hasAccess = hasActiveSubscription;

    return { hasAccess, error: null };
  } catch (error) {
    console.error('Error al verificar el acceso:', error);
    return { hasAccess: false, error: 'Error interno del servidor' };
  }
}

/**
 * Obtiene información detallada sobre las suscripciones de un usuario
 * @param userId ID del usuario
 * @param userEmail Email del usuario (opcional)
 * @returns Objeto con información sobre las suscripciones
 */
export async function getUserSubscriptions(userId: string, userEmail?: string) {
  try {
    if (!userId) {
      return { subscriptions: [], error: 'Se requiere el ID de usuario' };
    }

    // Buscar el ID de cliente de Stripe
    const stripeCustomerId = await findStripeCustomerId(userId, userEmail);

    // Si no encontramos un cliente de Stripe, asumimos que no hay suscripción
    if (!stripeCustomerId) {
      console.log('No se encontró un cliente de Stripe para el usuario:', userId);
      return { subscriptions: [], error: null };
    }

    // Obtenemos todas las suscripciones del cliente
    const subscriptions = await stripe.subscriptions.list({
      customer: stripeCustomerId,
      limit: 100,
    });

    return { subscriptions: subscriptions.data, error: null };
  } catch (error) {
    console.error('Error al obtener las suscripciones del usuario:', error);
    return { subscriptions: [], error: 'Error interno del servidor' };
  }
}
