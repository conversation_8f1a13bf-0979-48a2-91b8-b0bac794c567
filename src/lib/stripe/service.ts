/**
 * Servicio de Stripe
 * Este archivo implementa la lógica de negocio utilizando el adaptador de Stripe
 * SEGURO PARA IMPORTAR DESDE CÓDIGO DEL CLIENTE
 */
import type { StripePort } from './port';
import type {
  CreateCheckoutSessionParams,
  CheckoutSessionResult,
  CreateCustomerPortalParams,
  CustomerPortalResult,
  CustomerResult,
  ProductsAndPricesResult,
  CustomerSubscriptionsResult,
  VerifyWebhookEventParams,
  WebhookEventResult,
  CreateCustomerParams,
} from './types';

/**
 * Servicio de Stripe
 * Implementa la lógica de negocio utilizando el adaptador de Stripe
 */
export class StripeService {
  private stripePort: StripePort;

  /**
   * Constructor del servicio
   * @param stripePort Puerto de Stripe (requerido)
   */
  constructor(stripePort: StripePort) {
    this.stripePort = stripePort;
  }

  /**
   * Crea una sesión de checkout
   * @param params Parámetros para crear la sesión
   * @returns Resultado de la operación
   */
  async createCheckoutSession(params: CreateCheckoutSessionParams): Promise<CheckoutSessionResult> {
    return this.stripePort.createCheckoutSession(params);
  }

  /**
   * Crea un portal de cliente
   * @param params Parámetros para crear el portal
   * @returns Resultado de la operación
   */
  async createCustomerPortal(params: CreateCustomerPortalParams): Promise<CustomerPortalResult> {
    return this.stripePort.createCustomerPortal(params);
  }

  /**
   * Obtiene un cliente por su ID
   * @param customerId ID del cliente
   * @returns Resultado de la operación
   */
  async getCustomer(customerId: string): Promise<CustomerResult> {
    return this.stripePort.getCustomer(customerId);
  }

  /**
   * Crea un nuevo cliente
   * @param params Parámetros para crear el cliente
   * @returns Resultado de la operación
   */
  async createCustomer(params: CreateCustomerParams): Promise<CustomerResult> {
    return this.stripePort.createCustomer(params);
  }

  /**
   * Obtiene las suscripciones de un cliente
   * @param customerId ID del cliente
   * @returns Resultado de la operación
   */
  async getCustomerSubscriptions(customerId: string): Promise<CustomerSubscriptionsResult> {
    return this.stripePort.getCustomerSubscriptions(customerId);
  }

  /**
   * Obtiene todos los productos y precios
   * @returns Resultado de la operación
   */
  async getProductsAndPrices(): Promise<ProductsAndPricesResult> {
    return this.stripePort.getProductsAndPrices();
  }

  /**
   * Verifica un evento de webhook
   * @param params Parámetros para verificar el evento
   * @returns Resultado de la operación
   */
  verifyWebhookEvent(params: VerifyWebhookEventParams): WebhookEventResult {
    return this.stripePort.verifyWebhookEvent(params);
  }

  /**
   * Obtiene el adaptador de Stripe subyacente
   * @returns Adaptador de Stripe
   */
  getAdapter(): StripePort {
    return this.stripePort;
  }
}