/**
 * <PERSON><PERSON><PERSON><PERSON> (Cliente)
 * Este archivo exporta todo lo necesario para usar el servicio de Stripe en el cliente
 * SEGURO PARA IMPORTAR DESDE CÓDIGO DEL CLIENTE
 */
import { env } from '$env/dynamic/public';

// Exportar la clave pública de Stripe
export const stripePublicKey = env.PUBLIC_STRIPE_KEY;

// Exportar tipos
export * from './types';

// Exportar esquemas
export * from './schemas';

// Exportar puertos
export * from './port';

// Exportar adaptador
export * from './adapter';

// Exportar servicio
export * from './service';

// Exportar funciones de suscripción (cliente)
export * from './subscription.client';

// Exportar cliente
export * from './client';