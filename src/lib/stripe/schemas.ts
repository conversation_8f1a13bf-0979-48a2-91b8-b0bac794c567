/**
 * Esquemas Zod para validar los datos de Stripe
 * Este archivo define los esquemas Zod utilizados para validar los datos de entrada y salida
 */
import { z } from 'zod';

/**
 * Esquema para validar los parámetros de creación de una sesión de checkout
 */
export const createCheckoutSessionSchema = z.object({
  priceId: z.string().min(1, 'El ID del precio es requerido'),
  customerId: z.string().optional().transform(val => val || undefined),
  successUrl: z.string().url('La URL de éxito debe ser una URL válida'),
  cancelUrl: z.string().url('La URL de cancelación debe ser una URL válida'),
});

/**
 * Esquema para validar los parámetros de creación de un portal de cliente
 */
export const createCustomerPortalSchema = z.object({
  customerId: z.string().min(1, 'El ID del cliente es requerido'),
  returnUrl: z.string().url('La URL de retorno debe ser una URL válida'),
});

/**
 * Esquema para validar los parámetros de creación de un cliente
 */
export const createCustomerSchema = z.object({
  email: z.string().email('El email debe ser válido'),
  name: z.string().optional(),
  metadata: z.record(z.string()).optional(),
});

/**
 * Esquema para validar los parámetros de verificación de un evento de webhook
 */
export const verifyWebhookEventSchema = z.object({
  payload: z.string().min(1, 'El payload es requerido'),
  signature: z.string().min(1, 'La firma es requerida'),
  webhookSecret: z.string().min(1, 'El secreto del webhook es requerido'),
});

/**
 * Tipo inferido para los parámetros de creación de una sesión de checkout
 */
export type CreateCheckoutSessionSchemaType = z.infer<typeof createCheckoutSessionSchema>;

/**
 * Tipo inferido para los parámetros de creación de un portal de cliente
 */
export type CreateCustomerPortalSchemaType = z.infer<typeof createCustomerPortalSchema>;

/**
 * Tipo inferido para los parámetros de creación de un cliente
 */
export type CreateCustomerSchemaType = z.infer<typeof createCustomerSchema>;

/**
 * Tipo inferido para los parámetros de verificación de un evento de webhook
 */
export type VerifyWebhookEventSchemaType = z.infer<typeof verifyWebhookEventSchema>; 