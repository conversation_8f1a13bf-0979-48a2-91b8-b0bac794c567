/**
 * Tipos para el servicio de Stripe
 * Este archivo define los tipos utilizados en el servicio de Stripe
 */
import type Stripe from 'stripe';

/**
 * Parámetros para crear una sesión de checkout
 */
export interface CreateCheckoutSessionParams {
  /** ID del precio del producto */
  priceId: string;
  /** ID del cliente (opcional) */
  customerId?: string;
  /** URL de éxito a la que se redirigirá después del pago */
  successUrl: string;
  /** URL de cancelación a la que se redirigirá si se cancela el pago */
  cancelUrl: string;
}

/**
 * Resultado de crear una sesión de checkout
 */
export interface CheckoutSessionResult {
  /** Indica si la operación fue exitosa */
  success: boolean;
  /** ID de la sesión (si success es true) */
  sessionId?: string;
  /** URL de la sesión (si success es true) */
  url?: string;
  /** Error (si success es false) */
  error?: unknown;
}

/**
 * Parámetros para crear un portal de cliente
 */
export interface CreateCustomerPortalParams {
  /** ID del cliente */
  customerId: string;
  /** URL a la que se redirigirá después de salir del portal */
  returnUrl: string;
}

/**
 * Resultado de crear un portal de cliente
 */
export interface CustomerPortalResult {
  /** Indica si la operación fue exitosa */
  success: boolean;
  /** URL del portal (si success es true) */
  url?: string;
  /** Error (si success es false) */
  error?: unknown;
}

/**
 * Parámetros para crear un cliente
 */
export interface CreateCustomerParams {
  /** Email del cliente */
  email: string;
  /** Nombre del cliente (opcional) */
  name?: string;
  /** Metadatos adicionales (opcional) */
  metadata?: Record<string, string>;
}

/**
 * Resultado de crear un cliente
 */
export interface CustomerResult {
  /** Indica si la operación fue exitosa */
  success: boolean;
  /** Cliente (si success es true) */
  customer?: Stripe.Customer;
  /** Error (si success es false) */
  error?: unknown;
}

/**
 * Resultado de obtener productos y precios
 */
export interface ProductsAndPricesResult {
  /** Indica si la operación fue exitosa */
  success: boolean;
  /** Productos (si success es true) */
  products?: Stripe.Product[];
  /** Error (si success es false) */
  error?: unknown;
}

/**
 * Resultado de obtener suscripciones de un cliente
 */
export interface CustomerSubscriptionsResult {
  /** Indica si la operación fue exitosa */
  success: boolean;
  /** Suscripciones (si success es true) */
  subscriptions?: Stripe.Subscription[];
  /** Error (si success es false) */
  error?: unknown;
}

/**
 * Parámetros para verificar un evento de webhook
 */
export interface VerifyWebhookEventParams {
  /** Payload del webhook */
  payload: string;
  /** Firma del webhook */
  signature: string;
  /** Secreto del webhook */
  webhookSecret: string;
}

/**
 * Resultado de verificar un evento de webhook
 */
export interface WebhookEventResult {
  /** Indica si la operación fue exitosa */
  success: boolean;
  /** Evento (si success es true) */
  event?: Stripe.Event;
  /** Error (si success es false) */
  error?: unknown;
}

/**
 * Tipo para una suscripción con información del producto
 */
export interface SubscriptionWithProduct {
  id: string;
  user_id: string;
  status: string;
  metadata?: Record<string, string>;
  cancel_at_period_end?: boolean;
  created: string;
  current_period_start: string;
  current_period_end: string;
  ended_at?: string;
  cancel_at?: string;
  canceled_at?: string;
  trial_start?: string;
  trial_end?: string;
  prices?: {
    id: string;
    product_id: string;
    active: boolean;
    currency: string;
    description?: string;
    interval?: string;
    interval_count?: number;
    metadata?: Record<string, string>;
    type?: string;
    unit_amount?: number;
    products?: {
      id: string;
      name: string;
      description?: string;
      active: boolean;
      metadata?: Record<string, string>;
    };
  };
}

/**
 * Tipo para representar una suscripción de Stripe
 */
export interface Subscription {
  id: string;
  customer: string;
  status: string;
  current_period_start: number;
  current_period_end: number;
  cancel_at_period_end: boolean;
  items: {
    data: Array<{
      id: string;
      price: {
        id: string;
        product: string;
        currency: string;
        unit_amount: number | null;
        type: string;
        active: boolean;
        recurring?: {
          interval: string;
          interval_count: number;
        };
      };
    }>;
  };
  default_payment_method?: string | null;
  latest_invoice?: string | null;
} 