/**
 * Inicialización del adaptador de Stripe para el servidor
 * Este archivo solo debe importarse desde código del servidor
 */
import { env } from '$env/dynamic/private';
import { StripeAdapter } from './adapter';

// Crear una instancia del adaptador con la clave secreta
export const stripeServer = new StripeAdapter(env.STRIPE_SECRET_KEY);

// Exportar la clave del webhook para verificar eventos
export const STRIPE_WEBHOOK_SECRET = env.STRIPE_WEBHOOK_SECRET;

// Re-exportar funciones de suscripción del servidor
export * from './subscription.server';
