/**
 * Tipos para Stripe
 * Este archivo define los tipos utilizados en la integración con Stripe
 */
import type <PERSON><PERSON> from 'stripe';

/**
 * Tipo para un producto de Stripe con su precio
 */
export interface StripeProduct {
  id: string;
  name: string;
  description: string | null;
  active: boolean;
  images: string[];
  metadata: Record<string, string>;
  price?: StripePrice;
}

/**
 * Tipo para un precio de Stripe
 */
export interface StripePrice {
  id: string;
  product: string;
  active: boolean;
  currency: string;
  unit_amount: number;
  type: string;
  recurring?: {
    interval: 'day' | 'week' | 'month' | 'year';
    interval_count: number;
  };
}

/**
 * Tipo para un ítem de suscripción de Stripe
 */
export interface StripeSubscriptionItem {
  id: string;
  price: StripePrice;
}

/**
 * Tipo para una suscripción de Stripe
 */
export interface StripeSubscription {
  id: string;
  customer: string;
  status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid';
  current_period_start: number;
  current_period_end: number;
  cancel_at_period_end: boolean;
  items: {
    data: StripeSubscriptionItem[];
  };
  default_payment_method: string | null;
  latest_invoice: string | null;
}

/**
 * Tipo para un cliente de Stripe
 */
export interface StripeCustomer {
  id: string;
  email: string | null;
  name: string | null;
  phone: string | null;
  metadata: Record<string, string>;
  created: number;
  default_source: string | null;
}

/**
 * Tipo para una sesión de checkout de Stripe
 */
export interface StripeCheckoutSession {
  id: string;
  client_secret: string | null;
  url: string | null;
  customer: string | null;
  payment_status: 'paid' | 'unpaid' | 'no_payment_required';
  status: 'open' | 'complete' | 'expired';
}

/**
 * Tipo para los parámetros de creación de una sesión de checkout
 */
export interface CreateCheckoutSessionParams {
  priceId: string;
  customerId?: string;
  successUrl: string;
  cancelUrl: string;
}

/**
 * Tipo para el resultado de crear una sesión de checkout
 */
export interface CheckoutSessionResult {
  success: boolean;
  sessionId?: string;
  clientSecret?: string;
  url?: string;
  error?: unknown;
}

/**
 * Tipo para los parámetros de creación de un portal de cliente
 */
export interface CreateCustomerPortalParams {
  customerId: string;
  returnUrl: string;
}

/**
 * Tipo para el resultado de crear un portal de cliente
 */
export interface CustomerPortalResult {
  success: boolean;
  url?: string;
  error?: unknown;
}

/**
 * Tipo para el resultado de obtener un cliente
 */
export interface CustomerResult {
  success: boolean;
  customer?: StripeCustomer;
  error?: unknown;
}

/**
 * Tipo para el resultado de obtener productos y precios
 */
export interface ProductsAndPricesResult {
  success: boolean;
  products?: StripeProduct[];
  error?: unknown;
}

/**
 * Tipo para el resultado de obtener suscripciones de un cliente
 */
export interface CustomerSubscriptionsResult {
  success: boolean;
  subscriptions?: StripeSubscription[];
  error?: unknown;
}

/**
 * Tipo para los parámetros de verificación de un evento de webhook
 */
export interface VerifyWebhookEventParams {
  payload: string;
  signature: string;
  webhookSecret: string;
}

/**
 * Tipo para el resultado de verificar un evento de webhook
 */
export interface WebhookEventResult {
  success: boolean;
  event?: Stripe.Event;
  error?: unknown;
} 