/**
 * Puertos (interfaces) para las operaciones de Stripe
 * Este archivo define las interfaces que deben implementar los adaptadores de Stripe
 */
import type {
  CreateCheckoutSessionParams,
  CheckoutSessionResult,
  CreateCustomerPortalParams,
  CustomerPortalResult,
  CustomerResult,
  ProductsAndPricesResult,
  CustomerSubscriptionsResult,
  VerifyWebhookEventParams,
  WebhookEventResult,
  CreateCustomerParams,
} from './types';

/**
 * Puerto para las operaciones de pago de Stripe
 */
export interface StripePaymentPort {
  /**
   * Crea una sesión de checkout
   * @param params Parámetros para crear la sesión
   * @returns Resultado de la operación
   */
  createCheckoutSession(params: CreateCheckoutSessionParams): Promise<CheckoutSessionResult>;

  /**
   * Crea un portal de cliente
   * @param params Parámetros para crear el portal
   * @returns Resultado de la operación
   */
  createCustomerPortal(params: CreateCustomerPortalParams): Promise<CustomerPortalResult>;
}

/**
 * Puerto para las operaciones de cliente de Stripe
 */
export interface StripeCustomerPort {
  /**
   * Obtiene un cliente por su ID
   * @param customerId ID del cliente
   * @returns Resultado de la operación
   */
  getCustomer(customerId: string): Promise<CustomerResult>;

  /**
   * Obtiene un cliente por su email
   * @param email Email del cliente
   * @returns Resultado de la operación
   */
  getCustomerByEmail(email: string): Promise<CustomerResult>;

  /**
   * Crea un nuevo cliente
   * @param params Parámetros para crear el cliente
   * @returns Resultado de la operación
   */
  createCustomer(params: CreateCustomerParams): Promise<CustomerResult>;

  /**
   * Actualiza un cliente existente
   * @param params Parámetros para actualizar el cliente
   * @returns Resultado de la operación
   */
  updateCustomer(params: { customerId: string, [key: string]: any }): Promise<CustomerResult>;

  /**
   * Obtiene las suscripciones de un cliente
   * @param customerId ID del cliente
   * @returns Resultado de la operación
   */
  getCustomerSubscriptions(customerId: string): Promise<CustomerSubscriptionsResult>;
}

/**
 * Puerto para las operaciones de producto de Stripe
 */
export interface StripeProductPort {
  /**
   * Obtiene todos los productos y precios
   * @returns Resultado de la operación
   */
  getProductsAndPrices(): Promise<ProductsAndPricesResult>;
}

/**
 * Puerto para las operaciones de webhook de Stripe
 */
export interface StripeWebhookPort {
  /**
   * Verifica un evento de webhook
   * @param params Parámetros para verificar el evento
   * @returns Resultado de la operación
   */
  verifyWebhookEvent(params: VerifyWebhookEventParams): WebhookEventResult;
}

/**
 * Puerto principal que combina todos los puertos de Stripe
 */
export interface StripePort extends StripePaymentPort, StripeCustomerPort, StripeProductPort, StripeWebhookPort { }