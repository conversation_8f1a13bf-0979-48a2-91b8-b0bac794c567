/**
 * Inicialización del adaptador de Stripe para el cliente
 * Este archivo puede importarse desde código del cliente
 */
import { env } from '$env/dynamic/public';
import { StripeAdapter } from './adapter';

// Crear una instancia del adaptador con la clave pública
export const stripeClient = new StripeAdapter(env.PUBLIC_STRIPE_KEY);

// Exportar la clave pública para usar con el widget de Stripe
export const STRIPE_PUBLIC_KEY = env.PUBLIC_STRIPE_KEY;
