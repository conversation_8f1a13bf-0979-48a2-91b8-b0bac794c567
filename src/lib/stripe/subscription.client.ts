/**
 * Servicio de suscripciones (Cliente)
 * Este archivo proporciona tipos y funciones para el cliente relacionadas con suscripciones
 * SEGURO PARA IMPORTAR DESDE CÓDIGO DEL CLIENTE
 */
import type { SubscriptionWithProduct } from './types';

/**
 * Tipo para el resultado de verificar una suscripción
 */
export interface SubscriptionCheckResult {
  hasActiveSubscription: boolean;
  subscriptions?: any[];
  error?: string | null;
}

/**
 * Tipo para el resultado de verificar acceso
 */
export interface AccessCheckResult {
  hasAccess: boolean;
  error?: string | null;
}

/**
 * Tipo para el resultado de obtener suscripciones de usuario
 */
export interface UserSubscriptionsResult {
  subscriptions: any[];
  error?: string | null;
}

/**
 * Verifica si un usuario tiene una suscripción activa (versión cliente)
 * Esta función hace una solicitud a la API en lugar de usar directamente Stripe
 * @param userId ID del usuario
 * @returns Promesa que resuelve a un objeto con información sobre la suscripción
 */
export async function checkSubscriptionClient(userId: string): Promise<SubscriptionCheckResult> {
  try {
    if (!userId) {
      return { hasActiveSubscription: false, error: 'Se requiere el ID de usuario' };
    }

    // Hacer una solicitud a la API para verificar la suscripción
    const response = await fetch(`/api/subscriptions/check?userId=${userId}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error al verificar la suscripción:', errorText);
      return { hasActiveSubscription: false, error: 'Error al verificar la suscripción' };
    }

    const data = await response.json();
    return {
      hasActiveSubscription: data.hasActiveSubscription,
      subscriptions: data.subscriptions || [],
      error: null
    };
  } catch (error) {
    console.error('Error al verificar la suscripción:', error);
    return { hasActiveSubscription: false, error: 'Error interno del cliente' };
  }
}

/**
 * Obtiene las suscripciones del usuario actual (versión cliente)
 * Esta función hace una solicitud a la API en lugar de usar directamente Stripe
 * @returns Promesa que resuelve a un objeto con información sobre las suscripciones
 */
export async function getUserSubscriptionsClient(): Promise<UserSubscriptionsResult> {
  try {
    // Hacer una solicitud a la API para obtener las suscripciones
    const response = await fetch('/api/subscriptions/user');

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error al obtener las suscripciones:', errorText);
      return { subscriptions: [], error: 'Error al obtener las suscripciones' };
    }

    const data = await response.json();
    return {
      subscriptions: data.success ? data.subscriptions : [],
      error: data.success ? null : data.error
    };
  } catch (error) {
    console.error('Error al obtener las suscripciones:', error);
    return { subscriptions: [], error: 'Error interno del cliente' };
  }
}
