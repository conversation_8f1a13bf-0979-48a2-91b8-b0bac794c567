/**
 * Adaptador para Stripe
 * Este archivo implementa los puertos definidos en port.ts utilizando la biblioteca de Stripe
 */
import Stripe from 'stripe';
// Importamos las variables de entorno como tipo para evitar el error de seguridad
import { env } from '$env/dynamic/public';
import type {
  CreateCheckoutSessionParams,
  CheckoutSessionResult,
  CreateCustomerPortalParams,
  CustomerPortalResult,
  CustomerResult,
  ProductsAndPricesResult,
  CustomerSubscriptionsResult,
  VerifyWebhookEventParams,
  WebhookEventResult,
  CreateCustomerParams,
} from './types';
import type { StripePort } from './port';
import {
  createCheckoutSessionSchema,
  createCustomerPortalSchema,
  createCustomerSchema,
  verifyWebhookEventSchema,
} from './schemas';

/**
 * Adaptador para Stripe que implementa todos los puertos
 */
export class StripeAdapter implements StripePort {
  private stripe: Stripe;

  /**
   * Constructor del adaptador
   * @param apiKey Clave API de Stripe (requerido)
   * @param apiVersion Versión de la API de Stripe (opcional)
   */
  constructor(
    apiKey: string,
    apiVersion: Stripe.StripeConfig['apiVersion'] = '2025-02-24.acacia'
  ) {
    this.stripe = new Stripe(apiKey, { apiVersion });
  }

  /**
   * Crea una sesión de checkout
   * @param params Parámetros para crear la sesión
   * @returns Resultado de la operación
   */
  async createCheckoutSession(params: CreateCheckoutSessionParams): Promise<CheckoutSessionResult> {
    try {
      // Validar los parámetros con Zod
      const validatedParams = createCheckoutSessionSchema.parse(params);

      // Crear los parámetros para la sesión de checkout
      const sessionParams: Stripe.Checkout.SessionCreateParams = {
        line_items: [
          {
            price: validatedParams.priceId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: validatedParams.successUrl,
        cancel_url: validatedParams.cancelUrl,
        customer_creation: validatedParams.customerId ? undefined : 'always',
      };

      // Añadir el customerId solo si está definido y no es null
      if (typeof validatedParams.customerId === 'string') {
        sessionParams.customer = validatedParams.customerId;
      }

      // Crear la sesión de checkout
      const session = await this.stripe.checkout.sessions.create(sessionParams);

      // Transformar null a undefined para cumplir con el tipo CheckoutSessionResult
      return {
        success: true,
        sessionId: session.id,
        url: session.url ?? undefined
      };
    } catch (error) {
      console.error('Error al crear la sesión de checkout:', error);
      return { success: false, error };
    }
  }

  /**
   * Crea un portal de cliente
   * @param params Parámetros para crear el portal
   * @returns Resultado de la operación
   */
  async createCustomerPortal(params: CreateCustomerPortalParams): Promise<CustomerPortalResult> {
    try {
      // Validar los parámetros con Zod
      const validatedParams = createCustomerPortalSchema.parse(params);

      // Crear el portal de cliente
      const session = await this.stripe.billingPortal.sessions.create({
        customer: validatedParams.customerId,
        return_url: validatedParams.returnUrl,
      });

      return { success: true, url: session.url };
    } catch (error) {
      console.error('Error al crear el portal de cliente:', error);
      return { success: false, error };
    }
  }

  /**
   * Obtiene un cliente por su ID
   * @param customerId ID del cliente
   * @returns Resultado de la operación
   */
  async getCustomer(customerId: string): Promise<CustomerResult> {
    try {
      // Validar el ID del cliente
      if (!customerId) {
        throw new Error('El ID del cliente es requerido');
      }

      // Obtener el cliente
      const customer = await this.stripe.customers.retrieve(customerId);

      // Verificar si el cliente fue eliminado
      if (customer.deleted) {
        throw new Error('El cliente ha sido eliminado');
      }

      return { success: true, customer: customer as Stripe.Customer };
    } catch (error) {
      console.error('Error al obtener el cliente:', error);
      return { success: false, error };
    }
  }

  /**
   * Obtiene un cliente por su email
   * @param email Email del cliente
   * @returns Resultado de la operación
   */
  async getCustomerByEmail(email: string): Promise<CustomerResult> {
    try {
      // Validar el email
      if (!email) {
        throw new Error('El email es requerido');
      }

      // Buscar clientes con este email
      const customers = await this.stripe.customers.list({
        email,
        limit: 1,
      });

      // Verificar si se encontró algún cliente
      if (customers.data.length > 0) {
        return { success: true, customer: customers.data[0] };
      }

      return { success: false, error: 'Cliente no encontrado' };
    } catch (error) {
      console.error('Error al obtener el cliente por email:', error);
      return { success: false, error };
    }
  }

  /**
   * Crea un nuevo cliente
   * @param params Parámetros para crear el cliente
   * @returns Resultado de la operación
   */
  async createCustomer(params: CreateCustomerParams): Promise<CustomerResult> {
    try {
      // Validar los parámetros con Zod
      const validatedParams = createCustomerSchema.parse(params);

      // Crear el cliente
      const customer = await this.stripe.customers.create({
        email: validatedParams.email,
        name: validatedParams.name,
        metadata: validatedParams.metadata,
      });

      return { success: true, customer };
    } catch (error) {
      console.error('Error al crear el cliente:', error);
      return { success: false, error };
    }
  }

  /**
   * Actualiza un cliente existente
   * @param params Parámetros para actualizar el cliente
   * @returns Resultado de la operación
   */
  async updateCustomer(params: { customerId: string, [key: string]: any }): Promise<CustomerResult> {
    try {
      // Validar el ID del cliente
      if (!params.customerId) {
        throw new Error('El ID del cliente es requerido');
      }

      // Extraer el ID del cliente y los parámetros de actualización
      const { customerId, ...updateParams } = params;

      // Actualizar el cliente
      const customer = await this.stripe.customers.update(customerId, updateParams);

      return { success: true, customer };
    } catch (error) {
      console.error('Error al actualizar el cliente:', error);
      return { success: false, error };
    }
  }

  /**
   * Obtiene las suscripciones de un cliente
   * @param customerId ID del cliente
   * @returns Resultado de la operación
   */
  async getCustomerSubscriptions(customerId: string): Promise<CustomerSubscriptionsResult> {
    try {
      // Validar el ID del cliente
      if (!customerId) {
        throw new Error('El ID del cliente es requerido');
      }

      // Obtener las suscripciones
      const subscriptions = await this.stripe.subscriptions.list({
        customer: customerId,
        status: 'all',
        expand: ['data.default_payment_method'],
      });

      return { success: true, subscriptions: subscriptions.data };
    } catch (error) {
      console.error('Error al obtener las suscripciones del cliente:', error);
      return { success: false, error };
    }
  }

  /**
   * Obtiene todos los productos y precios
   * @returns Resultado de la operación
   */
  async getProductsAndPrices(): Promise<ProductsAndPricesResult> {
    try {
      // Obtener los productos
      const products = await this.stripe.products.list({
        active: true,
        expand: ['data.default_price'],
      });

      return { success: true, products: products.data };
    } catch (error) {
      console.error('Error al obtener productos y precios:', error);
      return { success: false, error };
    }
  }

  /**
   * Verifica un evento de webhook
   * @param params Parámetros para verificar el evento
   * @returns Resultado de la operación
   */
  verifyWebhookEvent(params: VerifyWebhookEventParams): WebhookEventResult {
    try {
      // Validar los parámetros con Zod
      const validatedParams = verifyWebhookEventSchema.parse(params);

      // Verificar el evento
      const event = this.stripe.webhooks.constructEvent(
        validatedParams.payload,
        validatedParams.signature,
        validatedParams.webhookSecret
      );

      return { success: true, event };
    } catch (error) {
      console.error('Error al verificar la firma del webhook:', error);
      return { success: false, error };
    }
  }
}