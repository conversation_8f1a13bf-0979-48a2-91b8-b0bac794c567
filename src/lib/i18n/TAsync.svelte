<script lang="ts">
  import { translationService, language } from './store';
  import { onMount } from 'svelte';

  // Props
  export let key: string;
  export let params: Record<string, string | number> = {};
  export let fallback: string = '';
  
  // Estado local
  let translation = '';
  let isLoading = true;
  let source: 'local' | 'remote' | 'auto' = 'local';
  
  // Cargar traducción de forma asíncrona
  async function loadTranslation() {
    isLoading = true;
    try {
      const result = await translationService.getTranslation(key, $language, params);
      translation = result.text;
      source = result.source;
    } catch (error) {
      console.error(`Error loading translation for ${key}:`, error);
      translation = fallback || key;
    } finally {
      isLoading = false;
    }
  }
  
  // Actualizar cuando cambie el idioma o la clave
  $: if (key && $language) {
    loadTranslation();
  }
  
  // Inicializar
  onMount(() => {
    loadTranslation();
  });
</script>

{#if isLoading}
  <span class="translation-loading">{fallback || key}</span>
{:else}
  <span class="translation-{source}">{translation}</span>
{/if}
