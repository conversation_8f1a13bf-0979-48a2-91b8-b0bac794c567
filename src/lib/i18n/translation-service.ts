import { browser } from '$app/environment';

// Tipos para el servicio de traducción
export type TranslationSource = 'local' | 'remote' | 'auto';

export interface TranslationResult {
  text: string;
  source: TranslationSource;
}

export interface TranslationCache {
  [key: string]: {
    [key: string]: TranslationResult;
  };
}

// Clase para el servicio de traducción
export class TranslationService {
  private cache: TranslationCache = {};
  private localTranslations: Record<string, Record<string, string>> = {};
  private remoteEndpoint: string | null = null;
  private autoTranslateEnabled: boolean = false;
  private defaultLanguage: string = 'en';

  constructor(options: {
    defaultLanguage?: string;
    remoteEndpoint?: string;
    autoTranslateEnabled?: boolean;
  } = {}) {
    this.defaultLanguage = options.defaultLanguage || 'en';
    this.remoteEndpoint = options.remoteEndpoint || null;
    this.autoTranslateEnabled = options.autoTranslateEnabled || false;

    // Cargar caché desde localStorage si está disponible
    if (browser) {
      const cachedTranslations = localStorage.getItem('baberrih_translations_cache');
      if (cachedTranslations) {
        try {
          this.cache = JSON.parse(cachedTranslations);
        } catch (e) {
          console.error('Error parsing cached translations:', e);
          this.cache = {};
        }
      }
    }
  }

  // Cargar traducciones locales
  async loadLocalTranslations(lang: string): Promise<boolean> {
    if (this.localTranslations[lang]) {
      return true; // Ya están cargadas
    }

    try {
      console.log(`Loading local translations for ${lang}...`);
      const response = await fetch(`/translations/${lang}.json`);
      if (!response.ok) {
        console.warn(`No local translations found for ${lang}`);
        return false;
      }
      
      const data = await response.json();
      this.localTranslations[lang] = this.flattenTranslations(data);
      console.log(`Successfully loaded local translations for ${lang}`);
      return true;
    } catch (error) {
      console.error(`Error loading local translations for ${lang}:`, error);
      return false;
    }
  }

  // Aplanar las traducciones para facilitar el acceso
  private flattenTranslations(obj: any, prefix: string = ''): Record<string, string> {
    return Object.keys(obj).reduce((acc: Record<string, string>, key: string) => {
      const prefixedKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        Object.assign(acc, this.flattenTranslations(obj[key], prefixedKey));
      } else {
        acc[prefixedKey] = obj[key];
      }
      
      return acc;
    }, {});
  }

  // Obtener traducción
  async getTranslation(key: string, lang: string, params?: Record<string, string | number>): Promise<TranslationResult> {
    // Verificar caché primero
    if (this.cache[lang]?.[key]) {
      return this.cache[lang][key];
    }

    // Intentar obtener de traducciones locales
    if (this.localTranslations[lang]?.[key]) {
      const text = this.localTranslations[lang][key];
      const result = { text, source: 'local' as TranslationSource };
      this.cacheTranslation(lang, key, result);
      return this.applyParams(result, params);
    }

    // Si no está en el idioma solicitado, intentar con el idioma predeterminado
    if (lang !== this.defaultLanguage && this.localTranslations[this.defaultLanguage]?.[key]) {
      const text = this.localTranslations[this.defaultLanguage][key];
      const result = { text, source: 'local' as TranslationSource };
      this.cacheTranslation(lang, key, result);
      return this.applyParams(result, params);
    }

    // Si está habilitado el endpoint remoto, intentar obtener de ahí
    if (this.remoteEndpoint) {
      try {
        const remoteResult = await this.fetchRemoteTranslation(key, lang);
        if (remoteResult) {
          this.cacheTranslation(lang, key, remoteResult);
          return this.applyParams(remoteResult, params);
        }
      } catch (error) {
        console.error(`Error fetching remote translation for ${key} in ${lang}:`, error);
      }
    }

    // Si está habilitada la traducción automática, intentar traducir automáticamente
    if (this.autoTranslateEnabled && lang !== this.defaultLanguage) {
      try {
        // Obtener el texto en el idioma predeterminado
        const defaultText = this.localTranslations[this.defaultLanguage]?.[key] || key;
        const autoResult = await this.autoTranslate(defaultText, this.defaultLanguage, lang);
        if (autoResult) {
          this.cacheTranslation(lang, key, autoResult);
          return this.applyParams(autoResult, params);
        }
      } catch (error) {
        console.error(`Error auto-translating ${key} to ${lang}:`, error);
      }
    }

    // Si todo falla, devolver la clave como texto
    return this.applyParams({ text: key, source: 'local' }, params);
  }

  // Aplicar parámetros a la traducción
  private applyParams(result: TranslationResult, params?: Record<string, string | number>): TranslationResult {
    if (!params) return result;
    
    let text = result.text;
    Object.entries(params).forEach(([param, value]) => {
      text = text.replace(new RegExp(`{${param}}`, 'g'), String(value));
    });
    
    return { ...result, text };
  }

  // Obtener traducción remota
  private async fetchRemoteTranslation(key: string, lang: string): Promise<TranslationResult | null> {
    if (!this.remoteEndpoint) return null;
    
    try {
      const response = await fetch(`${this.remoteEndpoint}?key=${encodeURIComponent(key)}&lang=${lang}`);
      if (!response.ok) return null;
      
      const data = await response.json();
      return { text: data.text, source: 'remote' };
    } catch (error) {
      console.error('Error fetching remote translation:', error);
      return null;
    }
  }

  // Traducción automática (implementación básica, se puede mejorar con APIs reales)
  private async autoTranslate(text: string, fromLang: string, toLang: string): Promise<TranslationResult | null> {
    // Aquí se podría integrar con servicios como Google Translate, DeepL, etc.
    // Por ahora, simplemente devolvemos el texto original con un prefijo
    console.log(`Auto-translating from ${fromLang} to ${toLang}: ${text}`);
    return { text: `[${toLang}] ${text}`, source: 'auto' };
  }

  // Guardar en caché
  private cacheTranslation(lang: string, key: string, result: TranslationResult): void {
    if (!this.cache[lang]) {
      this.cache[lang] = {};
    }
    
    this.cache[lang][key] = result;
    
    // Guardar en localStorage si está disponible
    if (browser) {
      try {
        localStorage.setItem('baberrih_translations_cache', JSON.stringify(this.cache));
      } catch (e) {
        console.warn('Error saving translations cache to localStorage:', e);
      }
    }
  }

  // Limpiar caché
  clearCache(): void {
    this.cache = {};
    if (browser) {
      localStorage.removeItem('baberrih_translations_cache');
    }
  }
}
