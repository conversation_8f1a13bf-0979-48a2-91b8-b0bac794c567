import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';
import { TranslationService } from './translation-service';

// Define supported languages
export const supportedLanguages = [
  { code: "en", name: "English", flag: "/images/flags/gb.svg" },
  { code: "fr", name: "Fran<PERSON>", flag: "/images/flags/fr.svg" },
  { code: "es", name: "Español", flag: "/images/flags/es.svg" },
  { code: "de", name: "<PERSON><PERSON><PERSON>", flag: "/images/flags/de.svg" },
  { code: "ar", name: "العربية", flag: "/images/flags/sa.svg" },
  { code: "ru", name: "Русский", flag: "/images/flags/ru.svg" },
];

// Configuración para añadir nuevos idiomas fácilmente
export const availableLanguages = writable<string[]>(supportedLanguages.map(l => l.code));

// Define language store
export const language = writable<string>(getInitialLanguage());

// Crear instancia del servicio de traducción
export const translationService = new TranslationService({
  defaultLanguage: 'en',
  // Descomentar para habilitar traducción automática
  // autoTranslateEnabled: true,
  // Descomentar y configurar para usar un endpoint remoto de traducción
  // remoteEndpoint: 'https://api.example.com/translate',
});

// Function to get initial language from localStorage or browser settings
function getInitialLanguage(): string {
  if (!browser) return 'en'; // Default to English on server

  // Check localStorage first
  const storedLang = localStorage.getItem('baberrih_language');
  if (storedLang && isLanguageSupported(storedLang)) {
    return storedLang;
  }

  // Check URL path
  const urlLang = window.location.pathname.split('/')[1];
  if (urlLang && isLanguageSupported(urlLang)) {
    return urlLang;
  }

  // Check browser language
  const browserLang = navigator.language.split('-')[0];
  if (isLanguageSupported(browserLang)) {
    return browserLang;
  }

  // Default to English
  return 'en';
}

// Helper function to check if a language is supported
export function isLanguageSupported(lang: string): boolean {
  return get(availableLanguages).includes(lang);
}

// Function to set language and save to localStorage
export function setLanguage(lang: string): void {
  if (!isLanguageSupported(lang)) {
    console.warn(`Language ${lang} is not supported. Defaulting to English.`);
    lang = 'en';
  }
  
  language.set(lang);
  
  if (browser) {
    localStorage.setItem('baberrih_language', lang);
  }
}

// Function to add a new language dynamically
export function addLanguage(langCode: string, langName: string, flagPath?: string): void {
  if (isLanguageSupported(langCode)) {
    console.warn(`Language ${langCode} is already supported.`);
    return;
  }
  
  // Añadir a la lista de idiomas disponibles
  availableLanguages.update(langs => [...langs, langCode]);
  
  // Añadir a la lista de idiomas soportados con UI
  if (langName) {
    const flag = flagPath || `/images/flags/${langCode}.svg`;
    supportedLanguages.push({ code: langCode, name: langName, flag });
  }
  
  console.log(`Added support for language: ${langCode} (${langName})`);
}

// Function to load translations for a specific language
export async function loadTranslations(lang: string): Promise<void> {
  await translationService.loadLocalTranslations(lang);
}

// Store for translations (mantener para compatibilidad)
export const translations = writable<Record<string, Record<string, string>>>({});

// Derived store for current translations
export const t = derived(
  [language],
  ([$language]) => {
    // Function to get translation by key
    return (key: string, params?: Record<string, string | number>): string => {
      // Implementación síncrona simplificada
      // Esta versión es compatible con la anterior pero usa el servicio internamente
      try {
        // Obtener traducciones del idioma actual
        const langTranslations = translationService.localTranslations[$language] || {};
        
        // Obtener la traducción o usar fallback a inglés o la clave misma
        let translation = '';
        
        // Buscar en la estructura plana
        if (langTranslations[key]) {
          translation = langTranslations[key];
        } 
        // Fallback a inglés
        else if (translationService.localTranslations['en'] && translationService.localTranslations['en'][key]) {
          translation = translationService.localTranslations['en'][key];
        }
        // Usar la clave como último recurso
        else {
          translation = key;
        }
        
        // Reemplazar parámetros si se proporcionan
        if (params) {
          Object.entries(params).forEach(([param, value]) => {
            translation = translation.replace(new RegExp(`{${param}}`, 'g'), String(value));
          });
        }
        
        return translation;
      } catch (error) {
        console.error(`Error getting translation for ${key}:`, error);
        return key;
      }
    };
  }
);

// Initialize translations for current language
if (browser) {
  language.subscribe(async (lang) => {
    await loadTranslations(lang);
  });
}
