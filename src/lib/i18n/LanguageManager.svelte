<script lang="ts">
  import { addLanguage, supportedLanguages, language, setLanguage } from './store';
  import { onMount } from 'svelte';
  
  // Estado local
  let newLangCode = '';
  let newLangName = '';
  let message = '';
  let messageType = 'info';
  
  // Función para añadir un nuevo idioma
  function handleAddLanguage() {
    if (!newLangCode || !newLangName) {
      showMessage('Por favor, introduce el código y nombre del idioma', 'error');
      return;
    }
    
    if (!/^[a-z]{2}$/.test(newLangCode)) {
      showMessage('El código de idioma debe ser de 2 letras (ej: es, en, fr)', 'error');
      return;
    }
    
    try {
      addLanguage(newLangCode, newLangName);
      showMessage(`Idioma ${newLangName} (${newLangCode}) añadido correctamente`, 'success');
      newLangCode = '';
      newLangName = '';
    } catch (error) {
      showMessage(`Error al añadir el idioma: ${error.message}`, 'error');
    }
  }
  
  // Función para mostrar mensajes
  function showMessage(text: string, type: 'info' | 'success' | 'error' = 'info') {
    message = text;
    messageType = type;
    
    // Ocultar el mensaje después de 5 segundos
    setTimeout(() => {
      message = '';
    }, 5000);
  }
</script>

<div class="language-manager">
  <h3 class="text-lg font-medium mb-4">Gestor de Idiomas</h3>
  
  <div class="mb-6">
    <h4 class="text-sm font-medium mb-2">Idiomas Soportados</h4>
    <div class="grid grid-cols-2 gap-2">
      {#each supportedLanguages as lang}
        <div class="flex items-center p-2 border rounded {$language === lang.code ? 'bg-primary-100 border-primary-300' : 'border-gray-200'}">
          <img src={lang.flag} alt={lang.code} class="w-5 h-5 mr-2" />
          <span>{lang.name} ({lang.code})</span>
        </div>
      {/each}
    </div>
  </div>
  
  <div class="mb-6">
    <h4 class="text-sm font-medium mb-2">Añadir Nuevo Idioma</h4>
    <div class="space-y-3">
      <div>
        <label class="block text-sm mb-1" for="langCode">Código (2 letras)</label>
        <input 
          type="text" 
          id="langCode" 
          bind:value={newLangCode} 
          class="w-full p-2 border rounded" 
          placeholder="it"
          maxlength="2"
        />
      </div>
      <div>
        <label class="block text-sm mb-1" for="langName">Nombre</label>
        <input 
          type="text" 
          id="langName" 
          bind:value={newLangName} 
          class="w-full p-2 border rounded" 
          placeholder="Italiano"
        />
      </div>
      <button 
        on:click={handleAddLanguage}
        class="px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600"
      >
        Añadir Idioma
      </button>
    </div>
  </div>
  
  {#if message}
    <div class="p-3 rounded {messageType === 'error' ? 'bg-red-100 text-red-800' : messageType === 'success' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}">
      {message}
    </div>
  {/if}
</div>
