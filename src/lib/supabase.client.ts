/**
 * Cliente de Supabase para el navegador
 * Este archivo exporta una instancia de Supabase para usar en el cliente
 *
 * IMPORTANTE: Este archivo ahora usa @supabase/ssr en lugar de @supabase/supabase-js
 * para mantener consistencia con el enfoque del servidor y mejorar la sincronización
 * de la sesión entre cliente y servidor.
 */
import { createBrowserClient } from '@supabase/ssr';
import { env } from '$env/dynamic/public';
import { browser } from '$app/environment';

// Asegurarse de que las variables de entorno estén definidas
const supabaseUrl = env.PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = env.PUBLIC_SUPABASE_ANON_KEY || '';

// Crear una instancia de Supabase para el cliente usando el enfoque SSR
// Esto asegura que las cookies se manejen correctamente
export const supabase = browser
  ? createBrowserClient(supabaseUrl, supabaseAnonKey)
  : null; // Evitar errores en SSR

// Función para obtener una nueva instancia del cliente
// Útil para forzar la creación de un cliente fresco
export function getSupabaseClient() {
  if (!browser) return null;
  return createBrowserClient(supabaseUrl, supabaseAnonKey);
}
