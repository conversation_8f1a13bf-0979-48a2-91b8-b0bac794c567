/**
 * Interfaces y tipos para la API de Cloudbeds
 */

// Tipos para autenticación
export interface CloudbedsAuthTokens {
  readonly accessToken: string;
  readonly refreshToken?: string;
  readonly expiresIn?: number;
  readonly tokenType?: string;
}

// Interfaces para habitaciones
export interface CloudbedsRoom {
  readonly id: string;
  readonly name: string;
  readonly description?: string;
  readonly maxOccupancy?: number;
  readonly basePrice?: number;
  readonly roomTypeId?: string;
  readonly amenities?: string[];
  readonly photos?: string[];
  readonly isActive?: boolean;
}

export interface CloudbedsRoomType {
  readonly id?: string;
  readonly roomTypeID?: string;
  readonly propertyID?: string;
  readonly name?: string;
  readonly roomTypeName?: string;
  readonly roomTypeNameShort?: string;
  readonly description?: string;
  readonly roomTypeDescription?: string;
  readonly maxOccupancy?: number;
  readonly maxGuests?: number;
  readonly adultsIncluded?: number;
  readonly childrenIncluded?: number;
  readonly isPrivate?: boolean;
  readonly rooms?: CloudbedsRoom[];
  // Fotos y amenidades
  readonly photos?: string[];
  readonly roomTypePhotos?: string[];
  readonly amenities?: string[];
  readonly roomTypeFeatures?: Record<string, string>;
  readonly isVirtual?: boolean;
  readonly linkedRoomIDs?: string[];
  readonly linkedRoomTypeIDs?: string[];
  readonly linkedRoomTypeQty?: {
    readonly roomTypeId: string;
    readonly roomQty: number;
  }[];
  readonly roomsAvailable?: number;
  readonly roomTypeUnits?: number;
}

// Interfaces para disponibilidad
export interface CloudbedsAvailabilityDate {
  readonly date: string;
  readonly available: number;
  readonly price?: number;
  readonly minimumStay?: number;
  readonly maximumStay?: number;
  readonly status?: 'available' | 'unavailable' | 'restricted';
}

// Nueva estructura para la información de disponibilidad
export interface CloudbedsRoomTypePhoto {
  readonly thumb: string;
  readonly image: string;
}

export interface CloudbedsRoomTypeAvailability {
  readonly roomTypeID: string;
  readonly roomTypeName: string;
  readonly roomTypeNameShort: string;
  readonly roomTypeDescription?: string;
  readonly maxGuests: string;
  readonly adultsIncluded: string;
  readonly childrenIncluded: number;
  readonly roomTypePhotos?: CloudbedsRoomTypePhoto[];
  readonly roomTypeFeatures?: string[];
  readonly roomRateID?: string;
  readonly roomRate?: number;
  readonly ratePlanNamePublic?: string;
  readonly ratePlanNamePrivate?: string;
  readonly roomsAvailable: number;
  readonly adultsExtraCharge?: Record<string, number>;
  readonly childrenExtraCharge?: Record<string, number>;
}

export interface CloudbedsPropertyCurrency {
  readonly currencyCode: string;
  readonly currencySymbol: string;
  readonly currencyPosition: string;
}

export interface CloudbedsPropertyAvailability {
  readonly propertyID: string;
  readonly propertyCurrency: CloudbedsPropertyCurrency;
  readonly propertyRooms: CloudbedsRoomTypeAvailability[];
}

export interface CloudbedsRoomAvailability {
  readonly roomId?: string;
  readonly roomTypeId?: string;
  readonly dates?: CloudbedsAvailabilityDate[];
  // Para compatibilidad con la nueva estructura
  readonly propertyID?: string;
  readonly propertyCurrency?: CloudbedsPropertyCurrency;
  readonly propertyRooms?: CloudbedsRoomTypeAvailability[];
}

// Interfaces para respuestas de la API
export interface CloudbedsApiResponse<T> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: {
    readonly code: string;
    readonly message: string;
  };
  readonly message?: string;
}

// Interfaces para crear reservas
export interface CloudbedsReservationGuest {
  readonly firstName: string;
  readonly lastName: string;
  readonly email: string;
  readonly phone?: string;
  readonly address?: string;
  readonly city?: string;
  readonly state?: string;
  readonly country?: string;
  readonly postalCode?: string;
  readonly notes?: string;
}

export interface CloudbedsReservationRoom {
  readonly roomTypeID: string;
  readonly startDate: string;
  readonly endDate: string;
  readonly adults: number;
  readonly children?: number;
  readonly ratePlanID?: string;
  readonly notes?: string;
  readonly roomName?: string; // Added for dynamic room type ID resolution
}

export interface CloudbedsReservationRequest {
  readonly propertyID: string;
  readonly sourceID?: string | number; // Actualizado para permitir formatos como 's-2-1'
  readonly guestData: CloudbedsReservationGuest;
  readonly roomsData: CloudbedsReservationRoom[];
  readonly status?: 'confirmed' | 'not_confirmed' | 'canceled';
  readonly thirdPartyIdentifier: string; // Identificador único de la reserva en tu sistema
  readonly sendEmailConfirmation: boolean; // Controla si se envía email de confirmación
  readonly startDate?: string; // Fecha de inicio global para la reserva (formato YYYY-MM-DD)
  readonly endDate?: string; // Fecha de fin global para la reserva (formato YYYY-MM-DD)
}

export interface CloudbedsReservationResponse {
  readonly reservationID: string;
  readonly confirmationCode: string;
  readonly status?: string;
  readonly message?: string;
}

export interface CloudbedsRoomsResponse extends CloudbedsApiResponse<CloudbedsRoom[]> { }
export interface CloudbedsRoomTypesResponse extends CloudbedsApiResponse<CloudbedsRoomType[]> { }
export interface CloudbedsAvailabilityResponse extends CloudbedsApiResponse<CloudbedsRoomAvailability[]> { }
export interface CloudbedsCreateReservationResponse {
  readonly success: boolean;
  readonly reservationID?: string;
  readonly confirmationCode?: string;
  readonly message?: string;
  readonly error?: {
    readonly code?: string;
    readonly message?: string;
  };
  readonly data?: {
    readonly reservationID?: string;
    readonly confirmationCode?: string;
  };
}