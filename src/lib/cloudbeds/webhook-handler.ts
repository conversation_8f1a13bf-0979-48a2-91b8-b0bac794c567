import { RoomTypeCache } from './room-type-cache';
import type { CloudbedsApiService } from './api.server';

/**
 * Handles Cloudbeds webhook events for room type cache updates
 */
export class WebhookHandler {
  constructor(private readonly roomTypeCache: RoomTypeCache) {}

  /**
   * Handle a Cloudbeds webhook event
   * @param event The webhook event data
   * @returns A response indicating success or failure
   */
  async handleWebhookEvent(event: any): Promise<{ success: boolean, message: string }> {
    try {
      // Extract event information
      const eventType = event.event;
      const propertyId = event.propertyId || event.propertyID || event.propertyID_str;

      if (!eventType || !propertyId) {
        return {
          success: false,
          message: 'Invalid webhook event: missing event type or property ID'
        };
      }

      console.log(`Received webhook event: ${eventType} for property ${propertyId}`);

      // Handle the event
      await this.roomTypeCache.handleWebhookEvent(eventType, propertyId);

      return {
        success: true,
        message: `Successfully processed webhook event: ${eventType}`
      };
    } catch (error) {
      console.error('Error handling webhook event:', error);
      return {
        success: false,
        message: `Error handling webhook event: ${error.message}`
      };
    }
  }

  /**
   * Register webhook subscriptions with Cloudbeds
   * @param apiService The Cloudbeds API service
   * @param endpointUrl Your webhook endpoint URL
   * @param propertyId The property ID
   * @returns A response indicating success or failure
   */
  async registerWebhooks(
    apiService: CloudbedsApiService,
    endpointUrl: string,
    propertyId: string
  ): Promise<{ success: boolean, message: string }> {
    try {
      // Events to subscribe to
      const events = [
        { object: 'reservation', action: 'accommodation_type_changed' },
        { object: 'reservation', action: 'accommodation_changed' },
        { object: 'night_audit', action: 'completed' }
      ];

      // Register each webhook
      for (const event of events) {
        // This is a placeholder - the actual implementation would depend on
        // the Cloudbeds API for registering webhooks
        console.log(`Would register webhook for ${event.object}/${event.action} to ${endpointUrl}`);
        
        // Example of how this might be implemented:
        // await apiService.makeApiRequest('/postWebhook', 'POST', {
        //   endpointUrl,
        //   object: event.object,
        //   action: event.action,
        //   propertyID: propertyId
        // });
      }

      return {
        success: true,
        message: 'Successfully registered webhooks'
      };
    } catch (error) {
      console.error('Error registering webhooks:', error);
      return {
        success: false,
        message: `Error registering webhooks: ${error.message}`
      };
    }
  }
}
