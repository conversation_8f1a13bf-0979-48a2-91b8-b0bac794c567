import type {
  CloudbedsAuthTokens,
  CloudbedsRoomsResponse,
  CloudbedsRoomTypesResponse,
  CloudbedsAvailabilityResponse,
  CloudbedsReservationRequest,
  CloudbedsCreateReservationResponse
} from './types';

/**
 * Servicio para interactuar con la API de Cloudbeds desde el cliente
 * Este servicio utiliza endpoints del servidor para no exponer credenciales en el cliente
 */
export class CloudbedsApiClient {
  private readonly baseUrl: string = '/api/cloudbeds';

  /**
   * Realiza una petición a los endpoints del servidor
   * @param endpoint - Endpoint del servidor
   * @param method - Método HTTP
   * @param params - Parámetros de la petición
   * @returns Respuesta del servidor
   */
  private async makeApiRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' = 'GET',
    params: Record<string, any> = {}
  ): Promise<T> {
    console.log(`[CloudbedsApiClient] Iniciando solicitud ${method} a ${endpoint}`);

    const url = new URL(`${this.baseUrl}${endpoint}`, window.location.origin);

    let options: RequestInit = {
      method
    };

    // Si es GET, añadir parámetros a la URL
    if (method === 'GET') {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, value.toString());
        }
      });
      console.log(`[CloudbedsApiClient] URL completa: ${url.toString()}`);
    } else if (method === 'POST') {
      // Si es POST, añadir parámetros al body
      options.headers = {
        'Content-Type': 'application/json'
      };
      options.body = JSON.stringify(params);
      console.log(`[CloudbedsApiClient] Enviando datos POST: ${options.body}`);
    }

    try {
      console.log(`[CloudbedsApiClient] Enviando solicitud a ${url.toString()}...`);
      const response = await fetch(url.toString(), options);

      // Intentar leer la respuesta como texto para poder depurar
      const responseText = await response.text();
      console.log(`[CloudbedsApiClient] Respuesta recibida (${response.status}): ${responseText.substring(0, 200)}${responseText.length > 200 ? '...' : ''}`);

      // Intentar parsear la respuesta como JSON incluso si el status no es OK
      // Esto es importante porque la API puede devolver errores con status 200
      try {
        const jsonData = JSON.parse(responseText) as T;
        console.log('[CloudbedsApiClient] Respuesta JSON parseada correctamente:', jsonData);

        // Si la respuesta tiene success=false pero el status es 200, seguimos procesando
        if (!response.ok) {
          console.error(`[CloudbedsApiClient] Error en la petición (${response.status}): ${response.statusText}`, responseText);
          return jsonData;
        }

        return jsonData;
      } catch (parseError) {
        console.error('[CloudbedsApiClient] Error al parsear la respuesta como JSON:', parseError);

        if (!response.ok) {
          throw new Error(`Error en la petición: ${response.statusText} - ${responseText}`);
        }

        throw new Error(`Error al parsear la respuesta como JSON: ${parseError instanceof Error ? parseError.message : 'Error desconocido'}`);
      }

      // Este código nunca se ejecutará porque ya hemos manejado todos los casos arriba
    } catch (fetchError) {
      console.error('[CloudbedsApiClient] Error al realizar la solicitud fetch:', fetchError);
      throw fetchError;
    }
  }

  /**
   * Obtiene la lista de tipos de habitaciones
   * @param includePhotos - Incluir fotos de los tipos de habitación
   * @param includeAmenities - Incluir amenities de los tipos de habitación
   * @returns Lista de tipos de habitaciones
   */
  async getRoomTypes(includePhotos: boolean = true, includeAmenities: boolean = true): Promise<CloudbedsRoomTypesResponse> {
    const params: Record<string, any> = {};

    if (includePhotos) {
      params.includePhotos = '1';
    }

    if (includeAmenities) {
      params.includeAmenities = '1';
    }

    return this.makeApiRequest<CloudbedsRoomTypesResponse>('/room-types', 'GET', params);
  }

  /**
   * Obtiene la lista de habitaciones
   * @param includePhotos - Incluir fotos de las habitaciones
   * @param includeAmenities - Incluir amenities de las habitaciones
   * @param includeRoomRelations - Incluir relaciones entre habitaciones (habitaciones físicas y virtuales)
   * @returns Lista de habitaciones
   */
  async getRooms(includePhotos: boolean = true, includeAmenities: boolean = true, includeRoomRelations: boolean = true): Promise<CloudbedsRoomsResponse> {
    const params: Record<string, any> = {};

    if (includePhotos) {
      params.includePhotos = '1';
    }

    if (includeAmenities) {
      params.includeAmenities = '1';
    }

    if (includeRoomRelations) {
      params.includeRoomRelations = '1';
    }

    return this.makeApiRequest<CloudbedsRoomsResponse>('/rooms', 'GET', params);
  }

  /**
   * Obtiene información detallada de un tipo de habitación específico
   * @param roomTypeId - ID del tipo de habitación
   * @param includePhotos - Incluir fotos del tipo de habitación
   * @param includeAmenities - Incluir amenities del tipo de habitación
   * @returns Información detallada del tipo de habitación
   */
  async getRoomTypeDetails(
    roomTypeId: string,
    includePhotos: boolean = true,
    includeAmenities: boolean = true
  ): Promise<CloudbedsRoomTypesResponse> {
    const params: Record<string, any> = {
      roomTypeId
    };

    if (includePhotos) {
      params.includePhotos = '1';
    }

    if (includeAmenities) {
      params.includeAmenities = '1';
    }

    return this.makeApiRequest<CloudbedsRoomTypesResponse>('/room-type-details', 'GET', params);
  }

  /**
   * Obtiene la disponibilidad de habitaciones para un rango de fechas
   * @param startDate - Fecha de inicio (formato YYYY-MM-DD)
   * @param endDate - Fecha de fin (formato YYYY-MM-DD)
   * @param roomTypeId - ID del tipo de habitación (opcional)
   * @returns Disponibilidad de habitaciones
   */
  async getAvailability(
    startDate: string,
    endDate: string,
    roomTypeId?: string
  ): Promise<CloudbedsAvailabilityResponse> {
    console.group("CloudbedsApiClient - getAvailability");
    console.log("Parameters:", { startDate, endDate, roomTypeId });

    const params: Record<string, any> = {
      startDate,
      endDate
    };

    if (roomTypeId) {
      params.roomTypeId = roomTypeId;
      console.log("Room Type ID included in request");
    } else {
      console.warn("No Room Type ID provided for availability request");
    }

    try {
      console.time("API request duration");
      const response = await this.makeApiRequest<CloudbedsAvailabilityResponse>('/availability', 'GET', params);
      console.timeEnd("API request duration");

      console.log("API Response:", response);

      if (!response.success) {
        console.error("API Error:", response.error);
      } else if (!response.data || response.data.length === 0) {
        console.warn("API returned success but no data");
      } else {
        console.log("API returned data for", response.data.length, "room types");
        console.log("First room availability:", response.data[0]);
      }

      return response;
    } catch (error) {
      console.error("Exception in getAvailability:", error);
      throw error;
    } finally {
      console.groupEnd();
    }
  }

  /**
   * Crea una nueva reserva
   * @param reservationData - Datos de la reserva
   * @returns Respuesta de la creación de la reserva
   */
  async createReservation(
    reservationData: CloudbedsReservationRequest
  ): Promise<CloudbedsCreateReservationResponse> {
    try {
      // Validaciones básicas antes de enviar al servidor
      if (!reservationData.guestData) {
        return {
          success: false,
          error: {
            code: 'MISSING_GUEST_DATA',
            message: 'Los datos del huésped son obligatorios'
          }
        };
      }

      if (!reservationData.roomsData || reservationData.roomsData.length === 0) {
        return {
          success: false,
          error: {
            code: 'MISSING_ROOM_DATA',
            message: 'Se requiere al menos una habitación'
          }
        };
      }

      // Asegurarse de que se incluyan todos los campos requeridos
      let completeReservationData = {
        ...reservationData
      };

      // Obtener el propertyID de la variable de entorno o solicitar al servidor
      if (!completeReservationData.propertyID) {
        // Intentar usar la variable de entorno CLOUDBEDS_PROPERTY_ID si está disponible en el cliente
        const propertyIdFromEnv = typeof window !== 'undefined' ?
          (window as any).__CLOUDBEDS_PROPERTY_ID__ : null;

        if (propertyIdFromEnv) {
          completeReservationData.propertyID = propertyIdFromEnv;
        } else {
          // Si no está disponible, usaremos un valor temporal y el servidor lo reemplazará
          completeReservationData.propertyID = '0';
        }
      }

      // Asegurarse de que se incluya thirdPartyIdentifier
      if (!completeReservationData.thirdPartyIdentifier) {
        const timestamp = new Date().getTime();
        const randomId = Math.floor(Math.random() * 10000);
        completeReservationData.thirdPartyIdentifier = `client-${timestamp}-${randomId}`;
      }

      // Asegurarse de que se incluya sendEmailConfirmation
      if (completeReservationData.sendEmailConfirmation === undefined) {
        completeReservationData.sendEmailConfirmation = true;
      }

      // Asegurarse de que se incluyan startDate y endDate a nivel de reserva
      if (completeReservationData.roomsData && completeReservationData.roomsData.length > 0) {
        const firstRoom = completeReservationData.roomsData[0];
        if (firstRoom.startDate && firstRoom.endDate) {
          completeReservationData.startDate = firstRoom.startDate;
          completeReservationData.endDate = firstRoom.endDate;
        }
      }

      console.log('Enviando solicitud de reserva:', completeReservationData);
      return this.makeApiRequest<CloudbedsCreateReservationResponse>(
        '/reservation',
        'POST',
        completeReservationData
      );
    } catch (error) {
      console.error('Error al crear reserva desde el cliente:', error);
      return {
        success: false,
        error: {
          code: 'CLIENT_ERROR',
          message: error instanceof Error ? error.message : 'Error desconocido al crear la reserva'
        }
      };
    }
  }
}

export const cloudbedsApiService = new CloudbedsApiClient();