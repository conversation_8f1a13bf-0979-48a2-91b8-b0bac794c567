import { env } from '$env/dynamic/private';
import type {
  CloudbedsAuthTokens,
  CloudbedsRoomType,
  CloudbedsRoomTypesResponse,
  CloudbedsAvailabilityResponse,
  CloudbedsRoomsResponse,
  CloudbedsReservationRequest,
  CloudbedsReservationResponse,
  CloudbedsCreateReservationResponse,
  CloudbedsApiResponse
} from './types';
import { z } from 'zod';
import { RoomTypeCache } from './room-type-cache';
import { RoomTypeResolver } from './room-type-resolver';

/**
 * Servicio para interactuar con la API de Cloudbeds (lado del servidor)
 */
export class CloudbedsApiService {
  private readonly baseUrl: string = 'https://api.cloudbeds.com/api/v1.2';
  private readonly apiKey: string;
  private readonly clientId: string;
  private readonly clientSecret: string;
  private propertyId: string = '';
  private authTokens: CloudbedsAuthTokens | null = null;
  private tokenExpiration: number = 0;
  private credentialsVerified: boolean = false;

  // Cache for room types
  private roomTypesCache: CloudbedsRoomType[] = [];
  private roomTypesCacheExpiration: number = 0;
  private readonly CACHE_DURATION_MS: number = 3600000; // 1 hour cache duration

  // Room type mapping for name-based lookups
  private roomTypeMapping: Map<string, string> = new Map(); // Maps name patterns to IDs

  // Dynamic room type resolution system
  private roomTypeCache: RoomTypeCache;
  private roomTypeResolver: RoomTypeResolver;

  /**
   * Constructor para el servicio de API de Cloudbeds
   */
  constructor() {
    this.apiKey = env.CLOUDBEDS_API_KEY || '';
    this.clientId = env.CLOUDBEDS_CLIENT_ID || '';
    this.clientSecret = env.CLOUDBEDS_CLIENT_SECRET || '';
    this.propertyId = env.CLOUDBEDS_PROPERTY_ID || '';

    // Initialize the dynamic room type resolution system
    this.roomTypeCache = new RoomTypeCache(this as any, this.CACHE_DURATION_MS);
    this.roomTypeResolver = new RoomTypeResolver(this.roomTypeCache);

    // Initialize the service
    this.initialize().catch(error => {
      console.error('Error initializing CloudbedsApiService:', error);
    });
  }

  /**
   * Initialize the service by fetching and caching room types
   */
  private async initialize(): Promise<void> {
    try {
      await this.loadRoomTypes();
    } catch (error) {
      console.error('Error loading room types during initialization:', error);
    }
  }

  /**
   * Obtiene un token de acceso para la API de Cloudbeds
   * @returns Token de acceso
   */
  private async getAccessToken(): Promise<string> {
    console.log('Obteniendo token de acceso...');

    const now = Date.now();

    // Si tenemos un token OAuth válido y no ha expirado, lo devolvemos
    if (this.authTokens?.accessToken && now < this.tokenExpiration) {
      console.log('Usando token OAuth existente');
      return this.authTokens.accessToken;
    }

    // Si tenemos un token de actualización, intentamos renovar el token
    if (this.authTokens?.refreshToken) {
      try {
        console.log('Intentando renovar token con refreshToken');
        const url = `${this.baseUrl}/access_token`;
        const formData = new URLSearchParams();
        formData.append('grant_type', 'refresh_token');
        formData.append('client_id', this.clientId);
        formData.append('client_secret', this.clientSecret);
        formData.append('refresh_token', this.authTokens.refreshToken);

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
          },
          body: formData
        });

        if (response.ok) {
          const data = await response.json();
          this.authTokens = {
            accessToken: data.access_token,
            refreshToken: data.refresh_token,
            expiresIn: data.expires_in,
            tokenType: data.token_type
          };

          // Guardamos la expiración (restamos 60 segundos por seguridad)
          this.tokenExpiration = now + ((data.expires_in - 60) * 1000);
          console.log('Token renovado exitosamente');
          return this.authTokens.accessToken;
        } else {
          console.warn('No se pudo renovar el token, se intentará con el API Key');
        }
      } catch (error) {
        console.error('Error al renovar token:', error);
      }
    }

    // Para la API v1.2, podemos usar directamente el API Key como token de autorización
    if (this.apiKey) {
      console.log('No se pudo obtener token OAuth, usando API Key');
      return this.apiKey;
    }

    throw new Error('No hay métodos de autenticación disponibles');
  }

  /**
   * Establece un token OAuth externo
   * @param accessToken Token de acceso
   * @param refreshToken Token de actualización
   * @param expiresIn Tiempo de expiración en segundos
   * @param tokenType Tipo de token
   */
  public setOAuthToken(accessToken: string, refreshToken: string, expiresIn: number, tokenType: string): void {
    console.log('Estableciendo token OAuth externo');
    this.authTokens = {
      accessToken,
      refreshToken,
      expiresIn,
      tokenType
    };

    // Establecer tiempo de expiración (restar 60 segundos por seguridad)
    const now = Date.now();
    this.tokenExpiration = now + ((expiresIn - 60) * 1000);
  }

  /**
   * Obtiene la información de las propiedades del usuario
   * @returns ID de la propiedad del usuario
   */
  private async getPropertyId(): Promise<string> {
    console.log('Obteniendo Property ID...');

    // Si ya tenemos un property ID, lo devolvemos
    if (this.propertyId) {
      console.log('Usando Property ID cacheado:', this.propertyId);
      return this.propertyId;
    }

    try {
      const accessToken = await this.getAccessToken();

      // Obtenemos la lista de propiedades
      const url = `${this.baseUrl}/getHotels`;
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Error obteniendo propiedades: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.data && data.data.length > 0) {
        // Guardamos el ID de la primera propiedad
        this.propertyId = data.data[0].id;
        console.log('Property ID obtenido:', this.propertyId);
        return this.propertyId;
      } else {
        throw new Error('No se encontraron propiedades');
      }
    } catch (error) {
      console.error('Error al obtener property ID:', error);
      throw new Error('Error obteniendo información de propiedades');
    }
  }

  /**
   * Método público para obtener el ID de la propiedad
   * @returns ID de la propiedad del usuario
   */
  public async fetchPropertyId(): Promise<string> {
    return this.getPropertyId();
  }

  /**
   * Verifica las credenciales de API con Cloudbeds
   * @returns Información del token
   */
  private async verifyApiCredentials(): Promise<void> {
    try {
      console.log('Verificando credenciales de API con Cloudbeds...');

      // 1. Probar uso directo de API Key como header
      console.log('Método 1: Uso directo de API Key como header x-api-key');
      const headers1: Record<string, string> = {
        'x-api-key': this.apiKey,
        'Accept': 'application/json'
      };

      if (this.propertyId) {
        headers1['X-PROPERTY-ID'] = this.propertyId;
      }

      try {
        const response1 = await fetch(`${this.baseUrl}/getHotels`, {
          method: 'GET',
          headers: headers1
        });

        const text1 = await response1.text();
        console.log('Respuesta método 1:', text1);

        // Intentar parsear como JSON si es posible
        try {
          const json1 = JSON.parse(text1);
          console.log('Respuesta método 1 (JSON):', json1);
        } catch (e) {
          // No es JSON, ignorar
        }
      } catch (error) {
        console.error('Error en método 1:', error);
      }

      // 2. Probar API Key como Bearer token
      console.log('Método 2: API Key como Bearer token');
      const headers2: Record<string, string> = {
        'Authorization': `Bearer ${this.apiKey}`,
        'Accept': 'application/json'
      };

      if (this.propertyId) {
        headers2['X-PROPERTY-ID'] = this.propertyId;
      }

      try {
        const response2 = await fetch(`${this.baseUrl}/getHotels`, {
          method: 'GET',
          headers: headers2
        });

        const text2 = await response2.text();
        console.log('Respuesta método 2:', text2);

        // Intentar parsear como JSON si es posible
        try {
          const json2 = JSON.parse(text2);
          console.log('Respuesta método 2 (JSON):', json2);
        } catch (e) {
          // No es JSON, ignorar
        }
      } catch (error) {
        console.error('Error en método 2:', error);
      }

      // 3. Obtener token OAuth con client credentials
      console.log('Método 3: Obtener token OAuth con client credentials');
      const formData = new URLSearchParams();
      formData.append('grant_type', 'client_credentials');
      formData.append('client_id', this.clientId);
      formData.append('client_secret', this.clientSecret);

      try {
        const response3 = await fetch(`${this.baseUrl}/access_token`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
          },
          body: formData
        });

        const text3 = await response3.text();
        console.log('Respuesta método 3:', text3);

        // Intentar parsear como JSON si es posible
        try {
          const json3 = JSON.parse(text3);
          console.log('Respuesta método 3 (JSON):', json3);

          // Si obtenemos un token, probamos usarlo
          if (json3.access_token) {
            console.log('Probando con token OAuth obtenido');
            const headers4: Record<string, string> = {
              'Authorization': `Bearer ${json3.access_token}`,
              'Accept': 'application/json'
            };

            if (this.propertyId) {
              headers4['X-PROPERTY-ID'] = this.propertyId;
            }

            const response4 = await fetch(`${this.baseUrl}/getHotels`, {
              method: 'GET',
              headers: headers4
            });

            const text4 = await response4.text();
            console.log('Respuesta con token OAuth:', text4);
          }
        } catch (e) {
          // No es JSON, ignorar
          console.error('Error al parsear JSON en método 3:', e);
        }
      } catch (error) {
        console.error('Error en método 3:', error);
      }

      // 4. Verificar información del usuario
      console.log('Método 4: Verificar información del usuario');
      try {
        const response5 = await fetch(`${this.baseUrl}/userinfo`, {
          method: 'GET',
          headers: {
            'x-api-key': this.apiKey,
            'Accept': 'application/json'
          }
        });

        const text5 = await response5.text();
        console.log('Respuesta método 4:', text5);
      } catch (error) {
        console.error('Error en método 4:', error);
      }

    } catch (error) {
      console.error('Error general al verificar credenciales:', error);
    }
  }

  /**
   * Realiza una petición a la API de Cloudbeds
   * @param endpoint - Endpoint de la API
   * @param method - Método HTTP
   * @param params - Parámetros de la petición
   * @returns Respuesta de la API
   */
  private async makeApiRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    params: Record<string, any> = {}
  ): Promise<T> {
    console.group(`CloudbedsApiService - makeApiRequest (${endpoint})`);
    console.log('Iniciando makeApiRequest para endpoint:', endpoint);
    console.log('Método HTTP:', method);
    console.log('Parámetros originales:', JSON.stringify(params, null, 2));

    // Log request context for debugging
    console.log("🔍 DEBUGGING: Request context:", {
      endpoint,
      method,
      paramCount: Object.keys(params).length,
      isAvailabilityRequest: endpoint === '/getAvailableRoomTypes',
      isRoomTypesRequest: endpoint === '/getRoomTypes',
      apiKeyLength: this.apiKey ? this.apiKey.length : 0,
      clientIdPresent: !!this.clientId,
      propertyIdPresent: !!this.propertyId
    });

    // Si es la primera llamada a getRoomTypes, verificamos las credenciales
    if (endpoint === '/getRoomTypes' && !this.credentialsVerified) {
      console.log("🔍 DEBUGGING: First call to getRoomTypes, verifying credentials");
      this.credentialsVerified = true;
      await this.verifyApiCredentials();
    }

    const accessToken = await this.getAccessToken();
    // Asegurarnos de tener un propertyId
    const propertyId = await this.getPropertyId();
    console.log('PropertyID obtenido para la petición:', propertyId);

    const url = new URL(`${this.baseUrl}${endpoint}`);

    // Añadir parámetros a la URL si es un GET
    if (method === 'GET') {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, value.toString());
        }
      });
    }

    console.log(`Enviando petición a: ${url.toString()}`);

    // Preparar los headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };

    // Añadir el token de autorización
    // Para la API v1.2, se puede usar el API Key como Bearer token o como x-api-key
    if (this.apiKey === accessToken) {
      // Si es un API Key, usamos el header x-api-key
      headers['x-api-key'] = accessToken;
      console.log('Usando x-api-key como mecanismo de autenticación');
    } else {
      // Si es un token OAuth, usamos el header Authorization con Bearer
      headers['Authorization'] = `Bearer ${accessToken}`;
      console.log('Usando Bearer token como mecanismo de autenticación');
    }

    // *** ACTUALIZACIÓN CRÍTICA: Manejo consistente de PropertyID en headers ***
    // La documentación de Cloudbeds indica que el propertyID debe enviarse en el header
    // X-PROPERTY-ID, pero diferentes endpoints pueden requerir formatos diferentes
    console.log('Configurando header de PropertyID con formato consistente');

    // Formato principal: X-PROPERTY-ID (formato documentado oficial)
    headers['X-PROPERTY-ID'] = propertyId;
    console.log('Añadido header X-PROPERTY-ID:', propertyId);

    // Para solicitudes GET, añadir propertyID como parámetro de consulta
    if (method === 'GET') {
      url.searchParams.append('propertyID', propertyId);
      console.log('Añadido propertyID como query param:', propertyId);
    } else {
      // Para métodos no-GET (POST, PUT, DELETE)
      if (!params.propertyID && params instanceof Object) {
        params.propertyID = propertyId;
        console.log('Añadido propertyID en el body:', propertyId);
      } else if (params.propertyID) {
        console.log('El body ya tiene propertyID:', params.propertyID);
      }
    }

    // Asegurarnos de que el propertyID esté en todos los lugares necesarios para reservas
    if (endpoint === '/postReservation') {
      // Para reservas, asegurarnos de usar el mismo formato en el body y en los headers
      if (method === 'POST' && params instanceof Object) {
        // Verificar que el propertyID en el body coincide con el header
        if (params.propertyID !== propertyId) {
          console.log('PropertyID en body y header no coinciden. Actualizando body...');
          params.propertyID = propertyId;
        }
        console.log('PropertyID en body configurado correctamente:', params.propertyID);
      }
    }

    console.log('URL final:', url.toString());
    console.log('Headers completos:', JSON.stringify(headers, (key, value) =>
      key === 'x-api-key' || key === 'Authorization' ? '[hidden]' : value)
    );

    if (method !== 'GET' && params) {
      console.log('Body de la petición:', JSON.stringify(params, null, 2));
    }

    // Prueba específica para reservations
    if (endpoint === '/postReservation') {
      console.log('DIAGNÓSTICO ESPECIAL PARA RESERVAS:');
      console.log('1. PropertyID de la clase:', propertyId);
      console.log('2. PropertyID en los parámetros:', params.propertyID);
      console.log('3. API Key disponible:', !!this.apiKey);
      console.log('4. Headers de autenticación:', this.apiKey ? 'x-api-key' : 'Bearer token');

      // Comparar con otras llamadas exitosas
      console.log('Comparación con otra llamada exitosa (getRoomTypes):');
      const compareUrl = new URL(`${this.baseUrl}/getRoomTypes`);
      const compareHeaders = { ...headers };
      console.log('- URL:', compareUrl.toString());
      console.log('- Headers:', JSON.stringify(compareHeaders, (key, value) =>
        key === 'x-api-key' || key === 'Authorization' ? '[hidden]' : value)
      );
    }

    try {
      const response = await fetch(url.toString(), {
        method,
        headers,
        body: method !== 'GET' ? JSON.stringify(params) : undefined
      });

      // Añadir log de la respuesta completa
      const responseText = await response.text();
      console.log('Respuesta completa:', responseText);

      // Para postReservation intentar con formato alternativo si hay error
      if (!response.ok && endpoint === '/postReservation' && responseText.includes("don't have access to property ID")) {
        console.log('ERROR DE ACCESO A PROPERTY ID. Intentando método alternativo...');

        // Intentar con otro formato de property ID
        const altHeaders = { ...headers };
        delete altHeaders['X-PROPERTY-ID'];
        delete altHeaders['property-id'];
        delete altHeaders['propertyID'];

        // Probar agregando el ID del property de otra manera
        altHeaders['X-Property-Id'] = propertyId; // Formato camelCase

        console.log('Intentando con headers alternativos:', JSON.stringify(altHeaders, (key, value) =>
          key === 'x-api-key' || key === 'Authorization' ? '[hidden]' : value)
        );

        // Modificar el body para usar 's-' + propertyId (formato observado en algunos endpoints)
        const altParams = { ...params };
        altParams.propertyID = 's-' + propertyId;
        console.log('Probando con formato alternativo de propertyID: s-' + propertyId);

        const altResponse = await fetch(url.toString(), {
          method,
          headers: altHeaders,
          body: method !== 'GET' ? JSON.stringify(altParams) : undefined
        });

        const altResponseText = await altResponse.text();
        console.log('Respuesta alternativa:', altResponseText);

        // Si este método alternativo funcionó, devolver la respuesta
        if (altResponse.ok) {
          console.log('¡El método alternativo funcionó!');
          try {
            // Parsear la respuesta como JSON
            const jsonResponse = JSON.parse(altResponseText) as T;
            return jsonResponse;
          } catch (error) {
            console.error('Error al parsear la respuesta alternativa como JSON:', error);
          }
        } else {
          console.log('El método alternativo también falló.');
        }
      }

      if (!response.ok) {
        console.error('Error en la petición a Cloudbeds:', {
          status: response.status,
          statusText: response.statusText,
          url: url.toString(),
          errorBody: responseText
        });
        throw new Error(`Error en la petición a Cloudbeds: ${response.statusText} - ${responseText}`);
      }

      try {
        // Parsear la respuesta como JSON
        const jsonResponse = JSON.parse(responseText) as T;
        console.log("✅ API request completed successfully");
        console.groupEnd();
        return jsonResponse;
      } catch (error) {
        console.error('❌ Error al parsear la respuesta como JSON:', error);
        console.groupEnd();
        throw new Error('Error al parsear la respuesta como JSON');
      }
    } catch (error) {
      console.error('❌ Error en la solicitud fetch:', error);
      console.groupEnd();
      throw error;
    }
  }

  /**
   * Loads room types from the API and builds the mapping
   * @param forceRefresh - Force refresh the cache even if it's not expired
   * @returns List of room types
   */
  private async loadRoomTypes(forceRefresh: boolean = false): Promise<CloudbedsRoomType[]> {
    const now = Date.now();

    // Return cached data if available and not expired
    if (!forceRefresh && this.roomTypesCache.length > 0 && now < this.roomTypesCacheExpiration) {
      console.log('Using cached room types data');
      return this.roomTypesCache;
    }

    console.log('Fetching room types from API and building mapping...');

    try {
      // Fetch room types from API
      const response = await this.makeApiRequest<CloudbedsRoomTypesResponse>('/getRoomTypes', 'GET', {
        includePhotos: '0', // Don't need photos for mapping
        includeAmenities: '0' // Don't need amenities for mapping
      });

      if (!response.success || !response.data || response.data.length === 0) {
        console.error('Failed to fetch room types or empty response:', response.message || 'Unknown error');
        return [];
      }

      // Store in cache
      this.roomTypesCache = response.data;
      this.roomTypesCacheExpiration = now + this.CACHE_DURATION_MS;

      // Build mapping based on room names
      this.buildRoomTypeMapping(response.data);

      console.log(`Cached ${response.data.length} room types until ${new Date(this.roomTypesCacheExpiration).toISOString()}`);
      return response.data;
    } catch (error) {
      console.error('Error loading room types:', error);
      return [];
    }
  }

  /**
   * Builds a mapping of room type names to IDs for easy lookup
   * @param roomTypes - List of room types
   */
  private buildRoomTypeMapping(roomTypes: CloudbedsRoomType[]): void {
    // Clear existing mapping
    this.roomTypeMapping.clear();

    // Process each room type
    for (const roomType of roomTypes) {
      const id = roomType.roomTypeID || roomType.id;
      if (!id) continue;

      // Get the name in various formats
      const name = roomType.roomTypeName || roomType.name || '';
      const nameShort = roomType.roomTypeNameShort || '';

      if (name) {
        // Store the full name mapping (lowercase for case-insensitive matching)
        this.roomTypeMapping.set(name.toLowerCase(), id);

        // Store name parts for partial matching
        const nameParts = name.toLowerCase().split(/\s+/);

        // Create mappings for common patterns
        if (nameParts.length >= 2) {
          // Check for patterns like "Garden Deluxe", "Ocean Junior", etc.
          const location = nameParts.find(part =>
            ['garden', 'ocean', 'mountain', 'pool', 'beach', 'sea', 'lake'].includes(part)
          );

          const roomType = nameParts.find(part =>
            ['deluxe', 'junior', 'suite', 'standard', 'superior', 'executive', 'premium'].includes(part)
          );

          if (location && roomType) {
            const pattern = `${location} ${roomType}`;
            this.roomTypeMapping.set(pattern, id);
          }
        }
      }

      // Store short name mapping if available
      if (nameShort) {
        this.roomTypeMapping.set(nameShort.toLowerCase(), id);
      }
    }

    console.log('Room type mapping built with patterns:', Array.from(this.roomTypeMapping.keys()));
  }

  /**
   * Find a room type ID based on a name or pattern
   * @param roomName - Room name or pattern to search for
   * @returns Room type ID if found, undefined otherwise
   */
  public findRoomTypeIdByName(roomName: string): string | undefined {
    if (!roomName) return undefined;

    const normalizedName = roomName.toLowerCase();

    // Direct match
    if (this.roomTypeMapping.has(normalizedName)) {
      return this.roomTypeMapping.get(normalizedName);
    }

    // Try to match patterns
    for (const [pattern, id] of this.roomTypeMapping.entries()) {
      if (normalizedName.includes(pattern)) {
        return id;
      }
    }

    // Try to match by parts
    const nameParts = normalizedName.split(/\s+/);

    // Check for location + room type pattern
    const location = nameParts.find(part =>
      ['garden', 'ocean', 'mountain', 'pool', 'beach', 'sea', 'lake'].includes(part)
    );

    const roomType = nameParts.find(part =>
      ['deluxe', 'junior', 'suite', 'standard', 'superior', 'executive', 'premium'].includes(part)
    );

    if (location && roomType) {
      const pattern = `${location} ${roomType}`;

      // Check if this pattern exists in our mapping
      for (const [mappedPattern, id] of this.roomTypeMapping.entries()) {
        if (mappedPattern.includes(pattern)) {
          return id;
        }
      }
    }

    // No match found
    return undefined;
  }

  /**
   * Get the first available room type ID
   * @returns First room type ID if available, undefined otherwise
   */
  public getDefaultRoomTypeId(): string | undefined {
    if (this.roomTypesCache.length > 0) {
      const firstRoom = this.roomTypesCache[0];
      return firstRoom.roomTypeID || firstRoom.id;
    }

    return undefined;
  }

  /**
   * Validates and resolves a room type ID
   * @param roomTypeId - Room type ID to validate
   * @param roomName - Optional room name to use as fallback
   * @returns Valid room type ID or undefined if not found
   */
  public async validateRoomTypeId(roomTypeId?: string, roomName?: string): Promise<string | undefined> {
    // Get the property ID
    const propertyId = await this.getPropertyId();

    // Use the dynamic room type resolver
    return this.roomTypeResolver.resolveRoomTypeId(
      roomTypeId,
      roomName,
      propertyId
    );
  }

  /**
   * Obtiene la lista de tipos de habitaciones
   * @param includePhotos - Incluir fotos de los tipos de habitación
   * @param includeAmenities - Incluir amenities de los tipos de habitación
   * @returns Lista de tipos de habitaciones
   */
  async getRoomTypes(includePhotos: boolean = true, includeAmenities: boolean = true): Promise<CloudbedsRoomTypesResponse> {
    // Try to use cached data first if we don't need photos or amenities
    if (!includePhotos && !includeAmenities && this.roomTypesCache.length > 0) {
      const now = Date.now();
      if (now < this.roomTypesCacheExpiration) {
        console.log('Using cached room types for getRoomTypes call');
        return {
          success: true,
          data: this.roomTypesCache
        };
      }
    }

    // Otherwise, make a fresh API request
    const params: Record<string, string | number> = {};

    if (includePhotos) {
      params.includePhotos = '1';
    }

    if (includeAmenities) {
      params.includeAmenities = '1';
    }

    const response = await this.makeApiRequest<CloudbedsRoomTypesResponse>('/getRoomTypes', 'GET', params);

    // Update cache if successful and we got data
    if (response.success && response.data && response.data.length > 0) {
      // Only update the cache if we're getting the basic data (no photos/amenities)
      // This prevents overwriting our detailed cache with partial data
      if (!includePhotos && !includeAmenities) {
        this.roomTypesCache = response.data;
        this.roomTypesCacheExpiration = Date.now() + this.CACHE_DURATION_MS;
        this.buildRoomTypeMapping(response.data);
      }
    }

    return response;
  }

  /**
   * Obtiene la lista de habitaciones
   * @param includePhotos - Incluir fotos de las habitaciones
   * @param includeAmenities - Incluir amenities de las habitaciones
   * @param includeRoomRelations - Incluir relaciones entre habitaciones (habitaciones físicas y virtuales)
   * @returns Lista de habitaciones
   */
  async getRooms(includePhotos: boolean = true, includeAmenities: boolean = true, includeRoomRelations: boolean = true): Promise<CloudbedsRoomsResponse> {
    const params: Record<string, string | number> = {};

    if (includePhotos) {
      params.includePhotos = '1';
    }

    if (includeAmenities) {
      params.includeAmenities = '1';
    }

    if (includeRoomRelations) {
      params.includeRoomRelations = '1';
    }

    return this.makeApiRequest<CloudbedsRoomsResponse>('/getRooms', 'GET', params);
  }

  /**
   * Obtiene información detallada de un tipo de habitación específico
   * @param roomTypeId - ID del tipo de habitación
   * @param includePhotos - Incluir fotos del tipo de habitación
   * @param includeAmenities - Incluir amenities del tipo de habitación
   * @returns Información detallada del tipo de habitación
   */
  async getRoomTypeDetails(
    roomTypeId: string,
    includePhotos: boolean = true,
    includeAmenities: boolean = true
  ): Promise<CloudbedsRoomTypesResponse> {
    const params: Record<string, string | number> = {
      roomTypeId
    };

    if (includePhotos) {
      params.includePhotos = '1';
    }

    if (includeAmenities) {
      params.includeAmenities = '1';
    }

    return this.makeApiRequest<CloudbedsRoomTypesResponse>('/getRoomTypes', 'GET', params);
  }

  /**
   * Obtiene la disponibilidad de habitaciones para un rango de fechas
   * @param startDate - Fecha de inicio (formato YYYY-MM-DD)
   * @param endDate - Fecha de fin (formato YYYY-MM-DD)
   * @param roomTypeId - ID del tipo de habitación (opcional)
   * @returns Disponibilidad de habitaciones
   */
  async getAvailability(
    startDate: string,
    endDate: string,
    roomTypeId?: string
  ): Promise<CloudbedsAvailabilityResponse> {
    console.group("CloudbedsApiService - getAvailability");
    console.log("🔍 DEBUGGING: Method called with parameters:", { startDate, endDate, roomTypeId });

    try {
      // Validar formato de fechas
      const dateSchema = z.string().regex(/^\d{4}-\d{2}-\d{2}$/);
      dateSchema.parse(startDate);
      dateSchema.parse(endDate);
      console.log("✅ Date format validation passed");

      // Log environment variables for debugging
      console.log("🔍 DEBUGGING: Environment variables:", {
        apiKeyPresent: !!this.apiKey,
        clientIdPresent: !!this.clientId,
        clientSecretPresent: !!this.clientSecret,
        propertyIdPresent: !!this.propertyId,
        propertyIdValue: this.propertyId
      });

      const params: Record<string, string | number> = {
        startDate,
        endDate
      };

      if (roomTypeId) {
        params.roomTypeId = roomTypeId;
      }

      console.log("🔍 DEBUGGING: Preparing API request with params:", params);
      console.time("Cloudbeds API request duration");

      const result = await this.makeApiRequest<CloudbedsAvailabilityResponse>(
        '/getAvailableRoomTypes',
        'GET',
        params
      );

      console.timeEnd("Cloudbeds API request duration");

      // Enhanced logging for the response
      console.log("🔍 DEBUGGING: API response details:", {
        success: result.success,
        hasData: result.data && result.data.length > 0,
        dataLength: result.data?.length || 0,
        errorMessage: result.error?.message,
        firstItemRoomTypeId: result.data?.[0]?.roomTypeId || 'N/A',
        firstItemDatesCount: result.data?.[0]?.dates?.length || 0,
        emptyResponse: !result.data || result.data.length === 0
      });

      // Log the full response for detailed debugging
      console.log('Respuesta de disponibilidad desde API:', JSON.stringify(result, null, 2));

      // Check if the response is empty but successful
      if (result.success && (!result.data || result.data.length === 0)) {
        console.warn("⚠️ API returned success but with empty data array");
        console.log("🔍 DEBUGGING: This might indicate a configuration issue in Cloudbeds");

        // Return a properly formatted empty response
        return {
          success: true,
          data: [],
          roomCount: 0,
          count: 0,
          total: 0
        };
      }

      console.log("✅ API request completed successfully");
      console.groupEnd();
      return result;
    } catch (error) {
      console.error('❌ Error en getAvailability:', error);
      console.log("🔍 DEBUGGING: Error details:", {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });
      console.groupEnd();
      throw error;
    }
  }

  /**
   * Crea una reserva en Cloudbeds
   * @param {CloudbedsReservationRequest} reservationData - Datos de la reserva a crear
   * @returns {Promise<CloudbedsCreateReservationResponse>} - Respuesta de la API con el ID de la reserva creada
   */
  public async createReservation(reservationData: CloudbedsReservationRequest): Promise<CloudbedsCreateReservationResponse> {
    try {
      console.log('=== INICIO CREACIÓN DE RESERVA ===');
      // Crear un nuevo objeto de reserva para evitar modificar el original que es de solo lectura
      let reservation = {
        ...reservationData
      };

      // Asegurarnos de que propertyID esté definido
      if (!reservation.propertyID) {
        reservation = {
          ...reservation,
          propertyID: this.propertyId
        };
        console.log('PropertyID no estaba en la reserva, usando el de la configuración:', this.propertyId);
      } else {
        console.log('PropertyID en la reserva:', reservation.propertyID);
      }

      // IMPORTANTE: sourceID necesita tener formato específico "s-{id}-1" para crear reservas
      // Formatear sourceID según documentación de Cloudbeds
      let sourceid = reservation.sourceID || '2'; // Default a 2 (Web) si no se proporciona

      // Remover formato anterior si ya tiene prefijo/sufijo
      if (typeof sourceid === 'string') {
        sourceid = sourceid.replace(/^s-/, '').replace(/-1$/, '');
      }

      // Aplicar el formato requerido por Cloudbeds: s-{id}-1
      const formattedSourceId = `s-${sourceid}-1`;
      console.log('sourceID con formato especial para reservas:', formattedSourceId);

      reservation = {
        ...reservation,
        sourceID: formattedSourceId
      };

      // Asegurarnos de que thirdPartyIdentifier esté definido
      if (!reservation.thirdPartyIdentifier) {
        // Generar un identificador único con timestamp + random para evitar colisiones
        const uniqueId = `res_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;
        reservation = {
          ...reservation,
          thirdPartyIdentifier: uniqueId
        };
        console.log('Generado thirdPartyIdentifier único:', uniqueId);
      }

      // Asegurarnos de que status esté definido
      if (!reservation.status) {
        reservation = {
          ...reservation,
          status: 'confirmed'
        };
        console.log('Status no estaba definido, usando "confirmed" por defecto');
      }

      // Asegurarnos de que sendEmailConfirmation esté definido
      if (reservation.sendEmailConfirmation === undefined) {
        reservation = {
          ...reservation,
          sendEmailConfirmation: true
        };
        console.log('sendEmailConfirmation no estaba definido, configurando a true por defecto');
      }

      // IMPORTANTE: Según el error, también se requieren los campos startDate y endDate a nivel de raíz del objeto
      // Extraer startDate y endDate del primer roomData y añadirlos al objeto principal
      if (reservation.roomsData && reservation.roomsData.length > 0) {
        const firstRoom = reservation.roomsData[0];
        if (firstRoom.startDate && firstRoom.endDate) {
          reservation = {
            ...reservation,
            startDate: firstRoom.startDate,
            endDate: firstRoom.endDate
          };
          console.log('Añadidos startDate y endDate a nivel raíz:', firstRoom.startDate, firstRoom.endDate);
        }
      }

      console.log('Datos completos de la reserva a enviar:', JSON.stringify(reservation, null, 2));

      // Tratar de obtener un token OAuth si está disponible (en lugar de API Key)
      // Esto puede ayudar con problemas de permisos
      let accessToken;
      try {
        if (this.clientId && this.clientSecret) {
          console.log('Intentando obtener token OAuth para mayor nivel de acceso...');
          const formData = new URLSearchParams();
          formData.append('grant_type', 'client_credentials');
          formData.append('client_id', this.clientId);
          formData.append('client_secret', this.clientSecret);

          const response = await fetch(`${this.baseUrl}/access_token`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'Accept': 'application/json'
            },
            body: formData
          });

          if (response.ok) {
            const data = await response.json();
            accessToken = data.access_token;
            console.log('Token OAuth obtenido correctamente');
          } else {
            console.log('No se pudo obtener token OAuth, usando API Key');
            accessToken = await this.getAccessToken();
          }
        } else {
          accessToken = await this.getAccessToken();
        }
      } catch (error) {
        console.log('Error obteniendo OAuth token, usando API Key:', error);
        accessToken = await this.getAccessToken();
      }

      // IMPORTANTE: Agregar propertyID como parámetro de URL como sugiere Cloudbeds
      const propertyId = await this.getPropertyId();

      // Obtener las fechas para añadirlas a todos los parámetros posibles
      let startDate = '';
      let endDate = '';

      if (reservation.startDate && reservation.endDate) {
        startDate = reservation.startDate;
        endDate = reservation.endDate;
      } else if (reservation.roomsData && reservation.roomsData.length > 0) {
        startDate = reservation.roomsData[0].startDate;
        endDate = reservation.roomsData[0].endDate;
      }

      // Construir URL con el formato simplificado
      const url = new URL(`${this.baseUrl}/postReservation`);

      // Añadir propertyID como parámetro de URL
      url.searchParams.append('propertyID', propertyId);

      // NO añadir fechas como parámetros de URL, solo en el cuerpo
      console.log(`[DEBUG] Usando formato de URL simplificado: ${url.toString()}`);

      // Crear URLSearchParams para enviar como application/x-www-form-urlencoded (formato que funciona)
      const params = new URLSearchParams();

      // Información de la propiedad - asegurarse de que propertyID tenga un valor real
      params.append('propertyID', propertyId || '317353');
      params.append('startDate', startDate);
      params.append('endDate', endDate);

      // Información del huésped
      params.append('guestFirstName', reservation.guestData.firstName);
      params.append('guestLastName', reservation.guestData.lastName);
      params.append('guestEmail', reservation.guestData.email);
      params.append('guestPhone', reservation.guestData.phone || '');

      if (reservation.guestData.address) {
        params.append('guestAddress', reservation.guestData.address);
      }

      if (reservation.guestData.city) {
        params.append('guestCity', reservation.guestData.city);
      }

      if (reservation.guestData.state) {
        params.append('guestState', reservation.guestData.state);
      }

      if (reservation.guestData.country) {
        params.append('guestCountry', reservation.guestData.country || 'ES');
      } else {
        params.append('guestCountry', 'ES'); // País por defecto
      }

      if (reservation.guestData.postalCode) {
        params.append('guestZip', reservation.guestData.postalCode);
      }

      // Información de la habitación
      const room = reservation.roomsData[0];
      params.append('roomTypeID', room.roomTypeID);

      // Datos de habitación anidados (formato correcto) - asegurarse de que roomTypeID tenga un valor real
      params.append('rooms[0][roomTypeID]', room.roomTypeID || '650743');
      params.append('rooms[0][quantity]', '1');

      // Adultos y niños (formato correcto) - asegurarse de que roomTypeID tenga un valor real
      params.append('adults[0][roomTypeID]', room.roomTypeID || '650743');
      params.append('adults[0][quantity]', room.adults.toString());
      params.append('children[0][roomTypeID]', room.roomTypeID || '650743');
      params.append('children[0][quantity]', (room.children || 0).toString());

      // Información de pago
      params.append('paymentMethod', 'credit');

      // Otros
      params.append('source', formattedSourceId);
      params.append('status', reservation.status || 'confirmed');
      params.append('thirdPartyIdentifier', reservation.thirdPartyIdentifier);
      params.append('sendEmailConfirmation', reservation.sendEmailConfirmation ? 'true' : 'false');

      console.log(`[DEBUG] URLSearchParams creado con el formato correcto:`, params.toString());
      console.log(`[Timestamp: ${new Date().toISOString()}] URL completa:`, url.toString());

      // Preparar los headers con autenticación
      const headers: Record<string, string> = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      };

      // Añadir el token de autorización
      if (this.apiKey === accessToken) {
        headers['x-api-key'] = accessToken;
      } else {
        headers['Authorization'] = `Bearer ${accessToken}`;
      }

      console.log(`[DEBUG] Datos URLSearchParams enviados:`, params.toString());

      const response = await fetch(url.toString(), {
        method: 'POST',
        headers,
        body: params
      });

      console.log(`[DEBUG] Respuesta recibida: status=${response.status}, ok=${response.ok}`);

      const responseText = await response.text();
      console.log(`[Timestamp: ${new Date().toISOString()}] Código de estado:`, response.status);
      console.log(`[Timestamp: ${new Date().toISOString()}] Respuesta completa:`, responseText);
      console.log(`[Timestamp: ${new Date().toISOString()}] URL completa:`, url.toString());

      let responseData;
      try {
        responseData = JSON.parse(responseText) as CloudbedsApiResponse<CloudbedsReservationResponse>;
      } catch (error) {
        console.error(`[Timestamp: ${new Date().toISOString()}] Error al parsear la respuesta como JSON:`, error);
        throw new Error('Error al parsear la respuesta como JSON');
      }

      console.log(`[Timestamp: ${new Date().toISOString()}] Respuesta parseada:`, JSON.stringify(responseData, null, 2));

      if (responseData.success) {
        // Si la respuesta tiene data, usar esos valores
        if (responseData.data) {
          console.log(`[Timestamp: ${new Date().toISOString()}] Reserva creada exitosamente:`, responseData.data);
          return {
            success: true,
            reservationID: responseData.data.reservationID,
            confirmationCode: responseData.data.confirmationCode,
            message: responseData.message || 'Reserva creada exitosamente',
            data: responseData.data
          };
        } else {
          // Si no tiene data pero tiene reservationID directamente
          return {
            success: true,
            reservationID: responseData.reservationID,
            confirmationCode: responseData.confirmationCode,
            message: responseData.message || 'Reserva creada exitosamente'
          };
        }
      } else {
        console.error(`[Timestamp: ${new Date().toISOString()}] Error en la respuesta de Cloudbeds:`, responseData);
        return {
          success: false,
          message: responseData.message || 'No se pudo crear la reserva',
          error: responseData.error || { message: 'Error desconocido' }
        };
      }
    } catch (error) {
      console.error('Error creando reserva en Cloudbeds:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Error desconocido creando reserva'
      };
    } finally {
      console.log('=== FIN CREACIÓN DE RESERVA ===');
    }
  }

  /**
   * Crea una reserva en Cloudbeds - Versión directa
   * Intenta usar el formato exacto de la documentación de Cloudbeds
   * @param reservationData Datos de la reserva
   * @returns Respuesta de la creación de la reserva
   */
  public async createReservationDirect(reservationData: CloudbedsReservationRequest): Promise<CloudbedsCreateReservationResponse> {
    try {
      console.log('=== INICIO CREACIÓN DE RESERVA (MODO DIRECTO) ===');
      const propertyId = await this.getPropertyId();
      const accessToken = await this.getAccessToken();

      // Extraer fechas de los datos de habitación
      const startDate = reservationData.roomsData[0]?.startDate || reservationData.startDate || '';
      const endDate = reservationData.roomsData[0]?.endDate || reservationData.endDate || '';

      if (!startDate || !endDate) {
        return {
          success: false,
          message: 'Se requieren fechas de inicio y fin para la reserva'
        };
      }

      // Usar un enfoque simple como en la documentación
      const url = new URL(`${this.baseUrl}/postReservation`);

      // NO añadir parámetros a la URL, siguiendo el ejemplo exacto de Manuel Arbelo
      console.log('[DEBUG DIRECTO] Usando formato de URL simplificado:', url.toString());

      // Crear FormData para enviar como multipart/form-data (formato que funciona según Manuel Arbelo)
      const formData = new FormData();

      // Información de la propiedad - asegurarse de que propertyID tenga un valor real
      formData.append('propertyID', propertyId || '317353');
      formData.append('startDate', startDate);
      formData.append('endDate', endDate);

      // Información del huésped
      formData.append('guestFirstName', reservationData.guestData.firstName);
      formData.append('guestLastName', reservationData.guestData.lastName);
      formData.append('guestEmail', reservationData.guestData.email);
      formData.append('guestPhone', reservationData.guestData.phone || '');

      if (reservationData.guestData.address) {
        formData.append('guestAddress', reservationData.guestData.address);
      }

      if (reservationData.guestData.city) {
        formData.append('guestCity', reservationData.guestData.city);
      }

      if (reservationData.guestData.state) {
        formData.append('guestState', reservationData.guestData.state);
      }

      if (reservationData.guestData.country) {
        formData.append('guestCountry', reservationData.guestData.country || 'FR');
      } else {
        formData.append('guestCountry', 'FR'); // País por defecto según ejemplo de Manuel
      }

      if (reservationData.guestData.postalCode) {
        formData.append('guestZip', reservationData.guestData.postalCode);
      } else {
        formData.append('guestZip', '1234'); // Código postal por defecto según ejemplo de Manuel
      }

      // Información de la habitación
      const room = reservationData.roomsData[0];

      // Validate and resolve room type ID using our dynamic system
      console.log(`[DEBUG DIRECTO] Validando roomTypeID: ${room.roomTypeID}`);

      // Use the dynamic room type resolver
      const roomTypeID = await this.validateRoomTypeId(room.roomTypeID, room.roomName);

      if (!roomTypeID) {
        console.error('[DEBUG DIRECTO] No se pudo determinar un roomTypeID válido');
        return {
          success: false,
          message: 'No se pudo determinar un tipo de habitación válido'
        };
      }

      console.log(`[DEBUG DIRECTO] Usando roomTypeID validado: ${roomTypeID} (resuelto dinámicamente)`);

      // Datos de habitación anidados (formato correcto) con roomTypeID validado
      formData.append('rooms[0][roomTypeID]', roomTypeID);
      formData.append('rooms[0][quantity]', '1');

      // Adultos y niños (formato correcto) con roomTypeID validado
      formData.append('adults[0][roomTypeID]', roomTypeID);
      formData.append('adults[0][quantity]', room.adults.toString());
      formData.append('children[0][roomTypeID]', roomTypeID);
      formData.append('children[0][quantity]', (room.children || 0).toString());

      // Información de pago
      formData.append('paymentMethod', 'credit');

      // Usar sourceID s-1-1 según el ejemplo de Manuel Arbelo
      formData.append('sourceID', 's-1-1');

      // Otros parámetros
      formData.append('status', reservationData.status || 'confirmed');
      formData.append('thirdPartyIdentifier', reservationData.thirdPartyIdentifier || `direct-${Date.now()}`);
      formData.append('sendEmailConfirmation', reservationData.sendEmailConfirmation ? 'true' : 'false');

      console.log('[DEBUG DIRECTO] FormData creado con el formato correcto de Manuel Arbelo');

      // Headers básicos con tipo Record para permitir índices dinámicos
      const headers: Record<string, string> = {
        'Accept': 'application/json'
      };

      // Añadir autorización - Usar Bearer token como en el ejemplo de Manuel
      headers['Authorization'] = `Bearer ${accessToken}`;

      console.log('[DEBUG DIRECTO] URL:', url.toString());
      console.log('[DEBUG DIRECTO] Headers:', JSON.stringify(headers, (key, value) =>
        key === 'Authorization' ? '[hidden]' : value)
      );

      // Enviar la solicitud con FormData
      const response = await fetch(url.toString(), {
        method: 'POST',
        headers,
        body: formData
      });

      const responseText = await response.text();
      console.log('[DEBUG DIRECTO] Código de estado:', response.status);
      console.log('[DEBUG DIRECTO] Respuesta completa:', responseText);
      console.log('[DEBUG DIRECTO] URL completa:', url.toString());
      console.log('[DEBUG DIRECTO] Headers:', JSON.stringify(headers, (key, value) =>
        key === 'Authorization' ? '[hidden]' : value)
      );

      let responseData;
      try {
        responseData = JSON.parse(responseText);
        console.log('[DEBUG DIRECTO] Respuesta parseada:', JSON.stringify(responseData, null, 2));

        if (responseData.success) {
          // Si la respuesta tiene data, usar esos valores
          if (responseData.data) {
            return {
              success: true,
              reservationID: responseData.data.reservationID,
              confirmationCode: responseData.data.confirmationCode,
              message: responseData.message || 'Reserva creada exitosamente',
              data: responseData.data
            };
          } else {
            // Si no tiene data pero tiene reservationID directamente
            return {
              success: true,
              reservationID: responseData.reservationID,
              confirmationCode: responseData.confirmationCode,
              message: responseData.message || 'Reserva creada exitosamente'
            };
          }
        } else {
          console.error('[DEBUG DIRECTO] Error en la respuesta:', responseData);
          return {
            success: false,
            message: responseData.message || 'No se pudo crear la reserva (método directo)',
            error: responseData.error || { message: 'Error desconocido' }
          };
        }
      } catch (error) {
        return {
          success: false,
          message: `Error al procesar respuesta: ${error instanceof Error ? error.message : 'Error desconocido'}`
        };
      }
    } catch (error) {
      console.error('[DEBUG DIRECTO] Error en createReservationDirect:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Error desconocido en método directo'
      };
    } finally {
      console.log('=== FIN CREACIÓN DE RESERVA (MODO DIRECTO) ===');
    }
  }
}

// Instancia única para uso en el servidor
export const cloudbedsApiService = new CloudbedsApiService();