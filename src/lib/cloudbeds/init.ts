/**
 * Script de inicialización para el módulo de Cloudbeds
 * Este script carga la información necesaria para que el cliente pueda
 * interactuar con la API de Cloudbeds correctamente
 */

/**
 * Inicializa los valores necesarios para la integración con Cloudbeds
 * @returns Una promesa que se resuelve cuando la inicialización se completa
 */
export async function initCloudbeds(): Promise<void> {
  try {
    console.log('[CloudbedsInit] Iniciando carga de datos de Cloudbeds');

    // Cargar el Property ID desde el servidor
    const response = await fetch('/api/cloudbeds/config');
    if (!response.ok) {
      throw new Error(`Error obteniendo configuración de Cloudbeds: ${response.statusText}`);
    }

    const config = await response.json();

    if (config.propertyID) {
      console.log('[CloudbedsInit] Property ID obtenido:', config.propertyID);

      // Guardar el PropertyID en una variable global para acceso desde el cliente
      if (typeof window !== 'undefined') {
        (window as any).__CLOUDBEDS_PROPERTY_ID__ = config.propertyID;
        console.log('[CloudbedsInit] Property ID guardado globalmente');
      }
    } else {
      console.warn('[CloudbedsInit] No se recibió Property ID del servidor');
    }
  } catch (error) {
    console.error('[CloudbedsInit] Error inicializando datos de Cloudbeds:', error);
  }
}

// Inicialización diferida para no bloquear la carga de la página
if (typeof window !== 'undefined') {
  // Usar requestIdleCallback para inicializar cuando el navegador esté inactivo
  const initWithIdleCallback = () => {
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        console.log('[CloudbedsInit] Ejecutando inicialización diferida');
        initCloudbeds().catch(err => {
          console.error('[CloudbedsInit] Error en inicialización diferida:', err);
        });
      }, { timeout: 2000 }); // Timeout de 2 segundos como máximo
    } else {
      // Fallback para navegadores que no soportan requestIdleCallback
      setTimeout(() => {
        console.log('[CloudbedsInit] Ejecutando inicialización diferida (setTimeout)');
        initCloudbeds().catch(err => {
          console.error('[CloudbedsInit] Error en inicialización diferida:', err);
        });
      }, 1000); // Esperar 1 segundo después de la carga
    }
  };

  // Iniciar después de que la página esté completamente cargada
  if (document.readyState === 'complete') {
    initWithIdleCallback();
  } else {
    window.addEventListener('load', initWithIdleCallback);
  }
}