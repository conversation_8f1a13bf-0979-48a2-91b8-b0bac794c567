import { RoomTypeCache } from './room-type-cache';

/**
 * Resolves room type IDs using multiple strategies
 */
export class RoomTypeResolver {
  constructor(private readonly roomTypeCache: RoomTypeCache) {}

  /**
   * Validate a room type ID
   * @param roomTypeId The room type ID to validate
   * @param propertyId The property ID
   * @returns True if valid, false otherwise
   */
  async validateRoomTypeId(roomTypeId: string, propertyId: string): Promise<boolean> {
    if (!roomTypeId) return false;

    const roomTypes = await this.roomTypeCache.getRoomTypes(propertyId);

    return roomTypes.some(roomType =>
      (roomType.roomTypeID === roomTypeId) || (roomType.id === roomTypeId)
    );
  }

  /**
   * Find a room type ID by name
   * @param roomName The room name
   * @param propertyId The property ID
   * @returns The room type ID if found, undefined otherwise
   */
  async findRoomTypeIdByName(roomName: string, propertyId: string): Promise<string | undefined> {
    if (!roomName) return undefined;

    const mapping = await this.roomTypeCache.buildRoomTypeMapping(propertyId);
    const normalizedName = roomName.toLowerCase();

    // Direct match
    if (mapping.has(normalizedName)) {
      return mapping.get(normalizedName);
    }

    // Try to match patterns
    for (const [mappedName, id] of mapping.entries()) {
      if (normalizedName.includes(mappedName) || mappedName.includes(normalizedName)) {
        return id;
      }
    }

    // Try pattern matching
    return this.findRoomTypeByPattern(roomName, propertyId);
  }

  /**
   * Find a room type by pattern matching
   * @param roomName The room name
   * @param propertyId The property ID
   * @returns The room type ID if found, undefined otherwise
   */
  async findRoomTypeByPattern(roomName: string, propertyId: string): Promise<string | undefined> {
    if (!roomName) return undefined;

    const roomTypes = await this.roomTypeCache.getRoomTypes(propertyId);
    const normalizedName = roomName.toLowerCase();

    // Location patterns
    const locationPatterns = ['garden', 'ocean', 'mountain', 'pool', 'beach', 'sea', 'lake'];
    const roomTypePatterns = ['deluxe', 'junior', 'suite', 'standard', 'superior', 'executive', 'premium'];

    // Extract patterns from room name
    const foundPatterns: Record<string, string> = {};

    // Check for location patterns
    for (const pattern of locationPatterns) {
      if (normalizedName.includes(pattern)) {
        foundPatterns['location'] = pattern;
        break;
      }
    }

    // Check for room type patterns
    for (const pattern of roomTypePatterns) {
      if (normalizedName.includes(pattern)) {
        foundPatterns['roomType'] = pattern;
        break;
      }
    }

    // If we found both location and room type, search for a match
    if (foundPatterns.location && foundPatterns.roomType) {
      const patternToMatch = `${foundPatterns.location} ${foundPatterns.roomType}`;

      const matchedRoomType = roomTypes.find(rt => {
        const rtName = (rt.roomTypeName || rt.name || '').toLowerCase();
        return rtName.includes(patternToMatch);
      });

      if (matchedRoomType) {
        return matchedRoomType.roomTypeID || matchedRoomType.id;
      }
    }

    // Try fuzzy matching as a last resort
    return this.fuzzyMatchRoomType(roomName, roomTypes);
  }

  /**
   * Perform fuzzy matching on room names
   * @param roomName The room name to match
   * @param roomTypes The room types to match against
   * @returns The best matching room type ID or undefined
   */
  private fuzzyMatchRoomType(roomName: string, roomTypes: any[]): string | undefined {
    if (!roomName || !roomTypes.length) return undefined;

    // Simple Levenshtein distance implementation
    function levenshteinDistance(a: string, b: string): number {
      const matrix: number[][] = [];

      // Initialize matrix
      for (let i = 0; i <= b.length; i++) {
        matrix[i] = [i];
      }

      for (let j = 0; j <= a.length; j++) {
        matrix[0][j] = j;
      }

      // Fill matrix
      for (let i = 1; i <= b.length; i++) {
        for (let j = 1; j <= a.length; j++) {
          if (b.charAt(i-1) === a.charAt(j-1)) {
            matrix[i][j] = matrix[i-1][j-1];
          } else {
            matrix[i][j] = Math.min(
              matrix[i-1][j-1] + 1, // substitution
              matrix[i][j-1] + 1,   // insertion
              matrix[i-1][j] + 1    // deletion
            );
          }
        }
      }

      return matrix[b.length][a.length];
    }

    // Find the room type with the smallest Levenshtein distance
    let bestMatch = null;
    let bestDistance = Infinity;

    for (const roomType of roomTypes) {
      const rtName = roomType.roomTypeName || roomType.name || '';
      const distance = levenshteinDistance(
        roomName.toLowerCase(),
        rtName.toLowerCase()
      );

      // Normalize by the length of the longer string
      const normalizedDistance = distance / Math.max(roomName.length, rtName.length);

      if (normalizedDistance < bestDistance) {
        bestDistance = normalizedDistance;
        bestMatch = roomType;
      }
    }

    // Only return if the match is reasonably close (threshold of 0.3)
    if (bestDistance < 0.3) {
      return bestMatch.roomTypeID || bestMatch.id;
    }

    return undefined;
  }

  /**
   * Get the default room type ID for a property
   * @param propertyId The property ID
   * @returns The default room type ID if available, undefined otherwise
   */
  async getDefaultRoomTypeId(propertyId: string): Promise<string | undefined> {
    const roomTypes = await this.roomTypeCache.getRoomTypes(propertyId);

    if (roomTypes.length > 0) {
      const firstRoom = roomTypes[0];
      return firstRoom.roomTypeID || firstRoom.id;
    }

    return undefined;
  }

  /**
   * Resolve a room type ID using multiple strategies
   * @param roomTypeId Optional room type ID to validate
   * @param roomName Optional room name to use for resolution
   * @param propertyId The property ID
   * @returns A valid room type ID or undefined if not found
   */
  async resolveRoomTypeId(
    roomTypeId: string | undefined,
    roomName: string | undefined,
    propertyId: string
  ): Promise<string | undefined> {
    console.log(`Resolving room type ID: ${roomTypeId}, name: ${roomName}, property: ${propertyId}`);

    // Strategy 1: Validate the provided room type ID
    if (roomTypeId) {
      const isValid = await this.validateRoomTypeId(roomTypeId, propertyId);
      if (isValid) {
        console.log(`Room type ID ${roomTypeId} is valid`);
        return roomTypeId;
      }
    }

    // Strategy 2: Find by room name
    if (roomName) {
      const idByName = await this.findRoomTypeIdByName(roomName, propertyId);
      if (idByName) {
        console.log(`Found room type ID ${idByName} by name "${roomName}"`);
        return idByName;
      }

      // Strategy 3: Find by pattern
      const idByPattern = await this.findRoomTypeByPattern(roomName, propertyId);
      if (idByPattern) {
        console.log(`Found room type ID ${idByPattern} by pattern matching "${roomName}"`);
        return idByPattern;
      }
    }

    // Strategy 4: Use default room type
    const defaultId = await this.getDefaultRoomTypeId(propertyId);
    if (defaultId) {
      console.log(`Using default room type ID ${defaultId}`);
      return defaultId;
    }

    console.warn(`Could not resolve room type ID for property ${propertyId}`);
    return undefined;
  }
}
