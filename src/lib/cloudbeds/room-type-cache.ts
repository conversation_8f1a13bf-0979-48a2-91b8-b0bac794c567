import type { CloudbedsApiService } from './api.server';
import type { CloudbedsRoomType } from './types';

/**
 * Manages the caching of room types with intelligent refresh strategies
 */
export class RoomTypeCache {
  private cache: Map<string, {
    roomTypes: CloudbedsRoomType[],
    lastUpdated: number,
    expiresAt: number
  }> = new Map();

  private readonly DEFAULT_CACHE_DURATION_MS = 3600000; // 1 hour

  constructor(
    private readonly apiService: CloudbedsApiService,
    private readonly cacheDuration: number = 3600000
  ) {}

  /**
   * Get room types for a specific property
   * @param propertyId The property ID
   * @param forceRefresh Force a cache refresh
   * @returns Array of room types
   */
  async getRoomTypes(propertyId: string, forceRefresh: boolean = false): Promise<CloudbedsRoomType[]> {
    const cacheKey = `property_${propertyId}`;
    const now = Date.now();

    // Check if we have a valid cache entry
    if (!forceRefresh && this.cache.has(cacheKey)) {
      const cacheEntry = this.cache.get(cacheKey);

      if (cacheEntry && now < cacheEntry.expiresAt) {
        console.log(`Using cached room types for property ${propertyId}`);
        return cacheEntry.roomTypes;
      }
    }

    // Fetch fresh data
    console.log(`Fetching room types for property ${propertyId}`);
    try {
      const response = await this.apiService.getRoomTypes(false, false);

      if (response.success && response.data && response.data.length > 0) {
        // Update cache
        this.cache.set(cacheKey, {
          roomTypes: response.data,
          lastUpdated: now,
          expiresAt: now + this.cacheDuration
        });

        return response.data;
      } else {
        console.warn(`No room types found for property ${propertyId}`);
        return [];
      }
    } catch (error) {
      console.error(`Error fetching room types for property ${propertyId}:`, error);

      // Return cached data if available, even if expired
      if (this.cache.has(cacheKey)) {
        const cacheEntry = this.cache.get(cacheKey);
        console.warn(`Using expired cache for property ${propertyId} due to API error`);
        return cacheEntry.roomTypes;
      }

      return [];
    }
  }

  /**
   * Build a mapping of room names to room type IDs
   * @param propertyId The property ID
   * @returns Map of room names to room type IDs
   */
  async buildRoomTypeMapping(propertyId: string): Promise<Map<string, string>> {
    const roomTypes = await this.getRoomTypes(propertyId);
    const mapping = new Map<string, string>();

    for (const roomType of roomTypes) {
      const id = roomType.roomTypeID || roomType.id;
      const name = roomType.roomTypeName || roomType.name || '';
      const nameShort = roomType.roomTypeNameShort || '';

      if (id && name) {
        // Store lowercase for case-insensitive matching
        mapping.set(name.toLowerCase(), id);

        if (nameShort) {
          mapping.set(nameShort.toLowerCase(), id);
        }

        // Store name parts for partial matching
        const nameParts = name.toLowerCase().split(/\s+/);

        // Create mappings for common patterns
        if (nameParts.length >= 2) {
          // Check for patterns like "Garden Deluxe", "Ocean Junior", etc.
          const location = nameParts.find(part =>
            ['garden', 'ocean', 'mountain', 'pool', 'beach', 'sea', 'lake'].includes(part)
          );

          const roomType = nameParts.find(part =>
            ['deluxe', 'junior', 'suite', 'standard', 'superior', 'executive', 'premium'].includes(part)
          );

          if (location && roomType) {
            const pattern = `${location} ${roomType}`;
            mapping.set(pattern, id);
          }
        }
      }
    }

    return mapping;
  }

  /**
   * Handle webhook events to refresh cache when needed
   * @param event The webhook event
   * @param propertyId The property ID
   */
  async handleWebhookEvent(event: string, propertyId: string): Promise<void> {
    // Events that should trigger a cache refresh
    const refreshEvents = [
      'reservation/accommodation_type_changed',
      'night_audit/completed'
    ];

    if (refreshEvents.includes(event)) {
      console.log(`Refreshing room type cache for property ${propertyId} due to event: ${event}`);
      await this.getRoomTypes(propertyId, true);
    }
  }

  /**
   * Clear the cache for a specific property
   * @param propertyId The property ID
   */
  clearCache(propertyId: string): void {
    const cacheKey = `property_${propertyId}`;
    this.cache.delete(cacheKey);
  }

  /**
   * Clear the entire cache
   */
  clearAllCaches(): void {
    this.cache.clear();
  }
}
