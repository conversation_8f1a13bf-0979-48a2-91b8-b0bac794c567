/**
 * Store centralizado para la gestión del contexto de sitio actual
 * Este archivo proporciona un store global para manejar el sitio actual
 * y asegurar que todos los componentes tengan acceso al mismo contexto.
 */
import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';
import type { SupabaseClient } from '@supabase/supabase-js';
import { getUserSites } from '$lib/auth/utils';

// Tipos
export interface Site {
  id: string;
  name: string;
  domain?: string;
}

interface SiteContextState {
  currentSite: Site | null;
  availableSites: Site[];
  isLoading: boolean;
  initialized: boolean;
}

// Estado inicial
const initialState: SiteContextState = {
  currentSite: null,
  availableSites: [],
  isLoading: true,
  initialized: false
};

// Crear el store
function createSiteContextStore() {
  const { subscribe, set, update } = writable<SiteContextState>(initialState);
  const storeInstanceId = Math.random().toString(36).substring(2, 7);

  return {
    subscribe,
    update,

    // Inicializar el store con datos de sitios del usuario
    initialize: async (supabase: SupabaseClient, userId: string) => {
      if (!browser) return;

      update(state => ({ ...state, isLoading: true }));
      
      try {
        // Obtener los sitios a los que el usuario tiene acceso
        const userSites = await getUserSites(supabase, userId);
        
        // Mapear los datos a nuestro formato de Site
        const sites: Site[] = userSites.map(site => ({
          id: site.site_id,
          name: site.site_name,
          domain: site.site_domain
        }));
        
        // Si hay sitios disponibles, establecer el primero como sitio actual
        const currentSite = sites.length > 0 ? sites[0] : null;
        
        // Actualizar el store
        update(state => ({
          ...state,
          availableSites: sites,
          currentSite,
          isLoading: false,
          initialized: true
        }));

        // Guardar en localStorage si estamos en el navegador
        if (browser && currentSite) {
          localStorage.setItem('baberrih_current_site', currentSite.id);
        }
      } catch (error) {
        console.error('Error initializing site context:', error);
        update(state => ({ ...state, isLoading: false, initialized: true }));
      }
    },

    // Establecer el sitio actual
    setCurrentSite: (siteId: string) => {
      update(state => {
        const site = state.availableSites.find(s => s.id === siteId) || null;
        
        // Guardar en localStorage si estamos en el navegador
        if (browser && site) {
          localStorage.setItem('baberrih_current_site', site.id);
        }
        
        return {
          ...state,
          currentSite: site
        };
      });
    },

    // Limpiar el contexto de sitio
    clear: () => {
      if (browser) {
        localStorage.removeItem('baberrih_current_site');
      }
      set(initialState);
    }
  };
}

// Crear una instancia del store
export const siteContextStore = createSiteContextStore();

// Derivados para acceso fácil
export const currentSite = derived(siteContextStore, $s => $s.currentSite);
export const availableSites = derived(siteContextStore, $s => $s.availableSites);
export const isLoading = derived(siteContextStore, $s => $s.isLoading);
export const initialized = derived(siteContextStore, $s => $s.initialized);

// Funciones de utilidad
export const getCurrentSite = () => get(currentSite);
export const getAvailableSites = () => get(availableSites);
