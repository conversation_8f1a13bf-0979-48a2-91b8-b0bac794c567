/**
 * Configuración de Stripe (Cliente)
 * Este archivo proporciona una instancia de Stripe y funciones útiles para el cliente
 *
 * NOTA: Este archivo está obsoleto y se mantiene por compatibilidad.
 * Se recomienda usar los módulos en src/lib/stripe/ directamente.
 *
 * SEGURO PARA IMPORTAR DESDE CÓDIGO DEL CLIENTE
 */

// Re-exportar todo desde el nuevo módulo de Stripe (cliente)
export * from './stripe/index';

// Re-exportar la clase StripeService y el adaptador del cliente
export { StripeService } from './stripe/service';
export { stripeClient } from './stripe/client';