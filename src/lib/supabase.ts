import { createBrowserClient, createServerClient } from '@supabase/ssr';
import { env } from '$env/dynamic/public';
import type { Cookies } from '@sveltejs/kit';

// Asegurarse de que las variables de entorno estén definidas
const supabaseUrl = env.PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = env.PUBLIC_SUPABASE_ANON_KEY || '';

/**
 * Crea un cliente de Supabase para el navegador.
 * Este cliente se utiliza en el lado del cliente.
 */
export function createClient() {
  return createBrowserClient(supabaseUrl, supabaseAnonKey);
}

/**
 * Crea un cliente de Supabase para el servidor.
 * Este cliente se utiliza en el lado del servidor.
 * @param cookies - Las cookies de la solicitud
 */
export function createServerSupabaseClient(cookies: Cookies) {
  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      get: (key) => cookies.get(key),
      set: (key, value, options) => {
        const cookieOptions = {
          ...options,
          path: '/',
          sameSite: 'lax' as const,
          secure: process.env.NODE_ENV === 'production'
        };
        cookies.set(key, value, cookieOptions);
      },
      remove: (key, options) => {
        const cookieOptions = {
          ...options,
          path: '/'
        };
        cookies.delete(key, cookieOptions);
      }
    }
  });
} 