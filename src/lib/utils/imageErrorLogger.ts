/**
 * Image Error Logger
 *
 * This utility provides functions to track and log image loading errors across the application.
 * It can be used to debug image loading issues and collect error statistics.
 */

import { browser } from '$app/environment';

// Store for image loading errors
const imageErrors: Record<string, any>[] = [];

/**
 * Initialize the global image error listener
 */
export function initImageErrorLogger() {
  if (!browser) return;

  try {
    // Listen for custom imageError events from OptimizedImage component
    window.addEventListener('imageError', ((e: CustomEvent) => {
      const { src, errorDetails } = e.detail;
      console.group('Image Error Logger');
      console.error(`Image failed to load: ${src}`);
      console.table(errorDetails);
      console.groupEnd();

      // Store the error for later analysis
      imageErrors.push({
        ...errorDetails,
        timestamp: new Date().toISOString()
      });

      // Log to console how many errors we've collected
      console.log(`Total image errors collected: ${imageErrors.length}`);
    }) as EventListener);

    console.log('Image Error Logger initialized');
  } catch (error) {
    console.error('Failed to initialize Image Error Logger:', error);
  }
}

/**
 * Get all collected image errors
 */
export function getImageErrors() {
  if (!browser) return [];
  return [...imageErrors];
}

/**
 * Clear all collected image errors
 */
export function clearImageErrors() {
  if (!browser) return true;
  imageErrors.length = 0;
  return true;
}

/**
 * Get statistics about image errors
 */
export function getImageErrorStats() {
  if (!browser || imageErrors.length === 0) {
    return { count: 0, message: 'No image errors collected' };
  }

  try {
    // Count errors by source type
    const localErrors = imageErrors.filter(err => err.isLocal).length;
    const remoteErrors = imageErrors.filter(err => err.isRemote).length;

    // Count errors by URL domain
    const domainErrors: Record<string, number> = {};
    imageErrors.forEach(err => {
      if (err.isRemote && err.src) {
        try {
          const domain = new URL(err.src).hostname;
          domainErrors[domain] = (domainErrors[domain] || 0) + 1;
        } catch (e) {
          // Skip invalid URLs
        }
      }
    });

    return {
      count: imageErrors.length,
      localErrors,
      remoteErrors,
      domainErrors,
      mostRecentError: imageErrors[imageErrors.length - 1]
    };
  } catch (error) {
    console.error('Error getting image error stats:', error);
    return { count: imageErrors.length, message: 'Error processing stats' };
  }
}
