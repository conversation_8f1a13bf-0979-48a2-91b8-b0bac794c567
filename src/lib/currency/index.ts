/**
 * Currency module exports
 * This file exports all currency-related functionality
 */

// Export from store
export {
  currency,
  setCurrency,
  supportedCurrencies,
  availableCurrencies,
  exchangeRates,
  isCurrencySupported,
  addCurrency,
  convertPrice,
  formatPrice,
  formatCurrentPrice,
  getCurrencySymbol
} from './store';

// Export from conversion service
export {
  currencyConversionService,
  type ExchangeRates,
  type ConversionResult
} from './conversion-service';

// Export default currency selector component
export { default as CurrencySelector } from '../components/layout/CurrencySelector.svelte';
