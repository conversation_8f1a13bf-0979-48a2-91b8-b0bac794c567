/**
 * Currency initialization
 * This file initializes the currency conversion service
 */
import { browser } from '$app/environment';
import { currencyConversionService } from './conversion-service';

// Initialize currency service on client side
if (browser) {
  // Fetch exchange rates when the app starts
  currencyConversionService.fetchExchangeRates()
    .then(() => {
      console.log('Exchange rates loaded successfully');
    })
    .catch(error => {
      console.error('Error loading exchange rates:', error);
    });
}

export default currencyConversionService;
