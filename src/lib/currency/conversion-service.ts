/**
 * Servicio para la conversión de divisas
 * Este archivo proporciona funciones para obtener y gestionar tasas de cambio
 */
import { browser } from '$app/environment';
import { updateExchangeRates } from './store';

// Tipos para el servicio de conversión
export interface ExchangeRates {
  [key: string]: number;
}

export interface ConversionResult {
  amount: number;
  fromCurrency: string;
  toCurrency: string;
  rate: number;
  convertedAmount: number;
}

// Clase para el servicio de conversión
export class CurrencyConversionService {
  private cacheKey = 'baberrih_exchange_rates';
  private cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  private apiEndpoint: string | null = null;
  private fallbackRates: ExchangeRates = {
    MAD: 1,     // Base currency
    EUR: 0.091, // 1 MAD = 0.091 EUR
    USD: 0.099, // 1 MAD = 0.099 USD
    GBP: 0.078, // 1 MAD = 0.078 GBP
  };

  constructor(options: {
    apiEndpoint?: string;
  } = {}) {
    this.apiEndpoint = options.apiEndpoint || null;

    // Load cached rates if available
    if (browser) {
      this.loadCachedRates();
    }
  }

  /**
   * Load cached exchange rates from localStorage
   */
  private loadCachedRates(): void {
    const cachedData = localStorage.getItem(this.cacheKey);
    if (cachedData) {
      try {
        const { rates, timestamp } = JSON.parse(cachedData);
        const now = Date.now();
        
        // Check if cache is still valid
        if (now - timestamp < this.cacheExpiry) {
          console.log('Using cached exchange rates');
          updateExchangeRates(rates);
        } else {
          console.log('Cached exchange rates expired, will fetch new rates');
          this.fetchExchangeRates();
        }
      } catch (e) {
        console.error('Error parsing cached exchange rates:', e);
        this.fetchExchangeRates();
      }
    } else {
      // No cached rates, use fallback rates
      console.log('No cached exchange rates, using fallback rates');
      updateExchangeRates(this.fallbackRates);
    }
  }

  /**
   * Save exchange rates to localStorage
   */
  private saveRatesToCache(rates: ExchangeRates): void {
    if (!browser) return;

    const cacheData = {
      rates,
      timestamp: Date.now()
    };

    try {
      localStorage.setItem(this.cacheKey, JSON.stringify(cacheData));
    } catch (e) {
      console.error('Error saving exchange rates to cache:', e);
    }
  }

  /**
   * Fetch exchange rates from API
   */
  public async fetchExchangeRates(): Promise<ExchangeRates> {
    // If no API endpoint is configured, use fallback rates
    if (!this.apiEndpoint) {
      console.log('No API endpoint configured, using fallback rates');
      updateExchangeRates(this.fallbackRates);
      return this.fallbackRates;
    }

    try {
      const response = await fetch(this.apiEndpoint);
      
      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Process the API response to extract rates
      // This will depend on the specific API being used
      const rates = this.processApiResponse(data);
      
      // Update the store with new rates
      updateExchangeRates(rates);
      
      // Save to cache
      this.saveRatesToCache(rates);
      
      return rates;
    } catch (error) {
      console.error('Error fetching exchange rates:', error);
      
      // Use fallback rates in case of error
      updateExchangeRates(this.fallbackRates);
      return this.fallbackRates;
    }
  }

  /**
   * Process API response to extract exchange rates
   * This method should be customized based on the API being used
   */
  private processApiResponse(data: any): ExchangeRates {
    // This is a placeholder implementation
    // It should be adapted to match the structure of the API response
    
    // Example for a hypothetical API:
    // return {
    //   MAD: 1,
    //   EUR: data.rates.EUR,
    //   USD: data.rates.USD,
    //   GBP: data.rates.GBP,
    // };
    
    // For now, return fallback rates
    return this.fallbackRates;
  }

  /**
   * Convert an amount from one currency to another
   */
  public convert(amount: number, fromCurrency: string, toCurrency: string, rates: ExchangeRates): ConversionResult {
    // If currencies are the same, no conversion needed
    if (fromCurrency === toCurrency) {
      return {
        amount,
        fromCurrency,
        toCurrency,
        rate: 1,
        convertedAmount: amount
      };
    }
    
    // Check if we have the exchange rates
    if (!rates[fromCurrency] || !rates[toCurrency]) {
      console.warn(`Exchange rate not available for ${fromCurrency} to ${toCurrency}`);
      return {
        amount,
        fromCurrency,
        toCurrency,
        rate: 1,
        convertedAmount: amount // Return original amount if conversion not possible
      };
    }
    
    // Calculate the exchange rate
    const rate = rates[toCurrency] / rates[fromCurrency];
    
    // Convert the amount
    const convertedAmount = amount * rate;
    
    return {
      amount,
      fromCurrency,
      toCurrency,
      rate,
      convertedAmount
    };
  }
}

// Create and export a singleton instance
export const currencyConversionService = new CurrencyConversionService({
  // Configure with an API endpoint if available
  // apiEndpoint: 'https://api.example.com/exchange-rates'
});
