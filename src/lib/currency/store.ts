/**
 * Store centralizado para la gestión de divisas
 * Este archivo proporciona un store global para manejar las divisas y su conversión
 */
import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';

// Define supported currencies
export const supportedCurrencies = [
  { code: "MAD", name: "Moroccan Dirham", symbol: "MAD" },
  { code: "EUR", name: "Euro", symbol: "€" },
  { code: "USD", name: "US Dollar", symbol: "$" },
  { code: "GBP", name: "British Pound", symbol: "£" },
];

// Configuración para añadir nuevas divisas fácilmente
export const availableCurrencies = writable<string[]>(supportedCurrencies.map(c => c.code));

// Define currency store
export const currency = writable<string>(getInitialCurrency());

// Exchange rates store (relative to base currency MAD)
export const exchangeRates = writable<Record<string, number>>({
  MAD: 1, // Base currency
  EUR: 0.091, // 1 MAD = 0.091 EUR
  USD: 0.099, // 1 MAD = 0.099 USD
  GBP: 0.078, // 1 MAD = 0.078 GBP
});

// Function to get initial currency from localStorage or browser settings
function getInitialCurrency(): string {
  if (!browser) return 'MAD'; // Default to MAD on server

  // Check localStorage first
  const storedCurrency = localStorage.getItem('baberrih_currency');
  if (storedCurrency && isCurrencySupported(storedCurrency)) {
    return storedCurrency;
  }

  // Default to MAD (Moroccan Dirham)
  return 'MAD';
}

// Helper function to check if a currency is supported
export function isCurrencySupported(currencyCode: string): boolean {
  return get(availableCurrencies).includes(currencyCode);
}

// Function to set currency and save to localStorage
export function setCurrency(currencyCode: string): void {
  if (!isCurrencySupported(currencyCode)) {
    console.warn(`Currency ${currencyCode} is not supported. Defaulting to MAD.`);
    currencyCode = 'MAD';
  }
  
  currency.set(currencyCode);
  
  if (browser) {
    localStorage.setItem('baberrih_currency', currencyCode);
  }
}

// Function to add a new currency dynamically
export function addCurrency(currencyCode: string, currencyName: string, currencySymbol?: string): void {
  if (isCurrencySupported(currencyCode)) {
    console.warn(`Currency ${currencyCode} is already supported.`);
    return;
  }
  
  // Añadir a la lista de divisas disponibles
  availableCurrencies.update(currencies => [...currencies, currencyCode]);
  
  // Añadir a la lista de divisas soportadas con UI
  if (currencyName) {
    const symbol = currencySymbol || currencyCode;
    supportedCurrencies.push({ code: currencyCode, name: currencyName, symbol });
  }
  
  console.log(`Added support for currency: ${currencyCode} (${currencyName})`);
}

// Function to update exchange rates
export function updateExchangeRates(rates: Record<string, number>): void {
  exchangeRates.update(currentRates => ({
    ...currentRates,
    ...rates
  }));
}

// Function to convert price between currencies
export function convertPrice(amount: number, fromCurrency: string, toCurrency: string): number {
  const rates = get(exchangeRates);
  
  // If currencies are the same, no conversion needed
  if (fromCurrency === toCurrency) return amount;
  
  // Check if we have the exchange rates
  if (!rates[fromCurrency] || !rates[toCurrency]) {
    console.warn(`Exchange rate not available for ${fromCurrency} to ${toCurrency}`);
    return amount; // Return original amount if conversion not possible
  }
  
  // Convert to base currency (MAD) first, then to target currency
  const amountInMAD = amount / rates[fromCurrency];
  return amountInMAD * rates[toCurrency];
}

// Utility function to format price with currency
export function formatPrice(amount: number | null | undefined, currencyCode?: string): string {
  if (amount === null || amount === undefined) return "N/A";
  
  const currentCurrency = currencyCode || get(currency);
  
  try {
    return new Intl.NumberFormat("es-ES", {
      style: "currency",
      currency: currentCurrency,
      maximumFractionDigits: 2,
    }).format(amount);
  } catch (error) {
    console.error("Error formatting price:", error);
    return `${amount} ${currentCurrency}`;
  }
}

// Derived store for formatting prices in the current currency
export const formatCurrentPrice = derived(
  [currency, exchangeRates],
  ([$currency, $exchangeRates]) => {
    return (amount: number | null | undefined, sourceCurrency: string = 'MAD'): string => {
      if (amount === null || amount === undefined) return "N/A";
      
      // Convert the amount if needed
      const convertedAmount = convertPrice(amount, sourceCurrency, $currency);
      
      // Format the converted amount
      return formatPrice(convertedAmount, $currency);
    };
  }
);

// Export a simple function to get currency symbol
export function getCurrencySymbol(currencyCode: string): string {
  const curr = supportedCurrencies.find(c => c.code === currencyCode);
  return curr ? curr.symbol : currencyCode;
}
