<script lang="ts">
  import { onMount, onDestroy } from "svelte";
  import { getImageErrors, getImageErrorStats, clearImageErrors } from "$lib/utils/imageErrorLogger";

  // Props
  const {
    position = "bottom-right",
    showOnLoad = true,
  } = $props<{
    position?: "top-left" | "top-right" | "bottom-left" | "bottom-right";
    showOnLoad?: boolean;
  }>();

  // State
  let visible = $state(showOnLoad);
  let errors = $state<Record<string, any>[]>([]);
  let stats = $state<any>({});
  let updateInterval: number | undefined;

  // Toggle visibility
  function toggleVisibility() {
    visible = !visible;
  }

  // Clear errors
  function clearErrors() {
    clearImageErrors();
    updateStats();
  }

  // Update stats
  function updateStats() {
    errors = getImageErrors();
    stats = getImageErrorStats();
  }

  // Initialize
  onMount(() => {
    if (typeof window !== 'undefined') {
      updateStats();

      // Set up interval to update stats every 2 seconds
      updateInterval = window.setInterval(() => {
        updateStats();
      }, 2000);

      // Listen for custom imageError events
      window.addEventListener('imageError', updateStats);
    }
  });

  // Clean up
  onDestroy(() => {
    if (typeof window !== 'undefined') {
      if (updateInterval) {
        window.clearInterval(updateInterval);
      }
      window.removeEventListener('imageError', updateStats);
    }
  });
</script>

<div class="image-error-debug {position} {visible ? 'visible' : 'hidden'}">
  <div class="debug-header">
    <h3>Image Error Debug</h3>
    <div class="debug-actions">
      <button class="debug-button" onclick={clearErrors}>Clear</button>
      <button class="debug-button" onclick={toggleVisibility}>
        {visible ? 'Hide' : 'Show'}
      </button>
    </div>
  </div>

  <div class="debug-content">
    <div class="debug-stats">
      <p>Total Errors: <strong>{stats.count || 0}</strong></p>
      {#if stats.count > 0}
        <p>Local Images: <strong>{stats.localErrors || 0}</strong></p>
        <p>Remote Images: <strong>{stats.remoteErrors || 0}</strong></p>

        {#if stats.domainErrors && Object.keys(stats.domainErrors).length > 0}
          <div class="domain-errors">
            <p>Errors by Domain:</p>
            <ul>
              {#each Object.entries(stats.domainErrors) as [domain, count]}
                <li>{domain}: <strong>{count}</strong></li>
              {/each}
            </ul>
          </div>
        {/if}

        {#if stats.mostRecentError}
          <div class="recent-error">
            <p>Most Recent Error:</p>
            <p class="error-url">{stats.mostRecentError.src}</p>
            <p>Type: {stats.mostRecentError.isLocal ? 'Local' : 'Remote'}</p>
            <p>Time: {new Date(stats.mostRecentError.timestamp).toLocaleTimeString()}</p>
          </div>
        {/if}
      {/if}
    </div>

    {#if errors.length > 0}
      <div class="error-list">
        <h4>Error List ({errors.length})</h4>
        <div class="error-items">
          {#each errors as error, i}
            <div class="error-item">
              <p>#{i+1}: {error.src.substring(0, 30)}{error.src.length > 30 ? '...' : ''}</p>
              <p class="error-type">{error.isLocal ? 'Local' : 'Remote'}</p>
            </div>
          {/each}
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .image-error-debug {
    position: fixed;
    z-index: 9999;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 4px;
    width: 300px;
    max-height: 400px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 12px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  .image-error-debug.hidden {
    opacity: 0.2;
    transform: scale(0.8);
    pointer-events: none;
  }

  .image-error-debug.visible {
    opacity: 1;
    transform: scale(1);
    pointer-events: auto;
  }

  .image-error-debug:hover {
    opacity: 1;
    pointer-events: auto;
  }

  .image-error-debug.top-left {
    top: 10px;
    left: 10px;
  }

  .image-error-debug.top-right {
    top: 10px;
    right: 10px;
  }

  .image-error-debug.bottom-left {
    bottom: 10px;
    left: 10px;
  }

  .image-error-debug.bottom-right {
    bottom: 10px;
    right: 10px;
  }

  .debug-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background-color: #dc3545;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  .debug-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: bold;
  }

  .debug-actions {
    display: flex;
    gap: 4px;
  }

  .debug-button {
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 2px 6px;
    border-radius: 2px;
    cursor: pointer;
    font-size: 10px;
  }

  .debug-button:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }

  .debug-content {
    padding: 8px;
  }

  .debug-stats p {
    margin: 4px 0;
  }

  .domain-errors, .recent-error {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  .domain-errors ul {
    margin: 4px 0;
    padding-left: 20px;
  }

  .error-url {
    word-break: break-all;
    color: #ffc107;
  }

  .error-list {
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  .error-list h4 {
    margin: 0 0 8px 0;
    font-size: 12px;
  }

  .error-items {
    max-height: 150px;
    overflow-y: auto;
  }

  .error-item {
    padding: 4px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .error-item p {
    margin: 2px 0;
  }

  .error-type {
    font-size: 10px;
    color: #6c757d;
  }
</style>
