<script lang="ts">
  import { page } from "$app/stores";
  import { Globe, DollarSign } from "lucide-svelte";
  import { T } from "$lib/i18n";
  import { language, setLanguage, supportedLanguages } from "$lib/i18n";
  import { currency, setCurrency, supportedCurrencies } from "$lib/currency/store";
  import { user, isAdmin } from "$lib/auth";
  import { enhance } from "$app/forms";
  import { getSupabaseClient } from "$lib/supabase.client";
  import { clearBrowserAuthCookies } from "$lib/auth/cookie-utils";
  import { User, LogOut } from "lucide-svelte";

  let { showMobileMenu = false, toggleMobileMenu = () => {} } = $props();

  // Check if current page matches the link
  function isActive(path: string): boolean {
    return (
      $page.url.pathname === path || $page.url.pathname.startsWith(path + "/")
    );
  }
</script>

<!-- Mobile Menu -->
<div
  class="z-40 fixed inset-0 bg-primary-50/95 backdrop-blur-sm pt-20 transition-opacity duration-300 {showMobileMenu
    ? 'opacity-100'
    : 'opacity-0 pointer-events-none'}"
>
  <div class="flex flex-col p-6 h-full overflow-y-auto">
    <!-- Mobile Logo -->
    <div class="flex justify-center mb-8">
      <img src="/baberrih-logo.svg" alt="Baberrih Logo" class="w-auto h-16" />
    </div>
    <div class="flex flex-col space-y-5">
      <a
        href="/location"
        onclick={() => showMobileMenu && toggleMobileMenu()}
        class="font-montserrat font-light text-primary-900 hover:text-primary-700 text-xl uppercase transition-colors relative {isActive(
          '/location'
        )
          ? 'text-primary-700 pl-4 border-l-2 border-primary-500'
          : ''}"
      >
        <T key="nav.location" />
      </a>
      <a
        href="/accommodation"
        onclick={() => showMobileMenu && toggleMobileMenu()}
        class="font-montserrat font-light text-primary-900 hover:text-primary-700 text-xl uppercase transition-colors relative {isActive(
          '/accommodation'
        )
          ? 'text-primary-700 pl-4 border-l-2 border-primary-500'
          : ''}"
      >
        <T key="nav.accommodation" />
      </a>
      <a
        href="/facilities"
        onclick={() => showMobileMenu && toggleMobileMenu()}
        class="font-montserrat font-light text-primary-900 hover:text-primary-700 text-xl uppercase transition-colors relative {isActive(
          '/facilities'
        )
          ? 'text-primary-700 pl-4 border-l-2 border-primary-500'
          : ''}"
      >
        <T key="nav.facilities" />
      </a>
      <a
        href="/restaurant"
        onclick={() => showMobileMenu && toggleMobileMenu()}
        class="font-montserrat font-light text-primary-900 hover:text-primary-700 text-xl uppercase transition-colors relative {isActive(
          '/restaurant'
        )
          ? 'text-primary-700 pl-4 border-l-2 border-primary-500'
          : ''}"
      >
        <T key="nav.restaurant" />
      </a>
      <a
        href="/experiences"
        onclick={() => showMobileMenu && toggleMobileMenu()}
        class="font-montserrat font-light text-primary-900 hover:text-primary-700 text-xl uppercase transition-colors relative {isActive(
          '/experiences'
        )
          ? 'text-primary-700 pl-4 border-l-2 border-primary-500'
          : ''}"
      >
        <T key="nav.experiences" />
      </a>
      <a
        href="/blog"
        onclick={() => showMobileMenu && toggleMobileMenu()}
        class="font-montserrat font-light text-primary-900 hover:text-primary-700 text-xl uppercase transition-colors relative {isActive(
          '/blog'
        )
          ? 'text-primary-700 pl-4 border-l-2 border-primary-500'
          : ''}"
      >
        <T key="nav.blog" />
      </a>
      <a
        href="/contact"
        onclick={() => showMobileMenu && toggleMobileMenu()}
        class="font-montserrat font-light text-primary-900 hover:text-primary-700 text-xl uppercase transition-colors relative {isActive(
          '/contact'
        )
          ? 'text-primary-700 pl-4 border-l-2 border-primary-500'
          : ''}"
      >
        <T key="nav.contact" />
      </a>

      <a
        href="/reservations/search"
        onclick={() => showMobileMenu && toggleMobileMenu()}
        class="font-montserrat font-light text-primary-900 hover:text-primary-700 text-xl uppercase transition-colors relative {isActive(
          '/reservations/search'
        )
          ? 'text-primary-700 pl-4 border-l-2 border-primary-500'
          : ''}"
      >
        <T key="nav.findReservation" />
      </a>
    </div>

    <!-- Language Options -->
    <div class="mt-8 pt-6 border-primary-200 border-t">
      <div class="flex items-center mb-4">
        <Globe class="mr-2 w-5 h-5 text-primary-700" />
        <p
          class="font-montserrat font-light text-primary-900 text-sm uppercase"
        >
          <T key="common.language" />
        </p>
      </div>
      <div class="gap-2 grid grid-cols-2 sm:grid-cols-3">
        {#each supportedLanguages as lang}
          <button
            onclick={() => {
              console.log("Changing language to:", lang.code);
              setLanguage(lang.code);
            }}
            class="text-left px-3 py-2 text-sm font-montserrat font-light hover:bg-primary-100 rounded-sm transition-colors {$language ===
            lang.code
              ? 'bg-primary-100 font-medium'
              : ''}"
            aria-label={`Switch language to ${lang.name}`}
          >
            <div class="flex items-center">
              <img
                src={lang.flag}
                alt={`${lang.code} flag`}
                class="mr-2 rounded-sm w-4 h-4"
              />
              {lang.name}
            </div>
          </button>
        {/each}
      </div>
    </div>

    <!-- Currency Options -->
    <div class="mt-6 pt-6 border-primary-200 border-t">
      <div class="flex items-center mb-4">
        <DollarSign class="mr-2 w-5 h-5 text-primary-700" />
        <p
          class="font-montserrat font-light text-primary-900 text-sm uppercase"
        >
          <T key="common.currency" />
        </p>
      </div>
      <div class="gap-2 grid grid-cols-2 sm:grid-cols-3">
        {#each supportedCurrencies as curr}
          <button
            onclick={() => {
              console.log("Changing currency to:", curr.code);
              setCurrency(curr.code);
            }}
            class="text-left px-3 py-2 text-sm font-montserrat font-light hover:bg-primary-100 rounded-sm transition-colors {$currency ===
            curr.code
              ? 'bg-primary-100 font-medium'
              : ''}"
            aria-label={`Switch currency to ${curr.name}`}
          >
            <div class="flex items-center">
              <span class="inline-block mr-2 w-4 text-center">{curr.symbol}</span>
              {curr.code}
            </div>
          </button>
        {/each}
      </div>
    </div>

    <!-- Login/Admin Button -->
    <div class="mt-6 pt-6 border-primary-200 border-t">
      {#if $user}
        {#if $isAdmin}
          <a
            href="/admin"
            onclick={() => showMobileMenu && toggleMobileMenu()}
            class="flex items-center hover:bg-primary-100 px-4 py-3 rounded-sm font-montserrat text-primary-900 text-sm uppercase tracking-wide"
          >
            <User class="mr-3 w-4 h-4" />
            Panel de administración
          </a>
        {:else}
          <a
            href="/private"
            onclick={() => showMobileMenu && toggleMobileMenu()}
            class="flex items-center hover:bg-primary-100 px-4 py-3 rounded-sm font-montserrat text-primary-900 text-sm uppercase tracking-wide"
          >
            <User class="mr-3 w-4 h-4" />
            Mi cuenta
          </a>
        {/if}

        <form
          action="/auth?/logout"
          method="POST"
          use:enhance={async () => {
            console.log(
              "[LAYOUT MOBILE] Botón de logout presionado, iniciando use:enhance"
            );
            try {
              // Obtener un cliente fresco para asegurar que tenemos los datos más recientes
              const client = getSupabaseClient();

              if (client) {
                // Step 1: Client-side sign out
                const { error: signOutError } = await client.auth.signOut();
                if (signOutError) {
                  console.error(
                    "[LAYOUT MOBILE] Error en client.auth.signOut():",
                    signOutError
                  );
                } else {
                  console.log(
                    "[LAYOUT MOBILE] client.auth.signOut() completado en cliente."
                  );
                }
              } else {
                console.error(
                  "[LAYOUT MOBILE] No se pudo crear un cliente de Supabase para logout"
                );
              }

              // Step 2: Update the auth store
              // authStore is already imported at the top

              // Step 3: Manually clear all auth cookies and localStorage
              clearBrowserAuthCookies();

              // Close mobile menu
              if (showMobileMenu) toggleMobileMenu();
            } catch (e) {
              console.error(
                "[LAYOUT MOBILE] Excepción durante logout en cliente:",
                e
              );
            }

            return async ({ result }) => {
              console.log(
                "[LAYOUT MOBILE] use:enhance callback - resultado del servidor:",
                result
              );
              // Invalidar la sesión en el cliente también
              if (result.type === "redirect") {
                console.log(
                  `[LAYOUT MOBILE] Redirigiendo a: ${result.location}`
                );
                // Forzar una recarga completa para asegurar que todas las cookies se limpien
                window.location.href = result.location;
              }
            };
          }}
        >
          <button
            type="submit"
            class="flex items-center hover:bg-primary-100 px-4 py-3 rounded-sm w-full font-montserrat text-error text-sm uppercase tracking-wide"
          >
            <LogOut class="mr-3 w-4 h-4" />
            Cerrar sesión
          </button>
        </form>
      {:else}
        <a
          href="/auth"
          onclick={() => showMobileMenu && toggleMobileMenu()}
          class="flex items-center hover:bg-primary-100 px-4 py-3 rounded-sm font-montserrat text-primary-900 text-sm uppercase tracking-wide"
        >
          <User class="mr-3 w-4 h-4" />
          Iniciar sesión
        </a>
      {/if}
    </div>

    <!-- Book Now Button -->
    <div class="mt-6 pt-6">
      <a
        href="/accommodation/reservations"
        class="block hover:bg-primary-600 py-3 w-full text-center transition-colors button"
      >
        <T key="common.bookNow" />
      </a>
    </div>
  </div>
</div>
