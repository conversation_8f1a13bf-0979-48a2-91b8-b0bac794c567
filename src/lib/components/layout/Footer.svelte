<script lang="ts">
  import { T } from "$lib/i18n";
</script>

<footer class="bg-primary-100 mt-12 py-12">
  <div class="mx-auto px-4 container">
    <div class="gap-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
      <div class="lg:col-span-1">
        <div class="mb-4">
          <img
            src="/baberrih-logo.svg"
            alt="Baberrih Logo"
            class="mb-2 w-auto h-16"
          />
        </div>
        <p
          class="mb-4 font-montserrat font-light text-primary-800 text-sm leading-relaxed"
        >
          Tissa, Essaouira, Morocco
        </p>
        <a
          href="tel:+212633333398"
          class="flex items-center mb-3 font-montserrat font-light text-primary-800 hover:text-primary-700 text-sm transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="mr-2 w-4 h-4"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path
              d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
            ></path>
          </svg>
          +212 633333398
        </a>
        <a
          href="mailto:<EMAIL>"
          class="flex items-center mb-3 font-montserrat font-light text-primary-800 hover:text-primary-700 text-sm transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="mr-2 w-4 h-4"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path
              d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
            ></path>
            <polyline points="22,6 12,13 2,6"></polyline>
          </svg>
          <EMAIL>
        </a>
        <a
          href="/contact"
          class="flex items-center font-montserrat font-light text-primary-800 hover:text-primary-700 text-sm transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="mr-2 w-4 h-4"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
            <circle cx="12" cy="10" r="3"></circle>
          </svg>
          <T key="common.contactUs" />
        </a>
      </div>

      <div class="lg:col-span-1">
        <h3
          class="mb-4 font-montserrat font-light text-primary-900 text-lg uppercase"
        >
          <T key="footer.quickLinks" />
        </h3>
        <ul class="space-y-2">
          <li>
            <a
              href="/location"
              class="flex items-center font-montserrat font-light text-primary-800 hover:text-primary-700 text-sm transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="mr-2 w-3 h-3"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
              <T key="nav.location" />
            </a>
          </li>
          <li>
            <a
              href="/accommodation"
              class="flex items-center font-montserrat font-light text-primary-800 hover:text-primary-700 text-sm transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="mr-2 w-3 h-3"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
              <T key="nav.accommodation" />
            </a>
          </li>
          <li>
            <a
              href="/facilities"
              class="flex items-center font-montserrat font-light text-primary-800 hover:text-primary-700 text-sm transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="mr-2 w-3 h-3"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
              <T key="nav.facilities" />
            </a>
          </li>
          <li>
            <a
              href="/restaurant"
              class="flex items-center font-montserrat font-light text-primary-800 hover:text-primary-700 text-sm transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="mr-2 w-3 h-3"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
              <T key="nav.restaurant" />
            </a>
          </li>
          <li>
            <a
              href="/experiences"
              class="flex items-center font-montserrat font-light text-primary-800 hover:text-primary-700 text-sm transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="mr-2 w-3 h-3"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
              <T key="nav.experiences" />
            </a>
          </li>
          <li>
            <a
              href="/blog"
              class="flex items-center font-montserrat font-light text-primary-800 hover:text-primary-700 text-sm transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="mr-2 w-3 h-3"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
              <T key="nav.blog" />
            </a>
          </li>
        </ul>
      </div>

      <div class="lg:col-span-2">
        <h3
          class="mb-4 font-montserrat font-light text-primary-900 text-lg uppercase"
        >
          <T key="accommodation.booking.title" />
        </h3>
        <p
          class="mb-4 font-montserrat font-light text-primary-800 text-sm leading-relaxed"
        >
          <T key="accommodation.booking.description" />
        </p>
        <div class="flex flex-wrap gap-3">
          <a
            href="/accommodation/reservations"
            class="hover:bg-primary-600 transition-colors button"
          >
            <T key="common.bookNow" />
          </a>
        </div>

        <!-- Social Media Links -->
        <div class="mt-6 pt-6 border-primary-200 border-t">
          <h4
            class="mb-3 font-montserrat font-light text-primary-900 text-sm uppercase"
          >
            <T key="footer.followUs" />
          </h4>
          <div class="flex space-x-4">
            <a
              href="https://instagram.com"
              target="_blank"
              class="text-primary-800 hover:text-primary-700 transition-colors"
              aria-label="Instagram"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-5 h-5"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"
                ></path>
                <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
              </svg>
            </a>
            <a
              href="https://facebook.com"
              target="_blank"
              class="text-primary-800 hover:text-primary-700 transition-colors"
              aria-label="Facebook"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-5 h-5"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path
                  d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"
                ></path>
              </svg>
            </a>
            <a
              href="https://twitter.com"
              target="_blank"
              class="text-primary-800 hover:text-primary-700 transition-colors"
              aria-label="Twitter"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-5 h-5"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path
                  d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"
                ></path>
              </svg>
            </a>
            <a
              href="https://pinterest.com"
              target="_blank"
              class="text-primary-800 hover:text-primary-700 transition-colors"
              aria-label="Pinterest"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="w-5 h-5"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path d="M8 12h8"></path>
                <path d="M12 8v8"></path>
                <circle cx="12" cy="12" r="10"></circle>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-8 pt-8 border-primary-200 border-t text-center">
      <p class="font-montserrat font-light text-primary-800 text-xs">
        <T
          key="footer.copyright"
          params={{ year: new Date().getFullYear() }}
        />
      </p>
    </div>
  </div>
</footer>
