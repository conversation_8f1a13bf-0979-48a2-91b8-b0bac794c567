<script lang="ts">
  import { User, LogOut } from "lucide-svelte";
  import { user, isAdmin, authStore } from "$lib/auth";
  import { enhance } from "$app/forms";
  import { getSupabaseClient } from "$lib/supabase.client";
  import { clearBrowserAuthCookies } from "$lib/auth/cookie-utils";
</script>

<!-- Login/Admin Button -->
{#if $user}
  <div class="flex items-center">
    {#if $isAdmin}
      <a
        href="/admin"
        class="flex items-center hover:bg-primary-100 mr-3 px-3 py-2 rounded-sm font-montserrat text-sm transition-colors"
      >
        <User class="mr-2 w-4 h-4" />
        Admin
      </a>
    {:else}
      <a
        href="/private"
        class="flex items-center hover:bg-primary-100 mr-3 px-3 py-2 rounded-sm font-montserrat text-sm transition-colors"
      >
        <User class="mr-2 w-4 h-4" />
        Mi cuenta
      </a>
    {/if}

    <form
      action="/auth?/logout"
      method="POST"
      use:enhance={async () => {
        console.log(
          "[LAYOUT] Botón de logout presionado, iniciando use:enhance"
        );
        try {
          // Obtener un cliente fresco para asegurar que tenemos los datos más recientes
          const client = getSupabaseClient();

          if (client) {
            // Step 1: Client-side sign out
            const { error: signOutError } =
              await client.auth.signOut();
            if (signOutError) {
              console.error(
                "[LAYOUT] Error en client.auth.signOut():",
                signOutError
              );
            } else {
              console.log(
                "[LAYOUT] client.auth.signOut() completado en cliente."
              );
            }
          } else {
            console.error(
              "[LAYOUT] No se pudo crear un cliente de Supabase para logout"
            );
          }

          // Step 2: Update the auth store
          authStore.clearSession();

          // Step 3: Manually clear all auth cookies and localStorage
          clearBrowserAuthCookies();
        } catch (e) {
          console.error(
            "[LAYOUT] Excepción durante logout en cliente:",
            e
          );
        }

        return async ({ result }) => {
          console.log(
            "[LAYOUT] use:enhance callback - resultado del servidor:",
            result
          );
          // Invalidar la sesión en el cliente también
          if (result.type === "redirect") {
            console.log(
              `[LAYOUT] Redirigiendo a: ${result.location}`
            );
            // Forzar una recarga completa para asegurar que todas las cookies se limpien
            window.location.href = result.location;
          }
        };
      }}
    >
      <button
        type="submit"
        class="flex items-center hover:bg-primary-100 px-3 py-2 rounded-sm font-montserrat text-error text-sm transition-colors"
      >
        <LogOut class="mr-2 w-4 h-4" />
        Salir
      </button>
    </form>
  </div>
{:else}
  <a
    href="/auth"
    class="flex items-center hover:bg-primary-100 mr-3 px-3 py-2 rounded-sm font-montserrat text-sm transition-colors"
  >
    <User class="mr-2 w-4 h-4" />
    Iniciar sesión
  </a>
{/if}
