<script lang="ts">
  import { ChevronDown } from "lucide-svelte";
  import { currency, setCurrency, supportedCurrencies } from "$lib/currency/store";
  import { T } from "$lib/i18n";

  // Currency handling
  let showCurrencyMenu = $state(false);

  // Toggle currency menu
  function toggleCurrencyMenu() {
    showCurrencyMenu = !showCurrencyMenu;
  }

  // Add click outside listener for currency menu
  function handleClickOutside(event) {
    const target = event.target;
    // Check if the click is outside the currency button and menu
    if (
      showCurrencyMenu &&
      !target.closest(".currency-button") &&
      !target.closest(".currency-menu")
    ) {
      showCurrencyMenu = false;
    }
  }

  // Add event listener when component is mounted
  $effect(() => {
    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  });
</script>

<!-- Currency Selector -->
<div class="relative currency-button">
  <button
    onclick={(e) => {
      e.stopPropagation();
      toggleCurrencyMenu();
    }}
    class="flex items-center font-montserrat font-light text-primary-900 hover:text-primary-700 text-sm uppercase transition-colors"
  >
    <span class="mr-1">{supportedCurrencies.find((c) => c.code === $currency)?.symbol || $currency}</span>
    {$currency}
    <ChevronDown class="ml-1 w-4 h-4" />
  </button>

  {#if showCurrencyMenu}
    <div
      class="right-0 z-50 absolute bg-primary-50 shadow-md mt-2 border border-primary-200 rounded-sm w-40 overflow-hidden currency-menu"
    >
      {#each supportedCurrencies as curr}
        <button
          onclick={() => {
            console.log("Changing currency to:", curr.code);
            setCurrency(curr.code);
            showCurrencyMenu = false;
          }}
          class="block hover:bg-primary-100 px-4 py-2 w-full font-montserrat font-light text-sm text-left transition-colors text-primary-900 {$currency ===
          curr.code
            ? 'bg-primary-100'
            : ''}"
          aria-label={`Switch currency to ${curr.name}`}
        >
          <div class="flex items-center">
            <span class="inline-block mr-2 w-4 text-center">{curr.symbol}</span>
            <span class="truncate">{curr.name}</span>
          </div>
        </button>
      {/each}
    </div>
  {/if}
</div>
