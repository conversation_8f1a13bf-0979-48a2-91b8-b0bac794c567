<script lang="ts">
  import { ChevronDown } from "lucide-svelte";
  import { language, setLanguage, supportedLanguages } from "$lib/i18n";

  // Language handling
  let showLanguageMenu = $state(false);

  // Toggle language menu
  function toggleLanguageMenu() {
    showLanguageMenu = !showLanguageMenu;
  }

  // Add click outside listener for language menu
  function handleClickOutside(event) {
    const target = event.target;
    // Check if the click is outside the language button and menu
    if (
      showLanguageMenu &&
      !target.closest(".language-button") &&
      !target.closest(".language-menu")
    ) {
      showLanguageMenu = false;
    }
  }

  // Add event listener when component is mounted
  $effect(() => {
    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  });
</script>

<!-- Language Selector -->
<div class="relative language-button">
  <button
    onclick={(e) => {
      e.stopPropagation();
      toggleLanguageMenu();
    }}
    class="flex items-center font-montserrat font-light text-primary-900 hover:text-primary-700 text-sm uppercase transition-colors"
  >
    <img
      src={supportedLanguages.find((l) => l.code === $language)?.flag}
      alt={`${$language} flag`}
      class="mr-1 rounded-sm w-4 h-4"
    />
    {$language.toUpperCase()}
    <ChevronDown class="ml-1 w-4 h-4" />
  </button>

  {#if showLanguageMenu}
    <div
      class="right-0 z-50 absolute bg-primary-50 shadow-md mt-2 border border-primary-200 rounded-sm w-40 overflow-hidden language-menu"
    >
      {#each supportedLanguages as lang}
        <button
          onclick={() => {
            console.log("Changing language to:", lang.code);
            setLanguage(lang.code);
            showLanguageMenu = false;
          }}
          class="block hover:bg-primary-100 px-4 py-2 w-full font-montserrat font-light text-sm text-left transition-colors text-primary-900 {$language ===
          lang.code
            ? 'bg-primary-100'
            : ''}"
          aria-label={`Switch language to ${lang.name}`}
        >
          <div class="flex items-center">
            <img
              src={lang.flag}
              alt={`${lang.code} flag`}
              class="mr-2 rounded-sm w-4 h-4"
            />
            <span class="truncate">{lang.name}</span>
          </div>
        </button>
      {/each}
    </div>
  {/if}
</div>
