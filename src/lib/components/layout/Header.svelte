<script lang="ts">
  import { Menu, X, ChevronDown } from "lucide-svelte";
  import { T } from "$lib/i18n";
  import UserMenu from "./UserMenu.svelte";
  import { page } from "$app/stores";

  let {
    isScrolled = false,
    showMobileMenu = false,
    toggleMobileMenu = () => {},
  } = $props();

  // State for dropdown menus
  let showExploreDropdown = false;

  // Check if current page matches the link
  function isActive(path: string): boolean {
    return (
      $page.url.pathname === path || $page.url.pathname.startsWith(path + "/")
    );
  }

  // Check if any path in an array is active
  function isAnyActive(paths: string[]): boolean {
    return paths.some(path => isActive(path));
  }
</script>

<header
  class="top-0 right-0 left-0 z-50 fixed flex items-center h-16 md:h-20 transition-all duration-300 {isScrolled ? '' : 'text-white'}"
>
  <!-- Background layer - separate element for better control -->
  <div
    class="absolute inset-0 transition-all duration-300"
    style="background-color: var(--primary-50);
           opacity: {isScrolled ? '1' : '0'};
           border-bottom: {isScrolled ? '1px solid var(--primary-200)' : 'none'};
           box-shadow: {isScrolled ? '0 2px 4px rgba(0, 0, 0, 0.03)' : 'none'};"
  ></div>
  <div class="mx-auto px-4 container">
    <div class="flex justify-between items-center">
      <!-- Logo - switches between white and regular versions based on scrolling -->
      <a href="/" class="hover:opacity-80 hover:scale-105 transition-all duration-300 transform">
        <img
          src={isScrolled ? "/baberrih-logo.svg" : "/baberrih-logo-white.svg"}
          alt="Baberrih Logo"
          class="w-auto h-12 transition-opacity duration-300"
        />
      </a>

      <!-- Desktop Navigation -->
      <nav class="hidden md:flex items-center space-x-4">
        <!-- Primary Navigation Group -->
        <div class="flex items-center space-x-2">
          <!-- Location Link -->
          <a
            href="/location"
            class="font-montserrat font-light text-sm uppercase px-2 py-1 transition-all duration-300 relative rounded-sm {
              isScrolled
                ? isActive('/location')
                  ? 'text-primary-700'
                  : 'text-primary-900 hover:text-primary-700 hover:bg-primary-100'
                : 'text-white hover:text-white/90 hover:bg-white/10'
            }"
          >
            <T key="nav.location" />
            {#if isActive("/location")}
              <span class="-bottom-1 left-0 absolute bg-primary-500 w-full h-0.5 transition-all duration-300"
              ></span>
            {/if}
          </a>

          <!-- Explore Dropdown -->
          <div class="group relative">
            <button
              class="flex items-center font-montserrat font-light text-sm uppercase px-2 py-1 transition-all duration-300 relative rounded-sm {
                isScrolled
                  ? isAnyActive(['/accommodation', '/facilities', '/restaurant', '/experiences'])
                    ? 'text-primary-700'
                    : 'text-primary-900 hover:text-primary-700 hover:bg-primary-100'
                  : 'text-white hover:text-white/90 hover:bg-white/10'
              }"
              onmouseenter={() => showExploreDropdown = true}
              onmouseleave={() => showExploreDropdown = false}
              onfocus={() => showExploreDropdown = true}
              onblur={() => showExploreDropdown = false}
            >
              <span><T key="nav.explore" /></span>
              <ChevronDown class="ml-1 w-4 h-4 transition-all duration-300" />

              {#if isAnyActive(['/accommodation', '/facilities', '/restaurant', '/experiences'])}
                <span class="-bottom-1 left-0 absolute bg-primary-500 w-full h-0.5 transition-all duration-300"></span>
              {/if}
            </button>

            <!-- Dropdown Menu -->
            <div
              role="menu"
              tabindex="0"
              class="invisible group-hover:visible top-full left-0 absolute opacity-0 group-hover:opacity-100 pt-2 w-56 transition-all duration-200"
              onmouseenter={() => showExploreDropdown = true}
              onmouseleave={() => showExploreDropdown = false}
            >
              <div class="bg-primary-50 shadow-md py-2 border border-primary-200 rounded-sm" style="background-color: rgba(248, 247, 244, 0.98); backdrop-filter: blur(8px);">
                <!-- Section Header -->
                <div class="mb-1 px-4 py-1 border-primary-200 border-b">
                  <h3 class="font-medium text-primary-700 text-xs uppercase">
                    <T key="nav.explore" />
                  </h3>
                </div>

                <!-- Accommodation -->
                <a
                  href="/accommodation"
                  class="flex items-center px-4 py-2 font-montserrat font-light text-sm hover:bg-primary-100/70 {isActive('/accommodation') ? 'text-primary-700 bg-primary-100/50' : 'text-primary-900'}"
                >
                  <span class="flex justify-center items-center mr-2 w-5 h-5">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                      <path d="M2 22V7a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v15"></path>
                      <path d="M2 17h20"></path>
                      <path d="M5 12h14"></path>
                      <path d="M9 7V2"></path>
                      <path d="M15 7V2"></path>
                    </svg>
                  </span>
                  <T key="nav.accommodation" />
                </a>

                <!-- Facilities -->
                <a
                  href="/facilities"
                  class="flex items-center px-4 py-2 font-montserrat font-light text-sm hover:bg-primary-100/70 {isActive('/facilities') ? 'text-primary-700 bg-primary-100/50' : 'text-primary-900'}"
                >
                  <span class="flex justify-center items-center mr-2 w-5 h-5">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                      <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                    </svg>
                  </span>
                  <T key="nav.facilities" />
                </a>

                <!-- Restaurant -->
                <a
                  href="/restaurant"
                  class="flex items-center px-4 py-2 font-montserrat font-light text-sm hover:bg-primary-100/70 {isActive('/restaurant') ? 'text-primary-700 bg-primary-100/50' : 'text-primary-900'}"
                >
                  <span class="flex justify-center items-center mr-2 w-5 h-5">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                      <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path>
                      <path d="M7 2v20"></path>
                      <path d="M21 15V2"></path>
                      <path d="M18 15c-1.1 0-2-.9-2-2v-2c0-1.1.9-2 2-2h3v6h-3Z"></path>
                    </svg>
                  </span>
                  <T key="nav.restaurant" />
                </a>

                <!-- Experiences -->
                <a
                  href="/experiences"
                  class="flex items-center px-4 py-2 font-montserrat font-light text-sm hover:bg-primary-100/70 {isActive('/experiences') ? 'text-primary-700 bg-primary-100/50' : 'text-primary-900'}"
                >
                  <span class="flex justify-center items-center mr-2 w-5 h-5">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                      <circle cx="12" cy="12" r="10"></circle>
                      <path d="m4.93 4.93 4.24 4.24"></path>
                      <path d="m14.83 9.17 4.24-4.24"></path>
                      <path d="m14.83 14.83 4.24 4.24"></path>
                      <path d="m9.17 14.83-4.24 4.24"></path>
                      <circle cx="12" cy="12" r="4"></circle>
                    </svg>
                  </span>
                  <T key="nav.experiences" />
                </a>
              </div>
            </div>
          </div>

          <!-- Blog Link -->
          <a
            href="/blog"
            class="font-montserrat font-light text-sm uppercase px-2 py-1 transition-all duration-300 relative rounded-sm {
              isScrolled
                ? isActive('/blog')
                  ? 'text-primary-700'
                  : 'text-primary-900 hover:text-primary-700 hover:bg-primary-100'
                : 'text-white hover:text-white/90 hover:bg-white/10'
            }"
          >
            <T key="nav.blog" />
            {#if isActive("/blog")}
              <span class="-bottom-1 left-0 absolute bg-primary-500 w-full h-0.5 transition-all duration-300"></span>
            {/if}
          </a>

          <!-- Contact Link -->
          <a
            href="/contact"
            class="font-montserrat font-light text-sm uppercase px-2 py-1 transition-all duration-300 relative rounded-sm {
              isScrolled
                ? isActive('/contact')
                  ? 'text-primary-700'
                  : 'text-primary-900 hover:text-primary-700 hover:bg-primary-100'
                : 'text-white hover:text-white/90 hover:bg-white/10'
            }"
          >
            <T key="nav.contact" />
            {#if isActive("/contact")}
              <span class="-bottom-1 left-0 absolute bg-primary-500 w-full h-0.5 transition-all duration-300"></span>
            {/if}
          </a>
        </div>

        <!-- Utility Navigation Group -->
        <div class="flex items-center space-x-2 ml-4">
          <!-- Find Reservation Link -->
          <a
            href="/reservations/search"
            class="font-montserrat font-light text-sm uppercase px-2 py-1 transition-all duration-300 relative rounded-sm {
              isScrolled
                ? isActive('/reservations/search')
                  ? 'text-primary-700'
                  : 'text-primary-900 hover:text-primary-700 hover:bg-primary-100'
                : 'text-white hover:text-white/90 hover:bg-white/10'
            }"
          >
            <T key="nav.findReservation" />
            {#if isActive("/reservations/search")}
              <span class="-bottom-1 left-0 absolute bg-primary-500 w-full h-0.5 transition-all duration-300"></span>
            {/if}
          </a>

          <!-- User Menu Component (includes language, currency, and auth) -->
          <UserMenu isScrolled={isScrolled} />

          <!-- Book Now Button - only visible after scrolling -->
          <a
            href="/accommodation/reservations"
            class="hover:bg-primary-600 hover:shadow-md ml-2 transition-all duration-500 button {isScrolled ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2 pointer-events-none'}"
          >
            <T key="common.bookNow" />
          </a>
        </div>
      </nav>

      <!-- Mobile Menu Button -->
      <button
        onclick={() => toggleMobileMenu()}
        class="md:hidden p-2 rounded-full transition-all duration-300 {
          isScrolled
            ? 'hover:bg-primary-100 shadow-sm hover:shadow text-primary-900'
            : 'hover:bg-white/10 text-white'
        }"
        aria-label="Toggle mobile menu"
      >
        {#if showMobileMenu}
          <X class="w-6 h-6" />
        {:else}
          <Menu class="w-6 h-6" />
        {/if}
      </button>
    </div>
  </div>
</header>
