<script lang="ts">
  import { User, LogOut } from "lucide-svelte";
  import { user, isAdmin, authStore } from "$lib/auth";
  import { enhance } from "$app/forms";
  import { getSupabaseClient } from "$lib/supabase.client";
  import { clearBrowserAuthCookies } from "$lib/auth/cookie-utils";
  import { T } from "$lib/i18n";
  import LanguageSelector from "$lib/components/ui/LanguageSelector.svelte";
  import CurrencySelector from "$lib/components/ui/CurrencySelector.svelte";

  // Props
  let { isScrolled = false } = $props();

  // State for dropdown menu
  let showUserMenu = $state(false);

  // Toggle user menu
  function toggleUserMenu() {
    showUserMenu = !showUserMenu;
  }

  // Add click outside listener for user menu
  function handleClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;
    // Check if the click is outside the user button and menu
    if (
      showUserMenu &&
      !target.closest(".user-button") &&
      !target.closest(".user-menu")
    ) {
      showUserMenu = false;
    }
  }

  // Add event listener when component is mounted
  $effect(() => {
    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  });
</script>

<!-- User Menu -->
<div class="relative user-button">
  <button
    onclick={(e) => {
      e.stopPropagation();
      toggleUserMenu();
    }}
    class="flex justify-center items-center p-2 rounded-full w-10 h-10 transition-all duration-300 {
      isScrolled
        ? 'hover:bg-primary-100 text-primary-900'
        : 'hover:bg-white/10 text-white'
    }"
    aria-label={$user ? "User menu" : "Login"}
  >
    <User class="w-5 h-5" />
  </button>

  {#if showUserMenu}
    <div
      role="menu"
      tabindex="0"
      class="right-0 z-50 absolute shadow-md mt-2 border border-primary-200 rounded-sm w-64 overflow-hidden user-menu"
      style="background-color: rgba(248, 247, 244, 0.98); backdrop-filter: blur(8px);"
    >
      {#if $user}
        <!-- User is logged in -->
        <div class="px-4 py-3 border-primary-200 border-b">
          <div class="flex items-center">
            <div class="bg-primary-100 mr-3 p-2 rounded-full">
              <User class="w-5 h-5 text-primary-700" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="max-w-[180px] font-montserrat font-medium text-primary-900 text-sm truncate" title={$user.email}>
                {$user.email}
              </div>
              {#if $isAdmin}
                <div class="mt-0.5 text-primary-700 text-xs">
                  Administrator
                </div>
              {/if}
            </div>
          </div>
        </div>

        <!-- Account links -->
        <div class="py-2">
          <div class="px-4 py-1 font-medium text-primary-700 text-xs uppercase">
            <T key="common.account" />
          </div>
          {#if $isAdmin}
            <a
              href="/admin"
              class="flex items-center hover:bg-primary-100/70 px-4 py-2 w-full font-montserrat font-light text-primary-900 text-sm text-left transition-colors"
              onclick={() => showUserMenu = false}
            >
              <User class="mr-2 w-4 h-4" />
              Admin Panel
            </a>
          {:else}
            <a
              href="/private"
              class="flex items-center hover:bg-primary-100/70 px-4 py-2 w-full font-montserrat font-light text-primary-900 text-sm text-left transition-colors"
              onclick={() => showUserMenu = false}
            >
              <User class="mr-2 w-4 h-4" />
              My Account
            </a>
          {/if}
        </div>

        <!-- Preferences section -->
        <div class="py-2 border-primary-200 border-t">
          <div class="px-4 py-1 font-medium text-primary-700 text-xs uppercase">
            <T key="common.preferences" />
          </div>

          <!-- Language selector -->
          <div class="px-4 py-2">
            <LanguageSelector label="Language" />
          </div>

          <!-- Currency selector -->
          <div class="px-4 py-2">
            <CurrencySelector label="Currency" />
          </div>
        </div>

        <!-- Logout section -->
        <div class="py-2 border-primary-200 border-t">
          <form
            action="/auth?/logout"
            method="POST"
            use:enhance={async () => {
              try {
                const client = getSupabaseClient();
                if (client) {
                  await client.auth.signOut();
                }
                authStore.clearSession();
                clearBrowserAuthCookies();
              } catch (e) {
                console.error("Error during logout:", e);
              }
              return async ({ result }) => {
                if (result.type === "redirect") {
                  window.location.href = result.location;
                }
              };
            }}
          >
            <button
              type="submit"
              class="flex items-center hover:bg-primary-100/70 px-4 py-2 w-full font-montserrat font-light text-error text-sm text-left transition-colors"
            >
              <LogOut class="mr-2 w-4 h-4" />
              <T key="nav.logout" />
            </button>
          </form>
        </div>
      {:else}
        <!-- User is not logged in -->
        <div class="py-2">
          <div class="px-4 py-1 font-medium text-primary-700 text-xs uppercase">
            <T key="common.account" />
          </div>
          <a
            href="/auth"
            class="flex items-center hover:bg-primary-100/70 px-4 py-2 w-full font-montserrat font-light text-primary-900 text-sm text-left transition-colors"
            onclick={() => showUserMenu = false}
          >
            <User class="mr-2 w-4 h-4" />
            <T key="nav.login" />
          </a>
        </div>

        <!-- Preferences section -->
        <div class="py-2 border-primary-200 border-t">
          <div class="px-4 py-1 font-medium text-primary-700 text-xs uppercase">
            <T key="common.preferences" />
          </div>

          <!-- Language selector -->
          <div class="px-4 py-2">
            <LanguageSelector label="Language" />
          </div>

          <!-- Currency selector -->
          <div class="px-4 py-2">
            <CurrencySelector label="Currency" />
          </div>
        </div>
      {/if}
    </div>
  {/if}
</div>
