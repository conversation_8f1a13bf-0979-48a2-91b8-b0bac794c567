<script lang="ts">
  import { T } from "$lib/i18n";

  // Definición de tipos para la reserva
  type ReservationType = {
    id: string;
    guest_name: string;
    guest_email: string;
    guest_phone?: string;
    check_in_date: string;
    check_out_date: string;
    adults: number;
    children: number;
    status: string;
    total_price?: number;
    currency?: string;
    suite_name?: string;
    site_name?: string;
    cloudbeds_confirmation_code?: string;
    created_at: string;
  };

  const { reservation } = $props<{ reservation: ReservationType }>();

  // Formatear fechas
  function formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("es-ES", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch (error) {
      console.error("Error al formatear fecha:", error);
      return dateString;
    }
  }

  // Obtener el color de estado
  function getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case "confirmed":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      case "completed":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  }

  // Traducir estado
  function translateStatus(status: string): string {
    switch (status.toLowerCase()) {
      case "confirmed":
        return "Confirmada";
      case "pending":
        return "Pendiente";
      case "cancelled":
        return "Cancelada";
      case "completed":
        return "Completada";
      default:
        return status;
    }
  }

  // Calcular la duración de la estancia
  function calculateStayDuration(checkIn: string, checkOut: string): number {
    try {
      const checkInDate = new Date(checkIn);
      const checkOutDate = new Date(checkOut);
      const diffTime = Math.abs(checkOutDate.getTime() - checkInDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    } catch (error) {
      console.error("Error al calcular duración de estancia:", error);
      return 0;
    }
  }

  // Formatear precio
  function formatPrice(price?: number, currency?: string): string {
    if (price === undefined) return "N/A";

    try {
      return new Intl.NumberFormat("es-ES", {
        style: "currency",
        currency: currency || "EUR",
      }).format(price);
    } catch (error) {
      console.error("Error al formatear precio:", error);
      return `${price} ${currency || "EUR"}`;
    }
  }

  // Calcular si la reserva es futura, actual o pasada
  function getReservationTiming(
    checkIn: string,
    checkOut: string
  ): "future" | "current" | "past" {
    const now = new Date();
    const checkInDate = new Date(checkIn);
    const checkOutDate = new Date(checkOut);

    if (checkInDate > now) {
      return "future";
    } else if (checkOutDate < now) {
      return "past";
    } else {
      return "current";
    }
  }

  const reservationTiming = getReservationTiming(
    reservation.check_in_date,
    reservation.check_out_date
  );
  const stayDuration = calculateStayDuration(
    reservation.check_in_date,
    reservation.check_out_date
  );
</script>

<div
  class="bg-white shadow-sm border border-primary-200 rounded-sm overflow-hidden"
>
  <div class="p-4 border-primary-100 border-b">
    <div class="flex justify-between items-start">
      <div>
        <h3 class="font-montserrat font-medium text-primary-900 text-lg">
          {reservation.suite_name || "Habitación"}
        </h3>
        <p class="text-primary-600 text-sm">
          {reservation.site_name || "Baberrih Hotel"}
        </p>
      </div>
      <div>
        <span
          class="px-2 py-1 rounded-full text-xs font-medium {getStatusColor(
            reservation.status
          )}"
        >
          {translateStatus(reservation.status)}
        </span>
      </div>
    </div>
  </div>

  <div class="p-4">
    <div class="gap-4 grid grid-cols-1 md:grid-cols-2">
      <div>
        <h4
          class="mb-2 font-montserrat text-primary-700 text-sm uppercase tracking-wider"
        >
          <T key="reservations.details" />
        </h4>
        <ul class="space-y-2">
          <li class="flex justify-between">
            <span class="text-primary-600 text-sm">
              <T key="reservations.checkIn" />
            </span>
            <span class="font-medium text-primary-900 text-sm">
              {formatDate(reservation.check_in_date)}
            </span>
          </li>
          <li class="flex justify-between">
            <span class="text-primary-600 text-sm">
              <T key="reservations.checkOut" />
            </span>
            <span class="font-medium text-primary-900 text-sm">
              {formatDate(reservation.check_out_date)}
            </span>
          </li>
          <li class="flex justify-between">
            <span class="text-primary-600 text-sm">
              <T key="reservations.duration" />
            </span>
            <span class="font-medium text-primary-900 text-sm">
              {stayDuration}
              {stayDuration === 1 ? "noche" : "noches"}
            </span>
          </li>
          <li class="flex justify-between">
            <span class="text-primary-600 text-sm">
              <T key="reservations.guests" />
            </span>
            <span class="font-medium text-primary-900 text-sm">
              {reservation.adults}
              {reservation.adults === 1 ? "adulto" : "adultos"}
              {#if reservation.children > 0}
                , {reservation.children}
                {reservation.children === 1 ? "niño" : "niños"}
              {/if}
            </span>
          </li>
          {#if reservation.total_price}
            <li class="flex justify-between">
              <span class="text-primary-600 text-sm">
                <T key="reservations.totalPrice" />
              </span>
              <span class="font-medium text-primary-900 text-sm">
                {formatPrice(reservation.total_price, reservation.currency)}
              </span>
            </li>
          {/if}
        </ul>
      </div>

      <div>
        <h4
          class="mb-2 font-montserrat text-primary-700 text-sm uppercase tracking-wider"
        >
          <T key="reservations.guestInfo" />
        </h4>
        <ul class="space-y-2">
          <li class="flex justify-between">
            <span class="text-primary-600 text-sm">
              <T key="reservations.name" />
            </span>
            <span class="font-medium text-primary-900 text-sm">
              {reservation.guest_name}
            </span>
          </li>
          <li class="flex justify-between">
            <span class="text-primary-600 text-sm">
              <T key="reservations.email" />
            </span>
            <span class="font-medium text-primary-900 text-sm">
              {reservation.guest_email}
            </span>
          </li>
          {#if reservation.guest_phone}
            <li class="flex justify-between">
              <span class="text-primary-600 text-sm">
                <T key="reservations.phone" />
              </span>
              <span class="font-medium text-primary-900 text-sm">
                {reservation.guest_phone}
              </span>
            </li>
          {/if}
          {#if reservation.cloudbeds_confirmation_code}
            <li class="flex justify-between">
              <span class="text-primary-600 text-sm">
                <T key="reservations.confirmationCode" />
              </span>
              <span class="font-medium text-primary-900 text-sm">
                {reservation.cloudbeds_confirmation_code}
              </span>
            </li>
          {/if}
        </ul>
      </div>
    </div>
  </div>

  <div class="bg-primary-50 p-4 border-primary-100 border-t">
    <div class="flex justify-between items-center">
      <div>
        <span class="text-primary-600 text-xs">
          <T key="reservations.reservedOn" />
          {formatDate(reservation.created_at)}
        </span>
      </div>
      <div>
        {#if reservationTiming === "future"}
          <span class="font-medium text-blue-600 text-xs">
            <T key="reservations.upcoming" />
          </span>
        {:else if reservationTiming === "current"}
          <span class="font-medium text-green-600 text-xs">
            <T key="reservations.active" />
          </span>
        {:else}
          <span class="font-medium text-gray-600 text-xs">
            <T key="reservations.past" />
          </span>
        {/if}
      </div>
    </div>
  </div>
</div>
