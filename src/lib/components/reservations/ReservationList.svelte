<script lang="ts">
  import ReservationCard from "./ReservationCard.svelte";
  import { T } from "$lib/i18n";

  // Definición de tipos para la reserva
  type Reservation = {
    id: string;
    guest_name: string;
    guest_email: string;
    guest_phone?: string;
    check_in_date: string;
    check_out_date: string;
    adults: number;
    children: number;
    status: string;
    total_price?: number;
    currency?: string;
    suite_name?: string;
    site_name?: string;
    cloudbeds_confirmation_code?: string;
    created_at: string;
  };

  // Props
  const {
    reservations = [],
    loading = false,
    error = null,
    emptyMessage = "No se encontraron reservas.",
  } = $props<{
    reservations?: Reservation[];
    loading?: boolean;
    error?: string | null;
    emptyMessage?: string;
  }>();

  // Ordenar reservas por fecha de check-in (más recientes primero)
  const sortedReservations = $derived(
    [...reservations].sort((a, b) => {
      const dateA = new Date(a.check_in_date);
      const dateB = new Date(b.check_in_date);
      return dateB.getTime() - dateA.getTime();
    })
  );

  // Agrupar reservas por estado
  const upcomingReservations = $derived(
    sortedReservations.filter((r) => {
      const checkInDate = new Date(r.check_in_date);
      const now = new Date();
      return checkInDate > now && r.status.toLowerCase() !== "cancelled";
    })
  );

  const activeReservations = $derived(
    sortedReservations.filter((r) => {
      const checkInDate = new Date(r.check_in_date);
      const checkOutDate = new Date(r.check_out_date);
      const now = new Date();
      return (
        checkInDate <= now &&
        checkOutDate >= now &&
        r.status.toLowerCase() !== "cancelled"
      );
    })
  );

  const pastReservations = $derived(
    sortedReservations.filter((r) => {
      const checkOutDate = new Date(r.check_out_date);
      const now = new Date();
      return checkOutDate < now || r.status.toLowerCase() === "cancelled";
    })
  );

  // Estado para mostrar/ocultar secciones
  let showUpcoming = $state(true);
  let showActive = $state(true);
  let showPast = $state(false);

  // Función para alternar la visibilidad de una sección
  function toggleSection(section: "upcoming" | "active" | "past"): void {
    if (section === "upcoming") showUpcoming = !showUpcoming;
    if (section === "active") showActive = !showActive;
    if (section === "past") showPast = !showPast;
  }
</script>

<div class="space-y-6">
  {#if loading}
    <div class="flex justify-center items-center py-8">
      <div
        class="border-primary-700 border-b-2 rounded-full w-8 h-8 animate-spin"
      ></div>
    </div>
  {:else if error}
    <div class="bg-red-50 p-4 border border-red-200 rounded-sm text-red-700">
      <p>{error}</p>
    </div>
  {:else if reservations.length === 0}
    <div
      class="bg-primary-50 p-6 border border-primary-200 rounded-sm text-primary-700 text-center"
    >
      <p>{emptyMessage}</p>
    </div>
  {:else}
    <!-- Reservas activas -->
    {#if activeReservations.length > 0}
      <div class="border border-primary-200 rounded-sm overflow-hidden">
        <button
          class="flex justify-between items-center bg-primary-100 p-4 w-full text-left"
          onclick={() => toggleSection("active")}
        >
          <h3 class="font-montserrat font-medium text-primary-900">
            <T key="reservations.activeReservations" />
            <span
              class="bg-primary-700 ml-2 px-2 py-0.5 rounded-full text-white text-xs"
            >
              {activeReservations.length}
            </span>
          </h3>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-primary-700 transition-transform {showActive
              ? 'rotate-180'
              : ''}"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </button>

        {#if showActive}
          <div class="space-y-4 p-4">
            {#each activeReservations as reservation (reservation.id)}
              <ReservationCard {reservation} />
            {/each}
          </div>
        {/if}
      </div>
    {/if}

    <!-- Reservas próximas -->
    {#if upcomingReservations.length > 0}
      <div class="border border-primary-200 rounded-sm overflow-hidden">
        <button
          class="flex justify-between items-center bg-primary-100 p-4 w-full text-left"
          onclick={() => toggleSection("upcoming")}
        >
          <h3 class="font-montserrat font-medium text-primary-900">
            <T key="reservations.upcomingReservations" />
            <span
              class="bg-primary-700 ml-2 px-2 py-0.5 rounded-full text-white text-xs"
            >
              {upcomingReservations.length}
            </span>
          </h3>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-primary-700 transition-transform {showUpcoming
              ? 'rotate-180'
              : ''}"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </button>

        {#if showUpcoming}
          <div class="space-y-4 p-4">
            {#each upcomingReservations as reservation (reservation.id)}
              <ReservationCard {reservation} />
            {/each}
          </div>
        {/if}
      </div>
    {/if}

    <!-- Reservas pasadas -->
    {#if pastReservations.length > 0}
      <div class="border border-primary-200 rounded-sm overflow-hidden">
        <button
          class="flex justify-between items-center bg-primary-100 p-4 w-full text-left"
          onclick={() => toggleSection("past")}
        >
          <h3 class="font-montserrat font-medium text-primary-900">
            <T key="reservations.pastReservations" />
            <span
              class="bg-primary-700 ml-2 px-2 py-0.5 rounded-full text-white text-xs"
            >
              {pastReservations.length}
            </span>
          </h3>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 text-primary-700 transition-transform {showPast
              ? 'rotate-180'
              : ''}"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </button>

        {#if showPast}
          <div class="space-y-4 p-4">
            {#each pastReservations as reservation (reservation.id)}
              <ReservationCard {reservation} />
            {/each}
          </div>
        {/if}
      </div>
    {/if}
  {/if}
</div>
