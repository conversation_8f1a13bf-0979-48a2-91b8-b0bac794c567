<script lang="ts">
  import { T } from "$lib/i18n";

  // Props
  const { onSearch } = $props<{
    onSearch: (email: string, confirmationCode: string) => Promise<void>;
  }>();

  // Estado del formulario
  let email = $state("");
  let confirmationCode = $state("");
  let loading = $state(false);
  let formSubmitted = $state(false);

  // Errores de validación
  let emailError = $state("");
  let confirmationCodeError = $state("");

  // Validar email
  function validateEmail(): boolean {
    if (!email) {
      emailError = "El correo electrónico es obligatorio";
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      emailError = "El correo electrónico no es válido";
      return false;
    }

    emailError = "";
    return true;
  }

  // Validar código de confirmación
  function validateConfirmationCode(): boolean {
    if (!confirmationCode) {
      confirmationCodeError = "El código de confirmación es obligatorio";
      return false;
    }

    confirmationCodeError = "";
    return true;
  }

  // Validar formulario completo
  function validateForm(): boolean {
    const isEmailValid = validateEmail();
    const isConfirmationCodeValid = validateConfirmationCode();
    return isEmailValid && isConfirmationCodeValid;
  }

  // Manejar envío del formulario
  async function handleSubmit(event: Event): Promise<void> {
    event.preventDefault();
    formSubmitted = true;

    if (!validateForm()) {
      return;
    }

    loading = true;

    try {
      await onSearch(email, confirmationCode);
    } catch (error) {
      console.error("Error al buscar reserva:", error);
    } finally {
      loading = false;
    }
  }
</script>

<div class="bg-white shadow-sm p-6 border border-primary-200 rounded-sm">
  <h2 class="mb-4 font-montserrat font-medium text-primary-900 text-xl">
    <T key="reservations.findYourReservation" />
  </h2>

  <p class="mb-6 text-primary-600">
    <T key="reservations.findYourReservationDescription" />
  </p>

  <form onsubmit={handleSubmit} class="space-y-4">
    <div>
      <label
        for="email"
        class="block mb-1 font-montserrat text-primary-800 text-sm"
      >
        <T key="reservations.email" />
      </label>
      <input
        type="email"
        id="email"
        bind:value={email}
        onblur={validateEmail}
        class="w-full px-3 py-2 border border-primary-300 rounded-sm focus:ring-primary-500 focus:border-primary-500 {emailError &&
        formSubmitted
          ? 'border-red-500'
          : ''}"
        placeholder="<EMAIL>"
      />
      {#if emailError && formSubmitted}
        <p class="mt-1 text-red-600 text-sm">{emailError}</p>
      {/if}
    </div>

    <div>
      <label
        for="confirmationCode"
        class="block mb-1 font-montserrat text-primary-800 text-sm"
      >
        <T key="reservations.confirmationCode" />
      </label>
      <input
        type="text"
        id="confirmationCode"
        bind:value={confirmationCode}
        onblur={validateConfirmationCode}
        class="w-full px-3 py-2 border border-primary-300 rounded-sm focus:ring-primary-500 focus:border-primary-500 {confirmationCodeError &&
        formSubmitted
          ? 'border-red-500'
          : ''}"
        placeholder="ABC123"
      />
      {#if confirmationCodeError && formSubmitted}
        <p class="mt-1 text-red-600 text-sm">{confirmationCodeError}</p>
      {/if}
    </div>

    <button
      type="submit"
      disabled={loading}
      class="bg-primary-700 hover:bg-primary-800 px-4 py-2 rounded-sm w-full font-montserrat text-white transition-colors"
    >
      {#if loading}
        <span
          class="inline-block mr-2 border-white border-b-2 rounded-full w-4 h-4 animate-spin"
        ></span>
        <T key="common.searching" />
      {:else}
        <T key="reservations.search" />
      {/if}
    </button>
  </form>
</div>
