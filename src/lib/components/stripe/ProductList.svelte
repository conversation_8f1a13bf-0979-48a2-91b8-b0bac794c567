<!--
  ProductList.svelte
  Este componente muestra una lista de productos disponibles para comprar.
-->
<script lang="ts">
  import { onMount } from "svelte";
  import { CreditCard, Package, Loader2, CheckCircle } from "lucide-svelte";
  import StripeCheckout from "./StripeCheckout.svelte";
  import type { StripeProduct, StripePrice } from "$lib/stripe/types.d";
  import type { Subscription } from "$lib/stripe/types";
  import { page } from "$app/stores";

  // Estado
  let products: StripeProduct[] = [];
  let userSubscriptions: Subscription[] = [];
  let loading = true;
  let loadingSubscriptions = true;
  let error: string | null = null;
  let subscriptionError: string | null = null;

  // Verificar si el usuario está autenticado
  $: isAuthenticated = !!$page.data.session;

  // Cargar productos al montar el componente
  onMount(async () => {
    try {
      const response = await fetch("/api/stripe/products");

      if (!response.ok) {
        throw new Error("Error al cargar los productos");
      }

      const data = await response.json();
      products = data.products || [];

      // Si el usuario está autenticado, cargar sus suscripciones
      if (isAuthenticated) {
        await loadUserSubscriptions();
      }
    } catch (err) {
      error = err instanceof Error ? err.message : "Error desconocido";
      console.error("Error al cargar productos:", err);
    } finally {
      loading = false;
    }
  });

  // Cargar las suscripciones del usuario
  async function loadUserSubscriptions() {
    try {
      loadingSubscriptions = true;
      subscriptionError = null;

      const response = await fetch("/api/subscriptions/user");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Error al cargar las suscripciones");
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || "Error al cargar las suscripciones");
      }

      userSubscriptions = data.subscriptions || [];
    } catch (err) {
      console.error("Error al cargar las suscripciones:", err);
      subscriptionError =
        err instanceof Error ? err.message : "Error desconocido";
    } finally {
      loadingSubscriptions = false;
    }
  }

  // Verificar si el usuario ya está suscrito a un producto
  function isSubscribedToProduct(priceId: string): boolean {
    if (!isAuthenticated || userSubscriptions.length === 0) return false;

    return userSubscriptions.some(
      (sub) =>
        sub.status === "active" &&
        sub.items &&
        sub.items.data &&
        sub.items.data.some(
          (item: any) => item.price && item.price.id === priceId
        )
    );
  }

  // Formatear precio
  function formatPrice(
    amount: number | null | undefined,
    currency: string
  ): string {
    if (amount === null || amount === undefined) return "Precio no disponible";

    return new Intl.NumberFormat("es-ES", {
      style: "currency",
      currency: currency || "EUR",
      minimumFractionDigits: 2,
    }).format(amount / 100);
  }

  // Formatear intervalo de facturación
  function formatInterval(recurring: StripePrice["recurring"]): string {
    if (!recurring) return "Pago único";

    const { interval, interval_count } = recurring;

    if (interval === "month") {
      return interval_count === 1 ? "Mensual" : `Cada ${interval_count} meses`;
    } else if (interval === "year") {
      return interval_count === 1 ? "Anual" : `Cada ${interval_count} años`;
    } else if (interval === "week") {
      return interval_count === 1
        ? "Semanal"
        : `Cada ${interval_count} semanas`;
    } else if (interval === "day") {
      return interval_count === 1 ? "Diario" : `Cada ${interval_count} días`;
    }

    return `Cada ${interval_count} ${interval}(s)`;
  }
</script>

<div class="p-4 md:p-8">
  <h2 class="mb-8 font-bold text-2xl">Productos disponibles</h2>

  {#if loading}
    <div class="flex justify-center items-center py-16">
      <Loader2 class="w-8 h-8 text-primary animate-spin" />
      <span class="ml-2">Cargando productos...</span>
    </div>
  {:else if error}
    <div class="alert alert-error">
      <span>{error}</span>
    </div>
  {:else if products.length === 0}
    <div class="alert alert-info">
      <span>No hay productos disponibles en este momento.</span>
    </div>
  {:else if isAuthenticated && userSubscriptions.length > 0}
    <div class="mb-6 alert alert-info">
      <span
        >Ya tienes suscripciones activas. Puedes gestionarlas en tu perfil.</span
      >
      <div class="flex-none">
        <a href="/account/subscriptions" class="btn btn-sm btn-primary">
          Gestionar suscripciones
        </a>
      </div>
    </div>
    <div class="gap-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
      {#each products as product}
        {#if product.active}
          <div class="flex flex-col bg-base-100 shadow-xl h-full card">
            <figure class="px-8 pt-8">
              {#if product.images && product.images.length > 0}
                <img
                  src={product.images[0]}
                  alt={product.name}
                  class="rounded-xl h-48 object-contain"
                />
              {:else}
                <div
                  class="flex justify-center items-center bg-base-200 rounded-xl w-full h-48"
                >
                  <Package class="opacity-30 w-16 h-16 text-base-content" />
                </div>
              {/if}
            </figure>

            <div class="flex-grow card-body">
              <h3 class="card-title">{product.name}</h3>

              {#if product.description}
                <p>{product.description}</p>
              {/if}

              {#if product.price}
                <div class="mt-4">
                  <div class="flex items-center">
                    <CreditCard class="mr-2 w-5 h-5 text-primary" />
                    <span class="font-bold text-xl">
                      {formatPrice(
                        product.price.unit_amount,
                        product.price.currency
                      )}
                    </span>
                  </div>

                  {#if product.price.recurring}
                    <div class="mt-1 text-sm text-base-content/70">
                      {formatInterval(product.price.recurring)}
                    </div>
                  {/if}
                </div>
              {/if}

              <div class="justify-end mt-4 card-actions">
                {#if product.price}
                  {#if isSubscribedToProduct(product.price.id)}
                    <div class="flex items-center text-success">
                      <CheckCircle class="mr-2 w-5 h-5" />
                      <span>Ya suscrito</span>
                    </div>
                  {:else}
                    <StripeCheckout
                      priceId={product.price.id}
                      productName={product.name}
                    />
                  {/if}
                {:else}
                  <button class="btn btn-disabled"> No disponible </button>
                {/if}
              </div>
            </div>
          </div>
        {/if}
      {/each}
    </div>
  {:else}
    <div class="gap-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
      {#each products as product}
        {#if product.active}
          <div class="flex flex-col bg-base-100 shadow-xl h-full card">
            <figure class="px-8 pt-8">
              {#if product.images && product.images.length > 0}
                <img
                  src={product.images[0]}
                  alt={product.name}
                  class="rounded-xl h-48 object-contain"
                />
              {:else}
                <div
                  class="flex justify-center items-center bg-base-200 rounded-xl w-full h-48"
                >
                  <Package class="opacity-30 w-16 h-16 text-base-content" />
                </div>
              {/if}
            </figure>

            <div class="flex-grow card-body">
              <h3 class="card-title">{product.name}</h3>

              {#if product.description}
                <p>{product.description}</p>
              {/if}

              {#if product.price}
                <div class="mt-4">
                  <div class="flex items-center">
                    <CreditCard class="mr-2 w-5 h-5 text-primary" />
                    <span class="font-bold text-xl">
                      {formatPrice(
                        product.price.unit_amount,
                        product.price.currency
                      )}
                    </span>
                  </div>

                  {#if product.price.recurring}
                    <div class="mt-1 text-sm text-base-content/70">
                      {formatInterval(product.price.recurring)}
                    </div>
                  {/if}
                </div>
              {/if}

              <div class="justify-end mt-4 card-actions">
                {#if product.price}
                  <StripeCheckout
                    priceId={product.price.id}
                    productName={product.name}
                  />
                {:else}
                  <button class="btn btn-disabled"> No disponible </button>
                {/if}
              </div>
            </div>
          </div>
        {/if}
      {/each}
    </div>
  {/if}
</div>
