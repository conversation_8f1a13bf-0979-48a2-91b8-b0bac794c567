<!--
  SubscriptionList.svelte
  Este componente muestra las suscripciones activas del usuario.
-->
<script lang="ts">
  import { onMount } from "svelte";
  import {
    CreditCard,
    Calendar,
    Loader2,
    AlertCircle,
    CheckCircle,
    XCircle,
  } from "lucide-svelte";
  import type { Subscription } from "$lib/stripe/types";
  import SubscriptionCard from "$lib/components/stripe/SubscriptionCard.svelte";
  import LoadingSpinner from "$lib/components/ui/LoadingSpinner.svelte";
  import ErrorMessage from "$lib/components/ui/ErrorMessage.svelte";

  // Estado
  let subscriptions: Subscription[] = [];
  let loading = true;
  let error: string | null = null;

  // Cargar suscripciones al montar el componente
  onMount(async () => {
    try {
      loading = true;
      error = null;

      // Usar el nuevo endpoint que obtiene las suscripciones del usuario autenticado
      const response = await fetch("/api/subscriptions/user");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Error al cargar las suscripciones");
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || "Error al cargar las suscripciones");
      }

      subscriptions = data.subscriptions;
    } catch (err) {
      console.error("Error al cargar las suscripciones:", err);
      error = err instanceof Error ? err.message : "Error desconocido";
    } finally {
      loading = false;
    }
  });

  // Formatear precio
  function formatPrice(
    amount: number | null | undefined,
    currency: string
  ): string {
    if (amount === null || amount === undefined) return "Precio no disponible";

    return new Intl.NumberFormat("es-ES", {
      style: "currency",
      currency: currency || "EUR",
      minimumFractionDigits: 2,
    }).format(amount / 100);
  }

  // Formatear fecha
  function formatDate(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleDateString("es-ES", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  }

  // Obtener el estado de la suscripción
  function getStatusBadge(status: string) {
    switch (status) {
      case "active":
        return { text: "Activa", color: "badge-success", icon: CheckCircle };
      case "canceled":
        return { text: "Cancelada", color: "badge-error", icon: XCircle };
      case "incomplete":
        return {
          text: "Incompleta",
          color: "badge-warning",
          icon: AlertCircle,
        };
      case "incomplete_expired":
        return { text: "Expirada", color: "badge-error", icon: XCircle };
      case "past_due":
        return {
          text: "Pago pendiente",
          color: "badge-warning",
          icon: AlertCircle,
        };
      case "trialing":
        return { text: "En prueba", color: "badge-info", icon: CheckCircle };
      case "unpaid":
        return { text: "No pagada", color: "badge-error", icon: AlertCircle };
      default:
        return { text: status, color: "badge-neutral", icon: AlertCircle };
    }
  }

  // Manejar la cancelación de una suscripción
  async function handleCancelSubscription(subscriptionId: string) {
    // Aquí implementarías la lógica para cancelar una suscripción
    // Por ahora, solo mostramos un mensaje en la consola
    console.log(`Cancelar suscripción: ${subscriptionId}`);
    alert("Esta funcionalidad aún no está implementada");
  }

  // Manejar la actualización de una suscripción
  async function handleManageSubscription(subscriptionId: string) {
    // Aquí implementarías la lógica para abrir el portal de cliente
    // Por ahora, solo mostramos un mensaje en la consola
    console.log(`Gestionar suscripción: ${subscriptionId}`);
    alert("Esta funcionalidad aún no está implementada");
  }
</script>

<div class="mx-auto w-full max-w-4xl">
  {#if loading}
    <div class="flex flex-col justify-center items-center p-8">
      <LoadingSpinner />
      <p class="mt-4 text-base-content/70">Cargando suscripciones...</p>
    </div>
  {:else if error}
    <ErrorMessage message={error} />
  {:else if subscriptions.length === 0}
    <div class="bg-base-200 mt-4 p-8 rounded-lg text-center">
      <p class="mb-4">No tienes suscripciones activas.</p>
      <a href="/pricing" class="btn btn-primary">Ver planes</a>
    </div>
  {:else}
    <div class="gap-6 grid grid-cols-1 md:grid-cols-2 mt-6">
      {#each subscriptions as subscription (subscription.id)}
        <SubscriptionCard {subscription} />
      {/each}
    </div>
  {/if}
</div>
