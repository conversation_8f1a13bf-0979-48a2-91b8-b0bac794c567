<script lang="ts">
  import { onMount } from "svelte";
  import { page } from "$app/stores";
  import PaywallMessage from "./PaywallMessage.svelte";
  import { checkSubscriptionClient } from "$lib/stripe/subscription.client";

  // Props
  export let paywallTitle = "Contenido Premium";
  export let paywallMessage =
    "Este contenido está disponible solo para suscriptores premium.";
  export let paywallButtonText = "Ver planes de suscripción";
  export let redirectUrl = "/products";

  // Estado
  let hasSubscription = false;
  let loading = true;
  let error: string | null = null;
  let checkAttempted = false;

  // Verificar si el usuario tiene una suscripción activa
  async function checkUserSubscription() {
    try {
      // Verificar si el usuario está autenticado
      if (!$page.data.session) {
        loading = false;
        return;
      }

      // Obtener el ID del cliente de Stripe del usuario
      const userId = $page.data.session.user.id;

      // Usar la función cliente para verificar la suscripción
      const result = await checkSubscriptionClient(userId);

      if (result.error) {
        console.error("Error al verificar la suscripción:", result.error);
        hasSubscription = false;
        checkAttempted = true;
        error = result.error;
        return;
      }

      hasSubscription = result.hasActiveSubscription;
      checkAttempted = true;
    } catch (err: unknown) {
      console.error("Error al verificar la suscripción:", err);
      // Si hay un error, asumimos que no hay suscripción pero no mostramos error
      hasSubscription = false;
      checkAttempted = true;
      if (err instanceof Error) {
        error = err.message;
      } else {
        error = "Error desconocido";
      }
    } finally {
      loading = false;
    }
  }

  onMount(() => {
    checkUserSubscription();
  });
</script>

{#if loading}
  <div class="flex justify-center items-center p-8">
    <span class="text-primary loading loading-spinner loading-lg"></span>
  </div>
{:else if error && import.meta.env.DEV}
  <!-- Mostrar error solo en desarrollo -->
  <div class="shadow-lg mx-auto my-8 max-w-md alert alert-error">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="flex-shrink-0 stroke-current w-6 h-6"
      fill="none"
      viewBox="0 0 24 24"
      ><path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
      /></svg
    >
    <span>Error: {error}</span>
  </div>
{:else if hasSubscription}
  <slot />
{:else}
  <PaywallMessage
    title={paywallTitle}
    message={paywallMessage}
    buttonText={paywallButtonText}
    {redirectUrl}
  />
{/if}
