<script>
  import { goto } from "$app/navigation";

  // Props
  export let title = "Contenido Premium";
  export let message =
    "Este contenido está disponible solo para suscriptores premium.";
  export let buttonText = "Ver planes de suscripción";
  export let redirectUrl = "/products";

  // Función para redirigir a la página de productos
  function handleRedirect() {
    goto(redirectUrl);
  }
</script>

<div class="bg-base-100 shadow-xl mx-auto my-8 w-96 card">
  <div class="items-center text-center card-body">
    <h2 class="text-2xl card-title">{title}</h2>
    <p class="py-4">{message}</p>
    <div class="card-actions">
      <button on:click={handleRedirect} class="btn btn-primary"
        >{buttonText}</button
      >
    </div>
  </div>
</div>
