<script lang="ts">
  import { onMount } from "svelte";
  import { page } from "$app/stores";

  // Props
  export let showOnlyForNonSubscribers = false;

  // Estado
  let hasSubscription = false;
  let loading = true;
  let error: string | null = null;
  let checkAttempted = false;

  // Verificar si el usuario tiene una suscripción activa
  async function checkSubscription() {
    try {
      // Verificar si el usuario está autenticado
      if (!$page.data.session) {
        loading = false;
        return;
      }

      // Obtener el ID del cliente de Stripe del usuario
      const userId = $page.data.session.user.id;

      // Hacer una solicitud a la API para verificar la suscripción
      const response = await fetch(`/api/subscriptions/check?userId=${userId}`);

      if (!response.ok) {
        // Si hay un error, asumimos que no hay suscripción pero no mostramos error
        console.error(
          "Error al verificar la suscripción:",
          await response.text()
        );
        hasSubscription = false;
        checkAttempted = true;
        loading = false;
        return;
      }

      const data = await response.json();
      hasSubscription = data.hasActiveSubscription;
      checkAttempted = true;
    } catch (err: unknown) {
      console.error("Error al verificar la suscripción:", err);
      // Si hay un error, asumimos que no hay suscripción pero no mostramos error
      hasSubscription = false;
      checkAttempted = true;
      if (err instanceof Error) {
        error = err.message;
      } else {
        error = "Error desconocido";
      }
    } finally {
      loading = false;
    }
  }

  onMount(() => {
    checkSubscription();
  });
</script>

{#if loading}
  <!-- No mostrar nada mientras carga -->
{:else if error && import.meta.env.DEV}
  <!-- Mostrar error solo en desarrollo -->
  <div class="bg-error p-2 text-error-content text-sm text-center">
    Error al verificar la suscripción: {error}
  </div>
{:else if !hasSubscription && $page.data.session && checkAttempted}
  <!-- Mostrar banner para usuarios autenticados sin suscripción -->
  <div class="bg-warning p-2 text-warning-content">
    <div class="flex justify-between items-center mx-auto container">
      <p class="text-sm">
        Estás utilizando la versión gratuita. Actualiza a premium para acceder a
        todas las funcionalidades.
      </p>
      <a href="/products" class="btn-outline btn btn-sm">Ver planes</a>
    </div>
  </div>
{:else if hasSubscription && !showOnlyForNonSubscribers && checkAttempted}
  <!-- Mostrar banner para usuarios con suscripción activa -->
  <div class="bg-success p-2 text-success-content">
    <div class="flex justify-between items-center mx-auto container">
      <p class="text-sm">
        ¡Gracias por ser un suscriptor premium! Disfruta de todas las
        funcionalidades.
      </p>
      <a href="/account/subscriptions" class="btn-outline btn btn-sm"
        >Gestionar suscripción</a
      >
    </div>
  </div>
{/if}
