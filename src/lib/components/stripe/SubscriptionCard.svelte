<!-- SubscriptionCard.svelte -->
<script lang="ts">
  import {
    Calendar,
    CreditCard,
    AlertCircle,
    CheckCircle,
    XCircle,
    Clock,
  } from "lucide-svelte";
  import type { Subscription } from "$lib/stripe/types";

  // Props
  export let subscription: Subscription;

  // Formatear precio
  function formatPrice(
    amount: number | null | undefined,
    currency: string = "usd"
  ): string {
    if (amount === null || amount === undefined) return "N/A";

    const formatter = new Intl.NumberFormat("es-ES", {
      style: "currency",
      currency: currency.toLowerCase(),
      minimumFractionDigits: 2,
    });

    return formatter.format(amount / 100);
  }

  // Formatear fecha
  function formatDate(timestamp: number): string {
    if (!timestamp) return "N/A";

    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString("es-ES", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  }

  // Obtener información de estado para mostrar el badge correcto
  function getStatusBadge(status: string) {
    switch (status) {
      case "active":
        return {
          text: "Activa",
          color: "badge-success",
          icon: CheckCircle,
        };
      case "canceled":
        return {
          text: "Cancelada",
          color: "badge-error",
          icon: XCircle,
        };
      case "past_due":
        return {
          text: "Pago pendiente",
          color: "badge-warning",
          icon: AlertCircle,
        };
      case "trialing":
        return {
          text: "Prueba",
          color: "badge-info",
          icon: Clock,
        };
      default:
        return {
          text: status,
          color: "badge-neutral",
          icon: AlertCircle,
        };
    }
  }

  // Manejar la gestión de la suscripción
  function handleManageSubscription(subscriptionId: string) {
    // Redirigir al portal de clientes de Stripe o a una página interna de gestión
    window.location.href = `/account/subscriptions/manage/${subscriptionId}`;
  }

  // Manejar la cancelación de la suscripción
  async function handleCancelSubscription(subscriptionId: string) {
    if (!confirm("¿Estás seguro de que deseas cancelar esta suscripción?")) {
      return;
    }

    try {
      const response = await fetch(
        `/api/stripe/subscriptions/${subscriptionId}/cancel`,
        {
          method: "POST",
        }
      );

      if (!response.ok) {
        throw new Error("Error al cancelar la suscripción");
      }

      // Recargar la página para mostrar el estado actualizado
      window.location.reload();
    } catch (error) {
      console.error("Error al cancelar la suscripción:", error);
      alert(
        "No se pudo cancelar la suscripción. Por favor, inténtalo de nuevo más tarde."
      );
    }
  }
</script>

<div class="bg-base-100 shadow-xl card">
  <div class="card-body">
    <div
      class="flex md:flex-row flex-col md:justify-between md:items-center gap-4"
    >
      <div>
        {#if subscription.items && subscription.items.data && subscription.items.data.length > 0 && subscription.items.data[0].price}
          <h3 class="card-title">
            {subscription.items.data[0].price.product || "Suscripción"}
          </h3>
        {:else}
          <h3 class="card-title">Suscripción</h3>
        {/if}

        {#if subscription.status}
          {@const badge = getStatusBadge(subscription.status)}
          <div class="badge {badge.color} gap-1 mt-2">
            <svelte:component this={badge.icon} class="w-3 h-3" />
            <span>{badge.text}</span>
          </div>
        {/if}
      </div>

      {#if subscription.items && subscription.items.data && subscription.items.data.length > 0 && subscription.items.data[0].price}
        <div class="flex items-center">
          <CreditCard class="mr-2 w-5 h-5 text-primary" />
          <span class="font-bold text-xl">
            {formatPrice(
              subscription.items.data[0].price.unit_amount,
              subscription.items.data[0].price.currency
            )}
            {#if subscription.items.data[0].price.recurring}
              <span class="font-normal text-sm text-base-content/70">
                /{subscription.items.data[0].price.recurring.interval}
              </span>
            {/if}
          </span>
        </div>
      {/if}
    </div>

    <div class="divider"></div>

    <div class="gap-4 grid grid-cols-1 md:grid-cols-2">
      <div class="flex items-center">
        <Calendar class="mr-2 w-5 h-5 text-primary" />
        <div>
          <div class="text-sm text-base-content/70">Período actual</div>
          <div>
            {formatDate(subscription.current_period_start)} -
            {formatDate(subscription.current_period_end)}
          </div>
        </div>
      </div>

      {#if subscription.cancel_at_period_end}
        <div class="py-2 alert alert-warning">
          <AlertCircle class="w-5 h-5" />
          <span>Se cancelará al final del período actual</span>
        </div>
      {/if}
    </div>

    <div class="justify-end mt-4 card-actions">
      <button
        class="btn-outline btn btn-sm"
        on:click={() => handleManageSubscription(subscription.id)}
      >
        Gestionar
      </button>

      {#if subscription.status === "active" && !subscription.cancel_at_period_end}
        <button
          class="btn btn-error btn-sm"
          on:click={() => handleCancelSubscription(subscription.id)}
        >
          Cancelar
        </button>
      {/if}
    </div>
  </div>
</div>

<style>
  .subscription-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
  }

  .card-header {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  @media (min-width: 768px) {
    .card-header {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
  }

  .status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
  }

  .badge-icon {
    width: 0.875rem;
    height: 0.875rem;
  }

  .badge-success {
    background-color: #10b981;
    color: white;
  }

  .badge-error {
    background-color: #ef4444;
    color: white;
  }

  .badge-warning {
    background-color: #f59e0b;
    color: white;
  }

  .badge-info {
    background-color: #3b82f6;
    color: white;
  }

  .badge-neutral {
    background-color: #6b7280;
    color: white;
  }

  .price-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .price-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: #3b82f6;
  }

  .price {
    font-weight: 700;
    font-size: 1.25rem;
  }

  .price-interval {
    font-weight: 400;
    font-size: 0.875rem;
    opacity: 0.7;
  }

  .divider {
    height: 1px;
    background-color: #e5e7eb;
    margin: 1rem 0;
  }

  .subscription-details {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  @media (min-width: 768px) {
    .subscription-details {
      grid-template-columns: 1fr 1fr;
    }
  }

  .period-info {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .period-icon {
    width: 1.25rem;
    height: 1.25rem;
    color: #3b82f6;
    margin-top: 0.125rem;
  }

  .period-label {
    font-size: 0.875rem;
    opacity: 0.7;
  }

  .period-dates {
    font-weight: 500;
  }

  .cancellation-notice {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background-color: #fef3c7;
    border-radius: 0.375rem;
    color: #92400e;
  }

  .notice-icon {
    width: 1.25rem;
    height: 1.25rem;
  }

  .card-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1rem;
  }

  .manage-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    background-color: transparent;
    border: 1px solid #6b7280;
    cursor: pointer;
    transition: all 0.2s;
  }

  .manage-button:hover {
    background-color: #f3f4f6;
  }

  .cancel-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    background-color: #ef4444;
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
  }

  .cancel-button:hover {
    background-color: #dc2626;
  }
</style>
