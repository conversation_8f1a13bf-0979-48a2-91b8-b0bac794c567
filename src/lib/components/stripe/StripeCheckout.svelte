<!--
  StripeCheckout.svelte
  Este componente implementa Stripe Checkout para procesar pagos.
-->
<script lang="ts">
  import { ShoppingCart, Loader2 } from "lucide-svelte";
  import { z } from "zod";
  import type { CreateCheckoutSessionParams } from "$lib/stripe/types.d";

  // Props
  export let priceId: string;
  export let productName: string;
  export let buttonText: string = "Comprar ahora";
  export let buttonClass: string = "btn btn-primary";

  // Estado
  let loading = false;
  let error: string | null = null;

  // Esquema de validación
  const checkoutSchema = z.object({
    priceId: z.string().min(1, "El ID del precio es requerido"),
    successUrl: z.string().url(),
    cancelUrl: z.string().url(),
  });

  // Iniciar el proceso de pago
  async function startCheckout() {
    if (loading) return;

    loading = true;
    error = null;

    try {
      // Construir las URLs de éxito y cancelación
      const successUrl = `${window.location.origin}/checkout/success`;
      const cancelUrl = `${window.location.origin}/checkout/cancel`;

      // Validar los datos
      const checkoutData = checkoutSchema.parse({
        priceId,
        successUrl,
        cancelUrl,
      });

      // Enviar la solicitud al servidor
      const response = await fetch("/api/stripe/checkout-sessions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(checkoutData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Error al crear la sesión de pago");
      }

      const data = await response.json();

      if (!data.success || !data.url) {
        throw new Error("No se recibió la URL de checkout");
      }

      // Redirigir al usuario a la página de checkout de Stripe
      window.location.href = data.url;
    } catch (err) {
      error = err instanceof Error ? err.message : "Error desconocido";
      console.error("Error al iniciar el pago:", err);
      loading = false;
    }
  }
</script>

<div class="w-full">
  <button
    class={buttonClass}
    on:click={startCheckout}
    disabled={loading}
    aria-label={`Comprar ${productName}`}
  >
    {#if loading}
      <Loader2 class="mr-2 w-5 h-5 animate-spin" />
      <span>Procesando...</span>
    {:else}
      <ShoppingCart class="mr-2 w-5 h-5" />
      <span>{buttonText}</span>
    {/if}
  </button>

  {#if error}
    <div class="mt-4 alert alert-error">
      <span>{error}</span>
    </div>
  {/if}
</div>
