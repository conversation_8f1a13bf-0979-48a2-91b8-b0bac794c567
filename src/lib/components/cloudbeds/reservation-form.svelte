<script lang="ts">
  import { onMount } from "svelte";
  import type {
    CloudbedsRoomType,
    CloudbedsReservationRequest,
    CloudbedsReservationGuest,
    CloudbedsReservationRoom,
  } from "$lib/cloudbeds";

  export let roomTypeId: string = "";
  export let roomTypeName: string = "";
  export let startDate: string = "";
  export let endDate: string = "";
  export let onSuccess: (reservationId: string) => void = () => {};
  export let onCancel: () => void = () => {};

  let roomTypes: CloudbedsRoomType[] = [];
  let loading: boolean = false;
  let success: boolean = false;
  let error: string | null = null;
  let confirmationCode: string = "";
  let reservationId: string = "";

  // Datos del formulario
  let guest: CloudbedsReservationGuest = {
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    country: "",
    postalCode: "",
    notes: "",
  };

  let roomData: CloudbedsReservationRoom = {
    roomTypeID: roomTypeId,
    startDate: startDate,
    endDate: endDate,
    adults: 1,
    children: 0,
    notes: "",
  };

  // Cargar tipos de habitaciones disponibles si no se proporciona roomTypeId
  onMount(async () => {
    if (!roomTypeId) {
      try {
        loading = true;

        // Usar el endpoint directamente
        const url = `/api/cloudbeds/room-types`;
        console.log("🔍 Fetching room types from URL:", url);

        const fetchResponse = await fetch(url);
        const response = await fetchResponse.json();

        if (response.success && response.data) {
          roomTypes = response.data;
          // Si hay habitaciones disponibles, seleccionar la primera por defecto
          if (roomTypes.length > 0) {
            const firstRoom = roomTypes[0];
            roomTypeId = firstRoom.roomTypeID || firstRoom.id || "";
            roomTypeName = firstRoom.roomTypeName || firstRoom.name || "";

            // Crear un nuevo objeto roomData para evitar el error de solo lectura
            roomData = {
              ...roomData,
              roomTypeID: roomTypeId,
            };
          }
        }
      } catch (err) {
        console.error("Error al cargar tipos de habitaciones:", err);
      } finally {
        loading = false;
      }
    } else {
      // Si ya se proporcionó roomTypeId, asegurarse de que roomData lo tenga
      roomData = {
        ...roomData,
        roomTypeID: roomTypeId,
      };
    }
  });

  // Función para crear la reserva
  async function createReservation() {
    try {
      loading = true;
      error = null;
      success = false;

      // Asegurarse de que los datos necesarios estén presentes
      if (!guest.firstName || !guest.lastName || !guest.email) {
        error =
          "Por favor, complete los campos obligatorios: nombre, apellido y email.";
        return;
      }

      if (!roomData.roomTypeID || !roomData.startDate || !roomData.endDate) {
        error =
          "Datos de habitación incompletos. Por favor, elija fechas de entrada y salida.";
        return;
      }

      // Generar un ID único para esta reserva
      const timestamp = new Date().getTime();
      const randomId = Math.floor(Math.random() * 10000);
      const thirdPartyId = `web-${timestamp}-${randomId}`;

      // Intentar obtener el propertyID si está disponible como variable global
      const propertyIdFromEnv =
        typeof window !== "undefined"
          ? (window as any).__CLOUDBEDS_PROPERTY_ID__
          : null;

      // Verificar que tenemos un propertyID
      if (!propertyIdFromEnv) {
        error = "No se pudo obtener el ID de la propiedad. Por favor, contacte al administrador.";
        loading = false;
        return;
      }

      // No need to validate room type IDs here, the service will handle it dynamically
      // Just ensure we include the room name for better matching
      if (roomTypeName && !roomData.roomName) {
        // Create a new object roomData with the room name (since roomTypeID is readonly)
        roomData = {
          ...roomData,
          roomName: roomTypeName
        };

        console.log(`Added room name for better ID matching: ${roomTypeName}`);
      }

      // Crear objeto de reserva
      const reservationRequest: CloudbedsReservationRequest = {
        propertyID: propertyIdFromEnv, // Usar solo el ID de la propiedad de las variables de entorno
        guestData: guest,
        roomsData: [roomData],
        status: "confirmed",
        thirdPartyIdentifier: thirdPartyId,
        sendEmailConfirmation: true,
        sourceID: 's-1-1', // Usar el sourceID recomendado por Manuel Arbelo
        startDate: roomData.startDate, // Añadir startDate a nivel de reserva
        endDate: roomData.endDate, // Añadir endDate a nivel de reserva
      };

      console.log(
        "Enviando reserva:",
        JSON.stringify(reservationRequest, null, 2)
      );

      // Enviar reserva usando el endpoint directamente
      const url = `/api/cloudbeds/reservation/direct`;
      console.log("🔍 Sending reservation to URL:", url);

      const fetchResponse = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reservationRequest)
      });

      const response = await fetchResponse.json();

      // Mejorar el manejo de respuestas
      console.log(
        "Respuesta completa de la API:",
        JSON.stringify(response, null, 2)
      );

      // Verificar si la reserva se creó correctamente
      if (response.success) {
        success = true;
        confirmationCode =
          response.confirmationCode || response.data?.confirmationCode || "";
        reservationId =
          response.reservationID || response.data?.reservationID || "";

        console.log("Reserva creada exitosamente:", {
          confirmationCode,
          reservationId,
        });

        // Llamar al callback de éxito
        if (reservationId) {
          onSuccess(reservationId);
        }
      } else {
        error =
          response.error?.message ||
          response.message ||
          "Error al crear la reserva";
        console.error("Error de API al crear reserva:", response);
      }
    } catch (err) {
      error =
        err instanceof Error
          ? err.message
          : "Error desconocido al crear la reserva";
      console.error("Error al crear la reserva:", err);
    } finally {
      loading = false;
    }
  }

  function handleCancel() {
    onCancel();
  }
</script>

<div class="mx-auto w-full max-w-2xl">
  <h2
    class="mb-6 font-montserrat font-light text-primary-900 text-xl text-center uppercase"
  >
    {roomTypeName ? `Book: ${roomTypeName}` : "New Reservation"}
  </h2>

  {#if success}
    <div class="bg-green-50 mb-6 p-4 border border-green-200 text-green-700">
      <div class="flex items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="mr-2 w-5 h-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
            clip-rule="evenodd"
          />
        </svg>
        <div>
          <h3 class="font-montserrat font-medium text-green-800">
            Reservation Confirmed!
          </h3>
          <div class="text-sm">
            {#if confirmationCode}
              Confirmation code: <span class="font-mono"
                >{confirmationCode}</span
              >
            {/if}
          </div>
        </div>
      </div>
    </div>
  {:else}
    <form on:submit|preventDefault={createReservation} class="space-y-8">
      <!-- Guest Information -->
      <div class="bg-primary-50 p-6 border border-primary-100">
        <h3
          class="mb-4 font-montserrat font-light text-primary-900 text-lg uppercase"
        >
          Guest Information
        </h3>

        <div class="gap-4 grid grid-cols-1 md:grid-cols-2 mb-4">
          <div>
            <label
              for="firstName"
              class="block mb-1 font-montserrat font-light text-primary-900"
            >
              First Name *
            </label>
            <input
              id="firstName"
              type="text"
              bind:value={guest.firstName}
              required
              class="p-2 border border-primary-200 w-full font-montserrat font-light"
            />
          </div>

          <div>
            <label
              for="lastName"
              class="block mb-1 font-montserrat font-light text-primary-900"
            >
              Last Name *
            </label>
            <input
              id="lastName"
              type="text"
              bind:value={guest.lastName}
              required
              class="p-2 border border-primary-200 w-full font-montserrat font-light"
            />
          </div>
        </div>

        <div class="gap-4 grid grid-cols-1 md:grid-cols-2">
          <div>
            <label
              for="email"
              class="block mb-1 font-montserrat font-light text-primary-900"
            >
              Email *
            </label>
            <input
              id="email"
              type="email"
              bind:value={guest.email}
              required
              class="p-2 border border-primary-200 w-full font-montserrat font-light"
            />
          </div>

          <div>
            <label
              for="phone"
              class="block mb-1 font-montserrat font-light text-primary-900"
            >
              Phone
            </label>
            <input
              id="phone"
              type="tel"
              bind:value={guest.phone}
              class="p-2 border border-primary-200 w-full font-montserrat font-light"
            />
          </div>
        </div>
      </div>

      <!-- Reservation Details -->
      <div class="bg-primary-50 p-6 border border-primary-100">
        <h3
          class="mb-4 font-montserrat font-light text-primary-900 text-lg uppercase"
        >
          Reservation Details
        </h3>

        {#if !roomTypeId && roomTypes.length > 0}
          <div class="mb-4">
            <label
              for="roomType"
              class="block mb-1 font-montserrat font-light text-primary-900"
            >
              Room Type *
            </label>
            <select
              id="roomType"
              bind:value={roomData.roomTypeID}
              required
              class="p-2 border border-primary-200 w-full font-montserrat font-light"
            >
              {#each roomTypes as roomType}
                <option value={roomType.roomTypeID || roomType.id}>
                  {roomType.roomTypeName || roomType.name}
                </option>
              {/each}
            </select>
          </div>
        {/if}

        <div class="gap-4 grid grid-cols-1 md:grid-cols-2 mb-4">
          <div>
            <label
              for="startDate"
              class="block mb-1 font-montserrat font-light text-primary-900"
            >
              Check-in Date *
            </label>
            <input
              id="startDate"
              type="date"
              bind:value={roomData.startDate}
              required
              class="p-2 border border-primary-200 w-full font-montserrat font-light"
            />
          </div>

          <div>
            <label
              for="endDate"
              class="block mb-1 font-montserrat font-light text-primary-900"
            >
              Check-out Date *
            </label>
            <input
              id="endDate"
              type="date"
              bind:value={roomData.endDate}
              required
              class="p-2 border border-primary-200 w-full font-montserrat font-light"
            />
          </div>
        </div>

        <div class="gap-4 grid grid-cols-1 md:grid-cols-2 mb-4">
          <div>
            <label
              for="adults"
              class="block mb-1 font-montserrat font-light text-primary-900"
            >
              Adults *
            </label>
            <input
              id="adults"
              type="number"
              min="1"
              bind:value={roomData.adults}
              required
              class="p-2 border border-primary-200 w-full font-montserrat font-light"
            />
          </div>

          <div>
            <label
              for="children"
              class="block mb-1 font-montserrat font-light text-primary-900"
            >
              Children
            </label>
            <input
              id="children"
              type="number"
              min="0"
              bind:value={roomData.children}
              class="p-2 border border-primary-200 w-full font-montserrat font-light"
            />
          </div>
        </div>

        <div>
          <label
            for="notes"
            class="block mb-1 font-montserrat font-light text-primary-900"
          >
            Additional Notes
          </label>
          <textarea
            id="notes"
            bind:value={roomData.notes}
            class="p-2 border border-primary-200 w-full h-24 font-montserrat font-light"
          ></textarea>
        </div>
      </div>

      {#if error}
        <div class="bg-red-50 mb-4 p-4 border border-red-200 text-red-700">
          <div class="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="mr-2 w-5 h-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clip-rule="evenodd"
              />
            </svg>
            <span>{error}</span>
          </div>
        </div>
      {/if}

      <div class="flex justify-end gap-4">
        <button
          type="button"
          class="button-secondary"
          on:click={handleCancel}
          disabled={loading}
        >
          Cancel
        </button>

        <button type="submit" class="button" disabled={loading}>
          {loading ? "Processing..." : "Confirm Reservation"}
        </button>
      </div>
    </form>
  {/if}
</div>
