<script lang="ts">
  import { onMount } from "svelte";
  import { fade } from "svelte/transition";
  import ImageGallery from "$lib/components/ui/ImageGallery.svelte";
  import PriceDisplay from "./PriceDisplay.svelte";
  import AvailabilityIndicator from "./AvailabilityIndicator.svelte";
  import QuickBookButton from "./QuickBookButton.svelte";

  // Define suite type
  interface Suite {
    id: string;
    site_id: string;
    name: string;
    slug: string;
    description: string;
    features: string[];
    amenities: string[];
    images: string[];
    videos: string[];
    size: string;
    capacity: string;
    price_info: Record<string, any>;
    status: string;
    created_at: string;
    updated_at: string;
    cloudbeds_room_type_id?: string;
    cloudbeds_property_id?: string;
    site_name?: string;
    site_domain?: string;
  }

  // Props - ahora acepta suite directamente o suiteSlug y siteId
  let { suiteSlug = null, siteId = null, suite: propSuite = null } = $props<{
    suiteSlug?: string | null;
    siteId?: string | null;
    suite?: Suite | null;
  }>();

  // Debug props
  console.log("SuiteDisplay: Props received:", { suiteSlug, siteId, hasSuiteProp: propSuite !== null });

  // Add global window debug
  if (typeof window !== 'undefined') {
    (window as any).debugSuiteDisplay = {
      props: { suiteSlug, siteId },
      loadData: () => loadSuiteData(),
      getState: () => ({ suite, loading, errorMessage, activeTab })
    };
    console.log("SuiteDisplay: Debug object added to window.debugSuiteDisplay");
  }

  // State
  let loading = $state(propSuite === null); // No necesitamos cargar si ya tenemos la suite
  let errorMessage = $state<string | null>(null);
  let suite = $state<Suite | null>(propSuite); // Inicializar con la suite proporcionada si existe
  let activeTab = $state<'overview' | 'amenities' | 'policies'>('overview');

  // Debug flag - set to true to show debug info on the page
  const DEBUG = true;

  // Debug state
  $effect(() => {
    console.log("Active tab changed to:", activeTab);
  });

  // Load suite data from API
  async function loadSuiteData() {
    // Si ya tenemos los datos de la suite, no necesitamos cargarlos
    if (propSuite !== null) {
      console.log("SuiteDisplay: Using suite data from props:", propSuite);
      suite = propSuite;
      processSuiteData();
      return;
    }

    // Si no tenemos suiteSlug o siteId, no podemos cargar los datos
    if (!suiteSlug || !siteId) {
      errorMessage = "Missing required parameters: suiteSlug and siteId";
      loading = false;
      return;
    }

    try {
      loading = true;
      errorMessage = null;

      console.log("SuiteDisplay: Loading suite data with params:", { suiteSlug, siteId });

      // Fetch suite data from API
      const apiUrl = `/api/suites?slug=${suiteSlug}&site_id=${siteId}&active_only=1`;
      console.log("SuiteDisplay: API URL:", apiUrl);

      const response = await fetch(apiUrl);
      console.log("SuiteDisplay: API response status:", response.status);

      const result = await response.json();
      console.log("SuiteDisplay: API response data:", result);

      if (!response.ok) {
        throw new Error(result.error || 'Failed to load suite data');
      }

      if (!result.success || !result.data) {
        throw new Error('Suite not found');
      }

      // Set suite data
      suite = result.data;
      console.log("SuiteDisplay: Suite data loaded:", suite);

      // Procesar los datos de la suite
      processSuiteData();
    }
  }

  // Función para procesar los datos de la suite
  function processSuiteData() {
    // Log specific fields for debugging
    if (suite) {
      console.log("SuiteDisplay: Processing suite fields:", {
        name: suite.name,
        description: suite.description,
        features: Array.isArray(suite.features) ? suite.features.length : 'not an array',
        amenities: Array.isArray(suite.amenities) ? suite.amenities.length : 'not an array',
        images: Array.isArray(suite.images) ? suite.images.length : 'not an array',
        videos: Array.isArray(suite.videos) ? suite.videos.length : 'not an array',
        size: suite.size,
        capacity: suite.capacity,
        cloudbeds_room_type_id: suite.cloudbeds_room_type_id,
        cloudbeds_property_id: suite.cloudbeds_property_id
      });

      // Ensure arrays are properly initialized and parse JSON strings if needed
      if (!Array.isArray(suite.features)) {
        console.warn("SuiteDisplay: Features is not an array, attempting to parse");
        try {
          // Check if it's a JSON string that needs parsing
          if (typeof suite.features === 'string') {
            suite.features = JSON.parse(suite.features);
            console.log("SuiteDisplay: Successfully parsed features:", suite.features);
          } else {
            console.warn("SuiteDisplay: Features is not a string or array, initializing empty array");
            suite.features = [];
          }
        } catch (err) {
          console.error("SuiteDisplay: Error parsing features:", err);
          suite.features = [];
        }
      }

      if (!Array.isArray(suite.amenities)) {
        console.warn("SuiteDisplay: Amenities is not an array, attempting to parse");
        try {
          // Check if it's a JSON string that needs parsing
          if (typeof suite.amenities === 'string') {
            suite.amenities = JSON.parse(suite.amenities);
            console.log("SuiteDisplay: Successfully parsed amenities:", suite.amenities);
          } else {
            console.warn("SuiteDisplay: Amenities is not a string or array, initializing empty array");
            suite.amenities = [];
          }
        } catch (err) {
          console.error("SuiteDisplay: Error parsing amenities:", err);
          suite.amenities = [];
        }
      }

      if (!Array.isArray(suite.images)) {
        console.warn("SuiteDisplay: Images is not an array, attempting to parse");
        try {
          // Check if it's a JSON string that needs parsing
          if (typeof suite.images === 'string') {
            suite.images = JSON.parse(suite.images);
            console.log("SuiteDisplay: Successfully parsed images:", suite.images);
          } else {
            console.warn("SuiteDisplay: Images is not a string or array, initializing empty array");
            suite.images = [];
          }
        } catch (err) {
          console.error("SuiteDisplay: Error parsing images:", err);
          suite.images = [];
        }
      }

      if (!Array.isArray(suite.videos)) {
        console.warn("SuiteDisplay: Videos is not an array, attempting to parse");
        try {
          // Check if it's a JSON string that needs parsing
          if (typeof suite.videos === 'string') {
            suite.videos = JSON.parse(suite.videos);
            console.log("SuiteDisplay: Successfully parsed videos:", suite.videos);
          } else {
            console.warn("SuiteDisplay: Videos is not a string or array, initializing empty array");
            suite.videos = [];
          }
        } catch (err) {
          console.error("SuiteDisplay: Error parsing videos:", err);
          suite.videos = [];
        }
      }

      // Log the parsed arrays for debugging
      console.log("SuiteDisplay: After parsing:", {
        features: Array.isArray(suite.features) ? suite.features.length + ' items' : 'still not an array',
        amenities: Array.isArray(suite.amenities) ? suite.amenities.length + ' items' : 'still not an array',
        images: Array.isArray(suite.images) ? suite.images.length + ' items' : 'still not an array',
        videos: Array.isArray(suite.videos) ? suite.videos.length + ' items' : 'still not an array'
      });
    }
    } catch (err) {
      console.error('Error loading suite data:', err);
      errorMessage = err instanceof Error ? err.message : 'An error occurred while loading suite data';
    } finally {
      loading = false;
    }
  }

  // Set active tab
  function setActiveTab(tab: 'overview' | 'amenities' | 'policies') {
    console.log(`Setting active tab to: ${tab}`);

    // Log the current state of the suite data for debugging
    if (tab === 'amenities') {
      console.log("Amenities tab selected. Current amenities data:", {
        amenities: suite?.amenities,
        isArray: Array.isArray(suite?.amenities),
        length: Array.isArray(suite?.amenities) ? suite?.amenities.length : 'N/A'
      });
    } else if (tab === 'overview') {
      console.log("Overview tab selected. Current features data:", {
        features: suite?.features,
        isArray: Array.isArray(suite?.features),
        length: Array.isArray(suite?.features) ? suite?.features.length : 'N/A'
      });
    }

    // Always set the tab, even if it's the same one
    activeTab = tab;

    // Force a re-render of the tab content
    setTimeout(() => {
      const tabPanel = document.getElementById(`${tab}-panel`);
      if (tabPanel) {
        console.log(`Tab panel found for ${tab}:`, tabPanel);

        // Toggle a class to force a repaint
        tabPanel.classList.add('tab-active');

        // Log the visibility state
        console.log(`Tab panel ${tab} display style:`,
          window.getComputedStyle(tabPanel).display,
          'visibility:', window.getComputedStyle(tabPanel).visibility,
          'hidden:', tabPanel.classList.contains('hidden')
        );

        setTimeout(() => {
          tabPanel.classList.remove('tab-active');
        }, 10);
      } else {
        console.warn(`Tab panel not found for ${tab}`);
      }
    }, 0);
  }

  onMount(() => {
    loadSuiteData();
  });
</script>

<style>
  .tab-active {
    animation: tab-flash 0.1s;
  }

  @keyframes tab-flash {
    0% { opacity: 0.99; }
    100% { opacity: 1; }
  }

  /* Ensure tab content is properly displayed */
  .tab-content > div[role="tabpanel"] {
    display: none;
  }

  .tab-content > div[role="tabpanel"].block {
    display: block;
  }
</style>

<div>
  {#if loading}
    <div class="p-8 text-center">
      <div class="inline-block border-4 border-gray-300 border-t-primary-600 rounded-full w-8 h-8 animate-spin"></div>
      <p class="mt-2 text-gray-600">Loading suite information...</p>
    </div>
  {:else if errorMessage}
    <div class="p-8 text-center">
      <p class="text-red-600">{errorMessage}</p>
    </div>
  {:else if suite}
    {#if DEBUG}
      <!-- Debug Info -->
      <div class="bg-blue-50 mb-4 p-4 border border-blue-200 rounded-md">
        <h3 class="mb-2 font-medium text-blue-800">Debug Info</h3>
        <pre class="bg-white p-2 border border-blue-100 rounded max-h-40 overflow-auto text-xs">
          Suite ID: {suite.id}
          Name: {suite.name}
          Slug: {suite.slug}
          Description: {suite.description ? (suite.description.length > 50 ? suite.description.substring(0, 50) + '...' : suite.description) : 'N/A'}
          Features: {Array.isArray(suite.features) ? suite.features.length : 'not an array'} items
          Amenities: {Array.isArray(suite.amenities) ? suite.amenities.length : 'not an array'} items
          Images: {Array.isArray(suite.images) ? suite.images.length : 'not an array'} items
          Videos: {Array.isArray(suite.videos) ? suite.videos.length : 'not an array'} items
          Size: {suite.size || 'N/A'}
          Capacity: {suite.capacity || 'N/A'}
          Cloudbeds Room Type ID: {suite.cloudbeds_room_type_id || 'N/A'}
        </pre>
      </div>
    {/if}

    <!-- Suite Header -->
    <div class="mb-8">
      <h1 class="font-montserrat font-light text-primary-900 text-3xl md:text-5xl uppercase tracking-wider">
        {suite.name}
      </h1>
      <div class="bg-primary-500 mt-3 w-24 h-0.5"></div>
    </div>

    <!-- Suite Images Gallery -->
    <div class="mb-10">
      <ImageGallery
        images={suite.images || []}
        videos={suite.videos || []}
        title={suite.name}
        aspectRatio="16/9"
      />
    </div>

    <!-- Quick Info Bar -->
    <div class="flex flex-wrap gap-6 bg-primary-50 shadow-sm mb-10 p-5 border border-primary-100 rounded-lg">
      {#if suite.size}
        <div class="flex items-center">
          <div class="flex justify-center items-center bg-primary-100 mr-3 rounded-full w-10 h-10">
            <svg class="w-5 h-5 text-primary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5"></path>
            </svg>
          </div>
          <div>
            <span class="block text-primary-600 text-xs uppercase tracking-wider">Size</span>
            <span class="font-medium text-primary-900">{suite.size}</span>
          </div>
        </div>
      {/if}

      {#if suite.capacity}
        <div class="flex items-center">
          <div class="flex justify-center items-center bg-primary-100 mr-3 rounded-full w-10 h-10">
            <svg class="w-5 h-5 text-primary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
          </div>
          <div>
            <span class="block text-primary-600 text-xs uppercase tracking-wider">Capacity</span>
            <span class="font-medium text-primary-900">{suite.capacity}</span>
          </div>
        </div>
      {/if}

      {#if suite.cloudbeds_room_type_id}
        <div class="flex items-center ml-auto">
          <div class="flex justify-center items-center bg-primary-100 mr-3 rounded-full w-10 h-10">
            <svg class="w-5 h-5 text-primary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
          </div>
          <div>
            <span class="block text-primary-600 text-xs uppercase tracking-wider">Availability</span>
            <AvailabilityIndicator roomTypeId={suite.cloudbeds_room_type_id} compact={true} />
          </div>
        </div>
      {/if}
    </div>

    <!-- Main Content Grid -->
    <div class="gap-10 grid grid-cols-1 lg:grid-cols-3">
      <!-- Left Column: Suite Details -->
      <div class="lg:col-span-2">
        <!-- Tabs Navigation -->
        <div class="mb-8 border-primary-100 border-b">
          <div class="flex flex-wrap -mb-px" role="tablist">
            <!-- Overview Tab -->
            <button
              type="button"
              role="tab"
              aria-selected={activeTab === 'overview'}
              aria-controls="overview-panel"
              id="overview-tab"
              class={`inline-block py-4 px-6 border-b-2 font-medium text-sm uppercase tracking-wider transition-all duration-200 ${activeTab === 'overview' ? 'border-primary-600 text-primary-800' : 'border-transparent text-primary-500 hover:text-primary-700 hover:border-primary-200'}`}
              onclick={() => setActiveTab('overview')}
              onkeydown={(e) => e.key === 'Enter' && setActiveTab('overview')}
            >
              Overview
            </button>

            <!-- Amenities Tab -->
            <button
              type="button"
              role="tab"
              aria-selected={activeTab === 'amenities'}
              aria-controls="amenities-panel"
              id="amenities-tab"
              class={`inline-block py-4 px-6 border-b-2 font-medium text-sm uppercase tracking-wider transition-all duration-200 ${activeTab === 'amenities' ? 'border-primary-600 text-primary-800' : 'border-transparent text-primary-500 hover:text-primary-700 hover:border-primary-200'}`}
              onclick={() => setActiveTab('amenities')}
              onkeydown={(e) => e.key === 'Enter' && setActiveTab('amenities')}
            >
              Amenities
            </button>

            <!-- Policies Tab -->
            <button
              type="button"
              role="tab"
              aria-selected={activeTab === 'policies'}
              aria-controls="policies-panel"
              id="policies-tab"
              class={`inline-block py-4 px-6 border-b-2 font-medium text-sm uppercase tracking-wider transition-all duration-200 ${activeTab === 'policies' ? 'border-primary-600 text-primary-800' : 'border-transparent text-primary-500 hover:text-primary-700 hover:border-primary-200'}`}
              onclick={() => setActiveTab('policies')}
              onkeydown={(e) => e.key === 'Enter' && setActiveTab('policies')}
            >
              Policies
            </button>
          </div>
        </div>

        <!-- Tab Content -->
        <div class="tab-content">
          <!-- Overview Tab Panel -->
          <div
            id="overview-panel"
            role="tabpanel"
            aria-labelledby="overview-tab"
            class={activeTab === 'overview' ? 'block' : 'hidden'}
          >
            <h2 class="mb-5 font-montserrat font-medium text-primary-900 text-2xl uppercase tracking-wider">Description</h2>
            <div class="mb-8 max-w-none prose">
              <p class="text-primary-800 leading-relaxed">{suite.description}</p>
            </div>

            {#if suite.features && suite.features.length > 0}
              <h2 class="mb-5 font-montserrat font-medium text-primary-900 text-2xl uppercase tracking-wider">Features</h2>
              <div class="gap-4 grid grid-cols-1 md:grid-cols-2 mb-8">
                {#each suite.features as feature, i}
                  <div class="flex items-center bg-primary-50 hover:bg-primary-100 hover:shadow-sm p-3 rounded-md transition-all duration-200" in:fade={{ duration: 300, delay: 100 + (i * 50) }}>
                    <div class="flex justify-center items-center bg-primary-100 mr-3 rounded-full w-8 h-8">
                      <svg
                        class="w-4 h-4 text-primary-700"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        ></path>
                      </svg>
                    </div>
                    <span class="text-primary-800">{feature}</span>
                  </div>
                {/each}
              </div>
            {/if}
          </div>

          <!-- Amenities Tab Panel -->
          <div
            id="amenities-panel"
            role="tabpanel"
            aria-labelledby="amenities-tab"
            class={activeTab === 'amenities' ? 'block' : 'hidden'}
          >
            <h2 class="mb-5 font-montserrat font-medium text-primary-900 text-2xl uppercase tracking-wider">Room Amenities</h2>
            {#if suite.amenities && suite.amenities.length > 0}
              <div class="gap-4 grid grid-cols-1 md:grid-cols-2">
                {#each suite.amenities as amenity, i}
                  <div class="flex items-center bg-primary-50 hover:bg-primary-100 hover:shadow-sm p-3 rounded-md transition-all duration-200" in:fade={{ duration: 300, delay: 100 + (i * 50) }}>
                    <div class="flex justify-center items-center bg-primary-100 mr-3 rounded-full w-8 h-8">
                      <svg
                        class="w-4 h-4 text-primary-700"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        ></path>
                      </svg>
                    </div>
                    <span class="text-primary-800">{amenity}</span>
                  </div>
                {/each}
              </div>
            {:else}
              <p class="bg-primary-50 p-4 rounded-md text-primary-600">No specific amenities listed for this suite.</p>
            {/if}
          </div>

          <!-- Policies Tab Panel -->
          <div
            id="policies-panel"
            role="tabpanel"
            aria-labelledby="policies-tab"
            class={activeTab === 'policies' ? 'block' : 'hidden'}
          >
            <h2 class="mb-5 font-montserrat font-medium text-primary-900 text-2xl uppercase tracking-wider">Booking Policies</h2>
            <div class="space-y-6">
              <div class="bg-primary-50 p-4 rounded-md" in:fade={{ duration: 300, delay: 100 }}>
                <h3 class="mb-2 font-montserrat font-medium text-primary-800 uppercase tracking-wider">Check-in / Check-out</h3>
                <p class="text-primary-700">Check-in: 3:00 PM - 8:00 PM</p>
                <p class="text-primary-700">Check-out: 11:00 AM</p>
              </div>
              <div class="bg-primary-50 p-4 rounded-md" in:fade={{ duration: 300, delay: 150 }}>
                <h3 class="mb-2 font-montserrat font-medium text-primary-800 uppercase tracking-wider">Cancellation Policy</h3>
                <p class="text-primary-700">Free cancellation up to 7 days before arrival. Cancellations made less than 7 days before arrival are subject to a charge of the first night's stay.</p>
              </div>
              <div class="bg-primary-50 p-4 rounded-md" in:fade={{ duration: 300, delay: 200 }}>
                <h3 class="mb-2 font-montserrat font-medium text-primary-800 uppercase tracking-wider">Children and Extra Beds</h3>
                <p class="text-primary-700">Children of all ages are welcome. Children under 12 years stay free when using existing beds.</p>
              </div>
              <div class="bg-primary-50 p-4 rounded-md" in:fade={{ duration: 300, delay: 250 }}>
                <h3 class="mb-2 font-montserrat font-medium text-primary-800 uppercase tracking-wider">Pets</h3>
                <p class="text-primary-700">Pets are not allowed.</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column: Booking Information -->
      <div class="top-4 sticky space-y-8" style="height: fit-content;">
        <!-- Booking Card -->
        <div class="bg-white shadow-md border border-primary-100 rounded-lg overflow-hidden" in:fade={{ duration: 300, delay: 100 }}>
          <!-- Card Header -->
          <div class="bg-primary-50 p-4 border-primary-100 border-b">
            <h3 class="font-montserrat font-medium text-primary-900 text-lg uppercase tracking-wider">Book Your Stay</h3>
          </div>

          <!-- Card Content -->
          <div class="p-6">
            {#if suite.cloudbeds_room_type_id}
              <!-- Price Display -->
              <div class="mb-6">
                <PriceDisplay roomTypeId={suite.cloudbeds_room_type_id} />
              </div>

              <!-- Availability Display -->
              <div class="mb-6">
                <AvailabilityIndicator roomTypeId={suite.cloudbeds_room_type_id} />
              </div>

              <!-- Booking Buttons -->
              <div class="flex flex-col gap-4">
                <QuickBookButton
                  roomTypeId={suite.cloudbeds_room_type_id}
                  propertyId={suite.cloudbeds_property_id}
                  buttonText="Book Now"
                  buttonClass="inline-flex justify-center items-center bg-primary-600 hover:bg-primary-700 px-6 py-4 rounded-md text-white w-full font-medium uppercase tracking-wider transition-all duration-200 shadow-sm hover:shadow"
                />

                <a
                  href="/accommodation/reservations"
                  class="inline-flex justify-center items-center bg-white hover:bg-primary-50 px-6 py-4 border border-primary-600 rounded-md w-full font-medium text-primary-600 uppercase tracking-wider transition-all duration-200"
                >
                  View All Availability
                </a>
              </div>
            {:else}
              <p class="mb-6 text-primary-700">For pricing and availability, please contact us directly or check our reservations page.</p>
              <a
                href="/accommodation/reservations"
                class="inline-flex justify-center items-center bg-primary-600 hover:bg-primary-700 shadow-sm hover:shadow px-6 py-4 rounded-md w-full font-medium text-white uppercase tracking-wider transition-all duration-200"
              >
                <svg
                  class="mr-2 w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                  ></path>
                </svg>
                Check Availability
              </a>
            {/if}
          </div>

          <!-- Card Footer -->
          <div class="bg-primary-50 p-4 border-primary-100 border-t">
            <div class="flex justify-between items-center">
              <div class="flex items-center">
                <svg class="mr-2 w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
                <span class="text-primary-700 text-sm">Best Price Guarantee</span>
              </div>
              <div class="flex items-center">
                <svg class="mr-2 w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
                <span class="text-primary-700 text-sm">Secure Booking</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Information -->
        <div class="bg-white shadow-md p-6 border border-primary-100 rounded-lg" in:fade={{ duration: 300, delay: 400 }}>
          <h3 class="mb-3 font-montserrat font-medium text-primary-900 text-lg uppercase tracking-wider">Need Help?</h3>
          <p class="mb-6 text-primary-700">Have questions about this suite or special requirements? Our team is here to help.</p>
          <a
            href="/contact"
            class="inline-flex justify-center items-center bg-white hover:bg-primary-50 px-6 py-4 border border-primary-600 rounded-md w-full font-medium text-primary-600 uppercase tracking-wider transition-all duration-200"
          >
            <svg
              class="mr-2 w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
              ></path>
            </svg>
            Contact Us
          </a>
        </div>
      </div>
    </div>
  {:else}
    <div class="p-8 text-center">
      <p class="text-red-600">Suite not found</p>
    </div>
  {/if}
</div>
