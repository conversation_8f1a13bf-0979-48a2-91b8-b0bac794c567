<script lang="ts">
  import { onMount } from "svelte";
  import { goto } from "$app/navigation";
  import { cloudbedsApiService } from "$lib/cloudbeds";
  import type { CloudbedsRoom, CloudbedsRoomType } from "$lib/cloudbeds";

  let roomTypes: CloudbedsRoomType[] = [];
  let loading: boolean = true;
  let error: string | null = null;

  onMount(async () => {
    try {
      loading = true;
      error = null;
      const response = await cloudbedsApiService.getRoomTypes(true, true);

      if (response.success && response.data) {
        roomTypes = response.data;
        console.log("Datos de habitaciones cargados:", roomTypes);
      } else {
        error =
          response.error?.message ||
          "Error al cargar los tipos de habitaciones";
      }
    } catch (err) {
      error =
        err instanceof Error
          ? err.message
          : "Error desconocido al cargar las habitaciones";
      console.error("Error al cargar los tipos de habitaciones:", err);
    } finally {
      loading = false;
    }
  });

  /**
   * Navega a la página de reservas con el ID del tipo de habitación seleccionado
   * @param roomTypeId - ID del tipo de habitación seleccionado
   */
  function goToReservation(roomTypeId: string): void {
    goto(`/accommodation/reservations?roomTypeId=${roomTypeId}`);
  }

  /**
   * Navega a la página de disponibilidad con el tipo de habitación seleccionado
   * @param roomTypeId - ID del tipo de habitación seleccionado
   */
  function viewAvailability(roomTypeId: string): void {
    // Aquí podríamos ir a una página detallada de disponibilidad
    // Por ahora, vamos a la misma página de reservas
    goto(`/accommodation/reservations?roomTypeId=${roomTypeId}`);
  }
</script>

<div class="mx-auto p-4 container">
  <h2 class="mb-4 font-bold text-2xl">Habitaciones Disponibles</h2>

  {#if loading}
    <div class="flex justify-center items-center h-40">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
  {:else if error}
    <div class="alert alert-error">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="stroke-current w-6 h-6 shrink-0"
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
      <span>{error}</span>
    </div>
  {:else if roomTypes.length === 0}
    <div class="alert alert-info">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        class="stroke-current w-6 h-6 shrink-0"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        ></path>
      </svg>
      <span>No hay habitaciones disponibles en este momento.</span>
    </div>
  {:else}
    <div class="gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
      {#each roomTypes as roomType}
        <div class="bg-base-100 shadow-xl card">
          <figure class="h-48 overflow-hidden">
            {#if roomType.roomTypePhotos && roomType.roomTypePhotos.length > 0}
              <img
                src={roomType.roomTypePhotos[0]}
                alt={roomType.roomTypeName || roomType.name || "Habitación"}
                class="w-full h-full object-cover"
              />
            {:else if roomType.photos && roomType.photos.length > 0}
              <img
                src={roomType.photos[0]}
                alt={roomType.roomTypeName || roomType.name || "Habitación"}
                class="w-full h-full object-cover"
              />
            {:else if roomType.rooms?.[0]?.photos?.[0]}
              <img
                src={roomType.rooms[0].photos[0]}
                alt={roomType.roomTypeName || roomType.name || "Habitación"}
                class="w-full h-full object-cover"
              />
            {:else}
              <div
                class="flex justify-center items-center bg-base-200 w-full h-full"
              >
                <span class="opacity-50 text-base-content">Sin imagen</span>
              </div>
            {/if}
          </figure>
          <div class="card-body">
            <h3 class="card-title">
              {roomType.roomTypeName ||
                roomType.name ||
                "Habitación sin nombre"}
            </h3>
            {#if roomType.roomTypeDescription || roomType.description}
              <p class="mb-3 text-sm">
                {roomType.roomTypeDescription || roomType.description}
              </p>
            {/if}

            <div class="flex flex-col gap-2">
              <div class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="mr-1 w-5 h-5"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
                <span
                  >Max: {roomType.maxGuests || roomType.maxOccupancy || "N/A"} personas</span
                >
              </div>

              {#if roomType.roomTypeFeatures && Object.keys(roomType.roomTypeFeatures).length > 0}
                <div class="mt-2">
                  <h4 class="mb-1 font-semibold text-sm">Comodidades:</h4>
                  <div class="flex flex-wrap gap-1">
                    {#each Object.values(roomType.roomTypeFeatures).slice(0, 3) as amenity}
                      <span class="badge-outline badge">{amenity}</span>
                    {/each}
                    {#if Object.keys(roomType.roomTypeFeatures).length > 3}
                      <span class="badge-outline badge"
                        >+{Object.keys(roomType.roomTypeFeatures).length - 3} más</span
                      >
                    {/if}
                  </div>
                </div>
              {:else if roomType.amenities && roomType.amenities.length > 0}
                <div class="mt-2">
                  <h4 class="mb-1 font-semibold text-sm">Comodidades:</h4>
                  <div class="flex flex-wrap gap-1">
                    {#each roomType.amenities.slice(0, 3) as amenity}
                      <span class="badge-outline badge">{amenity}</span>
                    {/each}
                    {#if roomType.amenities.length > 3}
                      <span class="badge-outline badge"
                        >+{roomType.amenities.length - 3} más</span
                      >
                    {/if}
                  </div>
                </div>
              {/if}
            </div>

            <div class="justify-end mt-3 card-actions">
              <button
                class="btn-outline btn btn-primary"
                on:click={() =>
                  viewAvailability(roomType.roomTypeID || roomType.id || "")}
              >
                Ver disponibilidad
              </button>
              <button
                class="btn btn-primary"
                on:click={() =>
                  goToReservation(roomType.roomTypeID || roomType.id || "")}
              >
                Reservar ahora
              </button>
            </div>
          </div>
        </div>
      {/each}
    </div>
  {/if}
</div>
