<script lang="ts">
  // Props
  let { features = [], title = "Features" } = $props<{
    features: string[];
    title?: string;
  }>();
</script>

<div class="mb-8">
  <h2 class="mb-5 font-montserrat font-medium text-primary-900 text-2xl uppercase tracking-wider">{title}</h2>
  
  {#if features && features.length > 0}
    <div class="gap-4 grid grid-cols-1 md:grid-cols-2">
      {#each features as feature, i}
        <div class="flex items-center bg-primary-50 hover:bg-primary-100 hover:shadow-sm p-3 rounded-md transition-all duration-200">
          <div class="flex justify-center items-center bg-primary-100 mr-3 rounded-full w-8 h-8">
            <svg
              class="w-4 h-4 text-primary-700"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              ></path>
            </svg>
          </div>
          <span class="text-primary-800">{feature}</span>
        </div>
      {/each}
    </div>
  {:else}
    <p class="bg-primary-50 p-4 rounded-md text-primary-600">No {title.toLowerCase()} listed for this suite.</p>
  {/if}
</div>
