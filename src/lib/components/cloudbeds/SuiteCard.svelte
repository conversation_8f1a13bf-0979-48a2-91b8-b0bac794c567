<script lang="ts">
  import { onMount } from "svelte";
  import SimpleImage from "$lib/components/ui/SimpleImage.svelte";
  import PriceDisplay from "./PriceDisplay.svelte";
  import AvailabilityIndicator from "./AvailabilityIndicator.svelte";
  import QuickBookButton from "./QuickBookButton.svelte";
  import { T, t } from "$lib/i18n";

  // Props
  const {
    suite,
    compact = false,
  } = $props<{
    suite: {
      id: string;
      title: string;
      description?: string;
      image: string;
      url: string;
      bookUrl: string;
      features?: string[];
      cloudbeds_room_type_id?: string | null;
      cloudbeds_property_id?: string | null;
      size?: string;
      capacity?: string;
      videos?: string[];
    };
    compact?: boolean;
  }>();

  // State for video hover effect
  let isHovering = $state(false);
  let videoElement = $state<HTMLVideoElement | null>(null);
  let hasVideo = $state(false);
  let videoLoaded = $state(false);
  let videoUrl = $state<string | null>(null);

  // Check if the suite has videos
  onMount(() => {
    // Check if the suite has videos
    console.log("SuiteCard: Checking for videos in suite:", suite.title);

    // Handle different types of video data
    if (suite.videos) {
      if (Array.isArray(suite.videos) && suite.videos.length > 0) {
        hasVideo = true;
        videoUrl = suite.videos[0];
        console.log("SuiteCard: Suite has video array, using first video:", videoUrl);
      } else if (typeof suite.videos === 'string') {
        hasVideo = true;
        videoUrl = suite.videos;
        console.log("SuiteCard: Suite has video string:", videoUrl);
      } else {
        console.log("SuiteCard: Suite has videos property but it's not usable:", suite.videos);
      }
    } else {
      console.log("SuiteCard: No videos found for suite:", suite.title);
    }

    // Log all suite properties for debugging
    console.log("SuiteCard: All suite properties:", Object.keys(suite));
  });

  // Handle mouse enter
  function handleMouseEnter() {
    console.log("SuiteCard: Mouse enter on suite:", suite.title);
    console.log("SuiteCard: hasVideo:", hasVideo, "videoElement:", !!videoElement, "videoUrl:", videoUrl);

    if (hasVideo && videoElement && videoUrl) {
      isHovering = true;
      console.log("SuiteCard: Setting isHovering to true");

      // If video is not loaded yet, set the source and load it
      if (!videoLoaded) {
        console.log("SuiteCard: Loading video for the first time:", videoUrl);
        videoElement.src = videoUrl;
        videoElement.load();
        videoLoaded = true;
      }

      // Play the video
      console.log("SuiteCard: Attempting to play video");
      videoElement.play().then(() => {
        console.log("SuiteCard: Video playback started successfully");
      }).catch(err => {
        console.error("SuiteCard: Error playing video:", err);
      });
    } else {
      console.log("SuiteCard: Cannot play video, missing required elements");
    }
  }

  // Handle mouse leave
  function handleMouseLeave() {
    console.log("SuiteCard: Mouse leave on suite:", suite.title);

    if (hasVideo && videoElement) {
      isHovering = false;
      console.log("SuiteCard: Setting isHovering to false");

      // Pause the video
      videoElement.pause();
      console.log("SuiteCard: Video paused");

      // Reset to the beginning for next hover
      videoElement.currentTime = 0;
      console.log("SuiteCard: Video reset to beginning");
    }
  }
</script>

<div
  class="group flex flex-col flex-shrink-0 bg-white shadow-sm hover:shadow-md border border-primary-100 transition-shadow {compact ? 'w-[85%] md:w-full lg:w-full' : 'h-full'}"
  role="article"
  onmouseenter={handleMouseEnter}
  onmouseleave={handleMouseLeave}
>
  <!-- Image/Video with availability badge -->
  <div class="relative">
    <a href={suite.url} class="block overflow-hidden">
      <div
        style="--aspect-ratio:16/9;--aspect-ratio-mobile:{compact ? '1/1.5' : '16/9'}"
        class="aspect-ratio-container"
      >
        <div class="relative w-full h-full overflow-hidden">
          <!-- Static Image (always visible, but opacity changes on hover) -->
          <SimpleImage
            src={suite.image}
            alt={suite.title}
            className="group-hover:scale-105 transition-transform duration-500 ease-out will-change-transform {isHovering ? 'opacity-0' : 'opacity-100'}"
          />

          <!-- Video (only visible on hover if available) -->
          {#if hasVideo}
            <video
              bind:this={videoElement}
              class="absolute inset-0 w-full h-full object-cover transition-opacity duration-300 {isHovering ? 'opacity-100' : 'opacity-0'}"
              muted
              playsinline
              preload="none"
              poster={suite.image}
            ></video>
          {/if}

          <!-- Gradient overlay -->
          <div
            class="absolute inset-0 bg-gradient-to-t from-primary-950/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          ></div>
        </div>
      </div>
    </a>

    {#if suite.cloudbeds_room_type_id}
      <div class="top-4 right-4 absolute">
        <div class="bg-primary-50 shadow-md px-3 py-1 rounded-full">
          <AvailabilityIndicator roomTypeId={suite.cloudbeds_room_type_id} compact={true} />
        </div>
      </div>
    {/if}
  </div>

  <div class="flex flex-col flex-grow p-4 md:p-6">
    <h4
      class="mb-2 font-montserrat font-medium text-primary-900 text-lg md:text-xl"
    >
      {suite.title}
    </h4>

    {#if !compact && suite.description}
      <p class="mb-4 font-eb-garamond font-light text-primary-800">
        {suite.description}
      </p>
    {/if}

    {#if !compact && suite.features && suite.features.length > 0}
      <div class="flex flex-wrap gap-2 mb-4">
        {#each suite.features as feature}
          <span class="bg-primary-100 px-3 py-1 font-montserrat font-light text-primary-800 text-xs">
            {feature}
          </span>
        {/each}
      </div>
    {/if}

    {#if suite.cloudbeds_room_type_id}
      <div class="{compact ? 'mt-2 mb-4' : 'mt-auto mb-4'}">
        <PriceDisplay roomTypeId={suite.cloudbeds_room_type_id} compact={true} />
      </div>
    {/if}

    <div
      class="{compact ? 'flex flex-wrap justify-between items-center gap-y-3 mt-auto pt-4' : 'flex flex-wrap gap-3 mt-auto'}"
    >
      <a
        href={suite.url}
        class="{compact ? 'hover:bg-primary-100 transition-colors button-secondary' : 'flex-1 text-center button-secondary'}"
      >
        {#if compact}
          <T key="common.readMore" />
        {:else}
          View Details
        {/if}
      </a>

      {#if suite.cloudbeds_room_type_id}
        <QuickBookButton
          roomTypeId={suite.cloudbeds_room_type_id}
          propertyId={suite.cloudbeds_property_id}
          buttonText={compact ? $t('common.bookNow') : "Quick Book"}
          buttonClass={compact ? "hover:bg-primary-600 transition-colors button" : "button flex-1 text-center"}
        />
      {:else}
        <a
          href={suite.bookUrl}
          class={compact ? "hover:bg-primary-600 transition-colors button" : "flex-1 text-center button"}
        >
          {#if compact}
            <T key="common.bookNow" />
          {:else}
            Book Now
          {/if}
        </a>
      {/if}
    </div>
  </div>
</div>

<style>
  /* Ensure smooth transitions for the hover effect */
  video {
    transition: opacity 0.3s ease-out;
  }
</style>
