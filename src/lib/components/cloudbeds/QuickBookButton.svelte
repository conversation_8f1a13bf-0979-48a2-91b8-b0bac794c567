<script lang="ts">
  import { onMount } from "svelte";
  import { T } from "$lib/i18n";

  // Props
  const {
    roomTypeId = "",
    propertyId = "",
    buttonText = "Quick Book",
    buttonClass = "button",
    showIcon = true,
  } = $props<{
    roomTypeId: string;
    propertyId?: string;
    buttonText?: string;
    buttonClass?: string;
    showIcon?: boolean;
  }>();

  // State
  let showModal = $state(false);
  let checkInDate = $state<string>(getTodayDate());
  let checkOutDate = $state<string>(getTomorrowDate());
  let adults = $state(2);
  let children = $state(0);

  // Get today's date in YYYY-MM-DD format
  function getTodayDate(): string {
    const today = new Date();
    return today.toISOString().split("T")[0];
  }

  // Get tomorrow's date in YYYY-MM-DD format
  function getTomorrowDate(): string {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split("T")[0];
  }

  // Open the modal
  function openModal() {
    showModal = true;
  }

  // Close the modal
  function closeModal() {
    showModal = false;
  }

  // Handle click outside the modal
  function handleClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (target.classList.contains("modal-overlay")) {
      closeModal();
    }
  }

  // Handle booking
  function handleBooking() {
    // Check if the room type is configured for public booking
    // This is a temporary solution until the room types are properly configured in Cloudbeds
    const isRoomTypeConfigured = false; // Set to false to always redirect to contact page

    // If room type is not configured for public booking, redirect to contact page
    if (!isRoomTypeConfigured) {
      console.log("Room type not configured for public booking, redirecting to contact page");
      window.location.href = "/contact";
      closeModal();
      return;
    }

    // Construct the Cloudbeds URL
    let url = "https://hotels.cloudbeds.com/reservation/";

    // Add property ID if available, otherwise show error
    if (propertyId) {
      url += propertyId;
    } else {
      console.error("No property ID provided for booking URL");
      // Redirect to contact page instead
      window.location.href = "/contact";
      return;
    }

    // Add parameters
    url += `#checkin=${checkInDate}&checkout=${checkOutDate}`;

    // Add room type if available
    if (roomTypeId) {
      // Use the room type ID directly - the dynamic resolution happens server-side
      // when creating actual reservations, not when generating booking URLs
      url += `&room_type=${roomTypeId}`;
    }

    // Add guests
    url += `&adults=${adults}&children=${children}`;

    // Add currency
    url += "&currency=eur";

    // Navigate to the URL
    window.open(url, "_blank");

    // Close the modal
    closeModal();
  }

  // Handle keyboard events
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === "Escape" && showModal) {
      closeModal();
    }
  }

  // Add keyboard event listener
  onMount(() => {
    window.addEventListener("keydown", handleKeydown);

    // Cleanup
    return () => {
      window.removeEventListener("keydown", handleKeydown);
    };
  });
</script>

<!-- Quick Book Button -->
<button
  onclick={openModal}
  class={buttonClass}
  aria-label="Quick book this suite"
>
  {#if showIcon}
    <svg class="mr-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
    </svg>
  {/if}
  {buttonText}
</button>

<!-- Modal -->
{#if showModal}
  <div
    class="z-50 fixed inset-0 flex justify-center items-center bg-black bg-opacity-50 p-4 modal-overlay"
    onclick={handleClickOutside}
    onkeydown={handleKeydown}
    role="presentation"
    tabindex="-1"
  >
    <div
      class="bg-white shadow-xl p-6 rounded-lg w-full max-w-md modal-content"
      role="dialog"
      aria-labelledby="quick-book-modal-title"
      aria-modal="true"
    >
      <div class="flex justify-between items-center mb-4">
        <h3 id="quick-book-modal-title" class="font-montserrat font-medium text-primary-900 text-xl">
          <T key="common.quickBook" />
        </h3>
        <button
          onclick={closeModal}
          class="text-gray-500 hover:text-gray-700"
          aria-label="Close modal"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <div class="space-y-4">
        <!-- Check-in Date -->
        <div>
          <label for="check-in-date" class="block mb-1 font-medium text-gray-700 text-sm">
            <T key="common.checkIn" />
          </label>
          <input
            type="date"
            id="check-in-date"
            bind:value={checkInDate}
            min={getTodayDate()}
            class="px-4 py-2 border border-gray-300 focus:border-primary-500 rounded-md focus:ring-primary-500 w-full"
          />
        </div>

        <!-- Check-out Date -->
        <div>
          <label for="check-out-date" class="block mb-1 font-medium text-gray-700 text-sm">
            <T key="common.checkOut" />
          </label>
          <input
            type="date"
            id="check-out-date"
            bind:value={checkOutDate}
            min={checkInDate}
            class="px-4 py-2 border border-gray-300 focus:border-primary-500 rounded-md focus:ring-primary-500 w-full"
          />
        </div>

        <!-- Guests -->
        <div class="gap-4 grid grid-cols-2">
          <div>
            <label for="adults" class="block mb-1 font-medium text-gray-700 text-sm">
              <T key="common.adults" />
            </label>
            <select
              id="adults"
              bind:value={adults}
              class="px-4 py-2 border border-gray-300 focus:border-primary-500 rounded-md focus:ring-primary-500 w-full"
            >
              {#each Array(10) as _, i}
                <option value={i + 1}>{i + 1}</option>
              {/each}
            </select>
          </div>

          <div>
            <label for="children" class="block mb-1 font-medium text-gray-700 text-sm">
              <T key="common.children" />
            </label>
            <select
              id="children"
              bind:value={children}
              class="px-4 py-2 border border-gray-300 focus:border-primary-500 rounded-md focus:ring-primary-500 w-full"
            >
              {#each Array(6) as _, i}
                <option value={i}>{i}</option>
              {/each}
            </select>
          </div>
        </div>
      </div>

      <div class="flex justify-end mt-6">
        <button
          onclick={closeModal}
          class="hover:bg-gray-50 mr-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700"
        >
          <T key="common.cancel" />
        </button>
        <button
          onclick={handleBooking}
          class="bg-primary-600 hover:bg-primary-700 px-4 py-2 rounded-md text-white"
        >
          <T key="common.book" />
        </button>
      </div>
    </div>
  </div>
{/if}
