<script lang="ts">

  import ImageGallery from "$lib/components/ui/ImageGallery.svelte";
  import SuiteFeatures from "./SuiteFeatures.svelte";
  import PriceDisplay from "./PriceDisplay.svelte";
  import AvailabilityIndicator from "./AvailabilityIndicator.svelte";
  import QuickBookButton from "./QuickBookButton.svelte";

  // Props
  let { suiteData } = $props<{
    suiteData: any;
  }>();

  // Ensure arrays are properly initialized
  let suite = $state({
    ...suiteData,
    features: Array.isArray(suiteData.features)
      ? suiteData.features
      : typeof suiteData.features === 'string'
        ? JSON.parse(suiteData.features)
        : [],
    amenities: Array.isArray(suiteData.amenities)
      ? suiteData.amenities
      : typeof suiteData.amenities === 'string'
        ? JSON.parse(suiteData.amenities)
        : [],
    images: Array.isArray(suiteData.images)
      ? suiteData.images
      : typeof suiteData.images === 'string'
        ? JSON.parse(suiteData.images)
        : [],
    videos: Array.isArray(suiteData.videos)
      ? suiteData.videos
      : typeof suiteData.videos === 'string'
        ? JSON.parse(suiteData.videos)
        : [],
  });

  // Debug Cloudbeds integration
  console.group("SimpleSuiteDisplay - Cloudbeds Integration");
  console.log("Suite data:", suite);
  console.log("Cloudbeds Room Type ID:", suite.cloudbeds_room_type_id);
  console.log("Cloudbeds Property ID:", suite.cloudbeds_property_id);

  if (!suite.cloudbeds_room_type_id) {
    console.warn("⚠️ No Cloudbeds Room Type ID provided for suite:", suite.name);
  }

  if (!suite.cloudbeds_property_id) {
    console.warn("⚠️ No Cloudbeds Property ID provided for suite:", suite.name);
  }

  console.groupEnd();


</script>

<div>


  <!-- Suite Header -->
  <div class="mb-8">
    <h1 class="font-montserrat font-light text-primary-900 text-3xl md:text-5xl uppercase tracking-wider">
      {suite.name}
    </h1>
    <div class="bg-primary-500 mt-3 w-24 h-0.5"></div>
  </div>

  <!-- Suite Images Gallery -->
  <div class="mb-10">
    <ImageGallery
      images={suite.images || []}
      videos={suite.videos || []}
      title={suite.name}
      aspectRatio="16/9"
    />
  </div>

  <!-- Description -->
  <div class="mb-8">
    <h2 class="mb-5 font-montserrat font-medium text-primary-900 text-2xl uppercase tracking-wider">Description</h2>
    <div class="mb-8 max-w-none prose">
      <p class="text-primary-800 leading-relaxed">{suite.description}</p>
    </div>
  </div>

  <!-- Features -->
  <SuiteFeatures features={suite.features} title="Features" />

  <!-- Amenities -->
  <SuiteFeatures features={suite.amenities} title="Room Amenities" />

  <!-- Booking Card -->
  <div class="bg-white shadow-md mt-8 border border-primary-100 rounded-lg overflow-hidden">
    <div class="bg-primary-50 p-4 border-primary-100 border-b">
      <h3 class="font-montserrat font-medium text-primary-900 text-lg uppercase tracking-wider">Book Your Stay</h3>
    </div>

    <div class="p-6">
      {#if suite.cloudbeds_room_type_id}
        <!-- Price Display -->
        <div class="mb-6">
          <PriceDisplay roomTypeId={suite.cloudbeds_room_type_id} />
        </div>

        <!-- Availability Display -->
        <div class="mb-6">
          <AvailabilityIndicator roomTypeId={suite.cloudbeds_room_type_id} />
        </div>

        <!-- Booking Buttons -->
        <div class="flex flex-col gap-4">
          <QuickBookButton
            roomTypeId={suite.cloudbeds_room_type_id}
            propertyId={suite.cloudbeds_property_id}
            buttonText="Book Now"
            buttonClass="inline-flex justify-center items-center bg-primary-600 hover:bg-primary-700 px-6 py-4 rounded-md text-white w-full font-medium uppercase tracking-wider transition-all duration-200 shadow-sm hover:shadow"
          />
        </div>
      {:else}
        <p class="mb-6 text-primary-700">For pricing and availability, please contact us directly or check our reservations page.</p>
      {/if}
    </div>
  </div>
</div>
