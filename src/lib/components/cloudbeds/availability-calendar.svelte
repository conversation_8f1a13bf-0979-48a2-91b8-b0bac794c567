<script lang="ts">
  import { onMount } from "svelte";
  import type {
    CloudbedsRoomAvailability,
    CloudbedsAvailabilityDate,
  } from "$lib/cloudbeds";
  import ReservationForm from "./reservation-form.svelte";

  export let roomTypeId: string | undefined = undefined;

  let startDate: string = getFormattedDate(new Date());
  let endDate: string = getFormattedDate(
    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
  ); // +30 días
  let availability: CloudbedsRoomAvailability[] = [];
  let loading: boolean = false;
  let error: string | null = null;
  let calendarDays: CalendarDay[] = [];
  let showReservationForm: boolean = false;
  let selectedDay: CalendarDay | null = null;

  interface CalendarDay {
    date: string;
    formattedDate: string;
    dayOfWeek: string;
    dayOfMonth: number;
    availability: number;
    price?: number;
    status: "available" | "unavailable" | "restricted";
  }

  const daysOfWeek = ["Dom", "Lun", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>áb"];

  function getFormattedDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  }

  function formatDisplayDate(dateStr: string): string {
    const [, month, day] = dateStr.split("-"); // Ignoramos el año
    return `${day}/${month}`;
  }

  async function loadAvailability(): Promise<void> {
    try {
      loading = true;
      error = null;
      console.log("Solicitando disponibilidad para:", {
        startDate,
        endDate,
        roomTypeId,
      });

      // Use a more reliable endpoint with better error handling
      const url = `/api/cloudbeds/availability?startDate=${startDate}&endDate=${endDate}${roomTypeId ? `&roomTypeId=${roomTypeId}` : ''}`;
      console.log("Fetching from URL:", url);

      const fetchResponse = await fetch(url);
      const response = await fetchResponse.json();

      console.log("Respuesta de disponibilidad:", response);

      if (response.success && response.data) {
        availability = response.data;
        generateCalendarDays();
      } else {
        // Manejo específico de errores comunes
        if (response.message === "You don't have access to property ID") {
          error =
            "No tienes acceso a esta propiedad. Verifica las credenciales API y los permisos asignados.";
        } else {
          error =
            response.error?.message ||
            response.message ||
            "Error al cargar la disponibilidad";
        }
        console.error("Error específico en la respuesta:", response);
      }
    } catch (err) {
      // Analizar el mensaje de error para dar más información
      let errorMessage = "Error desconocido al cargar la disponibilidad";

      if (err instanceof Error) {
        errorMessage = err.message;

        // Errores específicos de autenticación
        if (
          errorMessage.includes("No se pudo obtener un token de acceso") ||
          errorMessage.includes("Error de autenticación")
        ) {
          errorMessage =
            "Error de autenticación con Cloudbeds. Verifica tu API Key y permisos.";
        }
        // Errores de propertyID
        else if (errorMessage.includes("access to property ID")) {
          errorMessage =
            "No tienes acceso a esta propiedad. Verifica los permisos en Cloudbeds.";
        }
      }

      error = errorMessage;
      console.error("Error al cargar la disponibilidad:", err);
    } finally {
      loading = false;
    }
  }

  function generateCalendarDays(): void {
    calendarDays = [];

    if (!availability || availability.length === 0) return;

    // Nueva forma de procesar los datos según la estructura API actual
    if (availability[0].propertyRooms) {
      // Nos enfocamos en el primer tipo de habitación si no se especifica roomTypeId
      const propertyData = availability[0];

      // Verificamos que propertyRooms exista
      if (
        !propertyData.propertyRooms ||
        propertyData.propertyRooms.length === 0
      )
        return;

      // Encontramos la habitación específica si se proporcionó roomTypeId
      const roomTypeData = roomTypeId
        ? propertyData.propertyRooms.find(
            (room) => room.roomTypeID === roomTypeId
          )
        : propertyData.propertyRooms[0];

      if (!roomTypeData) return;

      // Crear datos de disponibilidad para 30 días
      const start = new Date(startDate);
      const end = new Date(endDate);

      // Iterar por cada día en el rango
      for (
        let day = new Date(start);
        day <= end;
        day.setDate(day.getDate() + 1)
      ) {
        const dateStr = getFormattedDate(day);
        const dateObj = new Date(dateStr);

        calendarDays.push({
          date: dateStr,
          formattedDate: formatDisplayDate(dateStr),
          dayOfWeek: daysOfWeek[dateObj.getDay()],
          dayOfMonth: dateObj.getDate(),
          availability: roomTypeData.roomsAvailable,
          price: roomTypeData.roomRate,
          status: roomTypeData.roomsAvailable > 0 ? "available" : "unavailable",
        });
      }
    }
    // Formato antiguo para mantener compatibilidad
    else if (availability[0].dates) {
      const roomAvailability = availability[0];

      if (roomAvailability.dates) {
        roomAvailability.dates.forEach((date: CloudbedsAvailabilityDate) => {
          const dateObj = new Date(date.date);
          calendarDays.push({
            date: date.date,
            formattedDate: formatDisplayDate(date.date),
            dayOfWeek: daysOfWeek[dateObj.getDay()],
            dayOfMonth: dateObj.getDate(),
            availability: date.available,
            price: date.price,
            status:
              date.status || (date.available > 0 ? "available" : "unavailable"),
          });
        });
      }
    }
  }

  function updateDates(): void {
    loadAvailability();
  }

  function selectDay(day: CalendarDay): void {
    if (day.status === "available" && day.availability > 0) {
      selectedDay = day;
      showReservationForm = true;
    }
  }

  function closeReservationForm(): void {
    showReservationForm = false;
    selectedDay = null;
  }

  function onReservationSuccess(_reservationId: string): void {
    // Recargar la disponibilidad después de una reserva exitosa
    setTimeout(() => {
      showReservationForm = false;
      selectedDay = null;
      loadAvailability();
    }, 2000);
  }

  onMount(() => {
    loadAvailability();
  });
</script>

<div class="w-full">
  <div class="flex md:flex-row flex-col items-end gap-4 mb-6">
    <div class="w-full md:w-auto">
      <label
        for="startDate"
        class="block mb-1 font-montserrat font-light text-primary-900"
      >
        Check-in Date
      </label>
      <input
        id="startDate"
        type="date"
        bind:value={startDate}
        class="p-2 border border-primary-200 w-full font-montserrat font-light"
      />
    </div>

    <div class="w-full md:w-auto">
      <label
        for="endDate"
        class="block mb-1 font-montserrat font-light text-primary-900"
      >
        Check-out Date
      </label>
      <input
        id="endDate"
        type="date"
        bind:value={endDate}
        class="p-2 border border-primary-200 w-full font-montserrat font-light"
      />
    </div>

    <button
      class="mt-4 md:mt-0 button"
      on:click={updateDates}
      disabled={loading}
    >
      {loading ? "Loading..." : "Check Availability"}
    </button>
  </div>

  {#if loading}
    <div class="flex justify-center items-center h-40">
      <div
        class="border-4 border-primary-500 border-t-transparent rounded-full w-10 h-10 animate-spin"
      ></div>
    </div>
  {:else if error}
    <div class="bg-red-50 mb-4 p-4 border border-red-200 text-red-700">
      <div class="flex items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="mr-2 w-5 h-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
            clip-rule="evenodd"
          />
        </svg>
        <span>{error}</span>
      </div>
    </div>
  {:else if calendarDays.length === 0}
    <div class="bg-blue-50 mb-4 p-4 border border-blue-200 text-blue-700">
      <div class="flex items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="mr-2 w-5 h-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
            clip-rule="evenodd"
          />
        </svg>
        <span>No availability information for the selected period.</span>
      </div>
    </div>
  {:else}
    <div class="pb-4 overflow-x-auto">
      <div
        class="flex gap-2 pb-2 overflow-x-auto snap-mandatory snap-x scrollbar-hide"
      >
        {#each calendarDays as day}
          <div
            class="flex-shrink-0 flex flex-col items-center p-3 border w-20 h-28 snap-start
            {day.status === 'available'
              ? 'border-primary-200 bg-primary-50 cursor-pointer hover:bg-primary-100'
              : day.status === 'restricted'
                ? 'border-yellow-200 bg-yellow-50'
                : 'border-red-200 bg-red-50'}"
            on:click={() => selectDay(day)}
            on:keydown={(e) => e.key === "Enter" && selectDay(day)}
            role="button"
            tabindex="0"
          >
            <span class="font-montserrat font-light text-primary-900 text-sm"
              >{day.dayOfWeek}</span
            >
            <span class="font-montserrat font-medium text-primary-900 text-xl"
              >{day.dayOfMonth}</span
            >
            <span class="font-montserrat font-light text-primary-800 text-xs"
              >{day.formattedDate}</span
            >
            <span
              class="mt-2 px-2 py-0.5 text-xs font-montserrat font-light
              {day.status === 'available'
                ? 'bg-primary-100 text-primary-800'
                : day.status === 'restricted'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-red-100 text-red-800'}"
            >
              {day.availability}
            </span>
            {#if day.price}
              <span
                class="mt-1 font-montserrat font-light text-primary-900 text-xs"
                >${day.price}</span
              >
            {/if}
          </div>
        {/each}
      </div>
    </div>
  {/if}

  {#if showReservationForm && selectedDay}
    <div
      class="z-50 fixed inset-0 flex justify-center items-center bg-black bg-opacity-50"
    >
      <div
        class="bg-base-100 shadow-xl p-4 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto"
      >
        <ReservationForm
          roomTypeId={roomTypeId || ""}
          startDate={selectedDay.date}
          endDate={new Date(
            new Date(selectedDay.date).getTime() + 24 * 60 * 60 * 1000
          )
            .toISOString()
            .split("T")[0]}
          onCancel={closeReservationForm}
          onSuccess={onReservationSuccess}
        />
      </div>
    </div>
  {/if}
</div>
