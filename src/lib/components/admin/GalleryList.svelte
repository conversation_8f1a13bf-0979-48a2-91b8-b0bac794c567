<script lang="ts">
  import { onMount } from "svelte";
  import { goto } from "$app/navigation";
  import { currentSite } from "$lib/site/store";
  import { Edit, Trash2, Plus, Image } from "lucide-svelte";

  // Props
  const { showAddButton = true } = $props<{
    showAddButton?: boolean;
  }>();

  // State
  let galleries = $state<any[]>([]);
  let loading = $state(true);
  let error = $state<string | null>(null);

  // Load galleries
  async function loadGalleries() {
    try {
      loading = true;
      error = null;

      // Construct URL with site_id if available
      let url = "/admin/gallery";
      if ($currentSite && $currentSite.id) {
        url += `?site_id=${$currentSite.id}`;
      }

      const response = await fetch(url);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to load galleries");
      }

      galleries = result.data || [];
    } catch (err: any) {
      console.error("Error loading galleries:", err);
      error = err.message || "Failed to load galleries";
    } finally {
      loading = false;
    }
  }

  // Delete gallery
  async function deleteGallery(id: string) {
    if (!confirm("Are you sure you want to delete this gallery?")) {
      return;
    }

    try {
      const response = await fetch(`/admin/gallery/${id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to delete gallery");
      }

      // Reload galleries
      loadGalleries();
    } catch (err: any) {
      console.error("Error deleting gallery:", err);
      alert(`Error: ${err.message || "Failed to delete gallery"}`);
    }
  }

  // Navigate to edit page
  function editGallery(id: string) {
    goto(`/admin/galleries/${id}`);
  }

  // Navigate to create page
  function createGallery() {
    goto("/admin/galleries/new");
  }

  // Format date
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString();
  }

  // Load galleries when component mounts or currentSite changes
  $effect(() => {
    loadGalleries();
  });
</script>

<div>
  <div class="flex justify-between items-center mb-6">
    <h2 class="font-medium text-gray-900 text-xl">Galleries</h2>
    {#if showAddButton}
      <button
        type="button"
        class="flex items-center bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-md text-white"
        on:click={createGallery}
      >
        <Plus class="mr-2 w-4 h-4" />
        Add Gallery
      </button>
    {/if}
  </div>

  {#if loading}
    <div class="flex justify-center items-center p-8">
      <div
        class="border-4 border-gray-300 border-t-blue-600 rounded-full w-8 h-8 animate-spin"
      ></div>
    </div>
  {:else if error}
    <div class="bg-red-50 p-4 border border-red-200 rounded-md">
      <p class="text-red-700">{error}</p>
    </div>
  {:else if galleries.length === 0}
    <div class="bg-gray-50 p-8 border border-gray-200 rounded-md text-center">
      <Image class="mx-auto mb-4 w-12 h-12 text-gray-400" />
      <h3 class="mb-2 font-medium text-gray-900">No Galleries Found</h3>
      <p class="mb-4 text-gray-600">
        You haven't created any galleries yet. Click the button below to create
        your first gallery.
      </p>
      <button
        type="button"
        class="inline-flex items-center bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-md text-white"
        on:click={createGallery}
      >
        <Plus class="mr-2 w-4 h-4" />
        Create Gallery
      </button>
    </div>
  {:else}
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Name
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Images
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Status
            </th>
            <th
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Created
            </th>
            <th scope="col" class="relative px-6 py-3">
              <span class="sr-only">Actions</span>
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          {#each galleries as gallery}
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">
                  {gallery.name}
                </div>
                {#if gallery.description}
                  <div class="text-sm text-gray-500">
                    {gallery.description.length > 50
                      ? gallery.description.substring(0, 50) + "..."
                      : gallery.description}
                  </div>
                {/if}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                  {gallery.images ? gallery.images.length : 0} images
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                  class:bg-green-100={gallery.status === "active"}
                  class:text-green-800={gallery.status === "active"}
                  class:bg-yellow-100={gallery.status === "draft"}
                  class:text-yellow-800={gallery.status === "draft"}
                  class:bg-red-100={gallery.status === "inactive"}
                  class:text-red-800={gallery.status === "inactive"}
                >
                  {gallery.status}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {formatDate(gallery.created_at)}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button
                  type="button"
                  class="text-blue-600 hover:text-blue-900 mr-3"
                  on:click={() => editGallery(gallery.id)}
                >
                  <Edit class="w-4 h-4" />
                </button>
                <button
                  type="button"
                  class="text-red-600 hover:text-red-900"
                  on:click={() => deleteGallery(gallery.id)}
                >
                  <Trash2 class="w-4 h-4" />
                </button>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  {/if}
</div>
