<script lang="ts">
  import { Plus, X } from "lucide-svelte";
  import { currentSite } from "$lib/site/store";
  import MediaManager from "$lib/components/ui/MediaManager.svelte";
  import { onMount, onDestroy } from "svelte";

  // Props
  let {
    facility: initialFacility = {
      id: null,
      site_id: "",
      name: "",
      slug: "",
      description: "",
      features: [],
      images: [],
      videos: [],
      hours: "",
      restrictions: "",
      status: "active",
    },
    isEdit = false,
  } = $props();

  // Make facility reactive and ensure arrays are initialized
  let facility = $state({
    id: initialFacility.id,
    site_id: initialFacility.site_id || "",
    name: initialFacility.name || "",
    slug: initialFacility.slug || "",
    description: initialFacility.description || "",
    hours: initialFacility.hours || "",
    restrictions: initialFacility.restrictions || "",
    status: initialFacility.status || "active",
    images: Array.isArray(initialFacility.images) ? [...initialFacility.images] : [],
    videos: Array.isArray(initialFacility.videos) ? [...initialFacility.videos] : [],
    features: Array.isArray(initialFacility.features)
      ? [...initialFacility.features]
      : [],
  });

  // Set site_id from currentSite when creating a new facility
  $effect(() => {
    if ($currentSite) {
      console.log("Setting site_id from currentSite:", $currentSite.id);
      facility.site_id = $currentSite.id;
    }
  });

  // Form state
  let loading = $state(false);
  let error = $state<string | null>(null);
  let success = $state(false);
  let newFeature = $state("");

  // Add a feature
  function addFeature() {
    if (newFeature.trim()) {
      facility.features = [...facility.features, newFeature.trim()];
      newFeature = "";
    }
  }

  // Remove a feature
  function removeFeature(index: number) {
    facility.features = facility.features.filter((_, i) => i !== index);
  }

  // Generate slug from name
  function generateSlug() {
    if (facility.name) {
      facility.slug = facility.name
        .toLowerCase()
        .replace(/[^\w\s-]/g, "")
        .replace(/\s+/g, "-");
    }
  }

  // Handle form submission
  async function handleSubmit() {
    try {
      loading = true;
      error = null;
      success = false;

      // Validate form
      if (!facility.site_id) {
        throw new Error("Please select a property");
      }

      if (!facility.name) {
        throw new Error("Name is required");
      }

      if (!facility.slug) {
        throw new Error("Slug is required");
      }

      // Prepare data
      const facilityData = {
        site_id: facility.site_id,
        name: facility.name,
        slug: facility.slug,
        description: facility.description || "",
        features: Array.isArray(facility.features) ? facility.features : [],
        images: Array.isArray(facility.images) ? facility.images : [],
        videos: Array.isArray(facility.videos) ? facility.videos : [],
        hours: facility.hours || "",
        restrictions: facility.restrictions || "",
        status: facility.status || "active",
      };

      // Force images and videos to be arrays in the database
      if (facilityData.images.length === 0) {
        facilityData.images = [];
      }

      if (facilityData.videos.length === 0) {
        facilityData.videos = [];
      }

      // Send request to server
      const url = isEdit
        ? `/admin/facilities/${facility.id}`
        : "/admin/facilities";
      const method = isEdit ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(facilityData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to save facility");
      }

      // Update facility ID if this was a new facility
      if (!isEdit && result.data?.id) {
        facility.id = result.data.id;
      }

      success = true;

      // Dispatch saved event
      const savedEvent = new CustomEvent("saved", {
        detail: { id: facility.id },
      });
      document.dispatchEvent(savedEvent);
    } catch (err) {
      console.error("Error saving facility:", err);
      error = err instanceof Error ? err.message : "An error occurred";
    } finally {
      loading = false;
    }
  }

  // Event handler for media changes
  function handleMediaChange(event: Event) {
    const customEvent = event as CustomEvent;
    const { items, type } = customEvent.detail;

    console.log(`FacilityForm: Received media-change-${type} event`, items);

    if (type === "image") {
      facility.images = [...items];
    } else if (type === "video") {
      facility.videos = [...items];
    }
  }

  onMount(() => {
    document.addEventListener("media-change-image", handleMediaChange);
    document.addEventListener("media-change-video", handleMediaChange);
  });

  onDestroy(() => {
    document.removeEventListener("media-change-image", handleMediaChange);
    document.removeEventListener("media-change-video", handleMediaChange);
  });
</script>

<div class="bg-white shadow p-6 rounded-lg">
  <h2 class="mb-6 font-semibold text-gray-900 text-xl">
    {isEdit ? "Edit Facility" : "Add New Facility"}
  </h2>

  {#if error}
    <div class="bg-red-50 mb-6 p-4 border border-red-200 rounded-md">
      <p class="text-red-700">{error}</p>
    </div>
  {/if}

  {#if success}
    <div class="bg-green-50 mb-6 p-4 border border-green-200 rounded-md">
      <p class="text-green-700">
        Facility {isEdit ? "updated" : "created"} successfully!
      </p>
    </div>
  {/if}

  <form
    on:submit|preventDefault={handleSubmit}
    class="space-y-6"
  >
    <!-- Hidden site_id field -->
    <input type="hidden" id="site_id" value={facility.site_id} required />

    <!-- Status -->
    <div class="mb-6">
      <label for="status" class="block mb-1 font-medium text-gray-700 text-sm"
        >Status</label
      >
      <select
        id="status"
        bind:value={facility.status}
        class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
      >
        <option value="active">Active</option>
        <option value="inactive">Inactive</option>
        <option value="draft">Draft</option>
      </select>
    </div>

    <!-- Name -->
    <div>
      <label for="name" class="block mb-1 font-medium text-gray-700 text-sm"
        >Name</label
      >
      <input
        type="text"
        id="name"
        bind:value={facility.name}
        on:blur={generateSlug}
        class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        required
      />
    </div>

    <!-- Slug -->
    <div>
      <label for="slug" class="block mb-1 font-medium text-gray-700 text-sm"
        >Slug</label
      >
      <div class="flex">
        <input
          type="text"
          id="slug"
          bind:value={facility.slug}
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
          required
        />
        <button
          type="button"
          on:click={generateSlug}
          class="bg-gray-200 hover:bg-gray-300 ml-2 px-4 py-2 rounded-md"
        >
          Generate
        </button>
      </div>
      <p class="mt-1 text-gray-500 text-xs">
        Used in the URL: /facilities/{facility.slug}
      </p>
    </div>

    <!-- Description -->
    <div>
      <label for="description" class="block mb-1 font-medium text-gray-700 text-sm"
        >Description</label
      >
      <textarea
        id="description"
        bind:value={facility.description}
        rows="4"
        class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
      ></textarea>
    </div>

    <!-- Hours -->
    <div>
      <label for="hours" class="block mb-1 font-medium text-gray-700 text-sm"
        >Hours</label
      >
      <input
        type="text"
        id="hours"
        bind:value={facility.hours}
        class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        placeholder="e.g., 9:00 AM - 10:00 PM daily"
      />
    </div>

    <!-- Restrictions -->
    <div>
      <label for="restrictions" class="block mb-1 font-medium text-gray-700 text-sm"
        >Restrictions</label
      >
      <textarea
        id="restrictions"
        bind:value={facility.restrictions}
        rows="2"
        class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        placeholder="e.g., Age restrictions, dress code, etc."
      ></textarea>
    </div>

    <!-- Features -->
    <div>
      <label class="block mb-1 font-medium text-gray-700 text-sm"
        >Features</label
      >
      <div class="flex mb-2">
        <input
          type="text"
          bind:value={newFeature}
          placeholder="Add a feature"
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        />
        <button
          type="button"
          on:click={addFeature}
          class="bg-blue-600 hover:bg-blue-700 ml-2 px-4 py-2 rounded-md text-white"
        >
          <Plus class="w-5 h-5" />
        </button>
      </div>

      <div class="flex flex-wrap gap-2 mt-2">
        {#each facility.features as feature, index}
          <div
            class="flex items-center bg-green-50 px-3 py-1 rounded-full text-green-700"
          >
            <span>{feature}</span>
            <button
              type="button"
              on:click={() => removeFeature(index)}
              class="ml-2 text-green-500 hover:text-green-700"
            >
              <X class="w-4 h-4" />
            </button>
          </div>
        {/each}
      </div>
    </div>

    <!-- Images -->
    <div class="mb-6">
      {#if Array.isArray(facility.images)}
        <p class="mb-2 text-gray-500 text-xs">
          Images: {facility.images.length} items
        </p>
      {/if}
      <div id="image-media-manager">
        <MediaManager
          mediaItems={facility.images || []}
          type="image"
          bucket="facilities"
          folder="images"
        />
      </div>
    </div>

    <!-- Videos -->
    <div class="mb-6">
      {#if Array.isArray(facility.videos)}
        <p class="mb-2 text-gray-500 text-xs">
          Videos: {facility.videos.length} items
        </p>
      {/if}
      <div id="video-media-manager">
        <MediaManager
          mediaItems={facility.videos || []}
          type="video"
          bucket="facilities"
          folder="videos"
        />
      </div>
    </div>

    <!-- Submit Button -->
    <div class="flex justify-end">
      <button
        type="submit"
        class="flex items-center bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-md text-white"
        disabled={loading}
      >
        {#if loading}
          <div
            class="mr-2 border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin"
          ></div>
        {/if}
        {isEdit ? "Update Facility" : "Create Facility"}
      </button>
    </div>
  </form>
</div>
