<script lang="ts">
  import { createEventDispatcher } from "svelte";
  import { supabase } from "$lib";

  // Props
  let {
    siteUser = {
      id: null,
      site_id: "",
      user_id: null,
      full_name: "",
      email: "",
      phone: "",
      status: "active",
    },
    sites = [],
    isEdit = false,
  } = $props();

  // State
  let loading = $state(false);
  let error = $state(null);
  let success = $state(false);
  let createAccount = $state(!isEdit);
  let password = $state("");
  let confirmPassword = $state("");
  let permissions = $state([
    { name: "manage_suites", checked: false, label: "Manage Suites" },
    {
      name: "manage_cloudbeds",
      checked: false,
      label: "Manage Cloudbeds Integration",
    },
    {
      name: "manage_reservations",
      checked: false,
      label: "Manage Reservations",
    },
    { name: "manage_users", checked: false, label: "Manage Users" },
    { name: "view_analytics", checked: false, label: "View Analytics" },
  ]);

  // Event dispatcher
  const dispatch = createEventDispatcher<{
    saved: { id: string | number };
  }>();

  // Load permissions if editing
  async function loadPermissions() {
    if (isEdit && siteUser.id) {
      try {
        const { data, error: permissionsError } = await supabase
          .from("user_site_permissions")
          .select("permission")
          .eq("site_user_id", siteUser.id);

        if (permissionsError) throw permissionsError;

        if (data && data.length > 0) {
          const userPermissions = data.map((p) => p.permission);

          // Update permissions state
          permissions = permissions.map((p) => ({
            ...p,
            checked: userPermissions.includes(p.name),
          }));
        }
      } catch (err) {
        console.error("Error loading permissions:", err);
      }
    }
  }

  // Generate random password
  function generatePassword() {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()";
    let result = "";
    for (let i = 0; i < 12; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    password = result;
    confirmPassword = result;
  }

  // Handle form submission
  async function handleSubmit() {
    try {
      loading = true;
      error = null;
      success = false;

      // Validate form
      if (!siteUser.site_id) {
        throw new Error("Please select a site");
      }

      if (!siteUser.full_name) {
        throw new Error("Full name is required");
      }

      if (!siteUser.email) {
        throw new Error("Email is required");
      }

      if (createAccount && !isEdit) {
        if (!password) {
          throw new Error("Password is required");
        }

        if (password !== confirmPassword) {
          throw new Error("Passwords do not match");
        }

        if (password.length < 8) {
          throw new Error("Password must be at least 8 characters");
        }
      }

      let userId = siteUser.user_id;

      // Create user account if needed
      if (createAccount && !isEdit) {
        const { data: userData, error: signUpError } =
          await supabase.auth.signUp({
            email: siteUser.email,
            password: password,
            options: {
              data: {
                full_name: siteUser.full_name,
              },
            },
          });

        if (signUpError) throw signUpError;

        if (!userData.user) {
          throw new Error("Failed to create user account");
        }

        userId = userData.user.id;
      }

      // Prepare site user data
      const siteUserData = {
        site_id: siteUser.site_id,
        user_id: userId,
        full_name: siteUser.full_name,
        email: siteUser.email,
        phone: siteUser.phone,
        status: siteUser.status,
      };

      let siteUserId;

      // Create or update site user
      if (isEdit && siteUser.id) {
        // Update existing site user
        const { data, error: updateError } = await supabase
          .from("site_users")
          .update(siteUserData)
          .eq("id", siteUser.id)
          .select()
          .single();

        if (updateError) throw updateError;
        siteUserId = data.id;
      } else {
        // Create new site user
        const { data, error: insertError } = await supabase
          .from("site_users")
          .insert(siteUserData)
          .select()
          .single();

        if (insertError) throw insertError;
        siteUserId = data.id;
      }

      // Handle permissions
      const selectedPermissions = permissions
        .filter((p) => p.checked)
        .map((p) => p.name);

      if (isEdit) {
        // Delete existing permissions
        const { error: deleteError } = await supabase
          .from("user_site_permissions")
          .delete()
          .eq("site_user_id", siteUserId);

        if (deleteError) throw deleteError;
      }

      // Add new permissions
      if (selectedPermissions.length > 0) {
        const permissionsData = selectedPermissions.map((permission) => ({
          site_user_id: siteUserId,
          permission,
        }));

        const { error: permissionsError } = await supabase
          .from("user_site_permissions")
          .insert(permissionsData);

        if (permissionsError) throw permissionsError;
      }

      success = true;
      dispatch("saved", { id: siteUserId });
    } catch (err) {
      console.error("Error saving site user:", err);
      error = err.message || "Failed to save site user";
    } finally {
      loading = false;
    }
  }

  // Initialize
  $effect(() => {
    if (isEdit && siteUser.id) {
      loadPermissions();
    }
  });
</script>

<div class="bg-white shadow p-6 rounded-lg">
  {#if error}
    <div class="bg-red-50 mb-6 p-4 border border-red-200 rounded-md">
      <p class="text-red-700">{error}</p>
    </div>
  {/if}

  {#if success}
    <div class="bg-green-50 mb-6 p-4 border border-green-200 rounded-md">
      <p class="text-green-700">User saved successfully!</p>
    </div>
  {/if}

  <form on:submit|preventDefault={handleSubmit}>
    <div class="gap-6 grid grid-cols-1 md:grid-cols-2 mb-6">
      <!-- Site Selection -->
      <div>
        <label
          for="site_id"
          class="block mb-1 font-medium text-gray-700 text-sm">Site *</label
        >
        <select
          id="site_id"
          bind:value={siteUser.site_id}
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
          required
          disabled={isEdit}
        >
          <option value="">Select a site</option>
          {#each sites as site}
            <option value={site.id}>{site.name}</option>
          {/each}
        </select>
      </div>

      <!-- Status -->
      <div>
        <label for="status" class="block mb-1 font-medium text-gray-700 text-sm"
          >Status</label
        >
        <select
          id="status"
          bind:value={siteUser.status}
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        >
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
          <option value="pending">Pending</option>
        </select>
      </div>
    </div>

    <div class="gap-6 grid grid-cols-1 md:grid-cols-2 mb-6">
      <!-- Full Name -->
      <div>
        <label
          for="full_name"
          class="block mb-1 font-medium text-gray-700 text-sm"
          >Full Name *</label
        >
        <input
          id="full_name"
          type="text"
          bind:value={siteUser.full_name}
          placeholder="John Doe"
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
          required
        />
      </div>

      <!-- Email -->
      <div>
        <label for="email" class="block mb-1 font-medium text-gray-700 text-sm"
          >Email *</label
        >
        <input
          id="email"
          type="email"
          bind:value={siteUser.email}
          placeholder="<EMAIL>"
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
          required
          disabled={isEdit}
        />
      </div>
    </div>

    <div class="gap-6 grid grid-cols-1 md:grid-cols-2 mb-6">
      <!-- Phone -->
      <div>
        <label for="phone" class="block mb-1 font-medium text-gray-700 text-sm"
          >Phone</label
        >
        <input
          id="phone"
          type="tel"
          bind:value={siteUser.phone}
          placeholder="+****************"
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        />
      </div>
    </div>

    <!-- Account Creation -->
    {#if !isEdit}
      <div class="mb-6 pt-6 border-t">
        <h3 class="mb-4 font-medium text-gray-900 text-lg">Account Creation</h3>

        <div class="flex items-center mb-4">
          <input
            id="create_account"
            type="checkbox"
            bind:checked={createAccount}
            class="border-gray-300 rounded focus:ring-blue-500 w-4 h-4 text-blue-600"
          />
          <label for="create_account" class="block ml-2 text-gray-700 text-sm">
            Create user account with login credentials
          </label>
        </div>

        {#if createAccount}
          <div class="gap-6 grid grid-cols-1 md:grid-cols-2 mb-4">
            <!-- Password -->
            <div>
              <label
                for="password"
                class="block mb-1 font-medium text-gray-700 text-sm"
                >Password *</label
              >
              <div class="flex">
                <input
                  id="password"
                  type="password"
                  bind:value={password}
                  placeholder="••••••••"
                  class="flex-1 px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-l-md focus:ring-blue-500"
                  required
                />
                <button
                  type="button"
                  on:click={generatePassword}
                  class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-r-md text-white"
                >
                  Generate
                </button>
              </div>
            </div>

            <!-- Confirm Password -->
            <div>
              <label
                for="confirm_password"
                class="block mb-1 font-medium text-gray-700 text-sm"
                >Confirm Password *</label
              >
              <input
                id="confirm_password"
                type="password"
                bind:value={confirmPassword}
                placeholder="••••••••"
                class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
                required
              />
            </div>
          </div>
        {/if}
      </div>
    {/if}

    <!-- Permissions -->
    <div class="mb-6 pt-6 border-t">
      <h3 class="mb-4 font-medium text-gray-900 text-lg">Permissions</h3>

      <div class="space-y-3">
        {#each permissions as permission}
          <div class="flex items-center">
            <input
              id={permission.name}
              type="checkbox"
              bind:checked={permission.checked}
              class="border-gray-300 rounded focus:ring-blue-500 w-4 h-4 text-blue-600"
            />
            <label
              for={permission.name}
              class="block ml-2 text-gray-700 text-sm"
            >
              {permission.label}
            </label>
          </div>
        {/each}
      </div>
    </div>

    <!-- Submit Button -->
    <div class="flex justify-end">
      <button
        type="submit"
        class="flex items-center bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-md text-white"
        disabled={loading}
      >
        {#if loading}
          <div
            class="mr-2 border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin"
          ></div>
        {/if}
        {isEdit ? "Update User" : "Create User"}
      </button>
    </div>
  </form>
</div>
