<script lang="ts">
  import { Plus, X } from "lucide-svelte";
  import { currentSite } from "$lib/site/store";
  import MediaManager from "$lib/components/ui/MediaManager.svelte";
  import { onMount, onDestroy } from "svelte";

  // Props
  let {
    activity: initialActivity = {
      id: null,
      site_id: "",
      name: "",
      slug: "",
      description: "",
      features: [],
      images: [],
      videos: [],
      schedule: "",
      duration: "",
      price_info: {},
      location: "",
      status: "active",
    },
    isEdit = false,
  } = $props();

  // Make activity reactive and ensure arrays are initialized
  let activity = $state({
    id: initialActivity.id,
    site_id: initialActivity.site_id || "",
    name: initialActivity.name || "",
    slug: initialActivity.slug || "",
    description: initialActivity.description || "",
    schedule: initialActivity.schedule || "",
    duration: initialActivity.duration || "",
    price_info: initialActivity.price_info || {},
    location: initialActivity.location || "",
    status: initialActivity.status || "active",
    images: Array.isArray(initialActivity.images) ? [...initialActivity.images] : [],
    videos: Array.isArray(initialActivity.videos) ? [...initialActivity.videos] : [],
    features: Array.isArray(initialActivity.features)
      ? [...initialActivity.features]
      : [],
  });

  // Set site_id from currentSite when creating a new activity
  $effect(() => {
    if ($currentSite) {
      console.log("Setting site_id from currentSite:", $currentSite.id);
      activity.site_id = $currentSite.id;
    }
  });

  // Form state
  let loading = $state(false);
  let error = $state<string | null>(null);
  let success = $state(false);
  let newFeature = $state("");
  let priceAmount = $state(activity.price_info?.amount || "");
  let priceCurrency = $state(activity.price_info?.currency || "USD");

  // Add a feature
  function addFeature() {
    if (newFeature.trim()) {
      activity.features = [...activity.features, newFeature.trim()];
      newFeature = "";
    }
  }

  // Remove a feature
  function removeFeature(index: number) {
    activity.features = activity.features.filter((_, i) => i !== index);
  }

  // Generate slug from name
  function generateSlug() {
    if (activity.name) {
      activity.slug = activity.name
        .toLowerCase()
        .replace(/[^\w\s-]/g, "")
        .replace(/\s+/g, "-");
    }
  }

  // Update price info
  $effect(() => {
    if (priceAmount) {
      activity.price_info = {
        ...activity.price_info,
        amount: priceAmount,
        currency: priceCurrency
      };
    }
  });

  // Handle form submission
  async function handleSubmit() {
    try {
      loading = true;
      error = null;
      success = false;

      // Validate form
      if (!activity.site_id) {
        throw new Error("Please select a property");
      }

      if (!activity.name) {
        throw new Error("Name is required");
      }

      if (!activity.slug) {
        throw new Error("Slug is required");
      }

      // Prepare data
      const activityData = {
        site_id: activity.site_id,
        name: activity.name,
        slug: activity.slug,
        description: activity.description || "",
        features: Array.isArray(activity.features) ? activity.features : [],
        images: Array.isArray(activity.images) ? activity.images : [],
        videos: Array.isArray(activity.videos) ? activity.videos : [],
        schedule: activity.schedule || "",
        duration: activity.duration || "",
        price_info: activity.price_info || {},
        location: activity.location || "",
        status: activity.status || "active",
      };

      // Force images and videos to be arrays in the database
      if (activityData.images.length === 0) {
        activityData.images = [];
      }

      if (activityData.videos.length === 0) {
        activityData.videos = [];
      }

      // Send request to server
      const url = isEdit
        ? `/admin/activities/${activity.id}`
        : "/admin/activities";
      const method = isEdit ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(activityData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to save activity");
      }

      // Update activity ID if this was a new activity
      if (!isEdit && result.data?.id) {
        activity.id = result.data.id;
      }

      success = true;

      // Dispatch saved event
      const savedEvent = new CustomEvent("saved", {
        detail: { id: activity.id },
      });
      document.dispatchEvent(savedEvent);
    } catch (err) {
      console.error("Error saving activity:", err);
      error = err instanceof Error ? err.message : "An error occurred";
    } finally {
      loading = false;
    }
  }

  // Event handler for media changes
  function handleMediaChange(event: Event) {
    const customEvent = event as CustomEvent;
    const { items, type } = customEvent.detail;

    console.log(`ActivityForm: Received media-change-${type} event`, items);

    if (type === "image") {
      activity.images = [...items];
    } else if (type === "video") {
      activity.videos = [...items];
    }
  }

  onMount(() => {
    document.addEventListener("media-change-image", handleMediaChange);
    document.addEventListener("media-change-video", handleMediaChange);
  });

  onDestroy(() => {
    document.removeEventListener("media-change-image", handleMediaChange);
    document.removeEventListener("media-change-video", handleMediaChange);
  });
</script>

<div class="bg-white shadow p-6 rounded-lg">
  <h2 class="mb-6 font-semibold text-gray-900 text-xl">
    {isEdit ? "Edit Activity" : "Add New Activity"}
  </h2>

  {#if error}
    <div class="bg-red-50 mb-6 p-4 border border-red-200 rounded-md">
      <p class="text-red-700">{error}</p>
    </div>
  {/if}

  {#if success}
    <div class="bg-green-50 mb-6 p-4 border border-green-200 rounded-md">
      <p class="text-green-700">
        Activity {isEdit ? "updated" : "created"} successfully!
      </p>
    </div>
  {/if}

  <form
    on:submit|preventDefault={handleSubmit}
    class="space-y-6"
  >
    <!-- Hidden site_id field -->
    <input type="hidden" id="site_id" value={activity.site_id} required />

    <!-- Status -->
    <div class="mb-6">
      <label for="status" class="block mb-1 font-medium text-gray-700 text-sm"
        >Status</label
      >
      <select
        id="status"
        bind:value={activity.status}
        class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
      >
        <option value="active">Active</option>
        <option value="inactive">Inactive</option>
        <option value="draft">Draft</option>
      </select>
    </div>

    <!-- Name -->
    <div>
      <label for="name" class="block mb-1 font-medium text-gray-700 text-sm"
        >Name</label
      >
      <input
        type="text"
        id="name"
        bind:value={activity.name}
        on:blur={generateSlug}
        class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        required
      />
    </div>

    <!-- Slug -->
    <div>
      <label for="slug" class="block mb-1 font-medium text-gray-700 text-sm"
        >Slug</label
      >
      <div class="flex">
        <input
          type="text"
          id="slug"
          bind:value={activity.slug}
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
          required
        />
        <button
          type="button"
          on:click={generateSlug}
          class="bg-gray-200 hover:bg-gray-300 ml-2 px-4 py-2 rounded-md"
        >
          Generate
        </button>
      </div>
      <p class="mt-1 text-gray-500 text-xs">
        Used in the URL: /activities/{activity.slug}
      </p>
    </div>

    <!-- Description -->
    <div>
      <label for="description" class="block mb-1 font-medium text-gray-700 text-sm"
        >Description</label
      >
      <textarea
        id="description"
        bind:value={activity.description}
        rows="4"
        class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
      ></textarea>
    </div>

    <!-- Schedule -->
    <div>
      <label for="schedule" class="block mb-1 font-medium text-gray-700 text-sm"
        >Schedule</label
      >
      <input
        type="text"
        id="schedule"
        bind:value={activity.schedule}
        class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        placeholder="e.g., Monday, Wednesday, Friday at 10:00 AM"
      />
    </div>

    <!-- Duration -->
    <div>
      <label for="duration" class="block mb-1 font-medium text-gray-700 text-sm"
        >Duration</label
      >
      <input
        type="text"
        id="duration"
        bind:value={activity.duration}
        class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        placeholder="e.g., 2 hours"
      />
    </div>

    <!-- Location -->
    <div>
      <label for="location" class="block mb-1 font-medium text-gray-700 text-sm"
        >Location</label
      >
      <input
        type="text"
        id="location"
        bind:value={activity.location}
        class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        placeholder="e.g., Beach, Garden, etc."
      />
    </div>

    <!-- Price -->
    <div>
      <label class="block mb-1 font-medium text-gray-700 text-sm"
        >Price</label
      >
      <div class="gap-4 grid grid-cols-2">
        <div>
          <input
            type="text"
            bind:value={priceAmount}
            placeholder="Amount"
            class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
          />
        </div>
        <div>
          <select
            bind:value={priceCurrency}
            class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
          >
            <option value="USD">USD</option>
            <option value="EUR">EUR</option>
            <option value="GBP">GBP</option>
            <option value="MAD">MAD</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Features -->
    <div>
      <label class="block mb-1 font-medium text-gray-700 text-sm"
        >Features</label
      >
      <div class="flex mb-2">
        <input
          type="text"
          bind:value={newFeature}
          placeholder="Add a feature"
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        />
        <button
          type="button"
          on:click={addFeature}
          class="bg-blue-600 hover:bg-blue-700 ml-2 px-4 py-2 rounded-md text-white"
        >
          <Plus class="w-5 h-5" />
        </button>
      </div>

      <div class="flex flex-wrap gap-2 mt-2">
        {#each activity.features as feature, index}
          <div
            class="flex items-center bg-green-50 px-3 py-1 rounded-full text-green-700"
          >
            <span>{feature}</span>
            <button
              type="button"
              on:click={() => removeFeature(index)}
              class="ml-2 text-green-500 hover:text-green-700"
            >
              <X class="w-4 h-4" />
            </button>
          </div>
        {/each}
      </div>
    </div>

    <!-- Images -->
    <div class="mb-6">
      {#if Array.isArray(activity.images)}
        <p class="mb-2 text-gray-500 text-xs">
          Images: {activity.images.length} items
        </p>
      {/if}
      <div id="image-media-manager">
        <MediaManager
          mediaItems={activity.images || []}
          type="image"
          bucket="activities"
          folder="images"
        />
      </div>
    </div>

    <!-- Videos -->
    <div class="mb-6">
      {#if Array.isArray(activity.videos)}
        <p class="mb-2 text-gray-500 text-xs">
          Videos: {activity.videos.length} items
        </p>
      {/if}
      <div id="video-media-manager">
        <MediaManager
          mediaItems={activity.videos || []}
          type="video"
          bucket="activities"
          folder="videos"
        />
      </div>
    </div>

    <!-- Submit Button -->
    <div class="flex justify-end">
      <button
        type="submit"
        class="flex items-center bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-md text-white"
        disabled={loading}
      >
        {#if loading}
          <div
            class="mr-2 border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin"
          ></div>
        {/if}
        {isEdit ? "Update Activity" : "Create Activity"}
      </button>
    </div>
  </form>
</div>
