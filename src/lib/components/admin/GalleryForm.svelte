<script lang="ts">
  import { currentSite } from "$lib/site/store";
  import SiteSelector from "./SiteSelector.svelte";
  import MediaManager from "$lib/components/ui/MediaManager.svelte";

  // Props
  let {
    gallery: initialGallery = {
      id: null,
      site_id: "",
      name: "",
      description: "",
      images: [],
      status: "active",
    },
    isEdit = false,
  } = $props();

  // Make gallery reactive
  let gallery = $state({ ...initialGallery });

  // Set site_id from currentSite when creating a new gallery
  $effect(() => {
    if (!isEdit && $currentSite) {
      console.log("Setting site_id from currentSite:", $currentSite.id);
      gallery.site_id = $currentSite.id;
    }
  });

  // State
  let loading = $state(false);
  let error = $state(null);
  let success = $state(false);

  // Custom event dispatcher
  function dispatchSavedEvent(id: string | number) {
    const event = new CustomEvent("saved", {
      detail: { id },
      bubbles: true,
    });
    document.dispatchEvent(event);
  }

  // Handle form submission
  async function handleSubmit() {
    try {
      loading = true;
      error = null;
      success = false;

      // Validate form
      if (!gallery.site_id) {
        throw new Error("Please select a property");
      }

      if (!gallery.name) {
        throw new Error("Gallery name is required");
      }

      // Prepare data
      const galleryData = {
        site_id: gallery.site_id,
        name: gallery.name,
        description: gallery.description || "",
        images: Array.isArray(gallery.images) ? gallery.images : [],
        status: gallery.status || "active",
      };

      // Log the data being sent
      console.log("Sending gallery data:", JSON.stringify(galleryData, null, 2));

      let galleryId;

      // Create or update gallery using server API for validation
      if (isEdit && gallery.id) {
        // Update existing gallery
        const response = await fetch("/admin/gallery", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...galleryData,
            id: gallery.id,
          }),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || "Failed to update gallery");
        }

        galleryId = result.data.id;
      } else {
        // Create new gallery
        console.log("Sending POST request to /admin/gallery");
        try {
          const response = await fetch("/admin/gallery", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(galleryData),
          });

          console.log("Response status:", response.status);

          let result;
          const responseText = await response.text();
          console.log("Response text:", responseText);

          try {
            result = JSON.parse(responseText);
            console.log("Response body:", result);
          } catch (parseError) {
            console.error("Error parsing JSON response:", parseError);
            throw new Error("Error parsing server response: " + responseText);
          }

          if (!response.ok) {
            console.error("Server returned error:", result);
            throw new Error(result.error || "Failed to create gallery");
          }

          if (!result.data || !result.data.id) {
            console.error("Missing data in response:", result);
            throw new Error("Invalid response from server: missing gallery ID");
          }

          galleryId = result.data.id;
          console.log("Gallery created with ID:", galleryId);
        } catch (fetchError) {
          console.error("Fetch error:", fetchError);
          throw fetchError;
        }
      }

      success = true;
      dispatchSavedEvent(galleryId);
    } catch (err: any) {
      console.error("Error saving gallery:", err);
      error = err.message || "Failed to save gallery";
    } finally {
      loading = false;
    }
  }

  // Handle media change
  function handleImagesChange(event: CustomEvent) {
    gallery.images = event.detail.items;
  }
</script>

<div class="bg-white shadow p-6 rounded-lg">
  {#if error}
    <div class="bg-red-50 mb-6 p-4 border border-red-200 rounded-md">
      <p class="text-red-700">{error}</p>
    </div>
  {/if}

  {#if success}
    <div class="bg-green-50 mb-6 p-4 border border-green-200 rounded-md">
      <p class="text-green-700">Gallery saved successfully!</p>
    </div>
  {/if}

  <form
    onsubmit={(e) => {
      e.preventDefault();
      handleSubmit();
    }}
  >
    <div class="gap-6 grid grid-cols-1 md:grid-cols-2 mb-6">
      <!-- Site Selection -->
      <div>
        <SiteSelector
          onSiteChange={(siteId: string) => {
            console.log("SiteSelector callback: setting site_id to", siteId);
            gallery.site_id = siteId;
          }}
        />
        <input type="hidden" id="site_id" value={gallery.site_id} required />
      </div>

      <!-- Status -->
      <div>
        <label for="status" class="block mb-1 font-medium text-gray-700 text-sm"
          >Status</label
        >
        <select
          id="status"
          bind:value={gallery.status}
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        >
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
          <option value="draft">Draft</option>
        </select>
      </div>
    </div>

    <!-- Name -->
    <div class="mb-6">
      <label for="name" class="block mb-1 font-medium text-gray-700 text-sm"
        >Name *</label
      >
      <input
        id="name"
        type="text"
        bind:value={gallery.name}
        placeholder="Summer Collection"
        class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        required
      />
    </div>

    <!-- Description -->
    <div class="mb-6">
      <label
        for="description"
        class="block mb-1 font-medium text-gray-700 text-sm">Description</label
      >
      <textarea
        id="description"
        bind:value={gallery.description}
        rows="4"
        placeholder="Describe the gallery..."
        class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
      ></textarea>
    </div>

    <!-- Images -->
    <div class="mb-6">
      <MediaManager
        mediaItems={gallery.images}
        type="image"
        bucket="galleries"
        folder="images"
        on:change={handleImagesChange}
      />
    </div>

    <!-- Submit Button -->
    <div class="flex justify-end">
      <button
        type="submit"
        class="flex items-center bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-md text-white"
        disabled={loading}
      >
        {#if loading}
          <div
            class="mr-2 border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin"
          ></div>
        {/if}
        {isEdit ? "Update Gallery" : "Create Gallery"}
      </button>
    </div>
  </form>
</div>
