<script lang="ts">
  import { Plus, X } from "lucide-svelte";
  import { currentSite } from "$lib/site/store";
  import SiteSelector from "./SiteSelector.svelte";
  import MediaManager from "$lib/components/ui/MediaManager.svelte";
  import { onMount, onDestroy } from "svelte";

  // Define types for Cloudbeds data
  interface CloudbedsRoomType {
    id?: string;
    roomTypeID?: string;
    name?: string;
    roomTypeName?: string;
    maxGuests?: number;
    photos?: Array<{ url: string }>;
    description?: string;
    amenities?: string[];
  }

  // Props
  let {
    suite: initialSuite = {
      id: null,
      site_id: "",
      name: "",
      slug: "",
      description: "",
      features: [],
      amenities: [],
      images: [],
      videos: [],
      size: "",
      capacity: "",
      price_info: {},
      status: "active",
    },
    isEdit = false,
  } = $props();

  // Make suite reactive and ensure arrays are initialized
  let suite = $state({
    id: initialSuite.id,
    site_id: initialSuite.site_id || "",
    name: initialSuite.name || "",
    slug: initialSuite.slug || "",
    description: initialSuite.description || "",
    size: initialSuite.size || "",
    capacity: initialSuite.capacity || "",
    price_info: initialSuite.price_info || {},
    status: initialSuite.status || "active",
    images: Array.isArray(initialSuite.images) ? [...initialSuite.images] : [],
    videos: Array.isArray(initialSuite.videos) ? [...initialSuite.videos] : [],
    features: Array.isArray(initialSuite.features)
      ? [...initialSuite.features]
      : [],
    amenities: Array.isArray(initialSuite.amenities)
      ? [...initialSuite.amenities]
      : [],
  });

  // Log suite data for debugging
  console.log("SuiteForm received suite data:", initialSuite);
  console.log("SuiteForm initialized suite with arrays:", suite);

  // Set site_id from currentSite when creating a new suite
  $effect(() => {
    if (!isEdit && $currentSite) {
      console.log("Setting site_id from currentSite:", $currentSite.id);
      suite.site_id = $currentSite.id;
    } else {
      console.log(
        "Not setting site_id. isEdit:",
        isEdit,
        "currentSite:",
        $currentSite
      );
    }
  });

  // Debug function to log the current state
  function logState() {
    console.log("SuiteForm state:", {
      isEdit,
      currentSite: $currentSite,
      siteId: suite.site_id,
      suiteId: suite.id,
    });
  }

  // Log state on mount
  $effect(() => {
    logState();
  });

  // State
  let loading = $state(false);
  let error = $state(null);
  let success = $state(false);
  let newFeature = $state("");
  let newAmenity = $state("");

  // Cloudbeds mapping
  let cloudbeds = $state({
    cloudbeds_room_type_id: "",
    cloudbeds_property_id: "",
    sync_images: false,
    sync_description: false,
    sync_amenities: false,
    sync_pricing: true,
    sync_availability: true,
  });

  let cloudbedsRoomTypes = $state<CloudbedsRoomType[]>([]);
  let loadingCloudbedsRoomTypes = $state(false);
  let selectedRoomTypeDetails = $state<CloudbedsRoomType | null>(null);

  // Custom event dispatcher
  function dispatchSavedEvent(id: string | number) {
    const event = new CustomEvent("saved", {
      detail: { id },
      bubbles: true,
    });
    document.dispatchEvent(event);
  }

  // Generate slug from name
  function generateSlug() {
    if (suite.name) {
      suite.slug = suite.name
        .toLowerCase()
        .replace(/[^\w\s-]/g, "")
        .replace(/[\s_-]+/g, "-")
        .replace(/^-+|-+$/g, "");
    }
  }

  // Add feature
  function addFeature() {
    if (newFeature.trim()) {
      suite.features = [...suite.features, newFeature.trim()];
      newFeature = "";
    }
  }

  // Remove feature
  function removeFeature(index: number) {
    suite.features = suite.features.filter((_: any, i: number) => i !== index);
  }

  // Add amenity
  function addAmenity() {
    if (newAmenity.trim()) {
      suite.amenities = [...suite.amenities, newAmenity.trim()];
      newAmenity = "";
    }
  }

  // Remove amenity
  function removeAmenity(index: number) {
    suite.amenities = suite.amenities.filter(
      (_: any, i: number) => i !== index
    );
  }

  // Load Cloudbeds room types
  let roomTypesLoaded = $state(false);
  async function loadCloudbedsRoomTypes() {
    // Prevent multiple calls
    if (roomTypesLoaded || loadingCloudbedsRoomTypes) {
      return;
    }

    try {
      loadingCloudbedsRoomTypes = true;

      const response = await fetch(
        "/api/cloudbeds/room-types?includePhotos=1&includeAmenities=1"
      );

      if (!response.ok) {
        throw new Error(
          `Failed to load Cloudbeds room types: ${response.statusText}`
        );
      }

      const result = await response.json();

      if (result.success && result.data) {
        cloudbedsRoomTypes = result.data;
        roomTypesLoaded = true;

        // If we have a selected room type ID, find its details
        if (cloudbeds.cloudbeds_room_type_id) {
          updateSelectedRoomTypeDetails(cloudbeds.cloudbeds_room_type_id);
        }
      } else {
        throw new Error(
          result.error?.message || "Failed to load Cloudbeds room types"
        );
      }
    } catch (err: any) {
      console.error("Error loading Cloudbeds room types:", err);
      error = err.message || "Failed to load Cloudbeds room types";
    } finally {
      loadingCloudbedsRoomTypes = false;
    }
  }

  // Update selected room type details
  function updateSelectedRoomTypeDetails(roomTypeId: string) {
    if (!roomTypeId || !cloudbedsRoomTypes.length) {
      selectedRoomTypeDetails = null;
      return;
    }

    const roomType = cloudbedsRoomTypes.find(
      (rt: any) => (rt.roomTypeID || rt.id) === roomTypeId
    );

    if (roomType) {
      selectedRoomTypeDetails = roomType;
    } else {
      selectedRoomTypeDetails = null;
    }
  }

  // Handle form submission
  async function handleSubmit() {
    try {
      loading = true;
      error = null;
      success = false;

      // Validate form
      if (!suite.site_id) {
        throw new Error("Please select a property");
      }

      if (!suite.name) {
        throw new Error("Suite name is required");
      }

      if (!suite.slug) {
        throw new Error("Suite slug is required");
      }

      // Prepare data
      const suiteData = {
        site_id: suite.site_id,
        name: suite.name,
        slug: suite.slug,
        description: suite.description || "",
        features: Array.isArray(suite.features) ? suite.features : [],
        amenities: Array.isArray(suite.amenities) ? suite.amenities : [],
        images: Array.isArray(suite.images) ? suite.images : [],
        videos: Array.isArray(suite.videos) ? suite.videos : [],
        size: suite.size || "",
        capacity: suite.capacity || "",
        price_info: suite.price_info || {},
        status: suite.status || "active",
      };

      // Force images and videos to be arrays in the database
      if (suiteData.images.length === 0) {
        suiteData.images = [];
      }

      if (suiteData.videos.length === 0) {
        suiteData.videos = [];
      }

      // Log the data types for debugging
      console.log("Suite data types:", {
        images: Array.isArray(suiteData.images)
          ? "array"
          : typeof suiteData.images,
        videos: Array.isArray(suiteData.videos)
          ? "array"
          : typeof suiteData.videos,
        imagesLength: Array.isArray(suiteData.images)
          ? suiteData.images.length
          : "N/A",
        videosLength: Array.isArray(suiteData.videos)
          ? suiteData.videos.length
          : "N/A",
      });

      // Log the data being sent
      console.log("Sending suite data:", JSON.stringify(suiteData, null, 2));

      // Debug current site context
      console.log("Current site context:", {
        currentSite: $currentSite,
        siteId: suite.site_id,
        isEdit,
      });

      let suiteId;

      // Create or update suite using server API for validation
      if (isEdit && suite.id) {
        // Update existing suite
        const response = await fetch("/admin/suites", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...suiteData,
            id: suite.id,
          }),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || "Failed to update suite");
        }

        suiteId = result.data.id;
      } else {
        // Create new suite
        console.log("Sending POST request to /admin/suites");
        try {
          const response = await fetch("/admin/suites", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(suiteData),
          });

          console.log("Response status:", response.status);
          console.log(
            "Response headers:",
            Object.fromEntries([...response.headers.entries()])
          );

          let result;
          const responseText = await response.text();
          console.log("Response text:", responseText);

          try {
            result = JSON.parse(responseText);
            console.log("Response body:", result);
          } catch (parseError) {
            console.error("Error parsing JSON response:", parseError);
            throw new Error("Error parsing server response: " + responseText);
          }

          if (!response.ok) {
            console.error("Server returned error:", result);
            throw new Error(result.error || "Failed to create suite");
          }

          if (!result.data || !result.data.id) {
            console.error("Missing data in response:", result);
            throw new Error("Invalid response from server: missing suite ID");
          }

          suiteId = result.data.id;
          console.log("Suite created with ID:", suiteId);
        } catch (fetchError) {
          console.error("Fetch error:", fetchError);
          throw fetchError;
        }
      }

      // Handle Cloudbeds mapping if room type ID is provided
      if (cloudbeds.cloudbeds_room_type_id) {
        const mappingData = {
          suite_id: suiteId,
          cloudbeds_room_type_id: cloudbeds.cloudbeds_room_type_id,
          cloudbeds_property_id: cloudbeds.cloudbeds_property_id || "",
          sync_images: cloudbeds.sync_images,
          sync_description: cloudbeds.sync_description,
          sync_amenities: cloudbeds.sync_amenities,
          sync_pricing: cloudbeds.sync_pricing,
          sync_availability: cloudbeds.sync_availability,
        };

        try {
          // Use fetch API to handle Cloudbeds mapping
          const response = await fetch(`/api/cloudbeds/mappings/${suiteId}`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(mappingData),
          });

          if (!response.ok) {
            const result = await response.json();
            console.warn("Warning: Cloudbeds mapping failed:", result.error);
            // Don't throw error here, just log a warning
          }
        } catch (mappingErr) {
          console.warn("Warning: Cloudbeds mapping failed:", mappingErr);
          // Don't throw error here, just log a warning
        }
      }

      success = true;
      dispatchSavedEvent(suiteId);
    } catch (err: any) {
      console.error("Error saving suite:", err);
      error = err.message || "Failed to save suite";
    } finally {
      loading = false;
    }
  }

  // Load Cloudbeds mapping if editing
  async function loadCloudbedsMapping() {
    if (isEdit && suite.id) {
      try {
        // Use fetch API to get Cloudbeds mapping
        const response = await fetch(`/api/cloudbeds/mappings/${suite.id}`);

        if (response.ok) {
          const result = await response.json();

          if (result.data) {
            cloudbeds = {
              cloudbeds_room_type_id: result.data.cloudbeds_room_type_id,
              cloudbeds_property_id: result.data.cloudbeds_property_id || "",
              sync_images: result.data.sync_images,
              sync_description: result.data.sync_description,
              sync_amenities: result.data.sync_amenities,
              sync_pricing: result.data.sync_pricing,
              sync_availability: result.data.sync_availability,
            };
          }
        }

        // Auto-load Cloudbeds room types after loading mapping
        await loadCloudbedsRoomTypes();
      } catch (err) {
        console.error("Error loading Cloudbeds mapping:", err);
        // Don't throw error, just log it

        // Still try to load room types even if mapping fails
        await loadCloudbedsRoomTypes();
      }
    } else {
      // If not editing, still load room types
      await loadCloudbedsRoomTypes();
    }
  }

  // Set property ID from current site - only called once
  let propertyIdSet = $state(false);
  function setPropertyIdFromCurrentSite() {
    if (propertyIdSet) return; // Only set once

    if ($currentSite && $currentSite.id) {
      // If we don't have a property ID yet, set it from the current site
      if (!cloudbeds.cloudbeds_property_id) {
        cloudbeds.cloudbeds_property_id = $currentSite.id;
        console.log(
          "Setting Cloudbeds property ID from current site:",
          $currentSite.id
        );
        propertyIdSet = true;
      }
    }
  }

  // Initialize - run only once on mount
  let initialized = $state(false);
  $effect(() => {
    if (!initialized) {
      initialized = true;

      // Set property ID from current site first
      setPropertyIdFromCurrentSite();

      if (isEdit && suite.id) {
        loadCloudbedsMapping();
      } else {
        // For new suites, just load room types
        loadCloudbedsRoomTypes();
      }

      // Add event listeners directly to the document
      console.log("Adding event listeners for media changes");
      // document.addEventListener("media-change-image", handleMediaChange); // This line should remain commented or removed
      // document.addEventListener("media-change-video", handleMediaChange); // This line should remain commented or removed

      // Add a global event listener for debugging
      document.addEventListener("media-change", (event: Event) => {
        const customEvent = event as CustomEvent<{
          items: string[];
          type: string;
        }>;
        console.log("Global media-change event received:", customEvent.detail);
      });
    }
  });

  // Event handler for media changes
  function handleMediaChange(event: Event) {
    const customEvent = event as CustomEvent;
    const { items, type } = customEvent.detail;

    console.log(`SuiteForm: Received media-change-${type} event`, items);

    if (type === "image") {
      suite.images = [...items];
      console.log("SuiteForm: Updated suite.images", suite.images);
    } else if (type === "video") {
      suite.videos = [...items];
      console.log("SuiteForm: Updated suite.videos", suite.videos);
    }
  }

  onMount(() => {
    console.log(
      "SuiteForm: Mounting and adding event listeners for media changes."
    );
    document.addEventListener("media-change-image", handleMediaChange); // This line should be active
    document.addEventListener("media-change-video", handleMediaChange); // This line should be active

    // Also listen to the generic media-change for debugging if needed
    // document.addEventListener("media-change", handleMediaChange);
  });

  onDestroy(() => {
    console.log(
      "SuiteForm: Unmounting and removing event listeners for media changes."
    );
    document.removeEventListener("media-change-image", handleMediaChange); // This line should be active
    document.removeEventListener("media-change-video", handleMediaChange); // This line should be active
    // document.removeEventListener("media-change", handleMediaChange);
  });

  // Update selected room type when selection changes
  $effect(() => {
    if (cloudbeds.cloudbeds_room_type_id) {
      updateSelectedRoomTypeDetails(cloudbeds.cloudbeds_room_type_id);
    } else {
      selectedRoomTypeDetails = null;
    }
  });
</script>

<div class="bg-white shadow p-6 rounded-lg">
  {#if error}
    <div class="bg-red-50 mb-6 p-4 border border-red-200 rounded-md">
      <p class="text-red-700">{error}</p>
    </div>
  {/if}

  {#if success}
    <div class="bg-green-50 mb-6 p-4 border border-green-200 rounded-md">
      <p class="text-green-700">Suite saved successfully!</p>
    </div>
  {/if}

  <form
    onsubmit={(e) => {
      e.preventDefault();
      handleSubmit();
    }}
  >
    <div class="gap-6 grid grid-cols-1 md:grid-cols-2 mb-6">
      <!-- Site Selection -->
      <div>
        <SiteSelector
          onSiteChange={(siteId: string) => {
            console.log("SiteSelector callback: setting site_id to", siteId);
            suite.site_id = siteId;
          }}
        />
        <input type="hidden" id="site_id" value={suite.site_id} required />
      </div>

      <!-- Status -->
      <div>
        <label for="status" class="block mb-1 font-medium text-gray-700 text-sm"
          >Status</label
        >
        <select
          id="status"
          bind:value={suite.status}
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        >
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
          <option value="draft">Draft</option>
        </select>
      </div>
    </div>

    <div class="gap-6 grid grid-cols-1 md:grid-cols-2 mb-6">
      <!-- Name -->
      <div>
        <label for="name" class="block mb-1 font-medium text-gray-700 text-sm"
          >Name *</label
        >
        <input
          id="name"
          type="text"
          bind:value={suite.name}
          onblur={generateSlug}
          placeholder="Ocean View Deluxe"
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
          required
        />
      </div>

      <!-- Slug -->
      <div>
        <label for="slug" class="block mb-1 font-medium text-gray-700 text-sm"
          >Slug *</label
        >
        <input
          id="slug"
          type="text"
          bind:value={suite.slug}
          placeholder="ocean-view-deluxe"
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
          required
        />
      </div>
    </div>

    <div class="gap-6 grid grid-cols-1 md:grid-cols-2 mb-6">
      <!-- Size -->
      <div>
        <label for="size" class="block mb-1 font-medium text-gray-700 text-sm"
          >Size</label
        >
        <input
          id="size"
          type="text"
          bind:value={suite.size}
          placeholder="45 m²"
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        />
      </div>
    </div>

    <!-- Description -->
    <div class="mb-6">
      <label
        for="description"
        class="block mb-1 font-medium text-gray-700 text-sm">Description</label
      >
      <textarea
        id="description"
        bind:value={suite.description}
        rows="4"
        placeholder="Describe the suite..."
        class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
      ></textarea>
    </div>

    <!-- Features -->
    <div class="mb-6">
      <div class="block mb-1 font-medium text-gray-700 text-sm">Features</div>
      <div class="flex">
        <input
          type="text"
          bind:value={newFeature}
          placeholder="Add a feature (e.g., King-size bed)"
          class="flex-1 px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-l-md focus:ring-blue-500"
        />
        <button
          type="button"
          onclick={addFeature}
          class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-r-md text-white"
        >
          <Plus class="w-5 h-5" />
        </button>
      </div>

      <div class="flex flex-wrap gap-2 mt-2">
        {#each suite.features as feature, index}
          <div
            class="flex items-center bg-blue-50 px-3 py-1 rounded-full text-blue-700"
          >
            <span>{feature}</span>
            <button
              type="button"
              onclick={() => removeFeature(index)}
              class="ml-2 text-blue-500 hover:text-blue-700"
            >
              <X class="w-4 h-4" />
            </button>
          </div>
        {/each}
      </div>
    </div>

    <!-- Amenities -->
    <div class="mb-6">
      <div class="block mb-1 font-medium text-gray-700 text-sm">Amenities</div>
      <div class="flex">
        <input
          type="text"
          bind:value={newAmenity}
          placeholder="Add an amenity (e.g., Free Wi-Fi)"
          class="flex-1 px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-l-md focus:ring-blue-500"
        />
        <button
          type="button"
          onclick={addAmenity}
          class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-r-md text-white"
        >
          <Plus class="w-5 h-5" />
        </button>
      </div>

      <div class="flex flex-wrap gap-2 mt-2">
        {#each suite.amenities as amenity, index}
          <div
            class="flex items-center bg-green-50 px-3 py-1 rounded-full text-green-700"
          >
            <span>{amenity}</span>
            <button
              type="button"
              onclick={() => removeAmenity(index)}
              class="ml-2 text-green-500 hover:text-green-700"
            >
              <X class="w-4 h-4" />
            </button>
          </div>
        {/each}
      </div>
    </div>

    <!-- Images -->
    <div class="mb-6">
      {#if Array.isArray(suite.images)}
        <p class="mb-2 text-gray-500 text-xs">
          Images: {suite.images.length} items
        </p>
      {/if}
      <div id="image-media-manager">
        <MediaManager
          mediaItems={suite.images || []}
          type="image"
          bucket="suites"
          folder="images"
        />
      </div>
    </div>

    <!-- Videos -->
    <div class="mb-6">
      {#if Array.isArray(suite.videos)}
        <p class="mb-2 text-gray-500 text-xs">
          Videos: {suite.videos.length} items
        </p>
      {/if}
      <div id="video-media-manager">
        <MediaManager
          mediaItems={suite.videos || []}
          type="video"
          bucket="suites"
          folder="videos"
        />
      </div>
    </div>

    <!-- Cloudbeds Integration -->
    <div class="mb-6 pt-6 border-t">
      <h3 class="mb-4 font-medium text-gray-900 text-lg">
        Cloudbeds Integration
      </h3>

      {#if loadingCloudbedsRoomTypes}
        <div class="p-4 text-center">
          <div
            class="inline-block border-4 border-gray-300 border-t-blue-600 rounded-full w-8 h-8 animate-spin"
          ></div>
          <p class="mt-2 text-gray-600">Loading Cloudbeds room types...</p>
        </div>
      {:else}
        <div class="mb-6">
          <!-- Room Type Selection -->
          <div>
            <label
              for="cloudbeds_room_type_id"
              class="block mb-1 font-medium text-gray-700 text-sm"
            >
              Cloudbeds Room Type
            </label>
            <select
              id="cloudbeds_room_type_id"
              bind:value={cloudbeds.cloudbeds_room_type_id}
              class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
            >
              <option value="">Select a room type</option>
              {#each cloudbedsRoomTypes as roomType}
                <option value={roomType.roomTypeID || roomType.id}>
                  {roomType.roomTypeName ||
                    roomType.name ||
                    "Unnamed Room Type"}
                </option>
              {/each}
            </select>

            {#if cloudbedsRoomTypes.length === 0}
              <div class="mt-2 text-amber-600 text-sm">
                <button
                  type="button"
                  onclick={() => {
                    roomTypesLoaded = false;
                    loadCloudbedsRoomTypes();
                  }}
                  class="text-blue-600 hover:text-blue-800 underline"
                >
                  Reload room types
                </button>
                if no rooms appear.
              </div>
            {/if}
          </div>
        </div>

        <!-- Selected Room Details -->
        {#if selectedRoomTypeDetails}
          <div class="bg-blue-50 mb-6 p-4 rounded-md">
            <h4 class="mb-2 font-medium text-blue-800 text-sm">
              Selected Room Details
            </h4>
            <div class="gap-4 grid grid-cols-1 md:grid-cols-2">
              <div>
                <p class="text-blue-700 text-sm">
                  <strong>Name:</strong>
                  {selectedRoomTypeDetails.roomTypeName ||
                    selectedRoomTypeDetails.name ||
                    "N/A"}
                </p>
                <p class="text-blue-700 text-sm">
                  <strong>ID:</strong>
                  {selectedRoomTypeDetails.roomTypeID ||
                    selectedRoomTypeDetails.id ||
                    "N/A"}
                </p>
                {#if selectedRoomTypeDetails.maxGuests}
                  <p class="text-blue-700 text-sm">
                    <strong>Max Guests:</strong>
                    {selectedRoomTypeDetails.maxGuests}
                  </p>
                {/if}
              </div>
              <div>
                {#if selectedRoomTypeDetails.photos && selectedRoomTypeDetails.photos.length > 0}
                  <div class="w-full h-24">
                    <img
                      src={selectedRoomTypeDetails.photos[0].url}
                      alt="Room preview"
                      class="rounded-md h-full object-cover"
                    />
                  </div>
                {/if}
              </div>
            </div>
          </div>
        {/if}

        <!-- Sync Options -->
        <div class="space-y-3 mb-6">
          <h4 class="mb-2 font-medium text-gray-700 text-sm">
            Synchronization Options
          </h4>

          <div class="flex items-center">
            <input
              id="sync_images"
              type="checkbox"
              bind:checked={cloudbeds.sync_images}
              class="border-gray-300 rounded focus:ring-blue-500 w-4 h-4 text-blue-600"
            />
            <label for="sync_images" class="block ml-2 text-gray-700 text-sm">
              Sync images from Cloudbeds
            </label>
          </div>

          <div class="flex items-center">
            <input
              id="sync_description"
              type="checkbox"
              bind:checked={cloudbeds.sync_description}
              class="border-gray-300 rounded focus:ring-blue-500 w-4 h-4 text-blue-600"
            />
            <label
              for="sync_description"
              class="block ml-2 text-gray-700 text-sm"
            >
              Sync description from Cloudbeds
            </label>
          </div>

          <div class="flex items-center">
            <input
              id="sync_amenities"
              type="checkbox"
              bind:checked={cloudbeds.sync_amenities}
              class="border-gray-300 rounded focus:ring-blue-500 w-4 h-4 text-blue-600"
            />
            <label
              for="sync_amenities"
              class="block ml-2 text-gray-700 text-sm"
            >
              Sync amenities from Cloudbeds
            </label>
          </div>

          <div class="flex items-center">
            <input
              id="sync_pricing"
              type="checkbox"
              bind:checked={cloudbeds.sync_pricing}
              class="border-gray-300 rounded focus:ring-blue-500 w-4 h-4 text-blue-600"
            />
            <label for="sync_pricing" class="block ml-2 text-gray-700 text-sm">
              Sync pricing from Cloudbeds
            </label>
          </div>

          <div class="flex items-center">
            <input
              id="sync_availability"
              type="checkbox"
              bind:checked={cloudbeds.sync_availability}
              class="border-gray-300 rounded focus:ring-blue-500 w-4 h-4 text-blue-600"
            />
            <label
              for="sync_availability"
              class="block ml-2 text-gray-700 text-sm"
            >
              Sync availability from Cloudbeds
            </label>
          </div>
        </div>
      {/if}
    </div>

    <!-- Submit Button -->
    <div class="flex justify-end">
      <button
        type="submit"
        class="flex items-center bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-md text-white"
        disabled={loading}
      >
        {#if loading}
          <div
            class="mr-2 border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin"
          ></div>
        {/if}
        {isEdit ? "Update Suite" : "Create Suite"}
      </button>
    </div>
  </form>
</div>
