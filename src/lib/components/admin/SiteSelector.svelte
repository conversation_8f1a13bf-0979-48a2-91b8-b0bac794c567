<script lang="ts">
  import {
    currentSite,
    availableSites,
    isLoading,
    siteContextStore,
  } from "$lib/site/store";
  import { Building } from "lucide-svelte";

  // Props
  let { showLabel = true, compact = false, onSiteChange = null, disabled = false } = $props();

  // Cuando cambia el sitio actual, notificar al componente padre si se proporcionó un callback
  $effect(() => {
    if (onSiteChange && $currentSite) {
      console.log("SiteSelector: currentSite changed to", $currentSite.id);
      onSiteChange($currentSite.id);
    } else if (onSiteChange && !$isLoading && $availableSites.length === 0) {
      // Si no hay sitios disponibles y no está cargando, notificar con un ID vacío
      console.log("SiteSelector: No sites available, notifying with empty ID");
      onSiteChange("");
    }
  });
</script>

<div class="site-selector {compact ? 'compact' : ''}">
  {#if showLabel}
    <label
      for="site-selector"
      class="block mb-1 font-medium text-gray-700 text-sm"
    >
      {$availableSites.length > 1 ? "Select Property" : ""}
    </label>
  {/if}

  {#if $isLoading}
    <div
      class="flex items-center bg-gray-50 px-4 py-2 border border-gray-300 rounded-md text-gray-500"
    >
      <div
        class="mr-2 border-2 border-gray-300 border-t-gray-600 rounded-full w-4 h-4 animate-spin"
      ></div>
      <span>Loading...</span>
    </div>
  {:else if $availableSites.length === 0}
    <div
      class="flex items-center bg-gray-50 px-4 py-2 border border-gray-300 rounded-md text-gray-500"
    >
      <Building class="mr-2 w-4 h-4" />
      <span>No properties available</span>
    </div>
  {:else if $availableSites.length === 1}
    <div
      class="flex items-center bg-gray-50 px-4 py-2 border border-gray-300 rounded-md"
    >
      <Building class="mr-2 w-4 h-4 text-primary-600" />
      <span class="font-medium"
        >{$currentSite?.name || $availableSites[0].name}</span
      >
    </div>
  {:else}
    <div class="relative">
      <select
        id="site-selector"
        value={$currentSite?.id || ""}
        onchange={(e) => siteContextStore.setCurrentSite(e.currentTarget.value)}
        class="py-2 pr-4 pl-10 border border-gray-300 focus:border-primary-500 rounded-md focus:ring-primary-500 w-full appearance-none"
      >
        {#each $availableSites as site}
          <option value={site.id}>{site.name}</option>
        {/each}
      </select>
      <div
        class="left-0 absolute inset-y-0 flex items-center pl-3 pointer-events-none"
      >
        <Building class="w-4 h-4 text-primary-600" />
      </div>
    </div>
  {/if}
</div>

<style>
  .site-selector.compact {
    display: inline-block;
    min-width: 200px;
  }
</style>
