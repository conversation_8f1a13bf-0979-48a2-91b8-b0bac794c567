<script lang="ts">
  import { onMount } from "svelte";
  import { supabase } from "$lib";
  import { goto } from "$app/navigation";
  import { Plus, Search, Edit, Trash, ExternalLink } from "lucide-svelte";
  import { currentSite } from "$lib/site/store";

  // Props
  let { showAddButton = true } = $props();

  // Activities data
  let activities = $state([]);
  let loading = $state(true);
  let error = $state(null);

  // Filter state
  let searchQuery = $state("");
  let selectedSite = $state("all");
  let sites = $state([]);

  // Pagination
  let currentPage = $state(1);
  let totalPages = $state(1);
  let pageSize = $state(10);

  // Load activities data
  async function loadActivities() {
    try {
      loading = true;
      error = null;

      // Calculate pagination
      const from = (currentPage - 1) * pageSize;
      const to = from + pageSize - 1;

      // Build query
      let query = supabase
        .from("activities_with_site_info")
        .select("*")
        .range(from, to);

      // Apply filters
      if (searchQuery) {
        query = query.or(
          `name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`
        );
      }

      if (selectedSite !== "all") {
        query = query.eq("site_id", selectedSite);
      }

      // Execute query
      const { data, error: fetchError, count } = await query;

      if (fetchError) throw fetchError;

      activities = data || [];

      // Calculate total pages
      if (count !== null) {
        totalPages = Math.ceil(count / pageSize);
      }
    } catch (err) {
      console.error("Error loading activities:", err);
      error = err.message || "Failed to load activities";
      activities = [];
    } finally {
      loading = false;
    }
  }

  // Load sites
  async function loadSites() {
    try {
      const { data, error: sitesError } = await supabase
        .from("sites")
        .select("id, name, domain")
        .order("name");

      if (sitesError) throw sitesError;

      sites = data || [];
    } catch (err) {
      console.error("Error loading sites:", err);
    }
  }

  // Handle page change
  function changePage(page: number) {
    if (page >= 1 && page <= totalPages) {
      currentPage = page;
      loadActivities();
    }
  }

  // Handle site filter change
  function handleSiteChange(event: Event) {
    const select = event.target as HTMLSelectElement;
    selectedSite = select.value;
    currentPage = 1;
    loadActivities();
  }

  // Handle search
  function handleSearch() {
    currentPage = 1;
    loadActivities();
  }

  // Edit activity
  function editActivity(id: string) {
    goto(`/admin/activities/${id}`);
  }

  // Create new activity
  function createActivity() {
    goto("/admin/activities/new");
  }

  // Delete activity
  async function deleteActivity(id: string, name: string) {
    if (
      !confirm(`Are you sure you want to delete the activity "${name}"? This action cannot be undone.`)
    ) {
      return;
    }

    try {
      const { error: deleteError } = await supabase
        .from("activities")
        .delete()
        .eq("id", id);

      if (deleteError) throw deleteError;

      // Reload the list
      loadActivities();
    } catch (err) {
      console.error("Error deleting activity:", err);
      alert(`Failed to delete activity: ${err.message}`);
    }
  }

  // Format price
  function formatPrice(priceInfo: any) {
    if (!priceInfo || !priceInfo.amount) return "Free";
    
    const amount = priceInfo.amount;
    const currency = priceInfo.currency || "USD";
    
    return `${amount} ${currency}`;
  }

  // Load data when component mounts or currentSite changes
  $effect(() => {
    loadSites();
    loadActivities();
  });

  // Update selectedSite when currentSite changes
  $effect(() => {
    if ($currentSite) {
      selectedSite = $currentSite.id;
      currentPage = 1;
      loadActivities();
    }
  });
</script>

<div>
  <div class="flex justify-between items-center mb-6">
    <h2 class="font-medium text-gray-900 text-xl">Activities</h2>
    {#if showAddButton}
      <button
        type="button"
        class="flex items-center bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-md text-white"
        on:click={createActivity}
      >
        <Plus class="mr-2 w-4 h-4" />
        Add Activity
      </button>
    {/if}
  </div>

  <!-- Filters -->
  <div class="bg-white mb-6 p-4 border border-gray-200 rounded-md">
    <div class="gap-4 grid grid-cols-1 md:grid-cols-3">
      <!-- Search -->
      <div>
        <label for="search" class="block mb-1 font-medium text-gray-700 text-sm"
          >Search</label
        >
        <div class="relative">
          <input
            type="text"
            id="search"
            bind:value={searchQuery}
            placeholder="Search activities..."
            class="pl-10 pr-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
          />
          <div
            class="left-0 absolute inset-y-0 flex items-center pl-3 pointer-events-none"
          >
            <Search class="w-4 h-4 text-gray-400" />
          </div>
        </div>
      </div>

      <!-- Site Filter -->
      <div>
        <label for="site-filter" class="block mb-1 font-medium text-gray-700 text-sm"
          >Property</label
        >
        <select
          id="site-filter"
          value={selectedSite}
          on:change={handleSiteChange}
          class="px-4 py-2 border border-gray-300 focus:border-blue-500 rounded-md focus:ring-blue-500 w-full"
        >
          <option value="all">All Properties</option>
          {#each sites as site}
            <option value={site.id}>{site.name}</option>
          {/each}
        </select>
      </div>

      <!-- Search Button -->
      <div class="flex items-end">
        <button
          type="button"
          on:click={handleSearch}
          class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md w-full"
        >
          Search
        </button>
      </div>
    </div>
  </div>

  <!-- Activities List -->
  <div class="bg-white p-4 border border-gray-200 rounded-md">
    {#if loading}
      <div class="p-8 text-center">
        <div
          class="inline-block border-4 border-gray-300 border-t-blue-600 rounded-full w-8 h-8 animate-spin"
        ></div>
        <p class="mt-2 text-gray-600">Loading activities...</p>
      </div>
    {:else if error}
      <div class="p-8 text-center">
        <p class="text-red-600">{error}</p>
        <button
          on:click={loadActivities}
          class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Try Again
        </button>
      </div>
    {:else if activities.length === 0}
      <div class="p-8 text-center">
        <p class="mb-4 text-gray-600">No activities found.</p>
        <button
          on:click={createActivity}
          class="mt-4 inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Add New Activity
        </button>
      </div>
    {:else}
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Name
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Site
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Schedule
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Price
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Status
            </th>
            <th
              class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          {#each activities as activity}
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">
                  {activity.name}
                </div>
                <div class="text-sm text-gray-500">{activity.slug}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{activity.site_name}</div>
                <div class="text-sm text-gray-500">{activity.site_domain}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{activity.schedule || "Not scheduled"}</div>
                <div class="text-sm text-gray-500">{activity.duration || ""}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{formatPrice(activity.price_info)}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full {activity.status ===
                  'active'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'}"
                >
                  {activity.status}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button
                    on:click={() => editActivity(activity.id)}
                    class="text-blue-600 hover:text-blue-900"
                    title="Edit Activity"
                  >
                    <Edit class="w-5 h-5" />
                  </button>

                  <a
                    href={`/activities/${activity.slug}`}
                    target="_blank"
                    class="text-gray-600 hover:text-gray-900"
                    title="View Activity"
                  >
                    <ExternalLink class="w-5 h-5" />
                  </a>

                  <button
                    on:click={() => deleteActivity(activity.id, activity.name)}
                    class="text-red-600 hover:text-red-900"
                    title="Delete Activity"
                  >
                    <Trash class="w-5 h-5" />
                  </button>
                </div>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>

      <!-- Pagination -->
      {#if totalPages > 1}
        <div class="flex justify-center items-center mt-6 space-x-2">
          <button
            on:click={() => changePage(currentPage - 1)}
            disabled={currentPage === 1}
            class="px-3 py-1 border border-gray-300 rounded-md {currentPage === 1
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-white text-gray-700 hover:bg-gray-50'}"
          >
            Previous
          </button>

          {#each Array(totalPages) as _, i}
            <button
              on:click={() => changePage(i + 1)}
              class="px-3 py-1 border border-gray-300 rounded-md {currentPage ===
              i + 1
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-700 hover:bg-gray-50'}"
            >
              {i + 1}
            </button>
          {/each}

          <button
            on:click={() => changePage(currentPage + 1)}
            disabled={currentPage === totalPages}
            class="px-3 py-1 border border-gray-300 rounded-md {currentPage ===
            totalPages
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-white text-gray-700 hover:bg-gray-50'}"
          >
            Next
          </button>
        </div>
      {/if}
    {/if}
  </div>
</div>
