<script lang="ts">
  import ImageGallery from "$lib/components/ui/ImageGallery.svelte";
  import { T, t } from "$lib/i18n";

  // Props
  let { activityData } = $props<{
    activityData: any;
  }>();

  // Ensure activity data is properly formatted
  let activity = $derived({
    id: activityData.id || null,
    name: activityData.name || "",
    slug: activityData.slug || "",
    description: activityData.description || "",
    features: Array.isArray(activityData.features) ? activityData.features : [],
    images: Array.isArray(activityData.images) ? activityData.images : [],
    videos: Array.isArray(activityData.videos) ? activityData.videos : [],
    schedule: activityData.schedule || "",
    duration: activityData.duration || "",
    price_info: activityData.price_info || {},
    location: activityData.location || "",
    status: activityData.status || "active",
  });

  // Format price
  function formatPrice(priceInfo: any) {
    if (!priceInfo || !priceInfo.amount) return "Free";
    
    const amount = priceInfo.amount;
    const currency = priceInfo.currency || "USD";
    
    return `${amount} ${currency}`;
  }
</script>

<div>
  <!-- Activity Header -->
  <div class="mb-8">
    <h1 class="font-montserrat font-light text-primary-900 text-3xl md:text-5xl uppercase tracking-wider">
      {activity.name}
    </h1>
    <div class="bg-primary-500 mt-3 w-24 h-0.5"></div>
  </div>

  <!-- Activity Images Gallery -->
  <div class="mb-10">
    <ImageGallery
      images={activity.images || []}
      videos={activity.videos || []}
      title={activity.name}
      aspectRatio="16/9"
    />
  </div>

  <!-- Description -->
  <div class="mb-8">
    <h2 class="mb-5 font-montserrat font-medium text-primary-900 text-2xl uppercase tracking-wider">
      <T key="activities.description" />
    </h2>
    <div class="mb-8 max-w-none prose">
      <p class="text-primary-800 leading-relaxed">{activity.description}</p>
    </div>
  </div>

  <!-- Details Card -->
  <div class="bg-white shadow-md mb-8 border border-primary-100 rounded-lg overflow-hidden">
    <div class="bg-primary-50 p-4 border-primary-100 border-b">
      <h3 class="font-montserrat font-medium text-primary-900 text-lg uppercase tracking-wider">
        <T key="activities.details" />
      </h3>
    </div>

    <div class="p-6">
      <!-- Schedule -->
      {#if activity.schedule}
        <div class="mb-4">
          <span class="font-montserrat font-medium text-primary-700">
            <T key="activities.schedule" />:
          </span>
          <span class="ml-2 font-eb-garamond text-primary-800">
            {activity.schedule}
          </span>
        </div>
      {/if}

      <!-- Duration -->
      {#if activity.duration}
        <div class="mb-4">
          <span class="font-montserrat font-medium text-primary-700">
            <T key="activities.duration" />:
          </span>
          <span class="ml-2 font-eb-garamond text-primary-800">
            {activity.duration}
          </span>
        </div>
      {/if}

      <!-- Location -->
      {#if activity.location}
        <div class="mb-4">
          <span class="font-montserrat font-medium text-primary-700">
            <T key="activities.location" />:
          </span>
          <span class="ml-2 font-eb-garamond text-primary-800">
            {activity.location}
          </span>
        </div>
      {/if}

      <!-- Price -->
      <div class="mb-4">
        <span class="font-montserrat font-medium text-primary-700">
          <T key="activities.price" />:
        </span>
        <span class="ml-2 font-eb-garamond text-primary-800">
          {formatPrice(activity.price_info)}
        </span>
      </div>
    </div>
  </div>

  <!-- Features -->
  {#if activity.features && activity.features.length > 0}
    <div class="mb-8">
      <h2 class="mb-5 font-montserrat font-medium text-primary-900 text-2xl uppercase tracking-wider">
        <T key="activities.features" />
      </h2>
      <div class="flex flex-wrap gap-3">
        {#each activity.features as feature}
          <span class="bg-primary-100 px-4 py-2 font-montserrat font-light text-primary-800 text-sm rounded-md">
            {feature}
          </span>
        {/each}
      </div>
    </div>
  {/if}
</div>
