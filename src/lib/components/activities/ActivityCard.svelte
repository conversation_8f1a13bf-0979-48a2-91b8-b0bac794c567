<script lang="ts">
  import { onMount } from "svelte";
  import SimpleImage from "$lib/components/ui/SimpleImage.svelte";
  import { T, t } from "$lib/i18n";

  // Props
  const {
    activity,
    compact = false,
  } = $props<{
    activity: {
      id: string;
      title: string;
      description?: string;
      image: string;
      url: string;
      features?: string[];
      schedule?: string;
      duration?: string;
      location?: string;
      price?: {
        amount: string;
        currency: string;
      };
      videos?: string[];
    };
    compact?: boolean;
  }>();

  // State
  let videoRef = $state<HTMLVideoElement | null>(null);
  let isHovering = $state(false);
  let hasVideo = $state(false);
  let videoUrl = $state("");

  // Check if there's a video available
  $effect(() => {
    hasVideo = Array.isArray(activity.videos) && activity.videos.length > 0;
    if (hasVideo) {
      videoUrl = activity.videos[0];
    }
  });

  // Handle mouse enter
  function handleMouseEnter() {
    if (hasVideo && videoRef) {
      isHovering = true;
      videoRef.currentTime = 0;
      videoRef.play().catch((error) => {
        console.error("Error playing video:", error);
      });
    }
  }

  // Handle mouse leave
  function handleMouseLeave() {
    if (hasVideo && videoRef) {
      isHovering = false;
      videoRef.pause();
    }
  }

  // Format price
  function formatPrice(price: any) {
    if (!price || !price.amount) return "Free";
    
    const amount = price.amount;
    const currency = price.currency || "USD";
    
    return `${amount} ${currency}`;
  }
</script>

<div
  class="flex flex-col bg-primary-50 border border-primary-100 overflow-hidden transition-shadow hover:shadow-md"
  on:mouseenter={handleMouseEnter}
  on:mouseleave={handleMouseLeave}
>
  <div class="relative aspect-ratio-container" style="--aspect-ratio:16/9">
    {#if hasVideo}
      <video
        bind:this={videoRef}
        src={videoUrl}
        class="absolute inset-0 w-full h-full object-cover {isHovering ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300"
        muted
        loop
        playsinline
        preload="metadata"
      >
        <track kind="captions" src="" label="English" />
      </video>
    {/if}
    <img
      src={activity.image}
      alt={activity.title}
      class="absolute inset-0 w-full h-full object-cover {isHovering && hasVideo ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300"
    />
  </div>

  <div class="flex flex-col flex-grow p-4 md:p-6">
    <h4
      class="mb-2 font-montserrat font-medium text-primary-900 text-lg md:text-xl"
    >
      {activity.title}
    </h4>

    {#if !compact && activity.description}
      <p class="mb-4 font-eb-garamond font-light text-primary-800">
        {activity.description}
      </p>
    {/if}

    {#if !compact && activity.features && activity.features.length > 0}
      <div class="flex flex-wrap gap-2 mb-4">
        {#each activity.features as feature}
          <span class="bg-primary-100 px-3 py-1 font-montserrat font-light text-primary-800 text-xs">
            {feature}
          </span>
        {/each}
      </div>
    {/if}

    {#if activity.schedule}
      <div class="mt-2 mb-2">
        <span class="font-montserrat font-medium text-primary-700 text-sm">
          <T key="activities.schedule" />: 
        </span>
        <span class="font-eb-garamond text-primary-800">
          {activity.schedule}
        </span>
      </div>
    {/if}

    {#if activity.duration}
      <div class="mb-2">
        <span class="font-montserrat font-medium text-primary-700 text-sm">
          <T key="activities.duration" />: 
        </span>
        <span class="font-eb-garamond text-primary-800">
          {activity.duration}
        </span>
      </div>
    {/if}

    {#if activity.price}
      <div class="mb-2">
        <span class="font-montserrat font-medium text-primary-700 text-sm">
          <T key="activities.price" />: 
        </span>
        <span class="font-eb-garamond text-primary-800">
          {formatPrice(activity.price)}
        </span>
      </div>
    {/if}

    <div class="mt-auto">
      <a
        href={activity.url}
        class="inline-block mt-4 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white font-montserrat text-sm uppercase tracking-wider transition-colors"
      >
        <T key="common.viewDetails" />
      </a>
    </div>
  </div>
</div>
