<script lang="ts">
  import ImageGallery from "$lib/components/ui/ImageGallery.svelte";
  import { T, t } from "$lib/i18n";

  // Props
  let { facilityData } = $props<{
    facilityData: any;
  }>();

  // Ensure facility data is properly formatted
  let facility = $derived({
    id: facilityData.id || null,
    name: facilityData.name || "",
    slug: facilityData.slug || "",
    description: facilityData.description || "",
    features: Array.isArray(facilityData.features) ? facilityData.features : [],
    images: Array.isArray(facilityData.images) ? facilityData.images : [],
    videos: Array.isArray(facilityData.videos) ? facilityData.videos : [],
    hours: facilityData.hours || "",
    restrictions: facilityData.restrictions || "",
    status: facilityData.status || "active",
  });
</script>

<div>
  <!-- Facility Header -->
  <div class="mb-8">
    <h1 class="font-montserrat font-light text-primary-900 text-3xl md:text-5xl uppercase tracking-wider">
      {facility.name}
    </h1>
    <div class="bg-primary-500 mt-3 w-24 h-0.5"></div>
  </div>

  <!-- Facility Images Gallery -->
  <div class="mb-10">
    <ImageGallery
      images={facility.images || []}
      videos={facility.videos || []}
      title={facility.name}
      aspectRatio="16/9"
    />
  </div>

  <!-- Description -->
  <div class="mb-8">
    <h2 class="mb-5 font-montserrat font-medium text-primary-900 text-2xl uppercase tracking-wider">
      <T key="facilities.description" />
    </h2>
    <div class="mb-8 max-w-none prose">
      <p class="text-primary-800 leading-relaxed">{facility.description}</p>
    </div>
  </div>

  <!-- Hours -->
  {#if facility.hours}
    <div class="mb-8">
      <h2 class="mb-5 font-montserrat font-medium text-primary-900 text-2xl uppercase tracking-wider">
        <T key="facilities.hours" />
      </h2>
      <div class="mb-8 max-w-none prose">
        <p class="text-primary-800 leading-relaxed">{facility.hours}</p>
      </div>
    </div>
  {/if}

  <!-- Restrictions -->
  {#if facility.restrictions}
    <div class="mb-8">
      <h2 class="mb-5 font-montserrat font-medium text-primary-900 text-2xl uppercase tracking-wider">
        <T key="facilities.restrictions" />
      </h2>
      <div class="mb-8 max-w-none prose">
        <p class="text-primary-800 leading-relaxed">{facility.restrictions}</p>
      </div>
    </div>
  {/if}

  <!-- Features -->
  {#if facility.features && facility.features.length > 0}
    <div class="mb-8">
      <h2 class="mb-5 font-montserrat font-medium text-primary-900 text-2xl uppercase tracking-wider">
        <T key="facilities.features" />
      </h2>
      <div class="flex flex-wrap gap-3">
        {#each facility.features as feature}
          <span class="bg-primary-100 px-4 py-2 font-montserrat font-light text-primary-800 text-sm rounded-md">
            {feature}
          </span>
        {/each}
      </div>
    </div>
  {/if}
</div>
