<script lang="ts">
  import { onMount, tick } from "svelte";
  import { fade } from "svelte/transition";
  import { cubicOut } from "svelte/easing";

  // Props
  const {
    images = [],
    videos = [],
    title = "",
    showThumbnails = true,
    aspectRatio = "16/9", // Now we'll use aspectRatio for consistent dimensions
  } = $props<{
    images: string[];
    videos?: string[];
    title?: string;
    showThumbnails?: boolean;
    aspectRatio?: string;
  }>();

  // State
  let currentIndex = $state(0);
  let thumbnailsVisible = $state(false);
  let allMedia = $state<Array<{ type: "image" | "video"; url: string }>>([]);
  let isTransitioning = $state(false);
  let touchStartX = $state(0);
  let touchEndX = $state(0);
  let preloadedImages = $state<Set<number>>(new Set());

  // Navigation functions
  async function nextImage() {
    if (allMedia.length > 0 && !isTransitioning) {
      isTransitioning = true;
      const nextIndex = (currentIndex + 1) % allMedia.length;

      // Preload the next image
      preloadImage(nextIndex);

      // Wait for the next tick to ensure the DOM is updated
      await tick();

      // Update the current index
      currentIndex = nextIndex;

      // Reset the transition state after animation completes
      setTimeout(() => {
        isTransitioning = false;
      }, 300);
    }
  }

  async function prevImage() {
    if (allMedia.length > 0 && !isTransitioning) {
      isTransitioning = true;
      const prevIndex = (currentIndex - 1 + allMedia.length) % allMedia.length;

      // Preload the previous image
      preloadImage(prevIndex);

      // Wait for the next tick to ensure the DOM is updated
      await tick();

      // Update the current index
      currentIndex = prevIndex;

      // Reset the transition state after animation completes
      setTimeout(() => {
        isTransitioning = false;
      }, 300);
    }
  }

  function goToImage(index: number) {
    if (index >= 0 && index < allMedia.length && !isTransitioning && index !== currentIndex) {
      isTransitioning = true;

      // Preload the selected image
      preloadImage(index);

      // Update the current index
      currentIndex = index;

      // Reset the transition state after animation completes
      setTimeout(() => {
        isTransitioning = false;

        // If we're showing thumbnails, make sure video thumbnails are initialized
        if (showThumbnails && allMedia.length > 1) {
          initVideoThumbnails();
        }
      }, 300);
    }
  }

  // Preload images for smoother transitions
  function preloadImage(index: number) {
    if (allMedia[index]?.type === 'image' && !preloadedImages.has(index)) {
      const img = new Image();
      img.src = allMedia[index].url;
      img.onload = () => {
        preloadedImages.add(index);
      };
    }
  }

  // Touch navigation
  function handleTouchStart(event: TouchEvent) {
    touchStartX = event.touches[0].clientX;
  }

  function handleTouchEnd(event: TouchEvent) {
    touchEndX = event.changedTouches[0].clientX;
    handleSwipe();
  }

  function handleSwipe() {
    const swipeThreshold = 50;
    const swipeDistance = touchEndX - touchStartX;

    if (swipeDistance > swipeThreshold) {
      // Swipe right - go to previous image
      prevImage();
    } else if (swipeDistance < -swipeThreshold) {
      // Swipe left - go to next image
      nextImage();
    }
  }

  // Keyboard navigation
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === "ArrowRight") {
      nextImage();
    } else if (event.key === "ArrowLeft") {
      prevImage();
    }
  }

  // Process media outside of reactive contexts
  function processMedia() {
    // Filtrar URLs vacías o inválidas
    const validImages = images.filter((url: string) => url && url.trim() !== "");
    const validVideos = videos.filter((url: string) => url && url.trim() !== "");

    return [
      // Put videos first, then images
      ...validVideos.map((url: string) => ({ type: "video" as const, url })),
      ...validImages.map((url: string) => ({ type: "image" as const, url })),
    ];
  }



  // Function to initialize video thumbnails
  function initVideoThumbnails() {
    // After a short delay to ensure DOM is ready
    setTimeout(() => {
      // Find all video elements in thumbnails
      const thumbnailVideos = document.querySelectorAll('.thumbnails-container video');

      // For each video, set currentTime to 0 to show the first frame
      thumbnailVideos.forEach((element) => {
        // Cast to HTMLVideoElement to access video-specific properties
        const video = element as HTMLVideoElement;
        // Set to a small value to ensure the first frame is loaded
        video.currentTime = 0.1;
      });
    }, 100);
  }

  // Inicialización y configuración en onMount
  onMount(() => {
    // Process media once on mount
    allMedia = processMedia();

    // Preload current and next images for smoother initial experience
    if (allMedia.length > 0) {
      preloadImage(0);
      if (allMedia.length > 1) {
        preloadImage(1);
      }
    }

    // Show thumbnails with animation
    setTimeout(() => {
      thumbnailsVisible = true;
      // Initialize video thumbnails after thumbnails are visible
      initVideoThumbnails();
    }, 300);

    // Add keyboard event listener
    window.addEventListener("keydown", handleKeydown);

    // Cleanup
    return () => {
      window.removeEventListener("keydown", handleKeydown);
    };
  });
</script>

<style>
  /* Estilos para la galería de imágenes */
  .image-gallery {
    /* Configuración de transiciones */
    --transition-duration: 300ms;
    --transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .carousel {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .carousel-nav {
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .carousel:hover .carousel-nav {
    opacity: 1;
  }

  .carousel-button {
    transform: scale(0.9);
    transition: transform 0.2s ease, background-color 0.2s ease;
  }

  .carousel-button:hover {
    transform: scale(1);
  }

  /* Estilos para el contador de imágenes */
  /* The media query previously here has been removed as the counter is now positioned consistently. */

  .thumbnails-container {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-300) transparent;
  }

  .thumbnails-container::-webkit-scrollbar {
    height: 6px;
  }

  .thumbnails-container::-webkit-scrollbar-track {
    background: transparent;
  }

  .thumbnails-container::-webkit-scrollbar-thumb {
    background-color: var(--primary-300);
    border-radius: 6px;
  }

  .thumbnail-item {
    cursor: pointer;
    transition: all 0.2s ease;
    overflow: hidden;
  }

  .thumbnail-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .thumbnail-item.active {
    border-color: var(--primary-500);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  /* Animaciones para las imágenes aplicadas a través de las transiciones de Svelte */
</style>

<div
  class="relative image-gallery"
  ontouchstart={handleTouchStart}
  ontouchend={handleTouchEnd}
>
  <!-- Main Image/Video Display -->
  <div
    class="relative shadow-lg rounded-lg aspect-ratio-container overflow-hidden carousel"
    style="--aspect-ratio: {aspectRatio}; padding-bottom: {aspectRatio === '16/9' ? '56.25%' : aspectRatio === '4/3' ? '75%' : aspectRatio === '1/1' ? '100%' : '56.25%'};"
  >
    {#if allMedia.length > 0}
      {#if allMedia[currentIndex].type === "image"}
        <div class="w-full h-full">
          {#key currentIndex}
            <img
              src={allMedia[currentIndex].url}
              alt={`${title} - Image ${currentIndex + 1}`}
              class="w-full h-full object-cover"
              loading="eager"
              fetchpriority="high"
              in:fade={{ duration: 300, easing: cubicOut }}
            />
          {/key}
        </div>
      {:else if allMedia[currentIndex].type === "video"}
        <div class="relative w-full h-full">
          {#key currentIndex}
            <video
              src={allMedia[currentIndex].url}
              class="w-full h-full object-cover"
              controls={false}
              muted
              loop
              playsinline
              in:fade={{ duration: 300, easing: cubicOut }}
            >
              <track kind="captions" src="" label="English" />
            </video>
            <!-- Custom video controls overlay -->
            <div class="absolute inset-0 flex justify-center items-center">
              <button
                class="flex justify-center items-center bg-black/40 hover:bg-black/60 rounded-full w-16 h-16 text-white hover:scale-110 transition-all duration-200 transform"
                aria-label="Play/Pause video"
                onclick={(e) => {
                  e.stopPropagation();
                  const target = e.currentTarget as HTMLButtonElement;
                  const videoElement = target.closest('.relative')?.querySelector('video') as HTMLVideoElement;

                  if (videoElement) {
                    if (videoElement.paused) {
                      videoElement.play();
                      target.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" fill="white" viewBox="0 0 24 24" class="w-8 h-8"><path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/></svg>';
                    } else {
                      videoElement.pause();
                      target.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" fill="white" viewBox="0 0 24 24" class="w-8 h-8"><path d="M8 5v14l11-7z"/></svg>';
                    }
                  }
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="white" viewBox="0 0 24 24" class="w-8 h-8">
                  <path d="M8 5v14l11-7z" />
                </svg>
              </button>
            </div>
          {/key}
        </div>
      {/if}

      <!-- Navigation Arrows -->
      <div class="top-1/2 z-10 absolute flex justify-between px-4 w-full -translate-y-1/2 transform carousel-nav">
        <button
          onclick={() => prevImage()}
          class="flex justify-center items-center bg-black/30 hover:bg-black/50 shadow-md rounded-full w-12 h-12 text-white transition-all carousel-button"
          aria-label="Previous image"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button
          onclick={() => nextImage()}
          class="flex justify-center items-center bg-black/30 hover:bg-black/50 shadow-md rounded-full w-12 h-12 text-white transition-all carousel-button"
          aria-label="Next image"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      <!-- Image Counter - Positioned at bottom-left with more spacing -->
      <div class="bottom-3 left-3 z-20 absolute">
        <div
          class="inline-flex justify-center items-center bg-neutral-900/90 shadow-sm backdrop-blur-sm px-3 py-1 rounded-md font-medium text-white text-xs leading-none whitespace-nowrap"
          in:fade={{ duration: 200, easing: cubicOut }}
        >
          <span class="mr-0.5 font-medium">{currentIndex + 1}</span>
          <span class="opacity-60">/ {allMedia.length}</span>
        </div>
      </div>
    {:else}
      <div class="flex justify-center items-center bg-primary-100 w-full h-full">
        <div class="p-6 text-center">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="mx-auto mb-2 w-12 h-12 text-primary-400">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
          </svg>
          <p class="text-primary-600">No images available</p>
        </div>
      </div>
    {/if}
  </div>

  <!-- Thumbnails -->
  {#if showThumbnails && allMedia.length > 1}
    <div
      class="mt-4 overflow-x-auto thumbnails-container"
      class:opacity-0={!thumbnailsVisible}
      class:opacity-100={thumbnailsVisible}
      style="transition: opacity 0.5s ease-in-out;"
    >
      <div class="flex gap-2 pb-2">
        {#each allMedia as media, index}
          <button
            onclick={() => goToImage(index)}
            class="flex-shrink-0 border-2 rounded-md w-20 h-20 overflow-hidden transition-all duration-200 thumbnail-item"
            class:border-primary-500={currentIndex === index}
            class:border-transparent={currentIndex !== index}
            class:opacity-70={currentIndex !== index}
            class:opacity-100={currentIndex === index}
            class:active={currentIndex === index}
            aria-label={`View image ${index + 1}`}
          >
            {#if media.type === "image"}
              <img src={media.url} alt={`Thumbnail ${index + 1}`} class="w-full h-full object-cover" />
            {:else if media.type === "video"}
              <div class="relative w-full h-full">
                <!-- Use the actual video as thumbnail -->
                <video
                  src={media.url}
                  class="w-full h-full object-cover"
                  preload="metadata"
                  muted
                  playsinline
                >
                  <track kind="captions" src="" label="English" />
                </video>
                <!-- Play button overlay to indicate it's a video -->
                <div class="absolute inset-0 flex justify-center items-center bg-black/20">
                  <div class="bg-black/50 p-1 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="white" viewBox="0 0 24 24" class="w-5 h-5">
                      <path d="M8 5v14l11-7z" />
                    </svg>
                  </div>
                </div>
              </div>
            {/if}
            {#if currentIndex === index}
              <div class="absolute inset-0 border-2 border-primary-500 rounded-sm pointer-events-none"></div>
            {/if}
          </button>
        {/each}
      </div>
    </div>
  {/if}
</div>
