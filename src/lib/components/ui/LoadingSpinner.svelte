<!-- LoadingSpinner.svelte -->
<script lang="ts">
  import { Loader2 } from "lucide-svelte";

  // Props opcionales
  export let size: string = "1.5rem";
  export let color: string = "currentColor";
</script>

<div class="flex justify-center items-center">
  <Loader2
    class="text-primary animate-spin"
    style="width: {size}; height: {size}; color: {color};"
  />
</div>

<style>
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style>
