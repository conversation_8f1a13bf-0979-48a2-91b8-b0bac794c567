<script lang="ts">
  // Props
  const {
    code,
    name,
    flag,
    selected = false,
  } = $props<{
    code: string;
    name: string;
    flag: string;
    selected?: boolean;
  }>();
</script>

<div class="flex items-center w-full {selected ? 'font-medium' : ''}">
  {#if flag}
    <img src={flag} alt={`${code} flag`} class="mr-2 rounded-sm w-4 h-4" />
  {/if}
  <span class="truncate">{name}</span>
</div>
