<script lang="ts">
  import { createEventDispatcher } from "svelte";
  import { supabase } from "$lib/supabase.client";
  import { X, Upload, Loader2 } from "lucide-svelte";

  // Props
  const {
    bucket = "media",
    folder = "",
    accept = "image/*",
    multiple = true,
    maxFileSize = 10 * 1024 * 1024, // 10MB default
    maxFiles = 20,
  } = $props<{
    bucket?: string;
    folder?: string;
    accept?: string;
    multiple?: boolean;
    maxFileSize?: number;
    maxFiles?: number;
  }>();

  // State
  let files = $state<File[]>([]);
  let uploading = $state(false);
  let uploadProgress = $state<Record<string, number>>({});
  let errors = $state<string[]>([]);
  let uploadedFiles = $state<
    Array<{
      path: string;
      url: string;
      name: string;
      size: number;
      type: string;
    }>
  >([]);

  // Event dispatcher
  const dispatch = createEventDispatcher<{
    uploaded: {
      files: Array<{
        path: string;
        url: string;
        name: string;
        size: number;
        type: string;
      }>;
    };
    error: { message: string };
  }>();

  // Handle file selection
  function handleFileSelect(event: Event) {
    console.log("FileUpload: handleFileSelect triggered", event);
    const input = event.target as HTMLInputElement;
    if (!input.files) {
      console.warn("FileUpload: No files selected in handleFileSelect");
      return;
    }

    // Clear previous errors
    errors = [];
    console.log("FileUpload: Cleared previous errors.");

    if (files.length + input.files.length > maxFiles) {
      const errorMsg =
        "You can only upload a maximum of " + maxFiles + " files.";
      console.warn(
        "FileUpload: Max files exceeded. Current:",
        files.length,
        "Trying to add:",
        input.files.length,
        "Max:",
        maxFiles
      );
      errors = [...errors, errorMsg];
      return;
    }

    console.log(
      "FileUpload: Processing " + input.files.length + " selected files."
    );
    for (let i = 0; i < input.files.length; i++) {
      const file = input.files[i];
      console.log(
        "FileUpload: Checking file '",
        file.name,
        "' (size: ",
        file.size,
        ")"
      );

      if (file.size > maxFileSize) {
        const errorMsg =
          "File '" +
          file.name +
          "' exceeds the maximum size of " +
          formatFileSize(maxFileSize) +
          ".";
        console.warn(
          "FileUpload: File size exceeded for '",
          file.name,
          "'. Size:",
          file.size,
          "Max:",
          maxFileSize
        );
        errors = [...errors, errorMsg];
        continue;
      }

      files = [...files, file];
      console.log(
        "FileUpload: Added '",
        file.name,
        "' to files list. Current files count:",
        files.length
      );
    }

    input.value = ""; // Reset the input
    console.log("FileUpload: File input reset.");

    // Automatically start upload if files were added and no errors occurred
    if (files.length > 0 && errors.length === 0) {
      console.log(
        "FileUpload: Automatically starting upload for",
        files.length,
        "files."
      );
      uploadFiles();
    }
  }

  // Remove a file from the list
  function removeFile(index: number) {
    const removedFile = files[index];
    files = files.filter((_, i) => i !== index);
    console.log(
      "FileUpload: Removed file '",
      removedFile?.name,
      "' at index ",
      index,
      ". Current files count:",
      files.length
    );
  }

  // Format file size for display
  function formatFileSize(bytes: number): string {
    if (bytes < 1024) return bytes + " bytes";
    else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + " KB";
    else if (bytes < 1024 * 1024 * 1024)
      return (bytes / (1024 * 1024)).toFixed(1) + " MB";
    return (bytes / (1024 * 1024 * 1024)).toFixed(1) + " GB";
  }

  // Upload files to Supabase Storage
  async function uploadFiles() {
    console.log(
      "FileUpload: uploadFiles triggered. Number of files to upload:",
      files.length
    );
    if (!supabase) {
      console.error("FileUpload: Supabase client is not initialized.");
      errors = [
        ...errors,
        "Supabase client is not initialized. Cannot upload files.",
      ];
      dispatch("error", { message: "Supabase client not initialized." });
      return;
    }
    if (files.length === 0) {
      console.warn("FileUpload: No files to upload.");
      return;
    }

    uploading = true;
    errors = [];
    uploadProgress = {};
    const currentUploadedFiles: typeof uploadedFiles = [];
    console.log(
      "FileUpload: Upload process started. uploading=true, errors cleared, progress reset."
    );

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileExt = file.name.split(".").pop();
        const fileName =
          Math.random().toString(36).substring(2, 15) +
          "_" +
          Date.now() +
          "." +
          fileExt;
        const filePath = folder ? folder + "/" + fileName : fileName;
        console.log(
          "FileUpload: Uploading file '",
          file.name,
          "' as '",
          fileName,
          "' to path '",
          filePath,
          "' in bucket '",
          bucket,
          "'."
        );

        uploadProgress[fileName] = 0;

        console.log(
          "FileUpload: Calling supabase.storage.from('",
          bucket,
          "').upload('",
          filePath,
          "', ...)"
        );
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from(bucket)
          .upload(filePath, file, {
            cacheControl: "3600",
            upsert: false,
            // @ts-expect-error - onUploadProgress is not in the type definition but works
            onUploadProgress: (event: { loaded: number; total: number }) => {
              if (event.total) {
                const progress = Math.round((event.loaded / event.total) * 100);
                uploadProgress[fileName] = progress;
                console.log(
                  `FileUpload: Upload progress for ${file.name} (${fileName}): ${progress}%`
                );
              }
            },
          });

        if (uploadError) {
          const errorMsg =
            "Error uploading '" + file.name + "': " + uploadError.message;
          console.error(
            "FileUpload: Supabase upload error for '",
            file.name,
            "'.",
            uploadError
          );
          errors = [...errors, errorMsg];
          continue;
        }

        console.log(
          "FileUpload: File '",
          file.name,
          "' uploaded successfully. Supabase response data:",
          uploadData
        );
        if (!uploadData || !uploadData.path) {
          console.error(
            "FileUpload: Supabase upload error for '",
            file.name,
            "'. Path missing in response.",
            uploadData
          );
          errors = [
            ...errors,
            "Upload failed for '" + file.name + "': Path missing in response.",
          ];
          continue;
        }

        console.log(
          "FileUpload: Getting public URL for '",
          filePath,
          "' from bucket '",
          bucket,
          "'."
        );
        const { data: urlData } = supabase.storage // Removed , error: urlError part here
          .from(bucket)
          .getPublicUrl(filePath);

        // Check for publicUrl directly as getPublicUrl doesn't return an error object in data for typical cases
        if (!urlData || !urlData.publicUrl) {
          const errorMsg =
            "Error getting public URL for '" +
            file.name +
            "': publicUrl is null or undefined.";
          console.error(
            "FileUpload:",
            errorMsg,
            "Attempted path:",
            filePath,
            "Received data:",
            urlData
          );
          errors = [...errors, errorMsg];
          continue;
        }

        console.log(
          "FileUpload: Public URL for '",
          file.name,
          "': '",
          urlData.publicUrl,
          "'"
        );

        currentUploadedFiles.push({
          path: uploadData.path,
          url: urlData.publicUrl,
          name: file.name,
          size: file.size,
          type: file.type,
        });
        console.log(
          "FileUpload: Added '",
          file.name,
          "' to currentUploadedFiles. Count:",
          currentUploadedFiles.length
        );
      }

      if (currentUploadedFiles.length > 0) {
        uploadedFiles = [...uploadedFiles, ...currentUploadedFiles];
        console.log(
          "FileUpload: Updated main uploadedFiles state:",
          uploadedFiles
        );
      }

      if (currentUploadedFiles.length > 0) {
        console.log(
          "FileUpload: Dispatching 'uploaded' event with files:",
          currentUploadedFiles
        );
        dispatch("uploaded", { files: currentUploadedFiles });
        console.log(
          "FileUpload: 'uploaded' event dispatched via Svelte dispatcher."
        );

        const customDocEvent = new CustomEvent("file-uploaded", {
          detail: { files: currentUploadedFiles },
          bubbles: true,
          composed: true,
        });
        document.dispatchEvent(customDocEvent);
        console.log(
          "FileUpload: Global 'file-uploaded' document event dispatched with files:",
          currentUploadedFiles
        );

        files = [];
        console.log(
          "FileUpload: Cleared selected files list after successful uploads."
        );
      } else {
        console.warn(
          "FileUpload: No files were successfully uploaded in this batch, 'uploaded' event not dispatched."
        );
      }
    } catch (error: any) {
      const errorMsg = "Upload failed: " + error.message;
      console.error(
        "FileUpload: General error in uploadFiles function.",
        error
      );
      errors = [...errors, errorMsg];
      dispatch("error", { message: errorMsg });
      console.log("FileUpload: Dispatched 'error' event.");
    } finally {
      uploading = false;
      console.log("FileUpload: Upload process finished. uploading=false.");
    }
  }

  // Reactive effect for individual file progress (if onUploadProgress is not used directly)
  // This is an alternative way if the callback in supabase.upload isn't ideal with Svelte 5 state
  // For now, this is commented out as the direct callback is generally fine.
  // $effect(() => {
  //   files.forEach(file => {
  //     if (uploading && uploadProgress[file.name] === undefined) {
  //       // Initialize progress if not set, though supabase callback should do it
  //       // uploadProgress[file.name] = 0;
  //     }
  //     // console.log(\`Progress for \${file.name}: \${uploadProgress[file.name] || 0}%\`);
  //   });
  // });
</script>

<div class="file-upload">
  {#if errors.length > 0}
    <div class="bg-red-50 mb-4 p-3 border border-red-200 rounded-md">
      {#each errors as error}
        <p class="text-red-700 text-sm">{error}</p>
      {/each}
    </div>
  {/if}

  <div
    class="bg-gray-50 hover:bg-blue-50 p-6 border-2 border-gray-300 hover:border-blue-500 border-dashed rounded-md text-center transition-colors"
  >
    <label class="block cursor-pointer">
      <input
        type="file"
        {accept}
        {multiple}
        class="hidden"
        onchange={handleFileSelect}
      />
      <Upload class="mx-auto mb-2 w-8 h-8 text-gray-400" />
      <p class="text-gray-700 text-sm">
        Drag and drop files here, or click to select
      </p>
      <p class="mt-1 text-gray-500 text-xs">
        Max file size: {formatFileSize(maxFileSize)}
      </p>
    </label>
  </div>

  {#if files.length > 0}
    <div class="mt-4">
      <div class="flex justify-between items-center mb-2">
        <h3 class="font-medium text-gray-700 text-sm">Selected Files</h3>
        <button
          type="button"
          class="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 px-4 py-2 rounded-md text-white text-sm disabled:cursor-not-allowed"
          onclick={uploadFiles}
          disabled={uploading}
        >
          {#if uploading}
            <Loader2 class="inline-block mr-1 w-4 h-4 animate-spin" />
            Uploading...
          {:else}
            Upload {files.length} {files.length === 1 ? "file" : "files"}
          {/if}
        </button>
      </div>

      <div class="space-y-2">
        {#each files as file, index}
          <div
            class="flex justify-between items-center bg-white p-3 border border-gray-200 rounded-md"
          >
            <div class="flex items-center space-x-3">
              {#if file.type.startsWith("image/")}
                <img
                  src={URL.createObjectURL(file)}
                  alt={file.name}
                  class="rounded w-10 h-10 object-cover"
                />
              {/if}
              <div class="flex-1 min-w-0">
                <p class="font-medium text-gray-900 text-sm truncate">
                  {file.name}
                </p>
                <p class="text-gray-500 text-xs">
                  {formatFileSize(file.size)}
                  {#if uploadProgress[file.name] !== undefined && uploading}
                    <span class="ml-2 text-blue-600"
                      >({uploadProgress[file.name]}%)</span
                    >
                  {/if}
                </p>
              </div>
            </div>
            {#if uploading && uploadProgress[file.name] !== undefined}
              <div class="w-20">
                <div class="bg-gray-200 rounded-full h-1.5">
                  <div
                    class="bg-blue-600 rounded-full h-1.5"
                    style="width: {uploadProgress[file.name]}%;"
                  ></div>
                </div>
              </div>
            {:else}
              <button
                type="button"
                class="p-1 text-gray-400 hover:text-red-500"
                onclick={() => removeFile(index)}
                aria-label="Remove file"
              >
                <X class="w-4 h-4" />
              </button>
            {/if}
          </div>
        {/each}
      </div>
    </div>
  {/if}
</div>
