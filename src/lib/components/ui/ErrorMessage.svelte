<!-- ErrorMessage.svelte -->
<script lang="ts">
  import { AlertCircle } from "lucide-svelte";

  // Props
  export let message: string;
  export let type: "error" | "warning" | "info" = "error";

  // Mapear tipo a clase de alerta de DaisyUI
  $: alertClass =
    type === "error"
      ? "alert-error"
      : type === "warning"
        ? "alert-warning"
        : "alert-info";
</script>

<div class="alert {alertClass} shadow-lg">
  <AlertCircle class="flex-shrink-0 w-6 h-6" />
  <span>{message}</span>
</div>

<style>
  .error-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border-radius: 0.375rem;
    margin: 1rem 0;
  }

  .error {
    background-color: #fee2e2;
    color: #b91c1c;
    border: 1px solid #fecaca;
  }

  .warning {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
  }

  .info {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
  }

  .icon {
    width: 1.25rem;
    height: 1.25rem;
    flex-shrink: 0;
  }
</style>
