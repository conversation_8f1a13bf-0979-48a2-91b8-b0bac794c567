# UI Components

This directory contains reusable UI components for the Baberrih website.

## Image Components

### OptimizedImage

`OptimizedImage` is the recommended component for displaying images in the Baberrih website. It provides advanced image optimization features including:

- Automatic detection of image source type (local or remote)
- Responsive image loading with srcset
- CDN-specific optimizations for Cloudinary, ImageKit, and Imgix
- Lazy loading for better performance
- Error handling with fallback options
- Consistent aspect ratio handling
- Support for all common image formats

#### Basic Usage

```svelte
<script>
  import OptimizedImage from '$lib/components/ui/OptimizedImage.svelte';
</script>

<OptimizedImage 
  src="/path/to/image.jpg" 
  alt="Description of image" 
/>
```

#### Advanced Usage

```svelte
<OptimizedImage 
  src="https://example.com/image.jpg" 
  alt="Remote image example"
  aspectRatio="16/9"
  loading="eager"
  fetchpriority="high"
  objectFit="cover"
  fallbackSrc="/fallback.jpg"
  widths={[640, 768, 1024, 1280, 1536]}
  quality={80}
/>
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `src` | `string` | (required) | The image source URL (local or remote) |
| `alt` | `string` | `""` | Alternative text for the image |
| `className` | `string` | `""` | Additional CSS classes to apply to the container |
| `loading` | `"lazy" \| "eager"` | `"lazy"` | Image loading strategy |
| `fetchpriority` | `"auto" \| "high" \| "low"` | `"auto"` | Fetch priority hint |
| `decoding` | `"async" \| "sync" \| "auto"` | `"async"` | Image decoding strategy |
| `sizes` | `string` | `"100vw"` | Sizes attribute for responsive images |
| `aspectRatio` | `string` | `"16/9"` | Aspect ratio (e.g., "16/9", "4/3", "1/1") |
| `objectFit` | `"cover" \| "contain" \| "fill" \| "none" \| "scale-down"` | `"cover"` | Object-fit property |
| `objectPosition` | `string` | `"center"` | Object-position property |
| `fallbackSrc` | `string` | `""` | Fallback image to use if the main image fails to load |
| `widths` | `number[]` | `[640, 768, 1024, 1280, 1536]` | Widths to use for srcset generation |
| `quality` | `number` | `80` | Image quality (0-100) for CDN-based optimization |
| `blur` | `boolean` | `true` | Whether to show a blur effect while loading |
| `placeholderColor` | `string` | `"#f0f0f0"` | Background color for the placeholder |

### SimpleImage (Legacy)

> **Note:** `SimpleImage` is now a compatibility wrapper around `OptimizedImage`. For new components, use `OptimizedImage` directly.

`SimpleImage` is maintained for backward compatibility and now uses `OptimizedImage` under the hood. It provides the same API as before but with all the benefits of `OptimizedImage`.

#### Usage

```svelte
<script>
  import SimpleImage from '$lib/components/ui/SimpleImage.svelte';
</script>

<SimpleImage 
  src="/path/to/image.jpg" 
  alt="Description of image" 
/>
```

## Best Practices for Images

1. **Always provide alt text** for accessibility
2. **Use appropriate loading strategies**:
   - For above-the-fold images: `loading="eager"` and `fetchpriority="high"`
   - For below-the-fold images: `loading="lazy"` (default)
3. **Specify aspect ratios** to prevent layout shifts
4. **Use appropriate image formats**:
   - WebP or AVIF for photos with transparency
   - JPEG for photos without transparency
   - PNG for graphics with transparency
   - SVG for vector graphics
5. **Optimize image dimensions** - don't use larger images than needed
6. **Consider mobile users** - use responsive images with appropriate sizes

## Demo

A demo of the `OptimizedImage` component is available at `/image-demo`.
