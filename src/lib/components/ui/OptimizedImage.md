# OptimizedImage Component

A comprehensive image optimization component for Svelte applications that handles both local and remote images with advanced features.

## Features

- **Source Type Detection**: Automatically detects if an image is local or remote
- **Responsive Images**: Generates appropriate `srcset` attributes for responsive loading
- **CDN Optimization**: Special handling for common image CDNs (Cloudinary, ImageKit, Imgix)
- **Lazy Loading**: Built-in support for lazy loading images
- **Error Handling**: Graceful fallback when images fail to load
- **Aspect Ratio Preservation**: Maintains image aspect ratios consistently
- **Loading States**: Visual feedback during image loading
- **TypeScript Support**: Full TypeScript interface for better developer experience

## Usage

```svelte
<script>
  import OptimizedImage from '$lib/components/ui/OptimizedImage.svelte';
</script>

<!-- Basic usage -->
<OptimizedImage 
  src="https://example.com/image.jpg" 
  alt="Example image" 
/>

<!-- With all options -->
<OptimizedImage 
  src="https://example.com/image.jpg" 
  alt="Example image"
  className="my-custom-class"
  loading="lazy"
  fetchpriority="high"
  decoding="async"
  sizes="(max-width: 768px) 100vw, 50vw"
  aspectRatio="16/9"
  objectFit="cover"
  objectPosition="center"
  fallbackSrc="/placeholder.jpg"
  widths={[640, 768, 1024, 1280, 1536]}
  quality={80}
  blur={true}
  placeholderColor="#f0f0f0"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `src` | `string` | (required) | The image source URL (local or remote) |
| `alt` | `string` | `""` | Alternative text for the image |
| `className` | `string` | `""` | Additional CSS classes to apply to the container |
| `loading` | `"lazy" \| "eager"` | `"lazy"` | Image loading strategy |
| `fetchpriority` | `"auto" \| "high" \| "low"` | `"auto"` | Fetch priority hint |
| `decoding` | `"async" \| "sync" \| "auto"` | `"async"` | Image decoding strategy |
| `sizes` | `string` | `"100vw"` | Sizes attribute for responsive images |
| `aspectRatio` | `string` | `"16/9"` | Aspect ratio (e.g., "16/9", "4/3", "1/1") |
| `objectFit` | `"cover" \| "contain" \| "fill" \| "none" \| "scale-down"` | `"cover"` | Object-fit property |
| `objectPosition` | `string` | `"center"` | Object-position property |
| `fallbackSrc` | `string` | `""` | Fallback image to use if the main image fails to load |
| `widths` | `number[]` | `[640, 768, 1024, 1280, 1536]` | Widths to use for srcset generation |
| `quality` | `number` | `80` | Image quality (0-100) for CDN-based optimization |
| `blur` | `boolean` | `true` | Whether to show a blur effect while loading |
| `placeholderColor` | `string` | `"#f0f0f0"` | Background color for the placeholder |

## Examples

### Basic Usage with Local Image

```svelte
<OptimizedImage 
  src="/images/local-image.jpg" 
  alt="Local image" 
/>
```

### Remote Image with Custom Aspect Ratio

```svelte
<OptimizedImage 
  src="https://example.com/image.jpg" 
  alt="Remote image"
  aspectRatio="4/3"
  loading="eager"
  fetchpriority="high"
/>
```

### With Error Fallback

```svelte
<OptimizedImage 
  src="https://example.com/might-fail.jpg" 
  alt="Image with fallback"
  fallbackSrc="/images/placeholder.jpg"
/>
```

### Responsive Image with Custom Sizes

```svelte
<OptimizedImage 
  src="https://example.com/responsive.jpg" 
  alt="Responsive image"
  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
  widths={[320, 640, 960, 1280, 1920]}
/>
```

## CDN Support

The component automatically detects and optimizes images from the following CDNs:

- **Cloudinary**: Automatically generates optimal transformation URLs
- **ImageKit**: Applies quality and width parameters
- **Imgix**: Applies quality and width parameters

For other remote images, the component will use the original URL without modifications.

## Performance Considerations

- Use appropriate `loading`, `fetchpriority`, and `decoding` values based on image importance
- For critical above-the-fold images, use `loading="eager"` and `fetchpriority="high"`
- For below-the-fold images, use `loading="lazy"` (default)
- Consider using smaller `widths` values for mobile-first approaches
- Adjust `quality` based on image content (lower for photos, higher for text/diagrams)
