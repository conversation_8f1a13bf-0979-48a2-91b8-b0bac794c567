# Image Optimization in Baberrih Web

This document explains the image optimization strategy used in the Baberrih Web project and provides guidance on handling image loading issues.

## Components

### OptimizedImage

The `OptimizedImage` component is a comprehensive solution for displaying images with optimizations:

- Automatically detects if an image is local or remote
- Generates appropriate `srcset` attributes for responsive loading
- Provides CDN-specific optimizations for Cloudinary, ImageKit, and Imgix
- Includes lazy loading for better performance
- Handles image loading errors with fallbacks
- Maintains consistent aspect ratios

#### Usage

```svelte
<OptimizedImage 
  src="/path/to/image.jpg" 
  alt="Description of image"
  aspectRatio="16/9"
  fallbackSrc="/images/placeholder.jpg"
/>
```

### SimpleImage (Legacy)

The `SimpleImage` component is maintained for backward compatibility and now uses `OptimizedImage` under the hood.

## Known Issues

### Domain Accessibility

Images from the `baberrih.ma` domain are not accessible in the current environment. This could be due to:

1. The domain no longer exists or has changed
2. CORS restrictions preventing access
3. Network connectivity issues to that specific domain

### Debugging Image Loading Issues

If you encounter image loading issues:

1. Check the browser console for errors
2. Use the `ImageErrorDebug` component to see detailed error information
3. Verify that the image URL is accessible by opening it directly in a browser
4. Test with different image sources to isolate the issue

```svelte
<!-- Add this to any page to debug image loading issues -->
<ImageErrorDebug position="bottom-right" showOnLoad={true} />
```

## Best Practices

### 1. Always Provide Fallbacks

Always provide a fallback image for critical images:

```svelte
<OptimizedImage 
  src="https://example.com/image.jpg" 
  alt="Example image"
  fallbackSrc="/images/placeholder.jpg"
/>
```

### 2. Use Local Images When Possible

Local images are more reliable than remote images:

```svelte
<OptimizedImage 
  src="/images/local-image.jpg" 
  alt="Local image"
/>
```

### 3. Optimize Image Dimensions

Don't use larger images than needed:

```svelte
<OptimizedImage 
  src="/images/hero.jpg" 
  alt="Hero image"
  widths={[640, 768, 1024, 1280, 1536]}
/>
```

### 4. Set Appropriate Loading Priorities

For above-the-fold images:

```svelte
<OptimizedImage 
  src="/images/hero.jpg" 
  alt="Hero image"
  loading="eager"
  fetchpriority="high"
/>
```

For below-the-fold images:

```svelte
<OptimizedImage 
  src="/images/content.jpg" 
  alt="Content image"
  loading="lazy"
/>
```

## Troubleshooting

### Images Not Loading

If images are not loading:

1. **Check the URL**: Verify that the image URL is correct and accessible
2. **Check for CORS issues**: If using remote images, ensure they allow CORS
3. **Use the ImageErrorDebug component**: Add it to the page to see detailed error information
4. **Check the network tab**: Look for 404 or other HTTP errors
5. **Try a different source**: Test with a known working image source

### Replacing baberrih.ma Images

For images from the inaccessible `baberrih.ma` domain:

1. Replace them with local images when possible
2. Use a reliable CDN like Unsplash, Cloudinary, or ImageKit
3. Ensure all images have appropriate fallbacks

Example replacement:

```svelte
<!-- Before -->
<OptimizedImage 
  src="https://baberrih.ma/media/hero/1_f.webp" 
  alt="Hero image"
/>

<!-- After -->
<OptimizedImage 
  src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4" 
  alt="Hero image"
  fallbackSrc="/images/placeholder.jpg"
/>
```

## Testing

A test page is available at `/image-test` to verify image loading from different sources.
