<script lang="ts">
  import { onMount } from "svelte";
  import { browser } from "$app/environment";

  /**
   * OptimizedImage Component
   *
   * A comprehensive image component that handles both local and remote images with optimizations:
   * - Automatically detects image source type (local or remote)
   * - Applies appropriate loading strategies based on source
   * - Supports responsive images with srcset
   * - Includes lazy loading for performance
   * - Handles image loading errors with fallbacks
   * - Maintains aspect ratios
   * - Supports common image formats
   */

  // Props
  const {
    src,
    alt = "",
    className = "",
    loading = "lazy",
    fetchpriority = "auto",
    decoding = "async",
    sizes = "100vw",
    aspectRatio = "16/9",
    objectFit = "cover",
    objectPosition = "center",
    fallbackSrc = "", // User-provided fallback image
    widths = [640, 768, 1024, 1280, 1536],
    quality = 80,
    blur = true,
    placeholderColor = "#f0f0f0",
  } = $props<{
    src: string;
    alt?: string;
    className?: string;
    loading?: "lazy" | "eager";
    fetchpriority?: "auto" | "high" | "low";
    decoding?: "async" | "sync" | "auto";
    sizes?: string;
    aspectRatio?: string;
    objectFit?: "cover" | "contain" | "fill" | "none" | "scale-down";
    objectPosition?: string;
    fallbackSrc?: string;
    widths?: number[];
    quality?: number;
    blur?: boolean;
    placeholderColor?: string;
  }>();

  // Default fallback image if none provided
  const defaultFallback = "/images/baberrih-placeholder.svg";

  // State
  let loaded = $state(false);
  let error = $state(false);
  let imgElement: HTMLImageElement;
  let isLocal = $state(false);
  let isRemote = $state(false);
  let srcset = $state("");
  let currentSrc = $state(src);

  // Function to check if an image URL is accessible with detailed error reporting
  async function checkImageAccessibility(url: string): Promise<boolean> {
    if (!browser) return true; // Skip check during SSR

    console.log(`OptimizedImage: Starting detailed accessibility check for ${url}`);

    // For baberrih.ma domain, log a special warning
    if (url.includes('baberrih.ma')) {
      console.warn(`OptimizedImage: URL contains baberrih.ma domain which may be inaccessible: ${url}`);
    }

    // Try XMLHttpRequest first for more detailed error information
    try {
      console.log(`OptimizedImage: Attempting XMLHttpRequest for ${url}`);

      const xhrPromise = new Promise<{success: boolean, status: number, statusText: string}>((resolve) => {
        const xhr = new XMLHttpRequest();

        xhr.onload = function() {
          console.log(`OptimizedImage: XMLHttpRequest success for ${url}`, {
            status: xhr.status,
            statusText: xhr.statusText,
            responseType: xhr.responseType,
            responseURL: xhr.responseURL,
            headers: xhr.getAllResponseHeaders()
          });
          resolve({success: true, status: xhr.status, statusText: xhr.statusText});
        };

        xhr.onerror = function(e) {
          console.error(`OptimizedImage: XMLHttpRequest error for ${url}`, {
            error: e,
            status: xhr.status,
            statusText: xhr.statusText,
            readyState: xhr.readyState
          });
          resolve({success: false, status: xhr.status, statusText: xhr.statusText});
        };

        // Handle timeout
        xhr.ontimeout = function() {
          console.warn(`OptimizedImage: XMLHttpRequest timeout for ${url}`);
          resolve({success: false, status: 0, statusText: 'Timeout'});
        };

        xhr.open('GET', url, true);
        xhr.timeout = 5000; // 5 seconds timeout

        // Try to get the image as a blob to check if it's a valid image
        xhr.responseType = 'blob';

        // Send the request
        try {
          xhr.send();
        } catch (sendError) {
          console.error(`OptimizedImage: XMLHttpRequest send error for ${url}`, sendError);
          resolve({success: false, status: 0, statusText: 'Send Error'});
        }
      });

      const xhrResult = await xhrPromise;

      if (xhrResult.success) {
        return true;
      } else {
        console.warn(`OptimizedImage: XMLHttpRequest failed with status ${xhrResult.status} (${xhrResult.statusText}) for ${url}`);
      }
    } catch (xhrError) {
      console.error(`OptimizedImage: XMLHttpRequest exception for ${url}:`, xhrError);
    }

    // If XMLHttpRequest failed, try with Image element as fallback
    try {
      console.log(`OptimizedImage: Attempting Image element load for ${url}`);

      // Create a new Image element to test loading
      const img = new Image();

      // Create a promise that resolves when the image loads or rejects when it fails
      const imageLoadPromise = new Promise<boolean>((resolve, reject) => {
        img.onload = () => {
          console.log(`OptimizedImage: Successfully loaded image with Image element: ${url}`, {
            naturalWidth: img.naturalWidth,
            naturalHeight: img.naturalHeight,
            complete: img.complete
          });
          resolve(true);
        };

        img.onerror = function(this: HTMLImageElement, ev: Event | string): boolean {
          const e = ev;
          // Get detailed error information
          const errorInfo = {
            event: e,
            eventType: e instanceof Event ? e.type : 'unknown',
            target: e instanceof Event ? e.target : null,
            timeStamp: e instanceof Event ? e.timeStamp : Date.now(),
            // Try to get network error details if available
            networkError: (window as any).performance?.getEntriesByName?.(url)
          };

          console.error(`OptimizedImage: Image element failed to load: ${url}`, errorInfo);
          reject(new Error(`Failed to load image: ${url}`));
          return true; // Prevent default error handling
        };

        // Set a timeout to avoid hanging
        setTimeout(() => {
          console.warn(`OptimizedImage: Timeout while checking image with Image element: ${url}`);
          resolve(false);
        }, 5000);
      });

      // Set crossOrigin to anonymous to detect CORS issues
      if (url.startsWith('http') && !url.includes(window.location.hostname)) {
        img.crossOrigin = "anonymous";
        console.log(`OptimizedImage: Set crossOrigin to anonymous for ${url}`);
      }

      // Set the src to start loading
      img.src = url;

      // Wait for the image to load or fail
      return await imageLoadPromise;
    } catch (error) {
      console.error(`OptimizedImage: Error checking accessibility for ${url}:`, error);
      return false;
    }
  }

  // Function to create a proxied URL for problematic domains
  function createProxiedUrl(originalUrl: string, fallbackUrl?: string): string {
    // Encode the URLs for use in query parameters
    const encodedUrl = encodeURIComponent(originalUrl);
    const encodedFallback = fallbackUrl ? encodeURIComponent(fallbackUrl) : '';

    // Build the proxy URL
    let proxyUrl = `/api/image-proxy?url=${encodedUrl}`;
    if (fallbackUrl) {
      proxyUrl += `&fallback=${encodedFallback}`;
    }

    return proxyUrl;
  }

  // Detect image source type
  onMount(async () => {
    console.log("OptimizedImage: Loading image with src:", src);

    // Determine if the image is local or remote
    isLocal = src.startsWith("/") && !src.startsWith("//") && !src.match(/^\/[a-zA-Z0-9]+:\/\//);
    isRemote = !isLocal;

    // Special handling for baberrih.ma domain which is known to have issues
    if (src.includes('baberrih.ma')) {
      console.warn(`OptimizedImage: URL contains baberrih.ma domain which may be inaccessible: ${src}`);
      console.info(`OptimizedImage: Using image proxy for baberrih.ma domain`);

      // Create a proxied URL that will fetch the image on the server side
      const proxyUrl = createProxiedUrl(src, fallbackSrc || defaultFallback);
      console.log(`OptimizedImage: Created proxy URL: ${proxyUrl}`);

      // Use the proxied URL instead of the original
      currentSrc = proxyUrl;
    } else {
      // For other domains, use the original source
      currentSrc = src;

      // Check if the image is accessible
      if (browser && isRemote) {
        const isAccessible = await checkImageAccessibility(src);
        if (!isAccessible) {
          console.warn(`OptimizedImage: Image may not be accessible: ${src}`);

          // For other problematic domains, we can still use the proxy as a fallback
          if (fallbackSrc || defaultFallback) {
            console.info(`OptimizedImage: Preparing fallback for inaccessible image`);
          }
        }
      }
    }

    // Generate srcset for remote images
    if (isRemote && browser) {
      try {
        const url = new URL(src);

        // For common CDNs, we can generate optimized URLs
        if (url.hostname.includes('cloudinary.com')) {
          // Cloudinary optimization
          srcset = generateCloudinarySrcSet(url, widths, quality);
        } else if (url.hostname.includes('imagekit.io')) {
          // ImageKit optimization
          srcset = generateImageKitSrcSet(url, widths, quality);
        } else if (url.hostname.includes('imgix.net')) {
          // Imgix optimization
          srcset = generateImgixSrcSet(url, widths, quality);
        } else {
          // For other remote images, we don't generate a srcset
          // as we can't reliably resize them
          srcset = "";
        }
      } catch (e) {
        console.error("OptimizedImage: Error generating srcset:", e);
        srcset = "";
      }
    } else if (isLocal) {
      // For local images, we don't generate a srcset as we would need
      // server-side processing to resize them
      srcset = "";
    }
  });

  // Handle image load error with detailed logging
  function handleError(e: Event) {
    const errorDetails = {
      src: src,
      currentSrc: currentSrc,
      isLocal: isLocal,
      isRemote: isRemote,
      hasSrcset: srcset !== "",
      errorEvent: e,
      elementNaturalWidth: imgElement?.naturalWidth || 0,
      elementNaturalHeight: imgElement?.naturalHeight || 0,
      loadingState: loaded ? 'loaded' : 'loading',
      timestamp: new Date().toISOString(),
      userAgent: browser ? navigator.userAgent : 'SSR',
      errorType: e?.type || 'unknown'
    };

    console.error(`OptimizedImage: Failed to load image:`, errorDetails);
    error = true;

    // Use fallback if provided, otherwise use default fallback
    if (fallbackSrc && fallbackSrc !== src) {
      console.log(`OptimizedImage: Attempting to use fallback image: ${fallbackSrc}`);
      currentSrc = fallbackSrc;
    } else if (defaultFallback && defaultFallback !== src) {
      console.log(`OptimizedImage: Attempting to use default fallback image: ${defaultFallback}`);
      currentSrc = defaultFallback;
    } else {
      console.warn(`OptimizedImage: No valid fallback available for: ${src}`);
    }

    // Dispatch error event for parent components
    if (browser) {
      const customEvent = new CustomEvent('imageError', {
        detail: {
          src,
          error: e,
          errorDetails,
          usedFallback: currentSrc !== src
        }
      });
      window.dispatchEvent(customEvent);
    }
  }

  // Handle image load success
  function handleLoad() {
    loaded = true;
  }

  // Generate srcset for Cloudinary images
  function generateCloudinarySrcSet(url: URL, widths: number[], quality: number): string {
    const baseUrl = url.origin + url.pathname.replace(/\/upload\//, '/upload/q_' + quality + ',c_limit,w_[WIDTH]/');
    return widths.map(w => `${baseUrl.replace('[WIDTH]', w.toString())} ${w}w`).join(', ');
  }

  // Generate srcset for ImageKit images
  function generateImageKitSrcSet(url: URL, widths: number[], quality: number): string {
    const baseUrl = url.href.includes('?')
      ? `${url.href}&tr=q-${quality},w-[WIDTH]`
      : `${url.href}?tr=q-${quality},w-[WIDTH]`;
    return widths.map(w => `${baseUrl.replace('[WIDTH]', w.toString())} ${w}w`).join(', ');
  }

  // Generate srcset for Imgix images
  function generateImgixSrcSet(url: URL, widths: number[], quality: number): string {
    const baseUrl = url.href.includes('?')
      ? `${url.href}&q=${quality}&w=[WIDTH]`
      : `${url.href}?q=${quality}&w=[WIDTH]`;
    return widths.map(w => `${baseUrl.replace('[WIDTH]', w.toString())} ${w}w`).join(', ');
  }

  // Calculate aspect ratio padding
  function getAspectRatioPadding(ratio: string): string {
    switch (ratio) {
      case '16/9': return '56.25%';
      case '4/3': return '75%';
      case '1/1': return '100%';
      case '3/2': return '66.67%';
      case '2/3': return '150%';
      default:
        // Handle custom ratios like "16/9"
        const parts = ratio.split('/');
        if (parts.length === 2) {
          const width = parseFloat(parts[0]);
          const height = parseFloat(parts[1]);
          if (!isNaN(width) && !isNaN(height) && height > 0) {
            return `${(height / width) * 100}%`;
          }
        }
        return '56.25%'; // Default to 16/9 if invalid
    }
  }
</script>

<div
  class="optimized-image-container {className}"
  style="--aspect-ratio: {aspectRatio}; padding-bottom: {getAspectRatioPadding(aspectRatio)};"
>
  <!-- Main image with srcset for responsive loading -->
  <img
    bind:this={imgElement}
    src={currentSrc}
    {alt}
    class="optimized-image {loaded ? 'loaded' : ''} {error ? 'error' : ''}"
    {loading}
    {fetchpriority}
    {decoding}
    sizes={sizes}
    srcset={srcset}
    style="object-fit: {objectFit}; object-position: {objectPosition};"
    onerror={handleError}
    onload={handleLoad}
  />

  <!-- Placeholder while loading -->
  {#if blur}
    <div
      class="image-placeholder"
      style="background-color: {placeholderColor}; backdrop-filter: blur(10px);"
    ></div>
  {/if}

  <!-- Error state indicator (shown when error=true) -->
  {#if error}
    <div class="image-error" title="Failed to load: {src}">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="image-error-icon">
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
        <circle cx="12" cy="12" r="3"></circle>
        <line x1="16.5" y1="7.5" x2="16.5" y2="7.501"></line>
      </svg>
      <span>Image failed to load</span>
      <span class="image-error-url">{src.substring(0, 30)}{src.length > 30 ? '...' : ''}</span>
      {#if fallbackSrc}
        <span class="image-error-fallback">Using fallback image</span>
      {/if}
    </div>
  {/if}
</div>

<style>
  .optimized-image-container {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 0; /* Height is determined by padding-bottom */
    display: block;
    min-height: 100px; /* Minimum height to ensure visibility */
  }

  .optimized-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover; /* Default, can be overridden by style prop */
    opacity: 0; /* Start invisible */
    z-index: 2;
    transition: opacity 0.5s ease-out;
    display: block;
  }

  .optimized-image.loaded {
    opacity: 1;
  }

  .image-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    transition: opacity 0.5s ease-out;
  }

  /* Hide placeholder when image is loaded */
  :global(.optimized-image.loaded ~ .image-placeholder) {
    opacity: 0;
  }

  .image-error {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    color: #6c757d;
    z-index: 3;
    font-size: 0.875rem;
    padding: 1rem;
    text-align: center;
    border: 1px dashed #dc3545;
  }

  .image-error-icon {
    width: 2rem;
    height: 2rem;
    margin-bottom: 0.5rem;
    color: #dc3545;
  }

  .image-error-url {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    color: #dc3545;
    word-break: break-all;
    max-width: 100%;
  }

  .image-error-fallback {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    color: #28a745;
    font-style: italic;
  }
</style>
