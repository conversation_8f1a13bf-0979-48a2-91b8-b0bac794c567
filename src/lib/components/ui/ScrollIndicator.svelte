<script lang="ts">
  import { onMount } from "svelte";
  import { fade } from "svelte/transition";
  import { cubicOut } from "svelte/easing";

  // Props
  const {
    className = "",
    hideAfterScroll = true,
    scrollThreshold = 100,
    arrowSize = "w-6 h-6",
    arrowColor = "text-primary-50",
    pulseColor = "bg-primary-50/30",
  } = $props<{
    className?: string;
    hideAfterScroll?: boolean;
    scrollThreshold?: number;
    arrowSize?: string;
    arrowColor?: string;
    pulseColor?: string;
  }>();

  // State
  let visible = $state(true);
  let hasScrolled = $state(false);

  // Handle scroll events
  function handleScroll() {
    if (hideAfterScroll && window.scrollY > scrollThreshold) {
      hasScrolled = true;
      visible = false;
    }
  }

  // Lifecycle
  onMount(() => {
    // Add scroll event listener
    window.addEventListener("scroll", handleScroll);
    
    // Check initial scroll position
    handleScroll();
    
    // Clean up
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  });
</script>

{#if visible}
  <div 
    class="scroll-indicator {className}" 
    in:fade={{ duration: 800, easing: cubicOut }}
    out:fade={{ duration: 300 }}
  >
    <div class="pulse-container">
      <div class="pulse {pulseColor}"></div>
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        stroke-width="2" 
        stroke-linecap="round" 
        stroke-linejoin="round" 
        class="arrow {arrowSize} {arrowColor}"
      >
        <path d="M12 5v14"></path>
        <path d="m19 12-7 7-7-7"></path>
      </svg>
    </div>
  </div>
{/if}

<style>
  .scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .pulse-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .arrow {
    position: relative;
    z-index: 2;
    animation: bounce 2s infinite;
  }

  .pulse {
    position: absolute;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    z-index: 1;
    animation: pulse 2s infinite;
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(0.8);
      opacity: 0.7;
    }
    50% {
      transform: scale(1);
      opacity: 0.3;
    }
    100% {
      transform: scale(0.8);
      opacity: 0.7;
    }
  }

  /* Disable animations for users who prefer reduced motion */
  @media (prefers-reduced-motion: reduce) {
    .arrow, .pulse {
      animation: none;
    }
  }
</style>
