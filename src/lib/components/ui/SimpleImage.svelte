<script lang="ts">
  /**
   * SimpleImage Component (Compatibility Layer)
   *
   * This component is now a wrapper around OptimizedImage to ensure backward compatibility.
   * It maintains the same API as the original SimpleImage component but uses the more advanced
   * OptimizedImage component under the hood.
   */
  import OptimizedImage from './OptimizedImage.svelte';

  // Props - maintain the same API as the original SimpleImage
  const {
    src,
    alt = "",
    className = "",
    loading = "lazy",
    fetchpriority = "auto",
    decoding = "async",
  } = $props<{
    src: string;
    alt?: string;
    className?: string;
    loading?: "lazy" | "eager";
    fetchpriority?: "auto" | "high" | "low";
    decoding?: "async" | "sync" | "auto";
  }>();

  // Log for debugging during transition
  console.log("SimpleImage (compatibility): Using OptimizedImage for", src);
</script>

<!-- Use OptimizedImage with the same props -->
<OptimizedImage
  {src}
  {alt}
  className={className}
  {loading}
  {fetchpriority}
  {decoding}
  blur={true}
  objectFit="cover"
  objectPosition="center"
/>

<!--
  Note: We've removed the custom styles since they're now handled by OptimizedImage.
  The OptimizedImage component provides all the same functionality with additional features.
-->
