<script lang="ts">
  import { X, GripVertical, Plus, Image, Video } from "lucide-svelte";
  import FileUpload from "./FileUpload.svelte";

  // Props
  const {
    mediaItems = [],
    type = "image",
    bucket = "media",
    folder = "",
    maxItems = 20,
  } = $props<{
    mediaItems: Array<string>;
    type?: "image" | "video";
    bucket?: string;
    folder?: string;
    maxItems?: number;
  }>();

  // State
  let items = $state<string[]>([...mediaItems]);
  let showUploader = $state(false);
  let draggedItem = $state<number | null>(null);
  let dragOverItem = $state<number | null>(null);

  // Initialize items from mediaItems
  let initialized = $state(false);

  $effect(() => {
    // Only run this effect once to initialize the items
    if (!initialized) {
      initialized = true;
      console.log(
        "MediaManager: initializing items from mediaItems",
        mediaItems,
        "type:",
        type
      );

      // Handle different types of mediaItems
      if (Array.isArray(mediaItems)) {
        // If it's already an array, use it
        items = [...mediaItems];
      } else if (mediaItems === null || mediaItems === undefined) {
        // If it's null or undefined, use an empty array
        console.warn("mediaItems is null or undefined, using empty array");
        items = [];
      } else if (typeof mediaItems === "string") {
        // If it's a string, try to parse it as JSON
        try {
          const parsed = JSON.parse(mediaItems);
          if (Array.isArray(parsed)) {
            items = [...parsed];
          } else {
            console.error("Parsed mediaItems is not an array:", parsed);
            items = [];
          }
        } catch (e) {
          console.error("Failed to parse mediaItems as JSON:", e);
          items = [];
        }
      } else if (typeof mediaItems === "object") {
        // If it's an object with a toJSON method (like a Proxy), try to use it
        try {
          const jsonString = JSON.stringify(mediaItems);
          const parsed = JSON.parse(jsonString);
          if (Array.isArray(parsed)) {
            items = [...parsed];
          } else {
            console.error("Stringified mediaItems is not an array:", parsed);
            items = [];
          }
        } catch (e) {
          console.error("Failed to stringify/parse mediaItems:", e);
          items = [];
        }
      } else {
        console.error(
          "mediaItems is not an array or convertible to array:",
          mediaItems
        );
        items = [];
      }

      console.log("MediaManager: items initialized to", items);
    }
  });

  // Custom event handler for change
  function dispatchChange() {
    // Create a more specific event name based on the media type
    const eventName = `media-change-${type}`;
    console.log(`Dispatching ${eventName} event with items:`, items);

    // Create and dispatch a custom event
    const event = new CustomEvent(eventName, {
      detail: {
        items,
        type,
      },
      bubbles: true,
      composed: true,
    });

    // Dispatch the event directly on the document
    document.dispatchEvent(event);
    console.log(`Event ${eventName} dispatched on document`);

    // Also dispatch a global event for debugging
    const globalEvent = new CustomEvent("media-change", {
      detail: {
        items,
        type,
      },
      bubbles: true,
      composed: true,
    });
    document.dispatchEvent(globalEvent);
    console.log("Global media-change event dispatched on document");
  }

  // Toggle uploader visibility
  function toggleUploader() {
    showUploader = !showUploader;
  }

  // Handle file upload completion
  function handleUploaded(event: CustomEvent) {
    console.log("MediaManager: handleUploaded triggered", event);
    if (!event.detail || !Array.isArray(event.detail.files)) {
      console.error(
        "MediaManager: Invalid event structure in handleUploaded. Expected event.detail.files to be an array.",
        event.detail
      );
      return;
    }
    const uploadedFiles = event.detail.files;

    // Add new files to the list
    const newItems = [
      ...items,
      ...uploadedFiles.map((file: any) => file.url),
    ].slice(0, maxItems);

    items = newItems;

    // Hide uploader after successful upload
    showUploader = false;

    // Dispatch change event
    dispatchChange();
  }

  // Remove an item
  function removeItem(index: number) {
    items = items.filter((_, i) => i !== index);
    dispatchChange();
  }

  // Drag and drop handlers
  function handleDragStart(index: number) {
    draggedItem = index;
  }

  function handleDragOver(event: DragEvent, index: number) {
    event.preventDefault();
    dragOverItem = index;
  }

  function handleDragEnd() {
    if (
      draggedItem !== null &&
      dragOverItem !== null &&
      draggedItem !== dragOverItem
    ) {
      // Create a copy of the items array
      const itemsCopy = [...items];

      // Remove the dragged item
      const draggedItemValue = itemsCopy.splice(draggedItem, 1)[0];

      // Insert it at the new position
      itemsCopy.splice(dragOverItem, 0, draggedItemValue);

      // Update the items array
      items = itemsCopy;

      // Dispatch change event
      dispatchChange();
    }

    // Reset drag state
    draggedItem = null;
    dragOverItem = null;
  }

  // Check if URL is an image
  function isImage(url: string): boolean {
    return /\.(jpg|jpeg|png|gif|webp|avif|svg)$/i.test(url);
  }

  // Check if URL is a video
  function isVideo(url: string): boolean {
    return /\.(mp4|webm|ogg|mov)$/i.test(url);
  }

  // Get media type icon
  function getMediaTypeIcon(url: string) {
    if (isImage(url)) return Image;
    if (isVideo(url)) return Video;
    return Image; // Default to image icon
  }
</script>

<div class="media-manager">
  <div class="flex justify-between items-center mb-4">
    <h3 class="font-medium text-gray-700 text-sm">
      {type === "image" ? "Images" : "Videos"} ({items.length}/{maxItems})
    </h3>
    {#if items.length < maxItems}
      <button
        type="button"
        class="flex items-center bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded-md text-white text-sm"
        onclick={toggleUploader}
      >
        <Plus class="mr-1 w-4 h-4" />
        Add {type === "image" ? "Image" : "Video"}
      </button>
    {/if}
  </div>

  {#if showUploader}
    <div class="mb-4">
      <FileUpload
        {bucket}
        folder={folder || type === "image" ? "images" : "videos"}
        accept={type === "image" ? "image/*" : "video/*"}
        on:uploaded={handleUploaded}
      />
    </div>
  {/if}

  {#if items.length > 0}
    <div class="gap-4 grid grid-cols-1 md:grid-cols-3">
      {#each items as item, index}
        <div
          class="group relative border border-gray-200 rounded-md overflow-hidden"
          draggable="true"
          ondragstart={() => handleDragStart(index)}
          ondragover={(e) => handleDragOver(e, index)}
          ondragend={handleDragEnd}
          class:bg-blue-50={dragOverItem === index}
          role="listitem"
          aria-label={type === "image" ? "Image item" : "Video item"}
        >
          {#if isImage(item)}
            <img src={item} alt="Media item" class="w-full h-32 object-cover" />
          {:else if isVideo(item)}
            <video src={item} class="w-full h-32 object-cover" controls={false}>
              <track kind="captions" src="" label="English" />
            </video>
          {:else}
            <div
              class="flex justify-center items-center bg-gray-100 w-full h-32"
            >
              {#if getMediaTypeIcon(item) === Image}
                <Image class="w-8 h-8 text-gray-400" />
              {:else if getMediaTypeIcon(item) === Video}
                <Video class="w-8 h-8 text-gray-400" />
              {:else}
                <Image class="w-8 h-8 text-gray-400" />
              {/if}
            </div>
          {/if}

          <div class="top-0 right-0 left-0 absolute flex justify-between p-2">
            <button
              type="button"
              class="bg-white/80 hover:bg-white p-1 rounded text-gray-600 cursor-move"
              aria-label="Drag to reorder"
            >
              <GripVertical class="w-4 h-4" />
            </button>
            <button
              type="button"
              class="bg-white/80 hover:bg-white p-1 rounded text-red-500 hover:text-red-700"
              onclick={() => removeItem(index)}
              aria-label="Remove item"
            >
              <X class="w-4 h-4" />
            </button>
          </div>
        </div>
      {/each}
    </div>
  {:else}
    <div
      class="flex justify-center items-center bg-gray-50 p-8 border border-gray-200 rounded-md"
    >
      <p class="text-gray-500 text-sm">
        No {type === "image" ? "images" : "videos"} added yet. Click the button above
        to add some.
      </p>
    </div>
  {/if}
</div>
