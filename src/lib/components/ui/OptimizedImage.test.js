import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/svelte';
import OptimizedImage from './OptimizedImage.svelte';

// Mock the $app/environment module
vi.mock('$app/environment', () => ({
  browser: true
}));

describe('OptimizedImage', () => {
  beforeEach(() => {
    // Reset mocks between tests
    vi.resetAllMocks();
  });

  it('renders with basic props', () => {
    const { container } = render(OptimizedImage, {
      src: 'https://example.com/image.jpg',
      alt: 'Test image'
    });
    
    const img = container.querySelector('img');
    expect(img).not.toBeNull();
    expect(img.src).toContain('https://example.com/image.jpg');
    expect(img.alt).toBe('Test image');
    expect(img.loading).toBe('lazy'); // Default value
  });

  it('applies custom props correctly', () => {
    const { container } = render(OptimizedImage, {
      src: 'https://example.com/image.jpg',
      alt: 'Custom props test',
      loading: 'eager',
      fetchpriority: 'high',
      aspectRatio: '4/3',
      objectFit: 'contain'
    });
    
    const img = container.querySelector('img');
    expect(img.loading).toBe('eager');
    expect(img.fetchpriority).toBe('high');
    expect(img.style.objectFit).toBe('contain');
    
    const container_div = container.querySelector('.optimized-image-container');
    expect(container_div.style.getPropertyValue('--aspect-ratio')).toBe('4/3');
  });

  it('handles custom class names', () => {
    const { container } = render(OptimizedImage, {
      src: 'https://example.com/image.jpg',
      alt: 'Class test',
      className: 'custom-class test-class'
    });
    
    const container_div = container.querySelector('.optimized-image-container');
    expect(container_div.classList.contains('custom-class')).toBe(true);
    expect(container_div.classList.contains('test-class')).toBe(true);
  });

  it('calculates aspect ratio padding correctly', () => {
    const testCases = [
      { ratio: '16/9', expected: '56.25%' },
      { ratio: '4/3', expected: '75%' },
      { ratio: '1/1', expected: '100%' },
      { ratio: '3/2', expected: '66.67%' },
      { ratio: '2/3', expected: '150%' },
      { ratio: 'invalid', expected: '56.25%' } // Should default to 16/9
    ];
    
    for (const { ratio, expected } of testCases) {
      const { container } = render(OptimizedImage, {
        src: 'https://example.com/image.jpg',
        alt: 'Aspect ratio test',
        aspectRatio: ratio
      });
      
      const container_div = container.querySelector('.optimized-image-container');
      expect(container_div.style.paddingBottom).toBe(expected);
    }
  });

  it('shows placeholder while loading', () => {
    const { container } = render(OptimizedImage, {
      src: 'https://example.com/image.jpg',
      alt: 'Placeholder test',
      blur: true
    });
    
    const placeholder = container.querySelector('.image-placeholder');
    expect(placeholder).not.toBeNull();
  });

  it('does not show placeholder when blur is false', () => {
    const { container } = render(OptimizedImage, {
      src: 'https://example.com/image.jpg',
      alt: 'No placeholder test',
      blur: false
    });
    
    const placeholder = container.querySelector('.image-placeholder');
    expect(placeholder).toBeNull();
  });
});
