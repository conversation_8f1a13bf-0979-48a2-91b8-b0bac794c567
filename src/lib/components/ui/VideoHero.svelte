<script lang="ts">
  import { onMount } from "svelte";
  import { fade } from "svelte/transition";
  import LoadingSpinner from "./LoadingSpinner.svelte";
  import OptimizedImage from "./OptimizedImage.svelte";

  // Props
  const {
    videoSrc,
    fallbackImageSrc = "",
    alt = "Hero background",
    autoplay = true,
    loop = true,
    muted = true,
    className = "",
  } = $props<{
    videoSrc: string;
    fallbackImageSrc?: string;
    alt?: string;
    autoplay?: boolean;
    loop?: boolean;
    muted?: boolean;
    className?: string;
  }>();

  // State
  let videoElement = $state<HTMLVideoElement | null>(null);
  let isLoading = $state(true);
  let hasError = $state(false);
  let loadingTimeout = $state<ReturnType<typeof setTimeout> | null>(null);

  // Functions
  function handleVideoLoaded() {
    isLoading = false;
    if (autoplay && videoElement) {
      videoElement.play()
        .catch(err => {
          console.error("Error auto-playing video:", err);
          // Don't set hasError here, just log it
        });
    }
  }

  function handleVideoError() {
    isLoading = false;
    hasError = true;
    console.error("Video failed to load:", videoSrc);
  }

  // Lifecycle
  onMount(() => {
    // We'll set up event listeners after the videoElement is bound
    const setupListeners = () => {
      if (videoElement) {
        videoElement.addEventListener("loadeddata", handleVideoLoaded);
        videoElement.addEventListener("error", handleVideoError);

        // Add canplaythrough event to ensure video is fully loaded
        videoElement.addEventListener("canplaythrough", handleVideoLoaded);
      }
    };

    // Initial setup if videoElement is already available
    setupListeners();

    // Set a very short timeout to hide the loading spinner quickly
    // This ensures a better user experience even if the video is still loading
    loadingTimeout = setTimeout(() => {
      isLoading = false;
    }, 800); // Much shorter timeout for better UX

    // Clean up
    return () => {
      if (videoElement) {
        videoElement.removeEventListener("loadeddata", handleVideoLoaded);
        videoElement.removeEventListener("error", handleVideoError);
        videoElement.removeEventListener("canplaythrough", handleVideoLoaded);
      }

      // Clear the timeout if component is unmounted
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
      }
    };
  });
</script>

<div
  class="video-hero-container {className}"
  role="region"
  aria-label="Video player"
>
  {#if hasError && fallbackImageSrc}
    <!-- Fallback image if video fails to load -->
    <div class="video-hero-fallback">
      <OptimizedImage
        src={fallbackImageSrc}
        alt={alt}
        className="video-hero-image"
        loading="eager"
        fetchpriority="high"
        objectFit="cover"
      />
    </div>
  {:else}
    <!-- Video element with optimized loading -->
    <video
      bind:this={videoElement}
      src={videoSrc}
      class="video-hero-video"
      {muted}
      {loop}
      autoplay={autoplay}
      playsinline
      preload="auto"
      poster={fallbackImageSrc}
    >
      <source src={videoSrc} type="video/webm" />
      <track kind="captions" src="" label="English" />
      Your browser does not support the video tag.
    </video>

    <!-- Loading overlay - hidden by default, now much more subtle -->
    {#if isLoading}
      <div class="video-hero-loading" in:fade={{ duration: 100 }} out:fade={{ duration: 200 }}>
        <LoadingSpinner size="0.75rem" color="rgba(255, 255, 255, 0.5)" />
      </div>
    {/if}

    <!-- Controls overlay removed as per requirements -->
  {/if}
</div>

<style>
  .video-hero-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .video-hero-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .video-hero-fallback {
    width: 100%;
    height: 100%;
  }

  .video-hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .video-hero-loading {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.15);
    border-radius: 50%;
    padding: 6px;
    z-index: 2;
    opacity: 0.7;
  }

  /* Controls removed as per requirements */
</style>
