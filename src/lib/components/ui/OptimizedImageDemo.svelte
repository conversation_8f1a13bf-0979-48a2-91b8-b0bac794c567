<script lang="ts">
  import OptimizedImage from './OptimizedImage.svelte';

  // Demo images
  const localImage = '/favicon.png'; // Local image from the project
  const remoteImage = 'https://baberrih.ma/media/hero/1_f.webp'; // Example remote image from the project
  const cloudinaryImage = 'https://res.cloudinary.com/demo/image/upload/sample.jpg'; // Cloudinary demo image

  // Aspect ratios to demonstrate
  const aspectRatios = ['16/9', '4/3', '1/1', '3/2', '2/3'];

  // Object fit options
  const objectFits = ['cover', 'contain', 'fill', 'none', 'scale-down'] as const;
</script>

<div class="bg-primary-50 p-6">
  <h1 class="mb-6 font-montserrat font-light text-primary-900 text-2xl uppercase tracking-wider">
    OptimizedImage Component Demo
  </h1>

  <div class="bg-white shadow-sm mb-8 p-6 rounded-md">
    <h2 class="mb-4 font-montserrat font-light text-primary-900 text-xl uppercase tracking-wider">
      Basic Usage
    </h2>

    <div class="gap-6 grid grid-cols-1 md:grid-cols-2">
      <!-- Local Image Example -->
      <div>
        <h3 class="mb-2 font-montserrat font-medium text-primary-800 text-lg">Local Image</h3>
        <div class="bg-primary-100 rounded-md h-64 overflow-hidden">
          <OptimizedImage
            src={localImage}
            alt="Local image example"
            aspectRatio="16/9"
          />
        </div>
        <p class="mt-2 text-primary-700 text-sm">Source: {localImage}</p>
      </div>

      <!-- Remote Image Example -->
      <div>
        <h3 class="mb-2 font-montserrat font-medium text-primary-800 text-lg">Remote Image</h3>
        <div class="bg-primary-100 rounded-md h-64 overflow-hidden">
          <OptimizedImage
            src={remoteImage}
            alt="Remote image example"
            aspectRatio="16/9"
            loading="eager"
            fetchpriority="high"
          />
        </div>
        <p class="mt-2 text-primary-700 text-sm">Source: {remoteImage}</p>
      </div>
    </div>
  </div>

  <div class="bg-white shadow-sm mb-8 p-6 rounded-md">
    <h2 class="mb-4 font-montserrat font-light text-primary-900 text-xl uppercase tracking-wider">
      Aspect Ratio Examples
    </h2>

    <div class="gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
      {#each aspectRatios as ratio}
        <div>
          <h3 class="mb-2 font-montserrat font-medium text-primary-800 text-lg">Aspect Ratio: {ratio}</h3>
          <div class="bg-primary-100 rounded-md overflow-hidden">
            <OptimizedImage
              src={remoteImage}
              alt="Aspect ratio example: {ratio}"
              aspectRatio={ratio}
            />
          </div>
        </div>
      {/each}
    </div>
  </div>

  <div class="bg-white shadow-sm mb-8 p-6 rounded-md">
    <h2 class="mb-4 font-montserrat font-light text-primary-900 text-xl uppercase tracking-wider">
      Object Fit Examples
    </h2>

    <div class="gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
      {#each objectFits as fit}
        <div>
          <h3 class="mb-2 font-montserrat font-medium text-primary-800 text-lg">Object Fit: {fit}</h3>
          <div class="bg-primary-100 rounded-md h-64 overflow-hidden">
            <OptimizedImage
              src={remoteImage}
              alt="Object fit example: {fit}"
              aspectRatio="16/9"
              objectFit={fit}
            />
          </div>
        </div>
      {/each}
    </div>
  </div>

  <div class="bg-white shadow-sm mb-8 p-6 rounded-md">
    <h2 class="mb-4 font-montserrat font-light text-primary-900 text-xl uppercase tracking-wider">
      Error Handling Example
    </h2>

    <div class="gap-6 grid grid-cols-1 md:grid-cols-2">
      <!-- Error with no fallback -->
      <div>
        <h3 class="mb-2 font-montserrat font-medium text-primary-800 text-lg">Error (No Fallback)</h3>
        <div class="bg-primary-100 rounded-md h-64 overflow-hidden">
          <OptimizedImage
            src="https://example.com/non-existent-image.jpg"
            alt="Error example without fallback"
            aspectRatio="16/9"
          />
        </div>
      </div>

      <!-- Error with fallback -->
      <div>
        <h3 class="mb-2 font-montserrat font-medium text-primary-800 text-lg">Error (With Fallback)</h3>
        <div class="bg-primary-100 rounded-md h-64 overflow-hidden">
          <OptimizedImage
            src="https://example.com/non-existent-image.jpg"
            alt="Error example with fallback"
            aspectRatio="16/9"
            fallbackSrc={localImage}
          />
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white shadow-sm p-6 rounded-md">
    <h2 class="mb-4 font-montserrat font-light text-primary-900 text-xl uppercase tracking-wider">
      CDN Optimization Example
    </h2>

    <div>
      <h3 class="mb-2 font-montserrat font-medium text-primary-800 text-lg">Cloudinary Image</h3>
      <div class="bg-primary-100 rounded-md h-64 overflow-hidden">
        <OptimizedImage
          src={cloudinaryImage}
          alt="Cloudinary image example"
          aspectRatio="16/9"
          widths={[400, 800, 1200]}
          quality={85}
        />
      </div>
      <p class="mt-2 text-primary-700 text-sm">Source: {cloudinaryImage}</p>
      <p class="text-primary-700 text-sm">This image will be automatically optimized with appropriate srcset values for Cloudinary.</p>
    </div>
  </div>
</div>
