<script lang="ts">
  import { currency, setCurrency, supportedCurrencies } from "$lib/currency/store";
  import { ChevronDown } from "lucide-svelte";
  import { T } from "$lib/i18n";

  // Props
  const {
    label = "",
    className = "",
  } = $props<{
    label?: string;
    className?: string;
  }>();

  // State
  let isOpen = $state(false);
  let containerRef = $state<HTMLDivElement | null>(null);

  // Toggle dropdown
  function toggleDropdown() {
    isOpen = !isOpen;
  }

  // Handle currency selection
  function selectCurrency(code: string) {
    setCurrency(code);
    isOpen = false;
  }

  // Close dropdown when clicking outside
  function handleClickOutside(event: MouseEvent) {
    if (containerRef && !containerRef.contains(event.target as Node)) {
      isOpen = false;
    }
  }

  // Add event listener for click outside
  $effect(() => {
    if (isOpen) {
      document.addEventListener('click', handleClickOutside);
    } else {
      document.removeEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  });
</script>

<div class="relative {className}" bind:this={containerRef}>
  {#if label}
    <label class="block mb-1 text-primary-800 text-xs">
      {label}
    </label>
  {/if}

  <button
    type="button"
    class="flex justify-between items-center bg-primary-100/50 px-3 py-1.5 border border-primary-200 rounded-sm focus:outline-none focus:ring-1 focus:ring-primary-300 w-full font-montserrat font-light text-primary-900 text-sm"
    on:click|stopPropagation={toggleDropdown}
    aria-haspopup="listbox"
    aria-expanded={isOpen}
  >
    <div class="flex items-center">
      <span class="inline-block mr-2 w-4 text-center">{supportedCurrencies.find(c => c.code === $currency)?.symbol}</span>
      <span>{supportedCurrencies.find(c => c.code === $currency)?.name}</span>
    </div>
    <ChevronDown class="w-4 h-4 text-primary-700" />
  </button>

  {#if isOpen}
    <div
      class="z-10 absolute bg-primary-50 shadow-md mt-1 border border-primary-200 rounded-sm w-full max-h-60 overflow-auto"
      style="background-color: rgba(248, 247, 244, 0.98);"
      role="listbox"
    >
      {#each supportedCurrencies as curr}
        <button
          class="flex items-center w-full px-3 py-2 text-left hover:bg-primary-100/70 text-primary-900 {$currency === curr.code ? 'bg-primary-100/50 font-medium' : ''}"
          role="option"
          aria-selected={$currency === curr.code}
          on:click={() => selectCurrency(curr.code)}
        >
          <span class="inline-block mr-2 w-4 text-center">{curr.symbol}</span>
          <span class="truncate">{curr.name}</span>
        </button>
      {/each}
    </div>
  {/if}
</div>
