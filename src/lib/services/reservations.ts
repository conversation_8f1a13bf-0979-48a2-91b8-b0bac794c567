/**
 * Servicios para gestionar reservas
 */
import type { SupabaseClient } from '@supabase/supabase-js';

// Definición de tipos para la reserva
export type Reservation = {
  id: string;
  site_id: string;
  suite_id: string;
  user_id?: string;
  cloudbeds_reservation_id?: string;
  cloudbeds_confirmation_code?: string;
  guest_name: string;
  guest_email: string;
  guest_phone?: string;
  check_in_date: string;
  check_out_date: string;
  adults: number;
  children: number;
  status: string;
  total_price?: number;
  currency?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  suite_name?: string;
  suite_slug?: string;
  site_name?: string;
  site_domain?: string;
  client_name?: string;
};

/**
 * Obtiene las reservas de un usuario
 * @param supabase Cliente de Supabase
 * @param userId ID del usuario
 * @returns Lista de reservas del usuario
 */
export async function getUserReservations(supabase: SupabaseClient, userId: string): Promise<{
  data: Reservation[] | null;
  error: string | null;
}> {
  try {
    if (!userId) {
      return { data: null, error: 'Se requiere el ID de usuario' };
    }

    const { data, error } = await supabase
      .from('reservations_with_details')
      .select('*')
      .eq('user_id', userId)
      .order('check_in_date', { ascending: false });

    if (error) {
      console.error('Error al obtener reservas del usuario:', error);
      return { data: null, error: error.message };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Error al obtener reservas del usuario:', error);
    return { data: null, error: 'Error interno del servidor' };
  }
}

/**
 * Busca reservas por correo electrónico y código de confirmación
 * @param supabase Cliente de Supabase
 * @param email Correo electrónico del huésped
 * @param confirmationCode Código de confirmación de la reserva
 * @returns Lista de reservas que coinciden con los criterios
 */
export async function findReservationByEmailAndCode(
  supabase: SupabaseClient,
  email: string,
  confirmationCode: string
): Promise<{
  data: Reservation[] | null;
  error: string | null;
}> {
  try {
    if (!email || !confirmationCode) {
      return { 
        data: null, 
        error: 'Se requieren el correo electrónico y el código de confirmación' 
      };
    }

    const { data, error } = await supabase
      .from('reservations_with_details')
      .select('*')
      .eq('guest_email', email.toLowerCase())
      .eq('cloudbeds_confirmation_code', confirmationCode.toUpperCase())
      .order('check_in_date', { ascending: false });

    if (error) {
      console.error('Error al buscar reserva:', error);
      return { data: null, error: error.message };
    }

    if (!data || data.length === 0) {
      return { 
        data: null, 
        error: 'No se encontró ninguna reserva con los datos proporcionados' 
      };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Error al buscar reserva:', error);
    return { data: null, error: 'Error interno del servidor' };
  }
}

/**
 * Obtiene una reserva por su ID
 * @param supabase Cliente de Supabase
 * @param reservationId ID de la reserva
 * @returns Detalles de la reserva
 */
export async function getReservationById(
  supabase: SupabaseClient,
  reservationId: string
): Promise<{
  data: Reservation | null;
  error: string | null;
}> {
  try {
    if (!reservationId) {
      return { data: null, error: 'Se requiere el ID de la reserva' };
    }

    const { data, error } = await supabase
      .from('reservations_with_details')
      .select('*')
      .eq('id', reservationId)
      .single();

    if (error) {
      console.error('Error al obtener reserva por ID:', error);
      return { data: null, error: error.message };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Error al obtener reserva por ID:', error);
    return { data: null, error: 'Error interno del servidor' };
  }
}

/**
 * Genera un token temporal para acceder a una reserva sin autenticación
 * @param supabase Cliente de Supabase
 * @param reservationId ID de la reserva
 * @returns Token temporal
 */
export async function generateReservationAccessToken(
  supabase: SupabaseClient,
  reservationId: string
): Promise<{
  token: string | null;
  error: string | null;
}> {
  try {
    if (!reservationId) {
      return { token: null, error: 'Se requiere el ID de la reserva' };
    }

    // Generar un token aleatorio
    const token = Math.random().toString(36).substring(2, 15) + 
                 Math.random().toString(36).substring(2, 15);
    
    // Almacenar el token en la tabla de tokens temporales
    // Nota: Esta tabla debe ser creada previamente
    const { error } = await supabase
      .from('reservation_access_tokens')
      .insert({
        reservation_id: reservationId,
        token,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 horas
      });

    if (error) {
      console.error('Error al generar token de acceso:', error);
      return { token: null, error: error.message };
    }

    return { token, error: null };
  } catch (error) {
    console.error('Error al generar token de acceso:', error);
    return { token: null, error: 'Error interno del servidor' };
  }
}

/**
 * Verifica si un token de acceso a una reserva es válido
 * @param supabase Cliente de Supabase
 * @param token Token de acceso
 * @returns ID de la reserva si el token es válido
 */
export async function verifyReservationAccessToken(
  supabase: SupabaseClient,
  token: string
): Promise<{
  reservationId: string | null;
  error: string | null;
}> {
  try {
    if (!token) {
      return { reservationId: null, error: 'Se requiere el token de acceso' };
    }

    // Buscar el token en la tabla de tokens temporales
    const { data, error } = await supabase
      .from('reservation_access_tokens')
      .select('*')
      .eq('token', token)
      .gt('expires_at', new Date().toISOString())
      .single();

    if (error) {
      console.error('Error al verificar token de acceso:', error);
      return { reservationId: null, error: 'Token inválido o expirado' };
    }

    return { reservationId: data.reservation_id, error: null };
  } catch (error) {
    console.error('Error al verificar token de acceso:', error);
    return { reservationId: null, error: 'Error interno del servidor' };
  }
}
