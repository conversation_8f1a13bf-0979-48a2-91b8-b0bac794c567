/**
 * Utilidades para la autenticación y autorización
 */
import type { SupabaseClient } from '@supabase/supabase-js';

/**
 * Verifica si un usuario tiene permisos de administración
 * @param supabase Cliente de Supabase
 * @param userId ID del usuario
 * @returns true si el usuario tiene permisos de administración, false en caso contrario
 */
export async function hasAdminPermission(supabase: SupabaseClient, userId: string): Promise<boolean> {
  try {
    // Primero verificar si el usuario tiene el campo is_admin en los metadatos de la aplicación
    // Usamos una consulta SQL directa para verificar los metadatos
    const { data: adminMetadata, error: adminMetadataError } = await supabase
      .rpc('check_user_is_admin', { user_id: userId });

    if (!adminMetadataError && adminMetadata === true) {
      console.log(`Usuario ${userId} es admin por metadatos de la aplicación`);
      return true;
    }

    // Si no es admin por metadatos, verificar si tiene permisos de administrador en algún sitio
    const { data, error } = await supabase
      .from('site_users_with_permissions')
      .select('*')
      .eq('user_id', userId)
      .contains('permissions', ['admin']);

    if (error) {
      console.error('Error al verificar permisos de administración en sitios:', error);
      return false;
    }

    // Si hay al menos un registro, el usuario tiene permisos de administración
    const isAdminBySitePermissions = data && data.length > 0;
    console.log(`Usuario ${userId} ${isAdminBySitePermissions ? 'es' : 'no es'} admin por permisos de sitio`);
    return isAdminBySitePermissions;
  } catch (error) {
    console.error('Error al verificar permisos de administración:', error);
    return false;
  }
}

/**
 * Verifica si un usuario tiene permisos para un sitio específico
 * @param supabase Cliente de Supabase
 * @param userId ID del usuario
 * @param siteId ID del sitio
 * @param permission Permiso a verificar (opcional)
 * @returns true si el usuario tiene permisos para el sitio, false en caso contrario
 */
export async function hasSitePermission(
  supabase: SupabaseClient,
  userId: string,
  siteId: string,
  permission?: string
): Promise<boolean> {
  try {
    console.log(`Verificando permiso para usuario ${userId} en sitio ${siteId}, permiso: ${permission || 'cualquiera'}`);

    // Usar la función RPC check_site_permission para evitar recursión infinita
    const { data: hasPermission, error } = await supabase
      .rpc('check_site_permission', {
        p_user_id: userId,
        p_site_id: siteId,
        p_permission: permission || null
      });

    if (error) {
      console.error('Error al verificar permisos con RPC:', error);

      // Fallback a la vista si la RPC falla
      console.log('Intentando fallback a la vista site_users_with_permissions');
      try {
        // Verificar si es admin primero
        const { data: adminData, error: adminError } = await supabase
          .from('site_users_with_permissions')
          .select('user_id')
          .eq('user_id', userId)
          .eq('site_id', siteId)
          .contains('permissions', ['admin']);

        if (!adminError && adminData && adminData.length > 0) {
          return true;
        }

        // Si no es admin y se especificó un permiso, verificar ese permiso
        if (permission) {
          const { data: permData, error: permError } = await supabase
            .from('site_users_with_permissions')
            .select('user_id')
            .eq('user_id', userId)
            .eq('site_id', siteId)
            .contains('permissions', [permission]);

          if (permError) {
            console.error(`Error en fallback al verificar permiso '${permission}':`, permError);
            return false;
          }

          return permData && permData.length > 0;
        }

        // Si no se especificó permiso, verificar si tiene acceso al sitio
        const { data: siteData, error: siteError } = await supabase
          .from('site_users')
          .select('id')
          .eq('user_id', userId)
          .eq('site_id', siteId);

        if (siteError) {
          console.error('Error en fallback al verificar acceso al sitio:', siteError);
          return false;
        }

        return siteData && siteData.length > 0;
      } catch (fallbackError) {
        console.error('Error en fallback de verificación de permisos:', fallbackError);
        return false;
      }
    }

    return hasPermission === true;
  } catch (error) {
    console.error('Error al verificar permisos de sitio:', error);
    return false;
  }
}

/**
 * Verifica si un usuario es propietario de un cliente
 * @param supabase Cliente de Supabase
 * @param userId ID del usuario
 * @param clientId ID del cliente
 * @returns true si el usuario es propietario del cliente, false en caso contrario
 */
export async function isClientOwner(
  supabase: SupabaseClient,
  userId: string,
  clientId: string
): Promise<boolean> {
  try {
    // Buscar si el usuario tiene permisos de propietario para el cliente
    const { data, error } = await supabase
      .from('client_permissions')
      .select('*')
      .eq('user_id', userId)
      .eq('client_id', clientId)
      .eq('role', 'owner');

    if (error) {
      console.error('Error al verificar propiedad del cliente:', error);
      return false;
    }

    // Si hay al menos un registro, el usuario es propietario del cliente
    return data && data.length > 0;
  } catch (error) {
    console.error('Error al verificar propiedad del cliente:', error);
    return false;
  }
}

/**
 * Obtiene los sitios a los que un usuario tiene acceso
 * @param supabase Cliente de Supabase
 * @param userId ID del usuario
 * @returns Array de sitios a los que el usuario tiene acceso
 */
export async function getUserSites(supabase: SupabaseClient, userId: string): Promise<any[]> {
  try {
    // Buscar los sitios a los que el usuario tiene acceso
    const { data, error } = await supabase
      .from('site_users_with_permissions')
      .select('site_id, site_name, site_domain, permissions')
      .eq('user_id', userId);

    if (error) {
      console.error('Error al obtener sitios del usuario:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error al obtener sitios del usuario:', error);
    return [];
  }
}

/**
 * Obtiene el sitio predeterminado para un usuario
 * @param supabase Cliente de Supabase
 * @param userId ID del usuario
 * @returns El sitio predeterminado del usuario o null si no tiene ninguno
 */
export async function getUserDefaultSite(supabase: SupabaseClient, userId: string): Promise<any | null> {
  try {
    // Obtener todos los sitios del usuario
    const sites = await getUserSites(supabase, userId);

    if (!sites || sites.length === 0) {
      return null;
    }

    // Por ahora, simplemente devolvemos el primer sitio
    // En el futuro, podríamos implementar una lógica para determinar el sitio predeterminado
    // basada en preferencias del usuario o en el último sitio visitado
    return sites[0];
  } catch (error) {
    console.error('Error al obtener el sitio predeterminado del usuario:', error);
    return null;
  }
}
