/**
 * Store centralizado para la gestión del estado de autenticación
 * Este archivo proporciona un store global para manejar el estado de autenticación
 * y asegurar que todos los componentes tengan acceso al mismo estado.
 */
import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';
import type { User, Session, SupabaseClient } from '@supabase/supabase-js';
import { supabase as globalSupabaseClient, getSupabaseClient } from '$lib/supabase.client';

// Asegurarse de que el cliente de Supabase esté disponible
const getActiveClient = () => {
  if (!browser) return null;
  return globalSupabaseClient || getSupabaseClient();
};

// Tipos
interface AuthState {
  user: User | null;
  session: Session | null;
  isAdmin: boolean;
  isLoading: boolean;
  initialized: boolean;
}

// Estado inicial
const initialState: AuthState = {
  user: null,
  session: null,
  isAdmin: false,
  isLoading: true,
  initialized: false
};

// Crear el store
function createAuthStore() {
  const { subscribe, set, update: originalUpdate } = writable<AuthState>(initialState);
  const storeInstanceId = Math.random().toString(36).substring(2, 7);
  console.log(`[AUTH STORE ${storeInstanceId}] Instance created. Supabase client from import:`, globalSupabaseClient);

  // Función de actualización personalizada para evitar actualizaciones innecesarias
  const update = (updater: (state: AuthState) => AuthState) => {
    const currentTimestamp = new Date().toISOString();
    console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] Update called.`);
    // Obtener el estado actual
    const currentState = get({ subscribe });

    // Calcular el nuevo estado
    const newState = updater(currentState);

    // Verificar si hay cambios significativos antes de actualizar
    const hasUserChanged =
      (currentState.user?.id !== newState.user?.id) ||
      (!!currentState.user !== !!newState.user);

    const hasSessionChanged =
      (currentState.session?.access_token !== newState.session?.access_token) ||
      (!!currentState.session !== !!newState.session);

    const hasAdminStatusChanged = currentState.isAdmin !== newState.isAdmin;
    const hasLoadingChanged = currentState.isLoading !== newState.isLoading;
    const hasInitializedChanged = currentState.initialized !== newState.initialized;

    console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] Changes - User: ${hasUserChanged}, Session: ${hasSessionChanged}, Admin: ${hasAdminStatusChanged}, Loading: ${hasLoadingChanged}, Initialized: ${hasInitializedChanged}`);
    // Si hay cambios, actualizar el store
    if (hasUserChanged || hasSessionChanged || hasAdminStatusChanged ||
      hasLoadingChanged || hasInitializedChanged) {
      console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] Applying update. New state:`, JSON.parse(JSON.stringify(newState))); // Deep copy for logging
      originalUpdate(updater);
    } else {
      console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] No significant changes, update skipped.`);
    }
  };

  return {
    subscribe,
    update, // Exponer la función update para uso interno

    // Inicializar el store con datos de sesión
    initialize: async (providedSupabaseClient: SupabaseClient | null = null) => {
      const currentTimestamp = new Date().toISOString();
      const activeSupabaseClient = providedSupabaseClient || globalSupabaseClient;
      console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] INITIALIZE method called. Using Supabase client:`, activeSupabaseClient, "Provided client:", providedSupabaseClient);

      if (!browser) {
        console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] INITIALIZE: Not in browser, returning.`);
        return;
      }

      // Allow re-initialization if a new supabase client is provided, or if not yet initialized.
      // This helps if navigating and the +layout.ts provides an updated client from new server data.
      const currentStoreState = get({ subscribe });
      if (currentStoreState.initialized && currentStoreState.user && !providedSupabaseClient) {
        // If already initialized with a user and no new client is given, assume it's a redundant call.
        console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] INITIALIZE: Already initialized with user and no new client provided. Skipping full re-init to prevent state flicker, but will run Phase 2 check.`);
        // Still run Phase 2 to ensure admin status and user validity are checked, but don't reset isLoading for Phase 1.
      } else {
        console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] Initializing store (Phase 1 - Quick from getSession). Current initialized state: ${currentStoreState.initialized}`);
        update(state => ({ ...state, isLoading: true, initialized: false })); // Set initialized to false to allow full run

        console.time(`[AUTH STORE ${storeInstanceId}] Phase 1 getSession`);
        try {
          const { data: { session: initialSession }, error: sessionError } = await activeSupabaseClient.auth.getSession();
          console.timeEnd(`[AUTH STORE ${storeInstanceId}] Phase 1 getSession`);
          const phase1Timestamp = new Date().toISOString();

          if (sessionError) {
            console.error(`[AUTH STORE ${storeInstanceId} - ${phase1Timestamp}] Error in getSession during Phase 1:`, sessionError);
            update(state => ({ ...state, user: null, session: null, isAdmin: false, isLoading: false, initialized: true })); // Mark initialized
            return;
          }

          if (initialSession && initialSession.user) {
            console.log(`[AUTH STORE ${storeInstanceId} - ${phase1Timestamp}] Phase 1: User found from getSession:`, initialSession.user.id, 'Session:', initialSession);
            update(state => ({
              ...state,
              user: initialSession.user,
              session: initialSession,
              isLoading: false, // <<< Set isLoading to false quickly
              // isAdmin will be updated in Phase 2
            }));
          } else {
            console.log(`[AUTH STORE ${storeInstanceId} - ${phase1Timestamp}] Phase 1: No active session from getSession.`);
            update(state => ({ ...state, user: null, session: null, isAdmin: false, isLoading: false, initialized: true })); // Mark initialized even if no session
          }
        } catch (e) {
          const catchTimestamp = new Date().toISOString();
          console.error(`[AUTH STORE ${storeInstanceId} - ${catchTimestamp}] Exception in Phase 1 initialize:`, e);
          update(state => ({ ...state, user: null, session: null, isAdmin: false, isLoading: false, initialized: true })); // Mark initialized
        }
        // End of new Phase 1 block if currentStoreState.initialized was false or client provided
        update(state => ({ ...state, initialized: true })); // Ensure initialized is true after Phase 1 attempts

        // Phase 2: Deeper validation and admin check (non-blocking for initial load)
        (async () => {
          const phase2StartTimestamp = new Date().toISOString();
          console.log(`[AUTH STORE ${storeInstanceId} - ${phase2StartTimestamp}] Starting Phase 2: getUser and admin check...`);
          // isLoading should be true here from Phase 1 start, or set by onAuthStateChange concurrently

          try {
            // Attempt to get the authoritative user
            console.time(`[AUTH STORE ${storeInstanceId}] Phase 2 activeSupabaseClient.auth.getUser`);
            const { data: { user: authoritativeUserFromGetUser }, error: getUserError } = await activeSupabaseClient.auth.getUser();
            console.timeEnd(`[AUTH STORE ${storeInstanceId}] Phase 2 activeSupabaseClient.auth.getUser`);
            const getUserTimestamp = new Date().toISOString();

            let userForAdminCheck: User | null = null;
            // Preserve session from Phase 1 initially, onAuthStateChange might update it concurrently
            let sessionForUpdate: Session | null = get({ subscribe }).session;

            if (getUserError) {
              console.error(`[AUTH STORE ${storeInstanceId} - ${getUserTimestamp}] Error in activeSupabaseClient.auth.getUser() during Phase 2:`, getUserError);
              const userFromPhase1 = get({ subscribe }).user; // Check store state from Phase 1
              if (userFromPhase1) {
                console.warn(`[AUTH STORE ${storeInstanceId} - ${getUserTimestamp}] Phase 2 activeSupabaseClient.auth.getUser() failed, but Phase 1 had a user (${userFromPhase1.id}). Proceeding with Phase 1 user for admin check and state.`);
                userForAdminCheck = userFromPhase1;
                // sessionForUpdate already holds the session from Phase 1 (via get({subscribe}).session)
              } else {
                console.log(`[AUTH STORE ${storeInstanceId} - ${getUserTimestamp}] Phase 2 activeSupabaseClient.auth.getUser() error: No user from Phase 1 in store either. Clearing session from store if any.`);
                update(state => ({ ...state, user: null, session: null, isAdmin: false, isLoading: false, initialized: true }));
                return; // Stop Phase 2 if no user source at all
              }
            } else if (authoritativeUserFromGetUser) {
              console.log(`[AUTH STORE ${storeInstanceId} - ${getUserTimestamp}] Phase 2: Authoritative user from activeSupabaseClient.auth.getUser():`, authoritativeUserFromGetUser.id);
              userForAdminCheck = authoritativeUserFromGetUser;
              // The session object from Phase 1 is likely still the most relevant one here for the store,
              // as getUser() primarily returns user details. onAuthStateChange handles session object updates.
            } else {
              // No user from getUser and no error implies user is logged out according to the client.
              const noAuthUserTimestamp = new Date().toISOString();
              console.log(`[AUTH STORE ${storeInstanceId} - ${noAuthUserTimestamp}] Phase 2: No authoritative user from activeSupabaseClient.auth.getUser() (and no error). Client indicates user is logged out. Clearing session from store if inconsistent.`);
              if (get({ subscribe }).user) { // If Phase 1 had a user (in store) but getUser now says no user
                update(state => ({ ...state, user: null, session: null, isAdmin: false, isLoading: false, initialized: true }));
              } else {
                // Store is already consistent with no user, ensure isLoading is false.
                update(state => ({ ...state, isLoading: false, initialized: true }));
              }
              return; // Stop Phase 2
            }

            if (userForAdminCheck) {
              const adminCheckStartTimestamp = new Date().toISOString();
              console.log(`[AUTH STORE ${storeInstanceId} - ${adminCheckStartTimestamp}] Phase 2: Proceeding with admin check for user:`, userForAdminCheck.id);
              let currentIsAdmin = false;
              console.time(`[AUTH STORE ${storeInstanceId}] Phase 2 adminCheck DB query`);
              try {
                const { data: adminData, error: adminError } = await activeSupabaseClient
                  .from('site_users_with_permissions')
                  .select('user_id, permissions') // Select only what's needed
                  .eq('user_id', userForAdminCheck.id)
                  .contains('permissions', ['admin']);
                console.timeEnd(`[AUTH STORE ${storeInstanceId}] Phase 2 adminCheck DB query`);
                const adminCheckTimestamp = new Date().toISOString();

                if (adminError) {
                  console.error(`[AUTH STORE ${storeInstanceId} - ${adminCheckTimestamp}] Error verifying admin in Phase 2:`, adminError);
                } else {
                  currentIsAdmin = Boolean(adminData && adminData.length > 0);
                  console.log(`[AUTH STORE ${storeInstanceId} - ${adminCheckTimestamp}] Phase 2: Admin check result:`, currentIsAdmin, 'Data:', adminData);
                }
              } catch (adminException) {
                const adminExTimestamp = new Date().toISOString();
                console.error(`[AUTH STORE ${storeInstanceId} - ${adminExTimestamp}] Exception verifying admin in Phase 2:`, adminException);
              }

              const phase2UpdateTimestamp = new Date().toISOString();
              console.log(`[AUTH STORE ${storeInstanceId} - ${phase2UpdateTimestamp}] Phase 2: Preparing to update state with user and admin status.`);
              update(state => ({
                ...state,
                user: userForAdminCheck, // Use the user we decided on (either from getUser or fallback from Phase 1)
                session: sessionForUpdate, // Session from Phase 1 (or as updated by onAuthStateChange)
                isAdmin: currentIsAdmin,
                isLoading: false // Ensure loading is false at the end of successful Phase 2 processing
              }));
              console.log(`[AUTH STORE ${storeInstanceId} - ${phase2UpdateTimestamp}] Phase 2 complete. User:`, userForAdminCheck.id, 'IsAdmin:', currentIsAdmin);
            } else {
              // This case implies userForAdminCheck was null, meaning Phase 2 decided there's no valid user.
              const noUserForAdminTs = new Date().toISOString();
              console.log(`[AUTH STORE ${storeInstanceId} - ${noUserForAdminTs}] Phase 2: No user available for admin check. Phase 2 did not complete with user data. Setting isLoading to false.`);
              update(state => ({ ...state, isLoading: false, initialized: true })); // Ensure isLoading is false and mark initialized
            }
          } catch (phase2Error) {
            const phase2ErrorTimestamp = new Date().toISOString();
            console.error(`[AUTH STORE ${storeInstanceId} - ${phase2ErrorTimestamp}] Exception in Phase 2 processing:`, phase2Error);
            update(state => ({ ...state, user: null, session: null, isAdmin: false, isLoading: false, initialized: true }));
          }
        })(); // IIFE to run async code without blocking
      }
    },

    // Cache para permisos de administrador
    adminPermissionsCache: new Map<string, { isAdmin: boolean, timestamp: number }>(),
    ADMIN_CACHE_DURATION: 10 * 60 * 1000, // 10 minutos en milisegundos

    // Actualizar el store con datos de sesión
    setSession: (session: Session | null, user: User | null) => {
      const currentTimestamp = new Date().toISOString();
      console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] SET_SESSION called. Session:`, session, 'User:', user);
      // Actualizar inmediatamente el estado básico
      update(state => ({ ...state, session, user, isLoading: false })); // Set isLoading to false

      // Si hay un usuario, verificar permisos de administrador (usando caché)
      if (user && user.id) {
        const userId = user.id;
        const now = Date.now();
        const cachedPermission = authStore.adminPermissionsCache.get(userId);

        // Si hay datos en caché y no han expirado, usarlos
        if (cachedPermission && (now - cachedPermission.timestamp < authStore.ADMIN_CACHE_DURATION)) {
          console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] SET_SESSION: Admin status for ${userId} from cache: ${cachedPermission.isAdmin}`);
          update(state => ({ ...state, isAdmin: cachedPermission.isAdmin }));
        } else {
          console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] SET_SESSION: Admin status for ${userId} not in cache or expired. Fetching...`);
          // Usar getActiveClient para asegurar que tenemos un cliente válido
          const client = getActiveClient();
          if (!client) {
            console.error(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] SET_SESSION: No client available for admin check.`);
            return;
          }

          client
            .from('site_users_with_permissions')
            .select('user_id, permissions') // Corrected: select only needed fields
            .eq('user_id', userId)
            .contains('permissions', ['admin'])
            .then(({ data, error }) => {
              if (!error) {
                const isAdmin = Boolean(data && data.length > 0);

                // Guardar en caché
                console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] SET_SESSION: Fetched admin status for ${userId}: ${isAdmin}. Caching.`);
                authStore.adminPermissionsCache.set(userId, { isAdmin, timestamp: now });

                // Actualizar el store
                update(state => ({ ...state, isAdmin }));
              } else {
                console.error(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] SET_SESSION: Error fetching admin status for ${userId}:`, error);
              }
            });
        }
      } else {
        console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] SET_SESSION: No user provided, setting isAdmin to false.`);
        update(state => ({ ...state, isAdmin: false }));
      }
    },

    // Limpiar el store (logout)
    clearSession: () => {
      const currentTimestamp = new Date().toISOString();
      console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] CLEAR_SESSION called.`);

      // Actualizar el store con valores nulos
      update(state => ({
        ...state,
        user: null,
        session: null,
        isAdmin: false
      }));

      // Forzar una verificación del estado de autenticación después de limpiar
      setTimeout(() => {
        const timeoutTimestamp = new Date().toISOString();
        console.log(`[AUTH STORE ${storeInstanceId} - ${timeoutTimestamp}] CLEAR_SESSION: Verifying auth status after 100ms delay.`);
        authStore.checkAuthStatus();
      }, 100);
    },

    // Verificar el estado de autenticación
    checkAuthStatus: async (providedSupabaseClient: SupabaseClient | null = null) => {
      const currentTimestamp = new Date().toISOString();
      const activeSupabaseClient = providedSupabaseClient || getActiveClient();
      console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] CHECK_AUTH_STATUS called. Using Supabase client:`, activeSupabaseClient, "Provided client:", providedSupabaseClient);
      if (!browser || !activeSupabaseClient) {
        console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] CHECK_AUTH_STATUS: Not in browser or no client available, returning.`);
        return;
      }
      update(state => ({ ...state, isLoading: true }));
      console.time(`[AUTH STORE ${storeInstanceId}] checkAuthStatus getSession`);

      try {
        // Obtener sesión
        const { data: { session }, error: sessionError } = await activeSupabaseClient.auth.getSession();
        console.timeEnd(`[AUTH STORE ${storeInstanceId}] checkAuthStatus getSession`);
        const getSessionTimestamp = new Date().toISOString();

        // Si no hay sesión válida, el usuario no está autenticado
        if (sessionError || !session) {
          console.log(`[AUTH STORE ${storeInstanceId} - ${getSessionTimestamp}] CHECK_AUTH_STATUS: No session or error. Error:`, sessionError, 'Session:', session);
          update(state => ({
            ...state,
            user: null,
            session: null,
            isAdmin: false,
            isLoading: false
          }));
          return;
        }

        // Si hay una sesión válida, actualizar el store
        const user = session.user;
        console.log(`[AUTH STORE ${storeInstanceId} - ${getSessionTimestamp}] CHECK_AUTH_STATUS: Session valid. User:`, user.id, 'Session:', session);

        // Verificar permisos de administrador (usando caché)
        const userId = user.id;
        const now = Date.now();
        const cachedPermission = authStore.adminPermissionsCache.get(userId);

        let isAdmin = false;

        if (cachedPermission && (now - cachedPermission.timestamp < authStore.ADMIN_CACHE_DURATION)) {
          isAdmin = cachedPermission.isAdmin;
          console.log(`[AUTH STORE ${storeInstanceId} - ${getSessionTimestamp}] CHECK_AUTH_STATUS: Admin status for ${userId} from cache: ${isAdmin}`);
        } else {
          console.log(`[AUTH STORE ${storeInstanceId} - ${getSessionTimestamp}] CHECK_AUTH_STATUS: Admin status for ${userId} not in cache or expired. Fetching...`);
          console.time(`[AUTH STORE ${storeInstanceId}] checkAuthStatus adminCheck`);
          const { data: adminData, error: adminError } = await activeSupabaseClient
            .from('site_users_with_permissions')
            .select('user_id, permissions') // Corrected: select only needed fields
            .eq('user_id', userId)
            .contains('permissions', ['admin']);
          console.timeEnd(`[AUTH STORE ${storeInstanceId}] checkAuthStatus adminCheck`);
          const adminCheckTimestamp = new Date().toISOString();

          if (adminError) {
            console.error(`[AUTH STORE ${storeInstanceId} - ${adminCheckTimestamp}] CHECK_AUTH_STATUS: Error verifying admin:`, adminError);
          } else {
            isAdmin = Boolean(adminData && adminData.length > 0);
            console.log(`[AUTH STORE ${storeInstanceId} - ${adminCheckTimestamp}] CHECK_AUTH_STATUS: Fetched admin status for ${userId}: ${isAdmin}. Caching.`);
            authStore.adminPermissionsCache.set(userId, { isAdmin, timestamp: now });
          }
        }
        console.log(`[AUTH STORE ${storeInstanceId} - ${new Date().toISOString()}] CHECK_AUTH_STATUS: Updating state with User:`, user.id, 'IsAdmin:', isAdmin, 'Session:', session);
        update(state => ({
          ...state,
          user,
          session,
          isAdmin,
          isLoading: false,
          initialized: true
        }));

      } catch (e) {
        const catchTimestamp = new Date().toISOString();
        console.error(`[AUTH STORE ${storeInstanceId} - ${catchTimestamp}] CHECK_AUTH_STATUS: Exception:`, e);
        update(state => ({
          ...state,
          user: null,
          session: null,
          isAdmin: false,
          isLoading: false
        }));
      }
    },

    // Forzar sincronización de la sesión (útil después de iniciar sesión)
    forceSync: async () => {
      const currentTimestamp = new Date().toISOString();
      console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] FORCE_SYNC called.`);

      if (!browser) {
        console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] FORCE_SYNC: Not in browser, returning.`);
        return;
      }

      // Crear un cliente fresco para asegurar que tenemos los datos más recientes
      const freshClient = getSupabaseClient();
      if (!freshClient) {
        console.error(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] FORCE_SYNC: Could not create fresh client.`);
        return;
      }

      console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] FORCE_SYNC: Created fresh client.`);

      // Actualizar el estado con el nuevo cliente
      return authStore.checkAuthStatus(freshClient);
    }
  };
}

// Exportar la instancia del store
export const authStore = createAuthStore();

// Suscriptor para onAuthStateChange
if (browser && globalSupabaseClient) {
  console.log(`[AUTH STORE GLOBAL] Setting up onAuthStateChange listener. Supabase client from import:`, globalSupabaseClient);
  globalSupabaseClient.auth.onAuthStateChange(async (event, session) => {
    const currentTimestamp = new Date().toISOString();
    const storeInstanceId = 'GLOBAL_LISTENER'; // Differentiate from store instance ID if needed
    console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] onAuthStateChange event: ${event}`, 'Session:', session);

    if (session && session.user) {
      console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] onAuthStateChange: User found:`, session.user.id);
      // Check admin status more carefully here
      let isAdmin = false;
      const userId = session.user.id;
      const now = Date.now();
      const cachedPermission = authStore.adminPermissionsCache.get(userId); // Access through exported store

      if (cachedPermission && (now - cachedPermission.timestamp < authStore.ADMIN_CACHE_DURATION)) {
        isAdmin = cachedPermission.isAdmin;
        console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] onAuthStateChange: Admin status for ${userId} from cache: ${isAdmin}`);
      } else {
        console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] onAuthStateChange: Admin status for ${userId} not in cache or expired. Fetching...`);
        console.time(`[AUTH STORE ${storeInstanceId}] onAuthStateChange adminCheck`);
        try {
          // Usar getActiveClient para asegurar que tenemos un cliente válido
          const client = getActiveClient();
          if (!client) {
            console.error(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] onAuthStateChange: No client available for admin check.`);
            return;
          }

          const { data: adminData, error: adminError } = await client
            .from('site_users_with_permissions')
            .select('user_id, permissions')
            .eq('user_id', userId)
            .contains('permissions', ['admin']);
          console.timeEnd(`[AUTH STORE ${storeInstanceId}] onAuthStateChange adminCheck`);
          const adminCheckTimestamp = new Date().toISOString();

          if (adminError) {
            console.error(`[AUTH STORE ${storeInstanceId} - ${adminCheckTimestamp}] onAuthStateChange: Error verifying admin:`, adminError);
          } else {
            isAdmin = Boolean(adminData && adminData.length > 0);
            console.log(`[AUTH STORE ${storeInstanceId} - ${adminCheckTimestamp}] onAuthStateChange: Fetched admin status for ${userId}: ${isAdmin}. Caching.`);
            authStore.adminPermissionsCache.set(userId, { isAdmin, timestamp: now }); // Access through exported store
          }
        } catch (adminEx) {
          const adminExTimestamp = new Date().toISOString();
          console.error(`[AUTH STORE ${storeInstanceId} - ${adminExTimestamp}] onAuthStateChange: Exception verifying admin:`, adminEx);
        }
      }
      console.log(`[AUTH STORE ${storeInstanceId} - ${new Date().toISOString()}] onAuthStateChange: Updating store with User:`, session.user.id, 'IsAdmin:', isAdmin, 'Session:', session);
      authStore.update(state => ({
        ...state,
        user: session.user,
        session: session,
        isAdmin: isAdmin, // Set admin status derived here
        isLoading: false,
        initialized: true // Mark as initialized
      }));
    } else if (event === 'SIGNED_OUT' || (event as string) === 'USER_DELETED' || (event as string) === 'BYPASSED_MFA' || !session) {
      console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] onAuthStateChange: User signed out, session cleared, or no session. Event: ${event}`);
      authStore.update(state => ({ ...initialState, isLoading: false, initialized: true })); // Reset to initial, mark initialized
    } else if (event === 'INITIAL_SESSION') {
      console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] onAuthStateChange: INITIAL_SESSION event. Session:`, session, "User:", session?.user?.id);
      if (session && session.user) {
        let isAdmin = false;
        const userId = session.user.id;
        const now = Date.now();
        const cachedPermission = authStore.adminPermissionsCache.get(userId);

        if (cachedPermission && (now - cachedPermission.timestamp < authStore.ADMIN_CACHE_DURATION)) {
          isAdmin = cachedPermission.isAdmin;
        } else {
          try {
            // Usar getActiveClient para asegurar que tenemos un cliente válido
            const client = getActiveClient();
            if (!client) {
              console.error(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] onAuthStateChange (INITIAL_SESSION): No client available for admin check.`);
              return;
            }

            const { data: adminData, error: adminError } = await client
              .from('site_users_with_permissions')
              .select('user_id, permissions')
              .eq('user_id', userId)
              .contains('permissions', ['admin']);
            if (!adminError && adminData && adminData.length > 0) {
              isAdmin = true;
              authStore.adminPermissionsCache.set(userId, { isAdmin, timestamp: now });
            }
          } catch (e) { console.warn(`[AUTH STORE ${storeInstanceId} - ${new Date().toISOString()}] onAuthStateChange (INITIAL_SESSION): Failed to check admin status for ${userId}`, e) /* ignore */ }
        }
        console.log(`[AUTH STORE ${storeInstanceId} - ${new Date().toISOString()}] onAuthStateChange (INITIAL_SESSION): Updating store with User:`, session.user.id, 'IsAdmin:', isAdmin, 'Session:', session);
        authStore.update(state => ({
          ...state,
          user: session.user,
          session: session,
          isAdmin: isAdmin,
          isLoading: false, // Potentially set isLoading to false earlier
          initialized: true
        }));
      } else {
        console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] onAuthStateChange: INITIAL_SESSION event with no user/session. Doing nothing, ensuring initialized.`);
        authStore.update(state => ({ ...state, user: null, session: null, isAdmin: false, isLoading: false, initialized: true }));
      }
    } else {
      console.log(`[AUTH STORE ${storeInstanceId} - ${currentTimestamp}] onAuthStateChange: Unhandled event or state. Event: ${event}`, 'Session:', session);
    }
  });
}

// Derivados para acceso fácil (Authoritative single definition)
export const user = derived(authStore, $s => $s.user);
export const session = derived(authStore, $s => $s.session);
export const isAdmin = derived(authStore, $s => $s.isAdmin);
export const isLoading = derived(authStore, $s => $s.isLoading);
export const initialized = derived(authStore, $s => $s.initialized);

// Funciones de utilidad (Authoritative single definition)
export const getAuthUser = () => get(user);
export const getIsAdmin = () => get(isAdmin);
export const getIsLoading = () => get(isLoading);
export const getInitialized = () => get(initialized);

console.log(`[AUTH STORE GLOBAL] authStore object created/imported. User derived store created:`, user);
console.log(`[AUTH STORE GLOBAL] isAdmin derived store created:`, isAdmin);
console.log(`[AUTH STORE GLOBAL] isLoading derived store created:`, isLoading);
console.log(`[AUTH STORE GLOBAL] initialized derived store created:`, initialized);

// TODO: Analizar si la lógica de `setSession` y `checkAuthStatus` es redundante
// con `onAuthStateChange` y `initialize`. Podría simplificarse.
// TODO: El manejo de `isLoading` y `initialized` necesita ser consistente
// a través de todas las funciones que modifican el estado.
