/**
 * Utilidades para la gestión de cookies de autenticación
 */
import type { Cookies } from '@sveltejs/kit';

/**
 * Elimina todas las cookies relacionadas con la autenticación de Supabase
 * @param cookies Objeto Cookies de SvelteKit
 */
export function clearSupabaseAuthCookies(cookies: Cookies): void {
  console.log('[COOKIE UTILS SERVER] Iniciando limpieza de cookies de autenticación en el servidor');

  const allCookies = cookies.getAll();
  console.log('[COOKIE UTILS SERVER] Cookies antes de limpiar:', allCookies.map(c => c.name));

  // Lista de patrones de cookies a eliminar
  const cookiePatterns = [
    'sb-',                // Cookies de Supabase que empiezan con sb-
    '-auth-token',        // Tokens de autenticación
    'supabase-auth',      // Otras cookies de autenticación de Supabase
    'access_token',       // Token de acceso
    'refresh_token',      // Token de refresco
    'auth-token',         // Otro formato de token de autenticación
    'auth.',              // Cookies que empiezan con auth.
    'supabase'            // Cualquier cookie relacionada con Supabase
  ];

  // Eliminar todas las cookies que coincidan con los patrones
  for (const cookie of allCookies) {
    // Verificar si la cookie coincide con alguno de los patrones
    const shouldRemove = cookiePatterns.some(pattern =>
      cookie.name.includes(pattern)
    );

    if (shouldRemove) {
      console.log('[COOKIE UTILS SERVER] Eliminando cookie:', cookie.name);

      // Eliminar la cookie con diferentes paths para asegurar la eliminación
      cookies.delete(cookie.name, { path: '/' });
      cookies.delete(cookie.name, { path: '/auth' });
      cookies.delete(cookie.name, { path: '/private' });
      cookies.delete(cookie.name, { path: '/admin' });
    }
  }

  // Verificar si la limpieza fue exitosa
  const remainingCookies = cookies.getAll();
  console.log('[COOKIE UTILS SERVER] Cookies después de limpiar:', remainingCookies.map(c => c.name));
}

/**
 * Script para eliminar cookies de autenticación en el navegador
 * Esta función debe ejecutarse en el cliente
 */
export function clearBrowserAuthCookies(): void {
  console.log('[COOKIE UTILS] Iniciando limpieza de cookies y localStorage en el navegador');

  // Obtener todas las cookies
  const cookies = document.cookie.split(';');
  console.log('[COOKIE UTILS] Cookies antes de limpiar:', cookies);

  // Lista de patrones de cookies a eliminar
  const cookiePatterns = [
    'sb-',                // Cookies de Supabase que empiezan con sb-
    '-auth-token',        // Tokens de autenticación
    'supabase-auth',      // Otras cookies de autenticación de Supabase
    'access_token',       // Token de acceso
    'refresh_token',      // Token de refresco
    'auth-token',         // Otro formato de token de autenticación
    'auth.',              // Cookies que empiezan con auth.
    'supabase'            // Cualquier cookie relacionada con Supabase
  ];

  // Encontrar y eliminar todas las cookies de autenticación
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim();
    const cookieName = cookie.split('=')[0].trim();

    // Verificar si la cookie coincide con alguno de los patrones
    const shouldRemove = cookiePatterns.some(pattern =>
      cookieName.includes(pattern) || cookie.includes(pattern)
    );

    if (shouldRemove) {
      console.log('[COOKIE UTILS] Eliminando cookie:', cookieName);

      // Eliminar la cookie con diferentes combinaciones de path y domain
      // para asegurar que se elimine correctamente
      const paths = ['/', '/auth', '/private', '/admin'];
      const domains = [
        window.location.hostname,
        `.${window.location.hostname}`,
        window.location.hostname.split('.').slice(1).join('.')
      ];

      // Intentar todas las combinaciones posibles
      paths.forEach(path => {
        // Sin dominio
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path};`;

        // Con diferentes dominios
        domains.forEach(domain => {
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain};`;
        });
      });
    }
  }

  // Limpiar elementos de localStorage relacionados con Supabase
  if (typeof localStorage !== 'undefined') {
    const keysToRemove = [];
    const localStoragePatterns = [
      'sb-',
      'supabase',
      'auth',
      'token',
      'session'
    ];

    // Primero recopilar todas las claves a eliminar
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && localStoragePatterns.some(pattern => key.includes(pattern))) {
        keysToRemove.push(key);
      }
    }

    // Luego eliminar cada clave
    console.log('[COOKIE UTILS] Elementos de localStorage a eliminar:', keysToRemove);
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });
  }

  // Limpiar sessionStorage también
  if (typeof sessionStorage !== 'undefined') {
    const keysToRemove = [];
    const sessionStoragePatterns = [
      'sb-',
      'supabase',
      'auth',
      'token',
      'session'
    ];

    // Recopilar claves a eliminar
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && sessionStoragePatterns.some(pattern => key.includes(pattern))) {
        keysToRemove.push(key);
      }
    }

    // Eliminar claves
    console.log('[COOKIE UTILS] Elementos de sessionStorage a eliminar:', keysToRemove);
    keysToRemove.forEach(key => {
      sessionStorage.removeItem(key);
    });
  }

  // Verificar si la limpieza fue exitosa
  console.log('[COOKIE UTILS] Cookies después de limpiar:', document.cookie);
  console.log('[COOKIE UTILS] Limpieza de cookies, localStorage y sessionStorage completada');
}

/**
 * Realiza un logout completo, limpiando tanto el estado del cliente como las cookies del servidor
 * @param supabaseClient Cliente de Supabase para el navegador
 */
export async function performFullLogout(supabaseClient: any): Promise<void> {
  try {
    console.log('[COOKIE UTILS] Iniciando proceso de logout completo');

    // 1. Cerrar sesión en Supabase (cliente) con scope global
    const { error } = await supabaseClient.auth.signOut({ scope: 'global' });
    if (error) {
      console.error('[COOKIE UTILS] Error al cerrar sesión en Supabase:', error);
    } else {
      console.log('[COOKIE UTILS] Sesión cerrada correctamente en Supabase');
    }

    // 2. Limpiar cookies y localStorage en el navegador
    clearBrowserAuthCookies();

    // 3. Realizar una solicitud al endpoint de logout del servidor
    try {
      console.log('[COOKIE UTILS] Enviando solicitud de logout al servidor');
      const response = await fetch('/auth?/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        // Asegurar que se envíe la solicitud con credenciales
        credentials: 'include'
      });

      if (response.ok) {
        console.log('[COOKIE UTILS] Logout en servidor exitoso');
      } else {
        console.error('[COOKIE UTILS] Error en logout del servidor:', response.status);
      }
    } catch (serverError) {
      console.error('[COOKIE UTILS] Error al enviar solicitud de logout al servidor:', serverError);
    }

    // 4. Intentar un segundo método de limpieza de cookies
    try {
      // Intentar limpiar cookies usando un enfoque alternativo
      const allCookies = document.cookie.split(';');
      for (const cookie of allCookies) {
        const cookieName = cookie.split('=')[0].trim();
        console.log('[COOKIE UTILS] Eliminando cookie (método alternativo):', cookieName);

        // Eliminar con diferentes combinaciones
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`;
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname};`;
      }
    } catch (cookieError) {
      console.error('[COOKIE UTILS] Error en limpieza alternativa de cookies:', cookieError);
    }

    // 5. Forzar una recarga completa para asegurar que todas las cookies se limpien
    console.log('[COOKIE UTILS] Redirigiendo a /auth');
    if (typeof window !== 'undefined') {
      // Pequeño retraso para asegurar que todas las operaciones asíncronas se completen
      setTimeout(() => {
        // Usar location.replace en lugar de location.href para evitar que quede en el historial
        window.location.replace('/auth');
      }, 300);
    }
  } catch (error) {
    console.error('[COOKIE UTILS] Error durante el proceso de logout completo:', error);
    // Intentar la redirección incluso si hay un error
    if (typeof window !== 'undefined') {
      window.location.replace('/auth');
    }
  }
}
