/**
 * Configuración de Stripe (Servidor)
 * Este archivo proporciona una instancia de Stripe y funciones útiles para el servidor
 *
 * NOTA: Este archivo está obsoleto y se mantiene por compatibilidad.
 * Se recomienda usar los módulos en src/lib/stripe/ directamente.
 *
 * SOLO DEBE IMPORTARSE DESDE CÓDIGO DEL SERVIDOR
 */

// Re-exportar todo desde el nuevo módulo de Stripe (servidor)
export * from './stripe/index.server';

// Re-exportar la clase StripeService, el adaptador del servidor y el servicio del servidor
export { StripeService } from './stripe/service';
export { stripeServer } from './stripe/server';
export { stripeServerService } from './stripe/service.server';
