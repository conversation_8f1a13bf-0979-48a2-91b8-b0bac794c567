<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />

		<!-- Metaetiquetas para mejorar el rendimiento -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="color-scheme" content="light">
		<meta name="theme-color" content="#f8f7f4">
		<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
		<meta http-equiv="Cache-Control" content="max-age=3600">

		<!-- Optimización de carga de fuentes -->
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

		<!-- Precargar fuentes críticas -->
		<link rel="preload" as="font" href="https://fonts.gstatic.com/s/montserrat/v25/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCs16Hw5aXp-p7K4KLg.woff2" crossorigin>
		<link rel="preload" as="font" href="https://fonts.gstatic.com/s/ebgaramond/v27/SlGDmQSNjdsmc35JDF1K5E55YMjF_7DPuGi-6_RUA4V-p7K4KLg.woff2" crossorigin>

		<!-- Cargar fuentes con display=swap para evitar FOIT -->
		<link href="https://fonts.googleapis.com/css2?family=EB+Garamond:ital,wght@0,400;0,500;0,600;1,400;1,500;1,600&family=Montserrat:wght@300;400;500;600&display=swap" rel="stylesheet">

		<!-- Font-display fallback en CSS -->
		<style>
			/* Fallback para fuentes mientras cargan */
			.font-eb-garamond {
				font-family: 'EB Garamond', Georgia, serif;
				font-display: swap;
			}
			.font-montserrat {
				font-family: 'Montserrat', system-ui, sans-serif;
				font-display: swap;
			}
		</style>

		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>
