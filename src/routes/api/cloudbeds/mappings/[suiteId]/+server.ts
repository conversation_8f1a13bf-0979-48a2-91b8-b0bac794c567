import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { hasSitePermission } from '$lib/auth';

/**
 * Get Cloudbeds mapping for a suite
 */
export const GET: RequestHandler = async ({ params, locals }) => {
  // Check if user is authenticated
  if (!locals.session || !locals.user) {
    return json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }

  const { suiteId } = params;

  if (!suiteId) {
    return json({ success: false, error: 'Suite ID is required' }, { status: 400 });
  }

  try {
    // First, get the suite to check its site_id
    const { data: suite, error: suiteError } = await locals.supabase
      .from('suites')
      .select('site_id')
      .eq('id', suiteId)
      .single();
    
    if (suiteError) throw suiteError;
    
    if (!suite) {
      return json({ success: false, error: 'Suite not found' }, { status: 404 });
    }
    
    // Check if user has permission to view this site
    const hasPermission = await hasSitePermission(
      locals.supabase,
      locals.user.id,
      suite.site_id,
      'view_suites'
    );
    
    if (!hasPermission) {
      return json(
        { success: false, error: 'You do not have permission to view suites for this property' },
        { status: 403 }
      );
    }
    
    // Get the mapping
    const { data, error } = await locals.supabase
      .from('suite_cloudbeds_mapping')
      .select('*')
      .eq('suite_id', suiteId)
      .maybeSingle();
    
    if (error) throw error;
    
    return json({ success: true, data });
  } catch (err) {
    console.error('Error getting Cloudbeds mapping:', err);
    return json(
      { 
        success: false, 
        error: err instanceof Error ? err.message : 'An error occurred while getting the Cloudbeds mapping' 
      },
      { status: 500 }
    );
  }
};

/**
 * Create or update Cloudbeds mapping for a suite
 */
export const POST: RequestHandler = async ({ request, params, locals }) => {
  // Check if user is authenticated
  if (!locals.session || !locals.user) {
    return json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }

  const { suiteId } = params;

  if (!suiteId) {
    return json({ success: false, error: 'Suite ID is required' }, { status: 400 });
  }

  try {
    // Parse request body
    const data = await request.json();
    
    // First, get the suite to check its site_id
    const { data: suite, error: suiteError } = await locals.supabase
      .from('suites')
      .select('site_id')
      .eq('id', suiteId)
      .single();
    
    if (suiteError) throw suiteError;
    
    if (!suite) {
      return json({ success: false, error: 'Suite not found' }, { status: 404 });
    }
    
    // Check if user has permission to manage this site
    const hasPermission = await hasSitePermission(
      locals.supabase,
      locals.user.id,
      suite.site_id,
      'manage_suites'
    );
    
    if (!hasPermission) {
      return json(
        { success: false, error: 'You do not have permission to manage suites for this property' },
        { status: 403 }
      );
    }
    
    // Check if mapping already exists
    const { data: existingMapping, error: checkError } = await locals.supabase
      .from('suite_cloudbeds_mapping')
      .select('id')
      .eq('suite_id', suiteId)
      .maybeSingle();
    
    if (checkError) throw checkError;
    
    let result;
    
    if (existingMapping) {
      // Update existing mapping
      const { data: updatedMapping, error } = await locals.supabase
        .from('suite_cloudbeds_mapping')
        .update({
          cloudbeds_room_type_id: data.cloudbeds_room_type_id,
          cloudbeds_property_id: data.cloudbeds_property_id || '',
          sync_images: data.sync_images,
          sync_description: data.sync_description,
          sync_amenities: data.sync_amenities,
          sync_pricing: data.sync_pricing,
          sync_availability: data.sync_availability,
        })
        .eq('id', existingMapping.id)
        .select()
        .single();
      
      if (error) throw error;
      result = updatedMapping;
    } else {
      // Create new mapping
      const { data: newMapping, error } = await locals.supabase
        .from('suite_cloudbeds_mapping')
        .insert({
          suite_id: suiteId,
          cloudbeds_room_type_id: data.cloudbeds_room_type_id,
          cloudbeds_property_id: data.cloudbeds_property_id || '',
          sync_images: data.sync_images || false,
          sync_description: data.sync_description || false,
          sync_amenities: data.sync_amenities || false,
          sync_pricing: data.sync_pricing || false,
          sync_availability: data.sync_availability || false,
        })
        .select()
        .single();
      
      if (error) throw error;
      result = newMapping;
    }
    
    return json({ success: true, data: result });
  } catch (err) {
    console.error('Error saving Cloudbeds mapping:', err);
    return json(
      { 
        success: false, 
        error: err instanceof Error ? err.message : 'An error occurred while saving the Cloudbeds mapping' 
      },
      { status: 500 }
    );
  }
};
