import { error, json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';

/**
 * Image Proxy Server
 * 
 * This endpoint proxies image requests to external domains that may have CORS or accessibility issues,
 * particularly the baberrih.ma domain.
 * 
 * It fetches the image on the server side and returns it to the client, bypassing CORS restrictions
 * and providing better error handling.
 * 
 * Query parameters:
 * - url: The URL of the image to proxy (required, must be encoded)
 * - fallback: Optional fallback URL to use if the primary URL fails
 * 
 * Example usage:
 * /api/image-proxy?url=https%3A%2F%2Fbaberrih.ma%2Fmedia%2Frestaurant%2F1_f.webp
 */
export const GET: RequestHandler = async ({ url, fetch, setHeaders }) => {
  try {
    // Get the image URL from the query parameter
    const imageUrl = url.searchParams.get('url');
    const fallbackUrl = url.searchParams.get('fallback');
    
    // Validate the image URL
    if (!imageUrl) {
      throw error(400, 'Missing required parameter: url');
    }

    // Decode the URL if it's encoded
    const decodedUrl = decodeURIComponent(imageUrl);
    
    // Log the request for debugging
    console.log(`Image Proxy: Fetching image from ${decodedUrl}`);
    
    // Try to fetch the image
    let response: Response;
    let fetchSuccess = false;
    
    try {
      response = await fetch(decodedUrl, {
        headers: {
          // Set a user agent to avoid being blocked by some servers
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      
      // Check if the response is ok (status 200-299)
      if (response.ok) {
        fetchSuccess = true;
      } else {
        console.warn(`Image Proxy: Primary image fetch failed with status ${response.status} for ${decodedUrl}`);
      }
    } catch (fetchError) {
      console.error(`Image Proxy: Error fetching primary image from ${decodedUrl}:`, fetchError);
    }
    
    // If the primary image fetch failed and we have a fallback URL, try the fallback
    if (!fetchSuccess && fallbackUrl) {
      const decodedFallbackUrl = decodeURIComponent(fallbackUrl);
      console.log(`Image Proxy: Trying fallback image from ${decodedFallbackUrl}`);
      
      try {
        response = await fetch(decodedFallbackUrl);
        
        if (response.ok) {
          fetchSuccess = true;
          console.log(`Image Proxy: Successfully fetched fallback image from ${decodedFallbackUrl}`);
        } else {
          console.warn(`Image Proxy: Fallback image fetch failed with status ${response.status} for ${decodedFallbackUrl}`);
        }
      } catch (fallbackError) {
        console.error(`Image Proxy: Error fetching fallback image from ${decodedFallbackUrl}:`, fallbackError);
      }
    }
    
    // If both primary and fallback fetches failed, return an error
    if (!fetchSuccess) {
      throw error(404, 'Failed to fetch image from both primary and fallback URLs');
    }
    
    // Get the image data
    const imageData = await response.arrayBuffer();
    
    // Get the content type from the response headers
    const contentType = response.headers.get('content-type') || 'image/jpeg';
    
    // Set cache headers for better performance
    setHeaders({
      'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
      'Content-Type': contentType
    });
    
    // Return the image data
    return new Response(imageData, {
      headers: {
        'Content-Type': contentType
      }
    });
  } catch (err) {
    console.error('Image Proxy Error:', err);
    
    // Return a JSON error response
    return json({
      error: true,
      message: err instanceof Error ? err.message : 'Unknown error occurred'
    }, { status: err instanceof Error && 'status' in err ? err.status : 500 });
  }
};
