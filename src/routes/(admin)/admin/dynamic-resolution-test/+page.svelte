<script lang="ts">
  import { onMount } from "svelte";
  import { fade } from "svelte/transition";

  // State variables
  let roomTypeId = $state("");
  let roomName = $state("");
  let availableRoomTypes = $state<Array<{id: string; name: string}>>([]);
  let isLoading = $state<Record<string, boolean>>({});
  let apiErrors = $state<Record<string, string>>({});
  let resolutionResult = $state(null);

  // Load room types on mount
  onMount(() => {
    loadRoomTypes();
  });

  // Load available room types
  async function loadRoomTypes() {
    try {
      isLoading['roomTypes'] = true;
      apiErrors['roomTypes'] = "";

      const response = await fetch('/api/cloudbeds/room-types?includePhotos=0&includeAmenities=0');
      const data = await response.json();

      if (data.success && data.data) {
        availableRoomTypes = data.data.map(rt => ({
          id: rt.roomTypeID || rt.id,
          name: rt.roomTypeName || rt.name
        }));
      } else {
        apiErrors['roomTypes'] = data.error?.message || "Failed to load room types";
      }
    } catch (error) {
      apiErrors['roomTypes'] = error instanceof Error ? error.message : "Unknown error";
    } finally {
      isLoading['roomTypes'] = false;
    }
  }

  // Test dynamic room type resolution
  async function testDynamicResolution() {
    try {
      isLoading['resolution'] = true;
      apiErrors['resolution'] = "";

      const url = `/api/cloudbeds/test-room-type-resolution?roomTypeId=${roomTypeId}&roomName=${encodeURIComponent(roomName || '')}`;
      const response = await fetch(url);
      const data = await response.json();

      resolutionResult = data;

      if (!data.success) {
        apiErrors['resolution'] = data.error?.message || "API call failed";
      }
    } catch (error) {
      apiErrors['resolution'] = error instanceof Error ? error.message : "Unknown error";
    } finally {
      isLoading['resolution'] = false;
    }
  }

  // Format JSON for display
  function formatJson(obj: any): string {
    return JSON.stringify(obj, null, 2);
  }
</script>

<div class="mx-auto p-6 max-w-7xl">
  <header class="mb-8">
    <h1 class="mb-2 font-bold text-primary-900 text-3xl">Dynamic Room Type Resolution Test</h1>
    <p class="text-primary-600">Test the dynamic room type resolution system</p>
    
    <div class="bg-blue-100 mt-4 p-4 border-blue-500 border-l-4 text-blue-700">
      <p class="font-bold">About Dynamic Room Type Resolution</p>
      <p>This system eliminates the need for hardcoded room type IDs by implementing intelligent caching, pattern matching, and fallback mechanisms to ensure reliable room type resolution across different properties.</p>
    </div>
  </header>

  <!-- Control Panel -->
  <div class="bg-primary-50 mb-8 p-4 border border-primary-100 rounded-lg">
    <h2 class="mb-4 font-semibold text-primary-900 text-xl">Test Parameters</h2>

    <div class="gap-4 grid grid-cols-1 md:grid-cols-2">
      <!-- Room Type Selector -->
      <div>
        <label for="roomTypeSelect" class="block mb-1 font-medium text-primary-700 text-sm">Room Type ID</label>
        <select
          id="roomTypeSelect"
          bind:value={roomTypeId}
          class="shadow-sm p-2 border border-primary-300 focus:border-primary-500 rounded-md focus:ring-primary-500 w-full">
          <option value="">-- Select Room Type --</option>
          {#each availableRoomTypes as roomType}
            <option value={roomType.id}>{roomType.name} ({roomType.id})</option>
          {/each}
        </select>
        {#if isLoading['roomTypes']}
          <p class="mt-1 text-primary-500 text-xs">Loading room types...</p>
        {/if}
        {#if apiErrors['roomTypes']}
          <p class="mt-1 text-red-500 text-xs">{apiErrors['roomTypes']}</p>
        {/if}
        <p class="mt-1 text-primary-500 text-xs">Leave empty to test name-based resolution only</p>
      </div>

      <!-- Room Name Input -->
      <div>
        <label for="roomNameInput" class="block mb-1 font-medium text-primary-700 text-sm">Room Name</label>
        <input
          id="roomNameInput"
          type="text"
          bind:value={roomName}
          placeholder="e.g., Garden Deluxe"
          class="shadow-sm p-2 border border-primary-300 focus:border-primary-500 rounded-md focus:ring-primary-500 w-full">
        <p class="mt-1 text-primary-500 text-xs">Leave empty to test ID-based resolution only</p>
      </div>
    </div>

    <div class="mt-4">
      <button
        onclick={testDynamicResolution}
        class="bg-primary-600 hover:bg-primary-700 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 text-white">
        {isLoading['resolution'] ? 'Testing...' : 'Test Resolution'}
      </button>
      
      <button
        onclick={loadRoomTypes}
        class="ml-2 bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 text-white">
        Refresh Room Types
      </button>
    </div>
  </div>

  <!-- Results -->
  <div class="gap-8 grid grid-cols-1 md:grid-cols-2">
    <!-- Resolution Results -->
    <div class="bg-white shadow-md p-6 border border-primary-100 rounded-lg">
      <h3 class="mb-4 font-semibold text-primary-800 text-xl">Resolution Results</h3>
      
      {#if isLoading['resolution']}
        <div class="flex justify-center items-center py-4">
          <div class="mr-2 border-2 border-primary-300 border-t-primary-600 rounded-full w-6 h-6 animate-spin"></div>
          <span class="text-primary-600">Testing resolution...</span>
        </div>
      {:else if apiErrors['resolution']}
        <div class="bg-red-50 p-3 rounded-md text-red-600">
          <p>{apiErrors['resolution']}</p>
        </div>
      {:else if resolutionResult}
        <div class="mb-4">
          <h4 class="mb-2 font-medium text-primary-700 text-sm">Input:</h4>
          <div class="bg-gray-50 p-3 border border-gray-200 rounded">
            <p><strong>Room Type ID:</strong> {resolutionResult.input.roomTypeId || 'Not provided'}</p>
            <p><strong>Room Name:</strong> {resolutionResult.input.roomName || 'Not provided'}</p>
            <p><strong>Property ID:</strong> {resolutionResult.input.propertyId}</p>
          </div>
        </div>

        <div class="mb-4">
          <h4 class="mb-2 font-medium text-primary-700 text-sm">Resolution:</h4>
          <div class="bg-gray-50 p-3 border border-gray-200 rounded">
            <p><strong>Resolved Room Type ID:</strong> {resolutionResult.resolution.resolvedRoomTypeId || 'Failed to resolve'}</p>
            <p><strong>Resolved Room Type Name:</strong> {resolutionResult.resolution.resolvedRoomTypeName}</p>
            <p><strong>Resolution Method:</strong> {resolutionResult.resolution.resolutionMethod}</p>
          </div>
        </div>
      {:else}
        <div class="bg-yellow-50 p-4 border border-yellow-200 rounded text-yellow-700">
          No resolution results yet. Test the resolution to see results here.
        </div>
      {/if}
    </div>

    <!-- Available Room Types -->
    <div class="bg-white shadow-md p-6 border border-primary-100 rounded-lg">
      <h3 class="mb-4 font-semibold text-primary-800 text-xl">Available Room Types</h3>
      
      {#if isLoading['roomTypes']}
        <div class="flex justify-center items-center py-4">
          <div class="mr-2 border-2 border-primary-300 border-t-primary-600 rounded-full w-6 h-6 animate-spin"></div>
          <span class="text-primary-600">Loading room types...</span>
        </div>
      {:else if apiErrors['roomTypes']}
        <div class="bg-red-50 p-3 rounded-md text-red-600">
          <p>{apiErrors['roomTypes']}</p>
        </div>
      {:else if availableRoomTypes.length > 0}
        <div class="bg-gray-50 p-3 border border-gray-200 rounded max-h-80 overflow-auto">
          <table class="min-w-full">
            <thead>
              <tr>
                <th class="py-2 px-3 text-left text-xs font-medium text-primary-500 uppercase tracking-wider">ID</th>
                <th class="py-2 px-3 text-left text-xs font-medium text-primary-500 uppercase tracking-wider">Name</th>
              </tr>
            </thead>
            <tbody>
              {#each availableRoomTypes as roomType}
                <tr class="border-t border-gray-200">
                  <td class="py-2 px-3 text-sm">{roomType.id}</td>
                  <td class="py-2 px-3 text-sm">{roomType.name}</td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      {:else}
        <div class="bg-yellow-50 p-4 border border-yellow-200 rounded text-yellow-700">
          No room types available. Check your Cloudbeds configuration.
        </div>
      {/if}
    </div>
  </div>

  <!-- Documentation -->
  <div class="mt-8 bg-white shadow-md p-6 border border-primary-100 rounded-lg">
    <h3 class="mb-4 font-semibold text-primary-800 text-xl">How It Works</h3>
    
    <div class="prose prose-primary max-w-none">
      <p>The dynamic room type resolution system uses multiple strategies to resolve room type IDs:</p>
      
      <ol>
        <li><strong>Direct ID Validation</strong>: If a valid room type ID is provided, it will be used</li>
        <li><strong>Exact Name Matching</strong>: If the exact room name is found in the cache</li>
        <li><strong>Pattern Matching</strong>: If the room name contains patterns like "Garden Deluxe" or "Ocean Junior"</li>
        <li><strong>Fuzzy Matching</strong>: For similar but not exact matches</li>
        <li><strong>Default Room Type</strong>: As a last resort, the first available room type is used</li>
      </ol>
      
      <p>This system eliminates the need for hardcoded room type IDs, making your application more portable and maintainable.</p>
    </div>
  </div>
</div>
