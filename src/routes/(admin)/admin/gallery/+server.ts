import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { hasSitePermission } from '$lib/auth';

/**
 * Server-side validation for gallery management
 * This ensures users can only create or update galleries for sites they have access to
 */
export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated
  if (!locals.session || !locals.user) {
    return json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const data = await request.json();
    
    // Validate required fields
    if (!data.site_id) {
      return json({ success: false, error: 'Site ID is required' }, { status: 400 });
    }
    
    if (!data.name) {
      return json({ success: false, error: 'Gallery name is required' }, { status: 400 });
    }
    
    // Check if user has permission to manage this site
    const hasPermission = await hasSitePermission(locals.supabase, locals.user.id, data.site_id);
    
    if (!hasPermission) {
      return json({ success: false, error: 'You do not have permission to manage this site' }, { status: 403 });
    }
    
    // Create gallery
    const { data: gallery, error } = await locals.supabase
      .from('galleries')
      .insert({
        site_id: data.site_id,
        name: data.name,
        description: data.description || '',
        images: data.images || [],
        status: data.status || 'active',
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error creating gallery:', error);
      return json({ success: false, error: error.message }, { status: 500 });
    }
    
    return json({ success: true, data: gallery });
  } catch (err: any) {
    console.error('Error creating gallery:', err);
    return json({ success: false, error: err.message }, { status: 500 });
  }
};

export const PUT: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated
  if (!locals.session || !locals.user) {
    return json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const data = await request.json();
    
    // Validate required fields
    if (!data.id) {
      return json({ success: false, error: 'Gallery ID is required' }, { status: 400 });
    }
    
    if (!data.site_id) {
      return json({ success: false, error: 'Site ID is required' }, { status: 400 });
    }
    
    // Check if user has permission to manage this site
    const hasPermission = await hasSitePermission(locals.supabase, locals.user.id, data.site_id);
    
    if (!hasPermission) {
      return json({ success: false, error: 'You do not have permission to manage this site' }, { status: 403 });
    }
    
    // Update gallery
    const { data: gallery, error } = await locals.supabase
      .from('galleries')
      .update({
        name: data.name,
        description: data.description || '',
        images: data.images || [],
        status: data.status || 'active',
        updated_at: new Date().toISOString(),
      })
      .eq('id', data.id)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating gallery:', error);
      return json({ success: false, error: error.message }, { status: 500 });
    }
    
    return json({ success: true, data: gallery });
  } catch (err: any) {
    console.error('Error updating gallery:', err);
    return json({ success: false, error: err.message }, { status: 500 });
  }
};

export const GET: RequestHandler = async ({ url, locals }) => {
  // Check if user is authenticated
  if (!locals.session || !locals.user) {
    return json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const siteId = url.searchParams.get('site_id');
    const galleryId = url.searchParams.get('id');
    
    // If gallery ID is provided, get a specific gallery
    if (galleryId) {
      const { data: gallery, error } = await locals.supabase
        .from('galleries')
        .select('*')
        .eq('id', galleryId)
        .single();
      
      if (error) {
        console.error('Error fetching gallery:', error);
        return json({ success: false, error: error.message }, { status: 500 });
      }
      
      // Check if user has permission to view this gallery
      const hasPermission = await hasSitePermission(locals.supabase, locals.user.id, gallery.site_id);
      
      if (!hasPermission) {
        return json({ success: false, error: 'You do not have permission to view this gallery' }, { status: 403 });
      }
      
      return json({ success: true, data: gallery });
    }
    
    // If site ID is provided, get all galleries for that site
    if (siteId) {
      // Check if user has permission to view this site
      const hasPermission = await hasSitePermission(locals.supabase, locals.user.id, siteId);
      
      if (!hasPermission) {
        return json({ success: false, error: 'You do not have permission to view galleries for this site' }, { status: 403 });
      }
      
      const { data: galleries, error } = await locals.supabase
        .from('galleries')
        .select('*')
        .eq('site_id', siteId)
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching galleries:', error);
        return json({ success: false, error: error.message }, { status: 500 });
      }
      
      return json({ success: true, data: galleries });
    }
    
    // If no site ID is provided, get all galleries the user has access to
    const { data: sites, error: sitesError } = await locals.supabase
      .from('user_site_permissions')
      .select('site_id')
      .eq('user_id', locals.user.id);
    
    if (sitesError) {
      console.error('Error fetching user sites:', sitesError);
      return json({ success: false, error: sitesError.message }, { status: 500 });
    }
    
    if (!sites || sites.length === 0) {
      return json({ success: true, data: [] });
    }
    
    const siteIds = sites.map(site => site.site_id);
    
    const { data: galleries, error } = await locals.supabase
      .from('galleries')
      .select('*')
      .in('site_id', siteIds)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching galleries:', error);
      return json({ success: false, error: error.message }, { status: 500 });
    }
    
    return json({ success: true, data: galleries });
  } catch (err: any) {
    console.error('Error fetching galleries:', err);
    return json({ success: false, error: err.message }, { status: 500 });
  }
};
