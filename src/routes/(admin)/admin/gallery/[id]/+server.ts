import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { hasSitePermission } from '$lib/auth';

/**
 * Delete a gallery
 */
export const DELETE: RequestHandler = async ({ params, locals }) => {
  // Check if user is authenticated
  if (!locals.session || !locals.user) {
    return json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = params;

  if (!id) {
    return json({ success: false, error: 'Gallery ID is required' }, { status: 400 });
  }

  try {
    // First, get the gallery to check permissions
    const { data: gallery, error: getError } = await locals.supabase
      .from('galleries')
      .select('site_id')
      .eq('id', id)
      .single();
    
    if (getError) {
      console.error('Error fetching gallery:', getError);
      return json({ success: false, error: getError.message }, { status: 500 });
    }
    
    if (!gallery) {
      return json({ success: false, error: 'Gallery not found' }, { status: 404 });
    }
    
    // Check if user has permission to manage this site
    const hasPermission = await hasSitePermission(locals.supabase, locals.user.id, gallery.site_id);
    
    if (!hasPermission) {
      return json({ success: false, error: 'You do not have permission to delete this gallery' }, { status: 403 });
    }
    
    // Delete the gallery
    const { error: deleteError } = await locals.supabase
      .from('galleries')
      .delete()
      .eq('id', id);
    
    if (deleteError) {
      console.error('Error deleting gallery:', deleteError);
      return json({ success: false, error: deleteError.message }, { status: 500 });
    }
    
    return json({ success: true });
  } catch (err: any) {
    console.error('Error deleting gallery:', err);
    return json({ success: false, error: err.message }, { status: 500 });
  }
};
