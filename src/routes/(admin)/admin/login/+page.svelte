<script lang="ts">
  import { enhance } from "$app/forms";
  import { goto } from "$app/navigation";
  import { page } from "$app/stores";
  import { LogIn } from "lucide-svelte";

  // Estado del formulario
  let email = $state("");
  let password = $state("");
  let loading = $state(false);
  let error = $state("");
  let fieldErrors = $state<Record<string, string[]>>({});

  // Función para manejar el envío del formulario
  function handleSubmit(event: SubmitEvent) {
    loading = true;
    error = "";
    fieldErrors = {};
  }

  // Función para manejar la respuesta del servidor
  function handleResponse(result: {
    type: string;
    status?: number;
    data?: any;
  }) {
    loading = false;

    if (result.type === "failure") {
      if (result.data?.error) {
        error = result.data.error;
      }
      if (result.data?.fieldErrors) {
        fieldErrors = result.data.fieldErrors;
      }
    } else if (result.type === "success") {
      if (result.data?.redirectTo) {
        goto(result.data.redirectTo);
      } else {
        goto("/admin");
      }
    }
  }

  // Verificar si hay un error en la URL
  $effect(() => {
    const urlError = $page.url.searchParams.get("error");
    if (urlError) {
      error =
        urlError === "unauthorized"
          ? "No tienes permisos para acceder al panel de administración"
          : "Error de autenticación";
    }
  });
</script>

<svelte:head>
  <title>Admin Login - Baberrih</title>
</svelte:head>

<div class="flex justify-center items-center bg-primary-50 min-h-screen">
  <div class="bg-white shadow-lg p-8 rounded-sm w-full max-w-md">
    <div class="mb-8 text-center">
      <img
        src="/baberrih-logo.svg"
        alt="Baberrih Logo"
        class="mx-auto mb-4 w-auto h-12"
      />
      <h1
        class="font-montserrat font-light text-primary-900 text-xl uppercase tracking-wider"
      >
        Admin Login
      </h1>
      <div class="bg-primary-300 mx-auto mt-4 mb-6 w-16 h-0.5"></div>
    </div>

    {#if error}
      <div
        class="bg-red-50 mb-6 p-4 border border-red-200 rounded-sm text-red-700"
      >
        {error}
      </div>
    {/if}

    <form
      method="POST"
      action="?/login"
      use:enhance={() => {
        handleSubmit(event);
        return ({ result }) => {
          handleResponse(result);
        };
      }}
    >
      <div class="mb-6">
        <label
          for="email"
          class="block mb-2 font-montserrat text-primary-800 text-sm"
        >
          Email
        </label>
        <input
          id="email"
          name="email"
          type="email"
          bind:value={email}
          required
          class="px-4 py-3 border border-primary-200 focus:border-primary-500 rounded-sm focus:ring-primary-500 w-full"
          placeholder="<EMAIL>"
        />
        {#if fieldErrors.email}
          <p class="mt-1 text-red-600 text-sm">{fieldErrors.email[0]}</p>
        {/if}
      </div>

      <div class="mb-6">
        <label
          for="password"
          class="block mb-2 font-montserrat text-primary-800 text-sm"
        >
          Password
        </label>
        <input
          id="password"
          name="password"
          type="password"
          bind:value={password}
          required
          class="px-4 py-3 border border-primary-200 focus:border-primary-500 rounded-sm focus:ring-primary-500 w-full"
          placeholder="••••••••"
        />
        {#if fieldErrors.password}
          <p class="mt-1 text-red-600 text-sm">{fieldErrors.password[0]}</p>
        {/if}
      </div>

      <button
        type="submit"
        disabled={loading}
        class="flex justify-center items-center bg-primary-700 hover:bg-primary-800 px-4 py-3 rounded-sm w-full font-montserrat text-white text-sm uppercase tracking-wider"
      >
        {#if loading}
          <div
            class="mr-2 border-2 border-white border-t-transparent rounded-full w-5 h-5 animate-spin"
          ></div>
          Iniciando sesión...
        {:else}
          <LogIn class="mr-2 w-4 h-4" />
          Iniciar sesión
        {/if}
      </button>
    </form>

    <div class="mt-8 text-center">
      <a href="/" class="text-primary-600 hover:text-primary-800 text-sm">
        Volver al sitio principal
      </a>
    </div>
  </div>
</div>
