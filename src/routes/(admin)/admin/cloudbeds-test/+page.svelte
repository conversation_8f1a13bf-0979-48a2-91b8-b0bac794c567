<script lang="ts">
  import { onMount } from "svelte";
  import { fade } from "svelte/transition";
  import { dev } from "$app/environment";

  // Import Cloudbeds components
  import PriceDisplay from "$lib/components/cloudbeds/PriceDisplay.svelte";
  import AvailabilityIndicator from "$lib/components/cloudbeds/AvailabilityIndicator.svelte";
  import QuickBookButton from "$lib/components/cloudbeds/QuickBookButton.svelte";

  // State variables
  let roomTypeId = $state("");
  let roomName = $state(""); // Added for dynamic room type resolution
  let propertyId = $state("");
  let startDate = $state(getTodayDate());
  let endDate = $state(getTomorrowDate());
  let adults = $state(2);
  let apiResponses = $state<Record<string, any>>({});
  let apiErrors = $state<Record<string, string>>({});
  let isLoading = $state<Record<string, boolean>>({});
  let availableRoomTypes = $state<Array<{id: string; name: string}>>([]);
  let selectedTab = $state("components");
  let showFullDetails = $state(true);
  // Define the type for resolution result
  interface ResolutionResult {
    success: boolean;
    input: {
      roomTypeId?: string;
      roomName?: string;
      propertyId: string;
    };
    resolution: {
      resolvedRoomTypeId: string;
      resolvedRoomTypeName: string;
      resolutionMethod: string;
    };
    availableRoomTypes: Array<{
      id: string;
      name: string;
    }>;
  }

  let resolutionResult = $state<ResolutionResult | null>(null); // For storing dynamic resolution test results

  // Helper functions
  function getTodayDate(): string {
    const today = new Date();
    return today.toISOString().split("T")[0];
  }

  function getTomorrowDate(): string {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split("T")[0];
  }

  // Load room types on mount
  onMount(async () => {
    await loadRoomTypes();
    await loadPropertyId();
  });

  // Load available room types
  async function loadRoomTypes() {
    try {
      isLoading['roomTypes'] = true;
      apiErrors['roomTypes'] = "";

      const response = await fetch('/api/cloudbeds/room-types?includePhotos=1&includeAmenities=1');
      const data = await response.json();

      apiResponses['roomTypes'] = data;

      if (data.success && data.data) {
        availableRoomTypes = data.data.map((roomType: any) => ({
          id: roomType.roomTypeID || roomType.id,
          name: roomType.roomTypeName || roomType.name
        }));

        if (availableRoomTypes.length > 0 && !roomTypeId) {
          roomTypeId = availableRoomTypes[0].id;
        }
      } else {
        apiErrors['roomTypes'] = data.error?.message || "Failed to load room types";
      }
    } catch (error) {
      apiErrors['roomTypes'] = error instanceof Error ? error.message : "Unknown error";
    } finally {
      isLoading['roomTypes'] = false;
    }
  }

  // Load property ID
  async function loadPropertyId() {
    try {
      isLoading['propertyId'] = true;
      apiErrors['propertyId'] = "";

      const response = await fetch('/api/cloudbeds/config');
      const data = await response.json();

      apiResponses['propertyId'] = data;

      if (data.success && data.propertyID) {
        propertyId = data.propertyID;
      } else {
        apiErrors['propertyId'] = data.error || "Failed to load property ID";
      }
    } catch (error) {
      apiErrors['propertyId'] = error instanceof Error ? error.message : "Unknown error";
    } finally {
      isLoading['propertyId'] = false;
    }
  }

  // Test availability API directly
  async function testAvailabilityApi() {
    try {
      isLoading['availability'] = true;
      apiErrors['availability'] = "";

      const url = `/api/cloudbeds/availability?startDate=${startDate}&endDate=${endDate}${roomTypeId ? `&roomTypeId=${roomTypeId}` : ''}`;
      const response = await fetch(url);
      const data = await response.json();

      apiResponses['availability'] = data;

      if (!data.success) {
        apiErrors['availability'] = data.error?.message || "API call failed";
      }
    } catch (error) {
      apiErrors['availability'] = error instanceof Error ? error.message : "Unknown error";
    } finally {
      isLoading['availability'] = false;
    }
  }

  // Test public availability API
  async function testPublicAvailabilityApi() {
    try {
      isLoading['publicAvailability'] = true;
      apiErrors['publicAvailability'] = "";

      const url = `/api/cloudbeds/public-availability?startDate=${startDate}&endDate=${endDate}${roomTypeId ? `&roomTypeId=${roomTypeId}` : ''}`;
      const response = await fetch(url);
      const data = await response.json();

      apiResponses['publicAvailability'] = data;

      if (!data.success) {
        apiErrors['publicAvailability'] = data.error?.message || "API call failed";
      }
    } catch (error) {
      apiErrors['publicAvailability'] = error instanceof Error ? error.message : "Unknown error";
    } finally {
      isLoading['publicAvailability'] = false;
    }
  }

  // Test dynamic resolution API
  async function testDynamicResolutionApi() {
    try {
      isLoading['dynamicResolution'] = true;
      apiErrors['dynamicResolution'] = "";

      const url = `/api/cloudbeds/test-room-type-resolution?${roomTypeId ? `roomTypeId=${roomTypeId}` : ''}${roomName ? `&roomName=${encodeURIComponent(roomName)}` : ''}`;
      const response = await fetch(url);
      const data = await response.json();

      apiResponses['dynamicResolution'] = data;
      resolutionResult = data;

      if (!data.success) {
        apiErrors['dynamicResolution'] = data.error?.message || "API call failed";
      }
    } catch (error) {
      apiErrors['dynamicResolution'] = error instanceof Error ? error.message : "Unknown error";
    } finally {
      isLoading['dynamicResolution'] = false;
    }
  }

  // Format JSON for display
  function formatJson(obj: any): string {
    return JSON.stringify(obj, null, 2);
  }
</script>

<!-- Page Layout -->
<div class="mx-auto p-6 max-w-7xl">
  <header class="mb-8">
    <h1 class="mb-2 font-bold text-primary-900 text-3xl">Cloudbeds Test Page</h1>
    <p class="text-primary-600">A comprehensive testing ground for all Cloudbeds functionality</p>

    {#if !dev}
      <div class="bg-yellow-100 mt-4 p-4 border-yellow-500 border-l-4 text-yellow-700">
        <p class="font-bold">Development Mode Notice</p>
        <p>This page is intended for testing and debugging purposes only. It should not be accessible in production.</p>
      </div>
    {/if}
  </header>

  <!-- Tab Navigation -->
  <div class="mb-6 border-primary-200 border-b">
    <nav class="flex space-x-8">
      <button
        class="py-4 px-1 border-b-2 font-medium text-sm {selectedTab === 'components' ? 'border-primary-500 text-primary-600' : 'border-transparent text-primary-500 hover:text-primary-700 hover:border-primary-300'}"
        onclick={() => selectedTab = 'components'}>
        Components
      </button>
      <button
        class="py-4 px-1 border-b-2 font-medium text-sm {selectedTab === 'api' ? 'border-primary-500 text-primary-600' : 'border-transparent text-primary-500 hover:text-primary-700 hover:border-primary-300'}"
        onclick={() => selectedTab = 'api'}>
        API Testing
      </button>
      <button
        class="py-4 px-1 border-b-2 font-medium text-sm {selectedTab === 'resolution' ? 'border-primary-500 text-primary-600' : 'border-transparent text-primary-500 hover:text-primary-700 hover:border-primary-300'}"
        onclick={() => selectedTab = 'resolution'}>
        Room Type Resolution
      </button>
      <button
        class="py-4 px-1 border-b-2 font-medium text-sm {selectedTab === 'logs' ? 'border-primary-500 text-primary-600' : 'border-transparent text-primary-500 hover:text-primary-700 hover:border-primary-300'}"
        onclick={() => selectedTab = 'logs'}>
        Logs & Responses
      </button>
      <button
        class="py-4 px-1 border-b-2 font-medium text-sm {selectedTab === 'docs' ? 'border-primary-500 text-primary-600' : 'border-transparent text-primary-500 hover:text-primary-700 hover:border-primary-300'}"
        onclick={() => selectedTab = 'docs'}>
        Documentation
      </button>
    </nav>
  </div>

  <!-- Control Panel (Always Visible) -->
  <div class="bg-primary-50 mb-8 p-4 border border-primary-100 rounded-lg">
    <h2 class="mb-4 font-semibold text-primary-900 text-xl">Test Parameters</h2>

    <div class="gap-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
      <!-- Room Type Selector -->
      <div>
        <label for="roomTypeSelect" class="block mb-1 font-medium text-primary-700 text-sm">Room Type</label>
        <select
          id="roomTypeSelect"
          bind:value={roomTypeId}
          class="shadow-sm p-2 border border-primary-300 focus:border-primary-500 rounded-md focus:ring-primary-500 w-full">
          <option value="">Select a room type</option>
          {#each availableRoomTypes as roomType}
            <option value={roomType.id}>{roomType.name}</option>
          {/each}
        </select>
        {#if isLoading['roomTypes']}
          <p class="mt-1 text-primary-500 text-xs">Loading room types...</p>
        {/if}
        {#if apiErrors['roomTypes']}
          <p class="mt-1 text-red-500 text-xs">{apiErrors['roomTypes']}</p>
        {/if}
      </div>

      <!-- Room Name Input (for dynamic resolution) -->
      <div>
        <label for="roomNameInput" class="block mb-1 font-medium text-primary-700 text-sm">Room Name (for Resolution)</label>
        <input
          id="roomNameInput"
          type="text"
          bind:value={roomName}
          placeholder="e.g., Garden Deluxe"
          class="shadow-sm p-2 border border-primary-300 focus:border-primary-500 rounded-md focus:ring-primary-500 w-full">
        <p class="mt-1 text-primary-500 text-xs">Use this for testing name-based resolution</p>
      </div>

      <!-- Date Range -->
      <div>
        <label for="startDate" class="block mb-1 font-medium text-primary-700 text-sm">Start Date</label>
        <input
          type="date"
          id="startDate"
          bind:value={startDate}
          class="shadow-sm p-2 border border-primary-300 focus:border-primary-500 rounded-md focus:ring-primary-500 w-full">
      </div>

      <div>
        <label for="endDate" class="block mb-1 font-medium text-primary-700 text-sm">End Date</label>
        <input
          type="date"
          id="endDate"
          bind:value={endDate}
          class="shadow-sm p-2 border border-primary-300 focus:border-primary-500 rounded-md focus:ring-primary-500 w-full">
      </div>

      <!-- Guest Count -->
      <div>
        <label for="adults" class="block mb-1 font-medium text-primary-700 text-sm">Adults</label>
        <input
          type="number"
          id="adults"
          bind:value={adults}
          min="1"
          max="10"
          class="shadow-sm p-2 border border-primary-300 focus:border-primary-500 rounded-md focus:ring-primary-500 w-full">
      </div>
    </div>

    <div class="flex flex-wrap gap-2 mt-4">
      <button
        onclick={loadRoomTypes}
        class="bg-primary-600 hover:bg-primary-700 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 text-white">
        Refresh Room Types
      </button>
      <button
        onclick={testAvailabilityApi}
        class="bg-primary-600 hover:bg-primary-700 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 text-white">
        Test Availability API
      </button>
      <button
        onclick={testPublicAvailabilityApi}
        class="bg-primary-600 hover:bg-primary-700 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 text-white">
        Test Public Availability API
      </button>
      <button
        onclick={testDynamicResolutionApi}
        class="bg-primary-600 hover:bg-primary-700 ml-2 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 text-white">
        Test Dynamic Resolution
      </button>
    </div>
  </div>

  <!-- Tab Content -->
  <div class="mt-8">
    <!-- Components Tab -->
    {#if selectedTab === 'components'}
      <div in:fade={{ duration: 200 }}>
        <h2 class="mb-4 font-bold text-primary-900 text-2xl">Cloudbeds Components</h2>
        <p class="mb-6 text-primary-600">Test all Cloudbeds components with the parameters set above.</p>

        <div class="gap-8 grid grid-cols-1 md:grid-cols-2">
          <!-- Price Display Component -->
          <div class="bg-white shadow-md p-6 border border-primary-100 rounded-lg">
            <h3 class="mb-4 font-semibold text-primary-800 text-xl">Price Display</h3>
            <p class="mb-4 text-primary-600 text-sm">Displays pricing information for a room type.</p>

            {#if roomTypeId}
              <div class="mb-4">
                <h4 class="mb-2 font-medium text-primary-700 text-sm">Standard Display:</h4>
                <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                  <PriceDisplay roomTypeId={roomTypeId} roomName={roomName} {showFullDetails} />
                </div>
              </div>

              <div class="mb-4">
                <h4 class="mb-2 font-medium text-primary-700 text-sm">Compact Display:</h4>
                <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                  <PriceDisplay roomTypeId={roomTypeId} roomName={roomName} compact={true} />
                </div>
              </div>
            {:else}
              <div class="bg-yellow-50 p-4 border border-yellow-200 rounded text-yellow-700">
                Please select a room type to test this component.
              </div>
            {/if}

            <div class="mt-4">
              <label class="inline-flex items-center">
                <input type="checkbox" bind:checked={showFullDetails} class="w-4 h-4 text-primary-600 form-checkbox">
                <span class="ml-2 text-primary-700 text-sm">Show Full Details</span>
              </label>
            </div>
          </div>

          <!-- Availability Indicator Component -->
          <div class="bg-white shadow-md p-6 border border-primary-100 rounded-lg">
            <h3 class="mb-4 font-semibold text-primary-800 text-xl">Availability Indicator</h3>
            <p class="mb-4 text-primary-600 text-sm">Shows availability status for a room type.</p>

            {#if roomTypeId}
              <div class="mb-4">
                <h4 class="mb-2 font-medium text-primary-700 text-sm">Standard Display:</h4>
                <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                  <AvailabilityIndicator roomTypeId={roomTypeId} roomName={roomName} />
                </div>
              </div>

              <div class="mb-4">
                <h4 class="mb-2 font-medium text-primary-700 text-sm">Compact Display:</h4>
                <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                  <AvailabilityIndicator roomTypeId={roomTypeId} roomName={roomName} compact={true} />
                </div>
              </div>
            {:else}
              <div class="bg-yellow-50 p-4 border border-yellow-200 rounded text-yellow-700">
                Please select a room type to test this component.
              </div>
            {/if}
          </div>

          <!-- Quick Book Button Component -->
          <div class="bg-white shadow-md p-6 border border-primary-100 rounded-lg">
            <h3 class="mb-4 font-semibold text-primary-800 text-xl">Quick Book Button</h3>
            <p class="mb-4 text-primary-600 text-sm">Button to quickly book a room.</p>

            {#if roomTypeId && propertyId}
              <div class="mb-4">
                <h4 class="mb-2 font-medium text-primary-700 text-sm">Standard Button:</h4>
                <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                  <QuickBookButton
                    roomTypeId={roomTypeId}
                    propertyId={propertyId}
                    buttonText="Book Now"
                    buttonClass="inline-flex justify-center items-center bg-primary-600 hover:bg-primary-700 px-6 py-3 rounded-md text-white font-medium tracking-wider transition-all duration-200 shadow-sm hover:shadow"
                  />
                </div>
              </div>

              <div class="mb-4">
                <h4 class="mb-2 font-medium text-primary-700 text-sm">Text-Only Button:</h4>
                <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                  <QuickBookButton
                    roomTypeId={roomTypeId}
                    propertyId={propertyId}
                    buttonText="Reserve"
                    showIcon={false}
                    buttonClass="text-primary-600 hover:text-primary-800 font-medium"
                  />
                </div>
              </div>
            {:else}
              <div class="bg-yellow-50 p-4 border border-yellow-200 rounded text-yellow-700">
                Please select a room type and ensure property ID is loaded to test this component.
              </div>
            {/if}

            <div class="mt-4 text-primary-600 text-sm">
              <p>Property ID: {propertyId || 'Not loaded'}</p>
              {#if isLoading['propertyId']}
                <p class="mt-1 text-primary-500 text-xs">Loading property ID...</p>
              {/if}
              {#if apiErrors['propertyId']}
                <p class="mt-1 text-red-500 text-xs">{apiErrors['propertyId']}</p>
              {/if}
            </div>
          </div>

          <!-- Combined Components -->
          <div class="bg-white shadow-md p-6 border border-primary-100 rounded-lg">
            <h3 class="mb-4 font-semibold text-primary-800 text-xl">Combined Components</h3>
            <p class="mb-4 text-primary-600 text-sm">All components together as they would appear on a suite page.</p>

            {#if roomTypeId && propertyId}
              <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                <div class="mb-4">
                  <PriceDisplay roomTypeId={roomTypeId} roomName={roomName} />
                </div>

                <div class="mb-4">
                  <AvailabilityIndicator roomTypeId={roomTypeId} roomName={roomName} />
                </div>

                <div>
                  <QuickBookButton
                    roomTypeId={roomTypeId}
                    propertyId={propertyId}
                    buttonText="Book Now"
                    buttonClass="inline-flex justify-center items-center bg-primary-600 hover:bg-primary-700 px-6 py-3 rounded-md text-white w-full font-medium uppercase tracking-wider transition-all duration-200 shadow-sm hover:shadow"
                  />
                </div>
              </div>
            {:else}
              <div class="bg-yellow-50 p-4 border border-yellow-200 rounded text-yellow-700">
                Please select a room type and ensure property ID is loaded to test these components.
              </div>
            {/if}
          </div>
        </div>
      </div>
    {/if}

    <!-- API Testing Tab -->
    {#if selectedTab === 'api'}
      <div in:fade={{ duration: 200 }}>
        <h2 class="mb-4 font-bold text-primary-900 text-2xl">API Testing</h2>
        <p class="mb-6 text-primary-600">Test direct API calls to Cloudbeds endpoints.</p>

        <div class="gap-8 grid grid-cols-1 md:grid-cols-2">
          <!-- Availability API -->
          <div class="bg-white shadow-md p-6 border border-primary-100 rounded-lg">
            <h3 class="mb-4 font-semibold text-primary-800 text-xl">Availability API</h3>
            <p class="mb-4 text-primary-600 text-sm">Test the regular availability API endpoint.</p>

            <div class="mb-4">
              <button
                onclick={testAvailabilityApi}
                class="bg-primary-600 hover:bg-primary-700 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 text-white"
                disabled={isLoading['availability']}>
                {isLoading['availability'] ? 'Testing...' : 'Test Availability API'}
              </button>
            </div>

            {#if isLoading['availability']}
              <div class="flex justify-center items-center bg-gray-50 p-4 border border-gray-200 rounded">
                <div class="mr-2 border-2 border-primary-300 border-t-primary-600 rounded-full w-6 h-6 animate-spin"></div>
                <span class="text-primary-600">Loading...</span>
              </div>
            {:else if apiErrors['availability']}
              <div class="bg-red-50 p-4 border border-red-200 rounded text-red-700">
                <p class="font-medium">Error:</p>
                <p>{apiErrors['availability']}</p>
              </div>
            {:else if apiResponses['availability']}
              <div class="mt-4">
                <h4 class="mb-2 font-medium text-primary-700 text-sm">API Response:</h4>
                <div class="bg-gray-50 p-4 border border-gray-200 rounded max-h-60 overflow-auto">
                  <pre class="text-xs">{formatJson(apiResponses['availability'])}</pre>
                </div>
              </div>
            {/if}
          </div>

          <!-- Public Availability API -->
          <div class="bg-white shadow-md p-6 border border-primary-100 rounded-lg">
            <h3 class="mb-4 font-semibold text-primary-800 text-xl">Public Availability API</h3>
            <p class="mb-4 text-primary-600 text-sm">Test the public availability API endpoint (filters private room types).</p>

            <div class="mb-4">
              <button
                onclick={testPublicAvailabilityApi}
                class="bg-primary-600 hover:bg-primary-700 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 text-white"
                disabled={isLoading['publicAvailability']}>
                {isLoading['publicAvailability'] ? 'Testing...' : 'Test Public Availability API'}
              </button>
            </div>

            {#if isLoading['publicAvailability']}
              <div class="flex justify-center items-center bg-gray-50 p-4 border border-gray-200 rounded">
                <div class="mr-2 border-2 border-primary-300 border-t-primary-600 rounded-full w-6 h-6 animate-spin"></div>
                <span class="text-primary-600">Loading...</span>
              </div>
            {:else if apiErrors['publicAvailability']}
              <div class="bg-red-50 p-4 border border-red-200 rounded text-red-700">
                <p class="font-medium">Error:</p>
                <p>{apiErrors['publicAvailability']}</p>
              </div>
            {:else if apiResponses['publicAvailability']}
              <div class="mt-4">
                <h4 class="mb-2 font-medium text-primary-700 text-sm">API Response:</h4>
                <div class="bg-gray-50 p-4 border border-gray-200 rounded max-h-60 overflow-auto">
                  <pre class="text-xs">{formatJson(apiResponses['publicAvailability'])}</pre>
                </div>
              </div>
            {/if}
          </div>

          <!-- Dynamic Resolution API -->
          <div class="bg-white shadow-md p-6 border border-primary-100 rounded-lg">
            <h3 class="mb-4 font-semibold text-primary-800 text-xl">Dynamic Resolution API</h3>
            <p class="mb-4 text-primary-600 text-sm">Test the dynamic room type resolution system.</p>

            <div class="mb-4">
              <button
                onclick={testDynamicResolutionApi}
                class="bg-primary-600 hover:bg-primary-700 px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 text-white"
                disabled={isLoading['dynamicResolution']}>
                {isLoading['dynamicResolution'] ? 'Testing...' : 'Test Dynamic Resolution'}
              </button>
            </div>

            {#if isLoading['dynamicResolution']}
              <div class="flex justify-center items-center bg-gray-50 p-4 border border-gray-200 rounded">
                <div class="mr-2 border-2 border-primary-300 border-t-primary-600 rounded-full w-6 h-6 animate-spin"></div>
                <span class="text-primary-600">Loading...</span>
              </div>
            {:else if apiErrors['dynamicResolution']}
              <div class="bg-red-50 p-4 border border-red-200 rounded text-red-700">
                <p class="font-medium">Error:</p>
                <p>{apiErrors['dynamicResolution']}</p>
              </div>
            {:else if resolutionResult}
              <div class="mt-4">
                <h4 class="mb-2 font-medium text-primary-700 text-sm">Resolution Result:</h4>
                <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                  <div class="mb-2">
                    <p><strong>Input:</strong></p>
                    <ul class="text-sm list-disc list-inside">
                      <li>Room Type ID: {resolutionResult.input.roomTypeId || 'Not provided'}</li>
                      <li>Room Name: {resolutionResult.input.roomName || 'Not provided'}</li>
                      <li>Property ID: {resolutionResult.input.propertyId}</li>
                    </ul>
                  </div>

                  <div class="mb-2">
                    <p><strong>Resolution:</strong></p>
                    <ul class="text-sm list-disc list-inside">
                      <li>Resolved Room Type ID: {resolutionResult.resolution.resolvedRoomTypeId || 'Failed to resolve'}</li>
                      <li>Resolved Room Type Name: {resolutionResult.resolution.resolvedRoomTypeName}</li>
                      <li>Resolution Method: {resolutionResult.resolution.resolutionMethod}</li>
                    </ul>
                  </div>

                  <div>
                    <p><strong>Available Room Types:</strong></p>
                    <div class="max-h-40 overflow-auto">
                      <table class="min-w-full text-sm">
                        <thead>
                          <tr>
                            <th class="px-3 py-2 font-medium text-primary-500 text-xs text-left uppercase tracking-wider">ID</th>
                            <th class="px-3 py-2 font-medium text-primary-500 text-xs text-left uppercase tracking-wider">Name</th>
                          </tr>
                        </thead>
                        <tbody>
                          {#each resolutionResult.availableRoomTypes as roomType}
                            <tr class="border-gray-200 border-t">
                              <td class="px-3 py-2">{roomType.id}</td>
                              <td class="px-3 py-2">{roomType.name}</td>
                            </tr>
                          {/each}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            {/if}
          </div>
        </div>
      </div>
    {/if}

    <!-- Logs & Responses Tab -->
    {#if selectedTab === 'logs'}
      <div in:fade={{ duration: 200 }}>
        <h2 class="mb-4 font-bold text-primary-900 text-2xl">Logs & Responses</h2>
        <p class="mb-6 text-primary-600">View all API responses and debug information.</p>

        <div class="bg-white shadow-md p-6 border border-primary-100 rounded-lg">
          <h3 class="mb-4 font-semibold text-primary-800 text-xl">API Responses</h3>

          {#if Object.keys(apiResponses).length === 0}
            <div class="bg-yellow-50 p-4 border border-yellow-200 rounded text-yellow-700">
              No API responses recorded yet. Test some components or APIs to see responses here.
            </div>
          {:else}
            <div class="space-y-4">
              {#each Object.entries(apiResponses) as [key, response]}
                <div>
                  <h4 class="mb-2 font-medium text-primary-700 text-sm">{key}:</h4>
                  <div class="bg-gray-50 p-4 border border-gray-200 rounded max-h-60 overflow-auto">
                    <pre class="text-xs">{formatJson(response)}</pre>
                  </div>
                </div>
              {/each}
            </div>
          {/if}
        </div>

        <div class="bg-white shadow-md mt-8 p-6 border border-primary-100 rounded-lg">
          <h3 class="mb-4 font-semibold text-primary-800 text-xl">Error Log</h3>

          {#if Object.values(apiErrors).filter(Boolean).length === 0}
            <div class="bg-green-50 p-4 border border-green-200 rounded text-green-700">
              No errors recorded.
            </div>
          {:else}
            <div class="space-y-4">
              {#each Object.entries(apiErrors).filter(([_, error]) => error) as [key, error]}
                <div class="bg-red-50 p-4 border border-red-200 rounded text-red-700">
                  <p class="font-medium">{key}:</p>
                  <p>{error}</p>
                </div>
              {/each}
            </div>
          {/if}
        </div>
      </div>
    {/if}

    <!-- Documentation Tab -->
    {#if selectedTab === 'docs'}
      <div in:fade={{ duration: 200 }}>
        <h2 class="mb-4 font-bold text-primary-900 text-2xl">Documentation</h2>
        <p class="mb-6 text-primary-600">Information about Cloudbeds components and API endpoints.</p>

        <div class="bg-white shadow-md p-6 border border-primary-100 rounded-lg">
          <h3 class="mb-4 font-semibold text-primary-800 text-xl">Components</h3>

          <div class="space-y-6">
            <div>
              <h4 class="mb-2 font-medium text-primary-800 text-lg">PriceDisplay</h4>
              <p class="mb-2 text-primary-600">Displays pricing information for a room type.</p>
              <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                <p class="mb-1 font-medium text-sm">Props:</p>
                <ul class="space-y-1 text-sm list-disc list-inside">
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">roomTypeId</code> - ID of the room type</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">roomName</code> - Name of the room type (for dynamic resolution)</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">compact</code> - Whether to show compact view (default: false)</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">showFullDetails</code> - Whether to show full price details (default: false)</li>
                </ul>
              </div>
            </div>

            <div>
              <h4 class="mb-2 font-medium text-primary-800 text-lg">AvailabilityIndicator</h4>
              <p class="mb-2 text-primary-600">Shows availability status for a room type.</p>
              <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                <p class="mb-1 font-medium text-sm">Props:</p>
                <ul class="space-y-1 text-sm list-disc list-inside">
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">roomTypeId</code> - ID of the room type</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">roomName</code> - Name of the room type (for dynamic resolution)</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">compact</code> - Whether to show compact view (default: false)</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">days</code> - Number of days to check (default: 14)</li>
                </ul>
              </div>
            </div>

            <div>
              <h4 class="mb-2 font-medium text-primary-800 text-lg">QuickBookButton</h4>
              <p class="mb-2 text-primary-600">Button to quickly book a room.</p>
              <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                <p class="mb-1 font-medium text-sm">Props:</p>
                <ul class="space-y-1 text-sm list-disc list-inside">
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">roomTypeId</code> - ID of the room type</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">propertyId</code> - ID of the property</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">buttonText</code> - Text to display on the button</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">buttonClass</code> - CSS classes for the button</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">showIcon</code> - Whether to show the calendar icon (default: true)</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white shadow-md mt-8 p-6 border border-primary-100 rounded-lg">
          <h3 class="mb-4 font-semibold text-primary-800 text-xl">API Endpoints</h3>

          <div class="space-y-6">
            <div>
              <h4 class="mb-2 font-medium text-primary-800 text-lg">/api/cloudbeds/availability</h4>
              <p class="mb-2 text-primary-600">Get availability for all room types or a specific room type.</p>
              <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                <p class="mb-1 font-medium text-sm">Parameters:</p>
                <ul class="space-y-1 text-sm list-disc list-inside">
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">startDate</code> - Start date (YYYY-MM-DD)</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">endDate</code> - End date (YYYY-MM-DD)</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">roomTypeId</code> - (Optional) ID of the room type</li>
                </ul>
              </div>
            </div>

            <div>
              <h4 class="mb-2 font-medium text-primary-800 text-lg">/api/cloudbeds/public-availability</h4>
              <p class="mb-2 text-primary-600">Get availability for public room types only (filters out private room types).</p>
              <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                <p class="mb-1 font-medium text-sm">Parameters:</p>
                <ul class="space-y-1 text-sm list-disc list-inside">
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">startDate</code> - Start date (YYYY-MM-DD)</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">endDate</code> - End date (YYYY-MM-DD)</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">roomTypeId</code> - (Optional) ID of the room type</li>
                </ul>
              </div>
            </div>

            <div>
              <h4 class="mb-2 font-medium text-primary-800 text-lg">/api/cloudbeds/room-types</h4>
              <p class="mb-2 text-primary-600">Get all room types.</p>
              <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                <p class="mb-1 font-medium text-sm">Parameters:</p>
                <ul class="space-y-1 text-sm list-disc list-inside">
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">includePhotos</code> - Include photos (1 or 0)</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">includeAmenities</code> - Include amenities (1 or 0)</li>
                </ul>
              </div>
            </div>

            <div>
              <h4 class="mb-2 font-medium text-primary-800 text-lg">/api/cloudbeds/public-room-types</h4>
              <p class="mb-2 text-primary-600">Get public room types only (filters out private room types).</p>
              <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                <p class="mb-1 font-medium text-sm">Parameters:</p>
                <ul class="space-y-1 text-sm list-disc list-inside">
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">includePhotos</code> - Include photos (1 or 0)</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">includeAmenities</code> - Include amenities (1 or 0)</li>
                </ul>
              </div>
            </div>

            <div>
              <h4 class="mb-2 font-medium text-primary-800 text-lg">/api/cloudbeds/dynamic-pricing</h4>
              <p class="mb-2 text-primary-600">Get pricing information using dynamic room type resolution.</p>
              <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                <p class="mb-1 font-medium text-sm">Parameters:</p>
                <ul class="space-y-1 text-sm list-disc list-inside">
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">startDate</code> - Start date (YYYY-MM-DD)</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">endDate</code> - End date (YYYY-MM-DD)</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">roomTypeId</code> - (Optional) ID of the room type</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">roomName</code> - (Optional) Name of the room type</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">days</code> - (Optional) Number of days to check (default: 14)</li>
                </ul>
              </div>
            </div>

            <div>
              <h4 class="mb-2 font-medium text-primary-800 text-lg">/api/cloudbeds/dynamic-availability</h4>
              <p class="mb-2 text-primary-600">Get availability information using dynamic room type resolution.</p>
              <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                <p class="mb-1 font-medium text-sm">Parameters:</p>
                <ul class="space-y-1 text-sm list-disc list-inside">
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">startDate</code> - Start date (YYYY-MM-DD)</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">endDate</code> - End date (YYYY-MM-DD)</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">roomTypeId</code> - (Optional) ID of the room type</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">roomName</code> - (Optional) Name of the room type</li>
                </ul>
              </div>
            </div>

            <div>
              <h4 class="mb-2 font-medium text-primary-800 text-lg">/api/cloudbeds/test-room-type-resolution</h4>
              <p class="mb-2 text-primary-600">Test the dynamic room type resolution system.</p>
              <div class="bg-gray-50 p-4 border border-gray-200 rounded">
                <p class="mb-1 font-medium text-sm">Parameters:</p>
                <ul class="space-y-1 text-sm list-disc list-inside">
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">roomTypeId</code> - (Optional) ID of the room type</li>
                  <li><code class="bg-gray-100 px-1 py-0.5 rounded">roomName</code> - (Optional) Name of the room type</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    {/if}
  </div>
</div>