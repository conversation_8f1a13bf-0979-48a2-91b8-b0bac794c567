import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { dev } from '$app/environment';
import { hasSitePermission } from '$lib/auth';

/**
 * Server-side load function to ensure the page is only accessible to admins
 * or in development mode
 */
export const load: PageServerLoad = async ({ locals }) => {
  // Allow access in development mode
  if (dev) {
    return {
      dev: true
    };
  }

  // Check if user is authenticated
  if (!locals.session || !locals.user) {
    throw redirect(302, '/admin/login');
  }

  // Check if user has admin permissions
  try {
    // Get the user's sites
    const { data: userSites } = await locals.supabase
      .from('user_sites')
      .select('site_id')
      .eq('user_id', locals.user.id);

    if (!userSites || userSites.length === 0) {
      throw redirect(302, '/admin/unauthorized');
    }

    // Check if user has admin permissions for any site
    let hasAdminAccess = false;
    for (const userSite of userSites) {
      const hasPermission = await hasSitePermission(
        locals.supabase,
        locals.user.id,
        userSite.site_id,
        'admin'
      );
      
      if (hasPermission) {
        hasAdminAccess = true;
        break;
      }
    }

    if (!hasAdminAccess) {
      throw redirect(302, '/admin/unauthorized');
    }

    return {
      dev: false
    };
  } catch (error) {
    if (error instanceof Response) throw error;
    console.error('Error checking admin permissions:', error);
    throw redirect(302, '/admin/unauthorized');
  }
};
