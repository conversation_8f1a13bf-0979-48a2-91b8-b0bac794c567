<script lang="ts">
  import { goto } from "$app/navigation";
  import FacilityForm from "$lib/components/admin/FacilityForm.svelte";

  // Handle form submission
  function handleSaved(event) {
    const { id } = event.detail;
    goto(`/admin/facilities/${id}`);
  }
</script>

<svelte:head>
  <title>Add New Facility - Baberrih Admin</title>
</svelte:head>

<div class="p-6">
  <div class="mb-6">
    <a
      href="/admin/facilities"
      class="text-blue-600 hover:text-blue-800 hover:underline"
    >
      &larr; Back to Facilities
    </a>
  </div>

  <FacilityForm on:saved={handleSaved} />
</div>
