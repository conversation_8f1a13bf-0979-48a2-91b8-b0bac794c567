import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { hasSitePermission } from '$lib/auth';

/**
 * Server-side validation for facility updates
 * This ensures users can only update facilities for sites they have access to
 */
export const PUT: RequestHandler = async ({ request, params, locals }) => {
  // Check if user is authenticated
  if (!locals.session || !locals.user) {
    return json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const facilityId = params.id;
    
    // Parse request body
    const data = await request.json();

    // Validate required fields
    if (!data.site_id) {
      return json({ success: false, error: 'Site ID is required' }, { status: 400 });
    }

    if (!data.name) {
      return json({ success: false, error: 'Name is required' }, { status: 400 });
    }

    if (!data.slug) {
      return json({ success: false, error: 'Slug is required' }, { status: 400 });
    }

    // Check if user has permission to manage this site
    const hasPermission = await hasSitePermission(locals.supabase, locals.user.id, data.site_id, 'admin');
    if (!hasPermission) {
      return json({ success: false, error: 'You do not have permission to manage this site' }, { status: 403 });
    }

    // Process data to ensure arrays are properly formatted
    const processedData = {
      ...data,
      features: Array.isArray(data.features) ? data.features : [],
      images: Array.isArray(data.images) ? data.images : [],
      videos: Array.isArray(data.videos) ? data.videos : []
    };

    // Update facility
    const { data: facility, error } = await locals.supabase
      .from('facilities')
      .update(processedData)
      .eq('id', facilityId)
      .select()
      .single();

    if (error) {
      console.error('Error updating facility:', error);
      return json({ success: false, error: error.message }, { status: 500 });
    }

    return json({ success: true, data: facility });
  } catch (err) {
    console.error('Error processing facility update:', err);
    return json({ success: false, error: 'Failed to process request' }, { status: 500 });
  }
};
