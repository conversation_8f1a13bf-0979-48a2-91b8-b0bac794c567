<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { supabase } from '$lib';
  import FacilityForm from '$lib/components/admin/FacilityForm.svelte';
  
  // Get facility ID from URL
  const facilityId = $page.params.id;
  
  // State
  let facility = $state(null);
  let loading = $state(true);
  let error = $state(null);
  
  // Load facility data
  async function loadFacility() {
    try {
      loading = true;
      error = null;
      
      const { data, error: facilityError } = await supabase
        .from('facilities')
        .select('*')
        .eq('id', facilityId)
        .single();
      
      if (facilityError) throw facilityError;
      
      if (!data) {
        throw new Error('Facility not found');
      }
      
      facility = data;
    } catch (err) {
      console.error('Error loading facility:', err);
      error = err.message || 'Failed to load facility';
    } finally {
      loading = false;
    }
  }
  
  // Handle form submission
  function handleSaved() {
    // Reload the page to show updated data
    window.location.reload();
  }
  
  onMount(() => {
    loadFacility();
  });
</script>

<svelte:head>
  <title>Edit Facility - Baberrih Admin</title>
</svelte:head>

<div class="p-6">
  <div class="mb-6">
    <a
      href="/admin/facilities"
      class="text-blue-600 hover:text-blue-800 hover:underline"
    >
      &larr; Back to Facilities
    </a>
  </div>
  
  {#if loading}
    <div class="bg-white rounded-lg shadow p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-blue-600"></div>
      <p class="mt-2 text-gray-600">Loading facility data...</p>
    </div>
  {:else if error}
    <div class="bg-white rounded-lg shadow p-8 text-center">
      <p class="text-red-600">{error}</p>
      <button 
        on:click={loadFacility}
        class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
      >
        Try Again
      </button>
    </div>
  {:else if facility}
    <FacilityForm facility={facility} isEdit={true} on:saved={handleSaved} />
  {/if}
</div>
