<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { supabase } from '$lib';
  import ActivityForm from '$lib/components/admin/ActivityForm.svelte';
  
  // Get activity ID from URL
  const activityId = $page.params.id;
  
  // State
  let activity = $state(null);
  let loading = $state(true);
  let error = $state(null);
  
  // Load activity data
  async function loadActivity() {
    try {
      loading = true;
      error = null;
      
      const { data, error: activityError } = await supabase
        .from('activities')
        .select('*')
        .eq('id', activityId)
        .single();
      
      if (activityError) throw activityError;
      
      if (!data) {
        throw new Error('Activity not found');
      }
      
      activity = data;
    } catch (err) {
      console.error('Error loading activity:', err);
      error = err.message || 'Failed to load activity';
    } finally {
      loading = false;
    }
  }
  
  // Handle form submission
  function handleSaved() {
    // Reload the page to show updated data
    window.location.reload();
  }
  
  onMount(() => {
    loadActivity();
  });
</script>

<svelte:head>
  <title>Edit Activity - Baberrih Admin</title>
</svelte:head>

<div class="p-6">
  <div class="mb-6">
    <a
      href="/admin/activities"
      class="text-blue-600 hover:text-blue-800 hover:underline"
    >
      &larr; Back to Activities
    </a>
  </div>
  
  {#if loading}
    <div class="bg-white rounded-lg shadow p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-blue-600"></div>
      <p class="mt-2 text-gray-600">Loading activity data...</p>
    </div>
  {:else if error}
    <div class="bg-white rounded-lg shadow p-8 text-center">
      <p class="text-red-600">{error}</p>
      <button 
        on:click={loadActivity}
        class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
      >
        Try Again
      </button>
    </div>
  {:else if activity}
    <ActivityForm activity={activity} isEdit={true} on:saved={handleSaved} />
  {/if}
</div>
