<script lang="ts">
  import { goto } from "$app/navigation";
  import ActivityForm from "$lib/components/admin/ActivityForm.svelte";

  // Handle form submission
  function handleSaved(event) {
    const { id } = event.detail;
    goto(`/admin/activities/${id}`);
  }
</script>

<svelte:head>
  <title>Add New Activity - Baberrih Admin</title>
</svelte:head>

<div class="p-6">
  <div class="mb-6">
    <a
      href="/admin/activities"
      class="text-blue-600 hover:text-blue-800 hover:underline"
    >
      &larr; Back to Activities
    </a>
  </div>

  <ActivityForm on:saved={handleSaved} />
</div>
