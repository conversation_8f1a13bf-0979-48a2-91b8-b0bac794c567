<script lang="ts">
  import { onMount } from 'svelte';
  import { supabase } from '$lib';
  import { goto } from '$app/navigation';
  import { Plus, Search, Edit, Trash, ExternalLink, Calendar } from 'lucide-svelte';
  
  // Reservations data
  let reservations = $state([]);
  let loading = $state(true);
  let error = $state(null);
  
  // Filter state
  let searchQuery = $state('');
  let selectedSite = $state('all');
  let selectedStatus = $state('all');
  let sites = $state([]);
  
  // Date filters
  let startDate = $state('');
  let endDate = $state('');
  
  // Pagination
  let currentPage = $state(1);
  let totalPages = $state(1);
  let pageSize = $state(10);
  
  // Load reservations data
  async function loadReservations() {
    try {
      loading = true;
      error = null;
      
      // Calculate pagination
      const from = (currentPage - 1) * pageSize;
      const to = from + pageSize - 1;
      
      // Build query
      let query = supabase
        .from('reservations_with_details')
        .select('*')
        .range(from, to);
      
      // Apply filters
      if (searchQuery) {
        query = query.or(`guest_name.ilike.%${searchQuery}%,guest_email.ilike.%${searchQuery}%,cloudbeds_confirmation_code.ilike.%${searchQuery}%`);
      }
      
      if (selectedSite !== 'all') {
        query = query.eq('site_id', selectedSite);
      }
      
      if (selectedStatus !== 'all') {
        query = query.eq('status', selectedStatus);
      }
      
      if (startDate) {
        query = query.gte('check_in_date', startDate);
      }
      
      if (endDate) {
        query = query.lte('check_out_date', endDate);
      }
      
      // Execute query
      const { data, error: fetchError, count } = await query;
      
      if (fetchError) throw fetchError;
      
      reservations = data || [];
      
      // Calculate total pages
      if (count !== null) {
        totalPages = Math.ceil(count / pageSize);
      }
      
      // Load sites for filter
      await loadSites();
    } catch (err) {
      console.error('Error loading reservations:', err);
      error = err.message || 'Failed to load reservations';
    } finally {
      loading = false;
    }
  }
  
  // Load sites for filter dropdown
  async function loadSites() {
    try {
      const { data, error: sitesError } = await supabase
        .from('sites')
        .select('id, name');
      
      if (sitesError) throw sitesError;
      
      sites = data || [];
    } catch (err) {
      console.error('Error loading sites:', err);
    }
  }
  
  // Handle search
  function handleSearch() {
    currentPage = 1;
    loadReservations();
  }
  
  // Handle filter changes
  function handleFilterChange() {
    currentPage = 1;
    loadReservations();
  }
  
  // Navigate to edit page
  function editReservation(id) {
    goto(`/admin/reservations/${id}`);
  }
  
  // Delete reservation
  async function deleteReservation(id) {
    if (!confirm('Are you sure you want to delete this reservation? This action cannot be undone.')) {
      return;
    }
    
    try {
      const { error: deleteError } = await supabase
        .from('reservations')
        .delete()
        .eq('id', id);
      
      if (deleteError) throw deleteError;
      
      // Reload reservations
      loadReservations();
    } catch (err) {
      console.error('Error deleting reservation:', err);
      alert('Failed to delete reservation: ' + (err.message || 'Unknown error'));
    }
  }
  
  // Pagination controls
  function nextPage() {
    if (currentPage < totalPages) {
      currentPage++;
      loadReservations();
    }
  }
  
  function prevPage() {
    if (currentPage > 1) {
      currentPage--;
      loadReservations();
    }
  }
  
  // Format date
  function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }
  
  onMount(() => {
    loadReservations();
  });
</script>

<svelte:head>
  <title>Manage Reservations - Baberrih Admin</title>
</svelte:head>

<div>
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-semibold text-gray-900">Manage Reservations</h1>
    <a href="/admin/reservations/new" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center">
      <Plus class="w-5 h-5 mr-1" />
      Add New Reservation
    </a>
  </div>
  
  <!-- Filters -->
  <div class="bg-white p-4 rounded-lg shadow mb-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
      <!-- Search -->
      <div>
        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
        <div class="relative">
          <input
            id="search"
            type="text"
            bind:value={searchQuery}
            placeholder="Search by name, email, or confirmation code..."
            class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          />
          <button 
            on:click={handleSearch}
            class="absolute right-2 top-2 text-gray-400 hover:text-gray-600"
          >
            <Search class="w-5 h-5" />
          </button>
        </div>
      </div>
      
      <!-- Site Filter -->
      <div>
        <label for="site-filter" class="block text-sm font-medium text-gray-700 mb-1">Site</label>
        <select
          id="site-filter"
          bind:value={selectedSite}
          on:change={handleFilterChange}
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Sites</option>
          {#each sites as site}
            <option value={site.id}>{site.name}</option>
          {/each}
        </select>
      </div>
      
      <!-- Status Filter -->
      <div>
        <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
        <select
          id="status-filter"
          bind:value={selectedStatus}
          on:change={handleFilterChange}
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Statuses</option>
          <option value="confirmed">Confirmed</option>
          <option value="pending">Pending</option>
          <option value="canceled">Canceled</option>
          <option value="completed">Completed</option>
        </select>
      </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Start Date Filter -->
      <div>
        <label for="start-date" class="block text-sm font-medium text-gray-700 mb-1">Check-in From</label>
        <input
          id="start-date"
          type="date"
          bind:value={startDate}
          on:change={handleFilterChange}
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
      
      <!-- End Date Filter -->
      <div>
        <label for="end-date" class="block text-sm font-medium text-gray-700 mb-1">Check-out Before</label>
        <input
          id="end-date"
          type="date"
          bind:value={endDate}
          on:change={handleFilterChange}
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
      
      <!-- Clear Filters Button -->
      <div class="flex items-end">
        <button
          on:click={() => {
            searchQuery = '';
            selectedSite = 'all';
            selectedStatus = 'all';
            startDate = '';
            endDate = '';
            handleFilterChange();
          }}
          class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
        >
          Clear Filters
        </button>
      </div>
    </div>
  </div>
  
  <!-- Reservations Table -->
  <div class="bg-white rounded-lg shadow overflow-hidden">
    {#if loading}
      <div class="p-8 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-blue-600"></div>
        <p class="mt-2 text-gray-600">Loading reservations...</p>
      </div>
    {:else if error}
      <div class="p-8 text-center">
        <p class="text-red-600">{error}</p>
        <button 
          on:click={loadReservations}
          class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Try Again
        </button>
      </div>
    {:else if reservations.length === 0}
      <div class="p-8 text-center">
        <p class="text-gray-600">No reservations found. Create your first reservation to get started.</p>
        <a 
          href="/admin/reservations/new"
          class="mt-4 inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Add New Reservation
        </a>
      </div>
    {:else}
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Guest</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Suite</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dates</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cloudbeds</th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          {#each reservations as reservation}
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{reservation.guest_name}</div>
                <div class="text-sm text-gray-500">{reservation.guest_email}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{reservation.suite_name}</div>
                <div class="text-sm text-gray-500">{reservation.site_name}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{formatDate(reservation.check_in_date)} - {formatDate(reservation.check_out_date)}</div>
                <div class="text-sm text-gray-500">
                  {reservation.adults} {reservation.adults === 1 ? 'adult' : 'adults'}
                  {#if reservation.children > 0}
                    , {reservation.children} {reservation.children === 1 ? 'child' : 'children'}
                  {/if}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full {
                  reservation.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                  reservation.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                  reservation.status === 'canceled' ? 'bg-red-100 text-red-800' :
                  reservation.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                  'bg-gray-100 text-gray-800'
                }">
                  {reservation.status}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                {#if reservation.cloudbeds_confirmation_code}
                  <div class="text-sm text-gray-900">{reservation.cloudbeds_confirmation_code}</div>
                {:else}
                  <span class="text-sm text-gray-500">Not linked</span>
                {/if}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button 
                    on:click={() => editReservation(reservation.id)}
                    class="text-blue-600 hover:text-blue-900"
                    title="Edit Reservation"
                  >
                    <Edit class="w-5 h-5" />
                  </button>
                  
                  <button 
                    on:click={() => deleteReservation(reservation.id)}
                    class="text-red-600 hover:text-red-900"
                    title="Delete Reservation"
                  >
                    <Trash class="w-5 h-5" />
                  </button>
                </div>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
      
      <!-- Pagination -->
      <div class="px-6 py-3 flex items-center justify-between border-t border-gray-200">
        <div class="text-sm text-gray-700">
          Showing <span class="font-medium">{(currentPage - 1) * pageSize + 1}</span> to <span class="font-medium">{Math.min(currentPage * pageSize, (totalPages - 1) * pageSize + reservations.length)}</span> of <span class="font-medium">{totalPages * pageSize}</span> results
        </div>
        <div class="flex space-x-2">
          <button 
            on:click={prevPage}
            disabled={currentPage === 1}
            class="px-3 py-1 border border-gray-300 rounded-md text-sm {currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'}"
          >
            Previous
          </button>
          <button 
            on:click={nextPage}
            disabled={currentPage === totalPages}
            class="px-3 py-1 border border-gray-300 rounded-md text-sm {currentPage === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'}"
          >
            Next
          </button>
        </div>
      </div>
    {/if}
  </div>
</div>
