<script lang="ts">
  import { goto } from "$app/navigation";
  import SuiteForm from "$lib/components/admin/SuiteForm.svelte";
  import {
    currentSite,
    isLoading as siteContextLoading,
  } from "$lib/site/store";

  // Handle form submission
  function handleSaved(event) {
    const { id } = event.detail;
    goto(`/admin/suites/${id}`);
  }
</script>

<svelte:head>
  <title>Add New Suite - Baberrih Admin</title>
</svelte:head>

<div>
  <div class="flex justify-between items-center mb-6">
    <h1 class="font-semibold text-gray-900 text-2xl">Add New Suite</h1>
    <a href="/admin/suites" class="text-blue-600 hover:text-blue-800">
      &larr; Back to Suites
    </a>
  </div>

  {#if $siteContextLoading}
    <div class="bg-white shadow p-8 rounded-lg text-center">
      <div
        class="inline-block border-4 border-gray-300 border-t-blue-600 rounded-full w-8 h-8 animate-spin"
      ></div>
      <p class="mt-2 text-gray-600">Loading property information...</p>
    </div>
  {:else if !$currentSite}
    <div class="bg-white shadow p-8 rounded-lg text-center">
      <p class="text-red-600">
        No property available. Please contact your administrator.
      </p>
    </div>
  {:else}
    <SuiteForm on:saved={handleSaved} />
  {/if}
</div>
