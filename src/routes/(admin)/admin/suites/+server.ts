import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { hasSitePermission } from '$lib/auth';

/**
 * Server-side validation for suite creation and updates
 * This ensures users can only create or update suites for sites they have access to
 */
export const POST: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated
  if (!locals.session || !locals.user) {
    return json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Parse request body
    const data = await request.json();

    // Validate required fields
    if (!data.site_id) {
      return json({ success: false, error: 'Site ID is required' }, { status: 400 });
    }

    if (!data.name) {
      return json({ success: false, error: 'Suite name is required' }, { status: 400 });
    }

    if (!data.slug) {
      return json({ success: false, error: 'Suite slug is required' }, { status: 400 });
    }

    // Validar que site_id sea un UUID válido
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(data.site_id)) {
      console.error('Invalid site_id format:', data.site_id);
      return json(
        { success: false, error: 'Invalid site ID format' },
        { status: 400 }
      );
    }

    // Verificar que el sitio existe
    const { data: siteExists, error: siteError } = await locals.supabase
      .from('sites')
      .select('id')
      .eq('id', data.site_id)
      .single();

    if (siteError || !siteExists) {
      console.error('Site not found:', data.site_id, siteError);
      return json(
        { success: false, error: 'Site not found' },
        { status: 404 }
      );
    }

    // Verificar permisos usando la función RPC check_site_permission
    // para evitar la recursión infinita en las políticas RLS
    console.log('Verificando permisos con RPC para usuario:', locals.user.id, 'sitio:', data.site_id);

    try {
      // Verificar primero si el usuario es un administrador del sistema
      // Esto es un bypass para permitir a los administradores crear suites sin restricciones
      const { data: isSystemAdmin, error: adminCheckError } = await locals.supabase
        .from('profiles')
        .select('is_admin')
        .eq('id', locals.user.id)
        .single();

      if (adminCheckError) {
        console.error('Error al verificar si es admin del sistema:', adminCheckError);
      } else if (isSystemAdmin?.is_admin) {
        console.log('Usuario es administrador del sistema, concediendo acceso sin verificar permisos de sitio');
      } else {
        // Si no es administrador del sistema, verificar permisos específicos del sitio
        console.log('Usuario no es administrador del sistema, verificando permisos específicos');

        // Usar la función RPC para verificar permisos
        const { data: hasPermission, error: permError } = await locals.supabase
          .rpc('check_site_permission', {
            p_user_id: locals.user.id,
            p_site_id: data.site_id,
            p_permission: 'manage_suites'
          });

        if (permError) {
          console.error('Error al verificar permisos con RPC:', permError);

          // Intentar verificar permisos directamente con una consulta SQL como fallback
          console.log('Intentando verificar permisos con consulta SQL directa');

          // Verificar si el usuario tiene acceso al sitio
          const { data: siteAccess, error: siteAccessError } = await locals.supabase
            .from('site_users')
            .select('id')
            .eq('user_id', locals.user.id)
            .eq('site_id', data.site_id);

          if (siteAccessError) {
            console.error('Error al verificar acceso al sitio:', siteAccessError);
            return json(
              { success: false, error: 'Error checking site access', details: siteAccessError },
              { status: 500 }
            );
          }

          if (!siteAccess || siteAccess.length === 0) {
            console.error('Usuario no tiene acceso al sitio:', locals.user.id, 'sitio:', data.site_id);
            return json(
              { success: false, error: 'You do not have access to this property' },
              { status: 403 }
            );
          }

          // Si tiene acceso al sitio, verificar si tiene permiso para gestionar suites
          const siteUserId = siteAccess[0].id;
          const { data: permissions, error: permissionsError } = await locals.supabase
            .from('user_site_permissions')
            .select('permission')
            .eq('site_user_id', siteUserId);

          if (permissionsError) {
            console.error('Error al verificar permisos específicos:', permissionsError);
            return json(
              { success: false, error: 'Error checking specific permissions', details: permissionsError },
              { status: 500 }
            );
          }

          const hasManageSuitesPermission = permissions && permissions.some(p => p.permission === 'manage_suites' || p.permission === 'admin');

          if (!hasManageSuitesPermission) {
            console.error('Permiso denegado para usuario:', locals.user.id, 'sitio:', data.site_id);
            return json(
              { success: false, error: 'You do not have permission to manage suites for this property' },
              { status: 403 }
            );
          }

          console.log('Usuario tiene permisos para gestionar suites (verificado con SQL), concediendo acceso');
        } else if (!hasPermission) {
          // Si la RPC funcionó pero el usuario no tiene permisos, denegar acceso
          console.error('Permiso denegado para usuario:', locals.user.id, 'sitio:', data.site_id);
          return json(
            { success: false, error: 'You do not have permission to manage suites for this property' },
            { status: 403 }
          );
        } else {
          console.log('Usuario tiene permisos para gestionar suites (verificado con RPC), concediendo acceso');
        }
      }
    } catch (permCheckError) {
      console.error('Error al verificar permisos:', permCheckError);
      return json(
        { success: false, error: 'Error checking permissions', details: permCheckError },
        { status: 500 }
      );
    }

    // Log the data being sent to the database
    console.log('Creating suite with data:', JSON.stringify(data, null, 2));
    console.log('User ID:', locals.user.id);
    console.log('Session:', locals.session ? 'Valid' : 'Invalid');

    try {
      console.log('Intentando crear suite con los siguientes datos:', JSON.stringify(data, null, 2));

      // Verificar que el sitio existe antes de intentar crear la suite
      const { data: siteCheck, error: siteCheckError } = await locals.supabase
        .from('sites')
        .select('id, name')
        .eq('id', data.site_id)
        .single();

      if (siteCheckError) {
        console.error('Error al verificar el sitio:', siteCheckError);
        return json(
          { success: false, error: 'Error verifying site', details: siteCheckError },
          { status: 500 }
        );
      }

      if (!siteCheck) {
        console.error('El sitio no existe:', data.site_id);
        return json(
          { success: false, error: 'The specified property does not exist' },
          { status: 400 }
        );
      }

      console.log('Sitio verificado:', siteCheck.name, '(ID:', siteCheck.id, ')');

      // Verificar si ya existe una suite con el mismo slug en el mismo sitio
      const { data: existingSuite, error: existingSuiteError } = await locals.supabase
        .from('suites')
        .select('id, name')
        .eq('site_id', data.site_id)
        .eq('slug', data.slug)
        .maybeSingle();

      if (existingSuiteError) {
        console.error('Error al verificar si existe una suite con el mismo slug:', existingSuiteError);
      } else if (existingSuite) {
        console.error('Ya existe una suite con el mismo slug en este sitio:', existingSuite);
        return json(
          {
            success: false,
            error: 'A suite with this slug already exists in this property. Please use a different slug.',
            details: { existing_suite: existingSuite.name, slug: data.slug }
          },
          { status: 400 }
        );
      }

      // Process the data to ensure arrays are properly handled
      const processedData = {
        ...data,
        // Ensure images is an array
        images: Array.isArray(data.images) ? data.images :
          (typeof data.images === 'string' ?
            (data.images === '[]' ? [] : JSON.parse(data.images)) :
            []),
        // Ensure videos is an array
        videos: Array.isArray(data.videos) ? data.videos :
          (typeof data.videos === 'string' ?
            (data.videos === '[]' ? [] : JSON.parse(data.videos)) :
            []),
      };

      // Force empty arrays to be stored as empty arrays, not strings
      if (processedData.images && processedData.images.length === 0) {
        processedData.images = [];
      }

      if (processedData.videos && processedData.videos.length === 0) {
        processedData.videos = [];
      }

      // Log the data being sent to the database
      console.log('Creating suite with data:', JSON.stringify(processedData, null, 2));
      console.log('Data types:', {
        images: Array.isArray(processedData.images) ? 'array' : typeof processedData.images,
        videos: Array.isArray(processedData.videos) ? 'array' : typeof processedData.videos,
      });

      // Create suite
      console.log('Ejecutando inserción en la tabla suites');
      const { data: suite, error } = await locals.supabase
        .from('suites')
        .insert(processedData)
        .select()
        .single();

      if (error) {
        console.error('Database error creating suite:', error);
        console.error('Error details:', JSON.stringify(error, null, 2));

        // Proporcionar un mensaje de error más específico basado en el código de error
        if (error.code === '23505') {
          return json(
            {
              success: false,
              error: 'A suite with this slug already exists in this property. Please use a different slug.',
              details: error.details
            },
            { status: 400 }
          );
        } else if (error.code === '23503') {
          return json(
            {
              success: false,
              error: 'The referenced property does not exist.',
              details: error.details
            },
            { status: 400 }
          );
        } else if (error.code === '42P17') {
          return json(
            {
              success: false,
              error: 'Infinite recursion detected in policy. Please contact support.',
              details: error.details
            },
            { status: 500 }
          );
        }

        throw error;
      }

      console.log('Suite created successfully:', suite?.id);
      return json({ success: true, data: suite });
    } catch (insertError) {
      console.error('Exception during suite creation:', insertError);

      // Try to get more information about the error
      if (insertError && typeof insertError === 'object') {
        console.error('Error object:', JSON.stringify(insertError, null, 2));
      }

      return json(
        {
          success: false,
          error: insertError instanceof Error ? insertError.message : 'An error occurred while creating the suite',
          details: insertError
        },
        { status: 500 }
      );
    }
  } catch (err) {
    console.error('Error creating suite:', err);

    // Check for specific database errors
    if (err && typeof err === 'object' && 'code' in err) {
      const pgError = err as { code: string; message: string; detail?: string };

      // Handle unique constraint violation
      if (pgError.code === '23505') {
        return json(
          {
            success: false,
            error: 'A suite with this slug already exists in this property. Please use a different slug.',
            details: pgError.detail || ''
          },
          { status: 400 }
        );
      }

      // Handle foreign key violation
      if (pgError.code === '23503') {
        return json(
          {
            success: false,
            error: 'The referenced property does not exist.',
            details: pgError.detail || ''
          },
          { status: 400 }
        );
      }
    }

    // Generic error handling
    return json(
      {
        success: false,
        error: err instanceof Error ? err.message : 'An error occurred while creating the suite',
        stack: process.env.NODE_ENV === 'development' ? (err instanceof Error ? err.stack : undefined) : undefined
      },
      { status: 500 }
    );
  }
};

export const PUT: RequestHandler = async ({ request, locals, params }) => {
  // Check if user is authenticated
  if (!locals.session || !locals.user) {
    return json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Parse request body
    const data = await request.json();
    const suiteId = data.id;

    if (!suiteId) {
      return json({ success: false, error: 'Suite ID is required' }, { status: 400 });
    }

    // Get the suite to check its site_id
    const { data: existingSuite, error: fetchError } = await locals.supabase
      .from('suites')
      .select('site_id')
      .eq('id', suiteId)
      .single();

    if (fetchError) {
      throw fetchError;
    }

    if (!existingSuite) {
      return json({ success: false, error: 'Suite not found' }, { status: 404 });
    }

    // Check if user has permission to manage this site
    const hasPermission = await hasSitePermission(
      locals.supabase,
      locals.user.id,
      existingSuite.site_id,
      'manage_suites'
    );

    if (!hasPermission) {
      return json(
        { success: false, error: 'You do not have permission to manage suites for this property' },
        { status: 403 }
      );
    }

    // Prevent changing site_id
    if (data.site_id && data.site_id !== existingSuite.site_id) {
      return json(
        { success: false, error: 'Changing the property of a suite is not allowed' },
        { status: 400 }
      );
    }

    // Process the data to ensure arrays are properly handled
    const processedData = {
      ...data,
      // Ensure images is an array
      images: Array.isArray(data.images) ? data.images :
        (typeof data.images === 'string' ?
          (data.images === '[]' ? [] : JSON.parse(data.images)) :
          []),
      // Ensure videos is an array
      videos: Array.isArray(data.videos) ? data.videos :
        (typeof data.videos === 'string' ?
          (data.videos === '[]' ? [] : JSON.parse(data.videos)) :
          []),
    };

    // Force empty arrays to be stored as empty arrays, not strings
    if (processedData.images && processedData.images.length === 0) {
      processedData.images = [];
    }

    if (processedData.videos && processedData.videos.length === 0) {
      processedData.videos = [];
    }

    // Log the data being sent to the database
    console.log('Updating suite with data:', JSON.stringify(processedData, null, 2));
    console.log('Data types:', {
      images: Array.isArray(processedData.images) ? 'array' : typeof processedData.images,
      videos: Array.isArray(processedData.videos) ? 'array' : typeof processedData.videos,
    });

    // Update suite
    const { data: updatedSuite, error } = await locals.supabase
      .from('suites')
      .update(processedData)
      .eq('id', suiteId)
      .select()
      .single();

    if (error) {
      console.error('Database error updating suite:', error);
      throw error;
    }

    return json({ success: true, data: updatedSuite });
  } catch (err) {
    console.error('Error updating suite:', err);

    // Check for specific database errors
    if (err && typeof err === 'object' && 'code' in err) {
      const pgError = err as { code: string; message: string; detail?: string };

      // Handle unique constraint violation
      if (pgError.code === '23505') {
        return json(
          {
            success: false,
            error: 'A suite with this slug already exists in this property. Please use a different slug.',
            details: pgError.detail || ''
          },
          { status: 400 }
        );
      }

      // Handle foreign key violation
      if (pgError.code === '23503') {
        return json(
          {
            success: false,
            error: 'The referenced property does not exist.',
            details: pgError.detail || ''
          },
          { status: 400 }
        );
      }
    }

    // Generic error handling
    return json(
      {
        success: false,
        error: err instanceof Error ? err.message : 'An error occurred while updating the suite',
        stack: process.env.NODE_ENV === 'development' ? (err instanceof Error ? err.stack : undefined) : undefined
      },
      { status: 500 }
    );
  }
};
