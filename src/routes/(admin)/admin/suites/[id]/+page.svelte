<script lang="ts">
  import { onMount } from "svelte";
  import { page } from "$app/stores";
  import { supabase } from "$lib";
  import SuiteForm from "$lib/components/admin/SuiteForm.svelte";
  import {
    currentSite,
    isLoading as siteContextLoading,
  } from "$lib/site/store";

  // Get suite ID from URL
  const suiteId = $page.params.id;

  // State
  let suite = $state(null);
  let loading = $state(true);
  let error = $state(null);

  // Load suite data
  async function loadSuite() {
    try {
      loading = true;
      error = null;

      if (!supabase) {
        throw new Error("Supabase client not available");
      }

      // Load suite data
      const { data, error: suiteError } = await supabase
        .from("suites")
        .select("*")
        .eq("id", suiteId)
        .single();

      if (suiteError) throw suiteError;

      if (!data) {
        throw new Error("Suite not found");
      }

      // Process the data to ensure arrays are properly handled
      suite = {
        ...data,
        // Ensure images is an array
        images: Array.isArray(data.images)
          ? data.images
          : typeof data.images === "string"
            ? data.images === "[]"
              ? []
              : JSON.parse(data.images)
            : [],
        // Ensure videos is an array
        videos: Array.isArray(data.videos)
          ? data.videos
          : typeof data.videos === "string"
            ? data.videos === "[]"
              ? []
              : JSON.parse(data.videos)
            : [],
      };

      console.log("Suite data loaded:", data);
      console.log("Processed suite data:", suite);
    } catch (err) {
      console.error("Error loading suite:", err);
      error =
        typeof err === "object" && err !== null && "message" in err
          ? err.message
          : "Failed to load suite";
    } finally {
      loading = false;
    }
  }

  // We no longer need to load sites as we're using the site context

  // Handle form submission
  function handleSaved() {
    // Reload the page to show updated data
    window.location.reload();
  }

  onMount(() => {
    loadSuite();
  });
</script>

<svelte:head>
  <title>Edit Suite - Baberrih Admin</title>
</svelte:head>

<div>
  <div class="flex justify-between items-center mb-6">
    <h1 class="font-semibold text-gray-900 text-2xl">
      {#if suite}
        Edit Suite: {suite.name}
      {:else}
        Edit Suite
      {/if}
    </h1>
    <a href="/admin/suites" class="text-blue-600 hover:text-blue-800">
      &larr; Back to Suites
    </a>
  </div>

  {#if loading}
    <div class="bg-white shadow p-8 rounded-lg text-center">
      <div
        class="inline-block border-4 border-gray-300 border-t-blue-600 rounded-full w-8 h-8 animate-spin"
      ></div>
      <p class="mt-2 text-gray-600">Loading suite data...</p>
    </div>
  {:else if error}
    <div class="bg-white shadow p-8 rounded-lg text-center">
      <p class="text-red-600">{error}</p>
      <button
        onclick={loadSuite}
        class="bg-blue-600 hover:bg-blue-700 mt-4 px-4 py-2 rounded-md text-white"
      >
        Try Again
      </button>
    </div>
  {:else if suite}
    <SuiteForm {suite} isEdit={true} on:saved={handleSaved} />
  {/if}
</div>
