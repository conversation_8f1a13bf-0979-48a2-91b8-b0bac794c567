import { json, type RequestHandler } from '@sveltejs/kit';
import { hasSitePermission } from '$lib/permissions';

export const PUT: RequestHandler = async ({ request, locals }) => {
  // Check if user is authenticated
  if (!locals.session || !locals.user) {
    return json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Parse request body
    const data = await request.json();
    const suiteId = data.id;

    if (!suiteId) {
      return json({ success: false, error: 'Suite ID is required' }, { status: 400 });
    }

    // Get the suite to check its site_id
    const { data: existingSuite, error: fetchError } = await locals.supabase
      .from('suites')
      .select('site_id')
      .eq('id', suiteId)
      .single();

    if (fetchError) {
      throw fetchError;
    }

    if (!existingSuite) {
      return json({ success: false, error: 'Suite not found' }, { status: 404 });
    }

    // Check if user has permission to manage this site
    const hasPermission = await hasSitePermission(
      locals.supabase,
      locals.user.id,
      existingSuite.site_id,
      'manage_suites'
    );

    if (!hasPermission) {
      return json(
        { success: false, error: 'You do not have permission to manage suites for this property' },
        { status: 403 }
      );
    }

    // Prepare update data - only include media fields
    const updateData: any = {};
    
    // Only include the fields that are present in the request
    if ('images' in data) {
      updateData.images = Array.isArray(data.images) ? data.images : [];
    }
    
    if ('videos' in data) {
      updateData.videos = Array.isArray(data.videos) ? data.videos : [];
    }
    
    // Force empty arrays to be stored as empty arrays, not strings
    if (updateData.images && updateData.images.length === 0) {
      updateData.images = [];
    }
    
    if (updateData.videos && updateData.videos.length === 0) {
      updateData.videos = [];
    }
    
    // Log the data being sent to the database
    console.log('Updating suite media with data:', JSON.stringify(updateData, null, 2));
    console.log('Media data types:', {
      images: updateData.images ? (Array.isArray(updateData.images) ? 'array' : typeof updateData.images) : 'not included',
      videos: updateData.videos ? (Array.isArray(updateData.videos) ? 'array' : typeof updateData.videos) : 'not included',
    });
    
    // Update suite
    const { data: updatedSuite, error } = await locals.supabase
      .from('suites')
      .update(updateData)
      .eq('id', suiteId)
      .select()
      .single();

    if (error) {
      console.error('Database error updating suite media:', error);
      throw error;
    }

    return json({ success: true, data: updatedSuite });
  } catch (err) {
    console.error('Error updating suite media:', err);
    
    // Generic error handling
    return json(
      {
        success: false,
        error: err instanceof Error ? err.message : 'An error occurred while updating suite media',
        stack: process.env.NODE_ENV === 'development' ? (err instanceof Error ? err.stack : undefined) : undefined
      },
      { status: 500 }
    );
  }
};
