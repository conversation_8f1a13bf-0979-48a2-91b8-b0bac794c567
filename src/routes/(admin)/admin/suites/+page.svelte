<script lang="ts">
  import { onMount } from 'svelte';
  import { supabase } from '$lib';
  import { goto } from '$app/navigation';
  import { Plus, Search, Edit, Trash, Link, ExternalLink } from 'lucide-svelte';
  
  // Suites data
  let suites = $state([]);
  let loading = $state(true);
  let error = $state(null);
  
  // Filter state
  let searchQuery = $state('');
  let selectedSite = $state('all');
  let sites = $state([]);
  
  // Pagination
  let currentPage = $state(1);
  let totalPages = $state(1);
  let pageSize = $state(10);
  
  // Load suites data
  async function loadSuites() {
    try {
      loading = true;
      error = null;
      
      // Calculate pagination
      const from = (currentPage - 1) * pageSize;
      const to = from + pageSize - 1;
      
      // Build query
      let query = supabase
        .from('suites_with_mappings')
        .select('*')
        .range(from, to);
      
      // Apply filters
      if (searchQuery) {
        query = query.or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
      }
      
      if (selectedSite !== 'all') {
        query = query.eq('site_id', selectedSite);
      }
      
      // Execute query
      const { data, error: fetchError, count } = await query;
      
      if (fetchError) throw fetchError;
      
      suites = data || [];
      
      // Calculate total pages
      if (count !== null) {
        totalPages = Math.ceil(count / pageSize);
      }
      
      // Load sites for filter
      await loadSites();
    } catch (err) {
      console.error('Error loading suites:', err);
      error = err.message || 'Failed to load suites';
    } finally {
      loading = false;
    }
  }
  
  // Load sites for filter dropdown
  async function loadSites() {
    try {
      const { data, error: sitesError } = await supabase
        .from('sites')
        .select('id, name');
      
      if (sitesError) throw sitesError;
      
      sites = data || [];
    } catch (err) {
      console.error('Error loading sites:', err);
    }
  }
  
  // Handle search
  function handleSearch() {
    currentPage = 1;
    loadSuites();
  }
  
  // Handle site filter change
  function handleSiteChange() {
    currentPage = 1;
    loadSuites();
  }
  
  // Navigate to edit page
  function editSuite(id) {
    goto(`/admin/suites/${id}`);
  }
  
  // Delete suite
  async function deleteSuite(id) {
    if (!confirm('Are you sure you want to delete this suite? This action cannot be undone.')) {
      return;
    }
    
    try {
      const { error: deleteError } = await supabase
        .from('suites')
        .delete()
        .eq('id', id);
      
      if (deleteError) throw deleteError;
      
      // Reload suites
      loadSuites();
    } catch (err) {
      console.error('Error deleting suite:', err);
      alert('Failed to delete suite: ' + (err.message || 'Unknown error'));
    }
  }
  
  // Pagination controls
  function nextPage() {
    if (currentPage < totalPages) {
      currentPage++;
      loadSuites();
    }
  }
  
  function prevPage() {
    if (currentPage > 1) {
      currentPage--;
      loadSuites();
    }
  }
  
  onMount(() => {
    loadSuites();
  });
</script>

<svelte:head>
  <title>Manage Suites - Baberrih Admin</title>
</svelte:head>

<div>
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-semibold text-gray-900">Manage Suites</h1>
    <a href="/admin/suites/new" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center">
      <Plus class="w-5 h-5 mr-1" />
      Add New Suite
    </a>
  </div>
  
  <!-- Filters -->
  <div class="bg-white p-4 rounded-lg shadow mb-6">
    <div class="flex flex-col md:flex-row gap-4">
      <div class="flex-1">
        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
        <div class="relative">
          <input
            id="search"
            type="text"
            bind:value={searchQuery}
            placeholder="Search by name or description..."
            class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          />
          <button 
            on:click={handleSearch}
            class="absolute right-2 top-2 text-gray-400 hover:text-gray-600"
          >
            <Search class="w-5 h-5" />
          </button>
        </div>
      </div>
      
      <div class="md:w-64">
        <label for="site-filter" class="block text-sm font-medium text-gray-700 mb-1">Filter by Site</label>
        <select
          id="site-filter"
          bind:value={selectedSite}
          on:change={handleSiteChange}
          class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Sites</option>
          {#each sites as site}
            <option value={site.id}>{site.name}</option>
          {/each}
        </select>
      </div>
    </div>
  </div>
  
  <!-- Suites Table -->
  <div class="bg-white rounded-lg shadow overflow-hidden">
    {#if loading}
      <div class="p-8 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-blue-600"></div>
        <p class="mt-2 text-gray-600">Loading suites...</p>
      </div>
    {:else if error}
      <div class="p-8 text-center">
        <p class="text-red-600">{error}</p>
        <button 
          on:click={loadSuites}
          class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Try Again
        </button>
      </div>
    {:else if suites.length === 0}
      <div class="p-8 text-center">
        <p class="text-gray-600">No suites found. Create your first suite to get started.</p>
        <a 
          href="/admin/suites/new"
          class="mt-4 inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Add New Suite
        </a>
      </div>
    {:else}
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Site</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cloudbeds</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          {#each suites as suite}
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{suite.name}</div>
                <div class="text-sm text-gray-500">{suite.slug}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{suite.site_name}</div>
                <div class="text-sm text-gray-500">{suite.site_domain}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                {#if suite.cloudbeds_room_type_id}
                  <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                    Linked
                  </span>
                {:else}
                  <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                    Not Linked
                  </span>
                {/if}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full {suite.status === 'active' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'}">
                  {suite.status}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button 
                    on:click={() => editSuite(suite.id)}
                    class="text-blue-600 hover:text-blue-900"
                    title="Edit Suite"
                  >
                    <Edit class="w-5 h-5" />
                  </button>
                  
                  <a 
                    href={`/suites/${suite.slug}`}
                    target="_blank"
                    class="text-gray-600 hover:text-gray-900"
                    title="View Suite"
                  >
                    <ExternalLink class="w-5 h-5" />
                  </a>
                  
                  <button 
                    on:click={() => deleteSuite(suite.id)}
                    class="text-red-600 hover:text-red-900"
                    title="Delete Suite"
                  >
                    <Trash class="w-5 h-5" />
                  </button>
                </div>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
      
      <!-- Pagination -->
      <div class="px-6 py-3 flex items-center justify-between border-t border-gray-200">
        <div class="text-sm text-gray-700">
          Showing <span class="font-medium">{(currentPage - 1) * pageSize + 1}</span> to <span class="font-medium">{Math.min(currentPage * pageSize, (totalPages - 1) * pageSize + suites.length)}</span> of <span class="font-medium">{totalPages * pageSize}</span> results
        </div>
        <div class="flex space-x-2">
          <button 
            on:click={prevPage}
            disabled={currentPage === 1}
            class="px-3 py-1 border border-gray-300 rounded-md text-sm {currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'}"
          >
            Previous
          </button>
          <button 
            on:click={nextPage}
            disabled={currentPage === totalPages}
            class="px-3 py-1 border border-gray-300 rounded-md text-sm {currentPage === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'}"
          >
            Next
          </button>
        </div>
      </div>
    {/if}
  </div>
</div>
