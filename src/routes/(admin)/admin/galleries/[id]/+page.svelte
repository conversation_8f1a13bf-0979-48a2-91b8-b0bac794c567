<script lang="ts">
  import { onMount } from "svelte";
  import { page } from "$app/stores";
  import { goto } from "$app/navigation";
  import GalleryForm from "$lib/components/admin/GalleryForm.svelte";

  // State
  let gallery = $state<any>(null);
  let loading = $state(true);
  let error = $state<string | null>(null);

  // Get gallery ID from URL
  const galleryId = $page.params.id;

  // Load gallery data
  async function loadGallery() {
    try {
      loading = true;
      error = null;

      const response = await fetch(`/admin/gallery?id=${galleryId}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to load gallery");
      }

      gallery = result.data;
    } catch (err: any) {
      console.error("Error loading gallery:", err);
      error = err.message || "Failed to load gallery";
    } finally {
      loading = false;
    }
  }

  // Handle saved event
  function handleSaved() {
    // Reload gallery data
    loadGallery();
  }

  // Load gallery when component mounts
  onMount(() => {
    loadGallery();
  });
</script>

<div class="container mx-auto p-6">
  <div class="mb-6">
    <h1 class="font-medium text-gray-900 text-2xl">Edit Gallery</h1>
    <p class="text-gray-600">
      Update your gallery information and manage images.
    </p>
  </div>

  {#if loading}
    <div class="flex justify-center items-center p-8">
      <div
        class="border-4 border-gray-300 border-t-blue-600 rounded-full w-8 h-8 animate-spin"
      ></div>
    </div>
  {:else if error}
    <div class="bg-red-50 p-4 border border-red-200 rounded-md">
      <p class="text-red-700">{error}</p>
      <button
        type="button"
        class="mt-2 text-blue-600 hover:text-blue-800 underline"
        on:click={() => goto("/admin/galleries")}
      >
        Back to Galleries
      </button>
    </div>
  {:else if gallery}
    <GalleryForm gallery={gallery} isEdit={true} on:saved={handleSaved} />
  {/if}
</div>
