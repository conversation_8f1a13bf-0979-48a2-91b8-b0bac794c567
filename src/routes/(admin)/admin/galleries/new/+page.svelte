<script lang="ts">
  import { goto } from "$app/navigation";
  import GalleryForm from "$lib/components/admin/GalleryForm.svelte";

  // Listen for saved event
  function handleSaved(event: CustomEvent) {
    const { id } = event.detail;
    goto(`/admin/galleries/${id}`);
  }
</script>

<div class="container mx-auto p-6">
  <div class="mb-6">
    <h1 class="font-medium text-gray-900 text-2xl">Create New Gallery</h1>
    <p class="text-gray-600">
      Create a new gallery to showcase images on your website.
    </p>
  </div>

  <GalleryForm on:saved={handleSaved} />
</div>
