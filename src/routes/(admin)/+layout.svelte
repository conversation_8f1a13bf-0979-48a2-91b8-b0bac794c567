<script lang="ts">
  import "../../app.css"; // Import CSS directly to make this layout completely independent
  import { browser } from "$app/environment"; // Import browser
  import { enhance } from "$app/forms"; // Import enhance for form handling
  // No necesitamos importar page ni goto
  import {
    Home,
    Users,
    Bed,
    Calendar,
    Settings,
    LogOut,
    Menu,
    X,
    Image,
    LayoutDashboard,
    Building,
    Palmtree,
  } from "lucide-svelte";

  import { supabase } from "$lib/supabase.client"; // Import client-side Supabase
  import { clearBrowserAuthCookies } from "$lib/auth/cookie-utils"; // Import cookie utility
  import { authStore, user } from "$lib/auth"; // Import auth store
  import { siteContextStore } from "$lib/site/store"; // Import site context store
  import SiteSelector from "$lib/components/admin/SiteSelector.svelte"; // Import site selector component

  let { children } = $props();

  // Mobile menu state
  let showMobileMenu = $state(false);

  // Toggle mobile menu
  function toggleMobileMenu() {
    showMobileMenu = !showMobileMenu;
  }

  // Check if a route is active
  function isActive(path: string): boolean {
    if (browser) {
      const pathname = window.location.pathname;
      return pathname.startsWith(path);
    }
    return false; // Default to false on the server
  }

  // Check if current page is login or unauthorized page
  function isAuthPage(): boolean {
    if (browser) {
      const pathname = window.location.pathname;
      return (
        pathname.includes("/admin/login") ||
        pathname.includes("/admin/unauthorized")
      );
    }
    return false;
  }

  // Computed property to determine if sidebar should be shown
  let showSidebar = $derived(!isAuthPage());

  // Initialize site context when user is available
  $effect(() => {
    if ($user && $user.id && browser && supabase) {
      console.log(
        "[Admin Layout] Initializing site context for user:",
        $user.id
      );
      siteContextStore.initialize(supabase, $user.id);
    }
  });

  // Navigation items
  const navItems = [
    { path: "/admin", label: "Dashboard", icon: Home },
    { path: "/admin/suites", label: "Suites", icon: Bed },
    { path: "/admin/facilities", label: "Facilities", icon: Building },
    { path: "/admin/activities", label: "Activities", icon: Palmtree },
    { path: "/admin/galleries", label: "Galleries", icon: Image },
    { path: "/admin/site-users", label: "Users", icon: Users },
    { path: "/admin/reservations", label: "Reservations", icon: Calendar },
    { path: "/admin/cloudbeds-test", label: "Cloudbeds Test", icon: LayoutDashboard },
    { path: "/admin/dynamic-resolution-test", label: "Room Type Resolution", icon: LayoutDashboard },
    { path: "/admin/settings", label: "Settings", icon: Settings },
  ];
</script>

<div class="flex bg-primary-50 min-h-screen">
  {#if showSidebar}
    <!-- Sidebar - Desktop -->
    <aside class="hidden md:flex flex-col bg-primary-100/80 shadow-md md:w-64">
      <div class="p-6 border-primary-200 border-b">
        <a href="/admin" class="flex items-center">
          <img
            src="/baberrih-logo.svg"
            alt="Baberrih Logo"
            class="w-auto h-8"
          />
          <span
            class="ml-2 font-montserrat font-light text-primary-900 uppercase tracking-wider"
            >Admin</span
          >
        </a>
      </div>

      <nav class="flex-1 p-4">
        <div class="bg-primary-300 mx-auto mb-6 w-16 h-0.5"></div>

        <!-- Site Selector -->
        <div class="mb-6">
          <SiteSelector />
        </div>

        <ul class="space-y-2">
          {#each navItems as item}
            <li>
              <a
                href={item.path}
                class="flex items-center px-4 py-3 rounded-sm font-montserrat uppercase tracking-wide text-xs {isActive(
                  item.path
                )
                  ? 'bg-primary-200/50 text-primary-800 font-medium'
                  : 'text-primary-700 hover:bg-primary-200/30'}"
              >
                {#if item.icon}
                  <div class="mr-3">
                    <item.icon class="w-4 h-4" />
                  </div>
                {/if}
                {item.label}
              </a>
            </li>
          {/each}
        </ul>

        <div class="mt-8 pt-4 border-primary-200 border-t">
          <form
            action="/admin/login?/logout"
            method="POST"
            use:enhance={async () => {
              console.log(
                "[CLIENT DEBUG /admin] Botón de logout (desktop) presionado, iniciando use:enhance"
              );
              try {
                console.log(
                  "[CLIENT DEBUG /admin] Estado de localStorage ANTES de supabase.auth.signOut() (desktop):",
                  JSON.stringify(localStorage)
                );

                // Step 1: Client-side sign out
                const { error: signOutError } =
                  (await supabase?.auth.signOut()) || { error: null };
                if (signOutError) {
                  console.error(
                    "[CLIENT DEBUG /admin] Error en supabase.auth.signOut() (desktop):",
                    signOutError
                  );
                } else {
                  console.log(
                    "[CLIENT DEBUG /admin] supabase.auth.signOut() completado en cliente (desktop)."
                  );
                }

                // Step 2: Update the auth store
                authStore.clearSession();

                // Step 3: Manually clear all auth cookies and localStorage
                clearBrowserAuthCookies();

                console.log(
                  "[CLIENT DEBUG /admin] Estado de localStorage DESPUÉS de limpieza (desktop):",
                  JSON.stringify(localStorage)
                );
                console.log(
                  "[CLIENT DEBUG /admin] Cookies después de limpieza (desktop):",
                  document.cookie
                );
              } catch (e) {
                console.error(
                  "[CLIENT DEBUG /admin] Excepción durante supabase.auth.signOut() en cliente (desktop):",
                  e
                );
              }

              return async ({ result }) => {
                console.log(
                  "[CLIENT DEBUG /admin] use:enhance callback (desktop) - resultado del servidor:",
                  result
                );
                // Invalidar la sesión en el cliente también
                if (result.type === "redirect") {
                  console.log(
                    `[CLIENT DEBUG /admin] Redirigiendo a (desktop): ${result.location}`
                  );
                  // Forzar una recarga completa para asegurar que todas las cookies se limpien
                  window.location.href = result.location;
                }
              };
            }}
          >
            <button
              type="submit"
              class="flex items-center hover:bg-primary-200/30 px-4 py-3 rounded-sm w-full font-montserrat text-primary-700 text-xs uppercase tracking-wide"
            >
              <LogOut class="mr-3 w-4 h-4" />
              Cerrar sesión
            </button>
          </form>
          <a
            href="/"
            class="flex items-center hover:bg-primary-200/30 mt-2 px-4 py-3 rounded-sm font-montserrat text-primary-700 text-xs uppercase tracking-wide"
          >
            <span class="mr-3 w-4 h-4"></span>
            Volver al sitio
          </a>
        </div>
      </nav>
    </aside>

    <!-- Mobile Header -->
    <div
      class="md:hidden top-0 right-0 left-0 z-10 fixed bg-primary-100/90 shadow-sm"
    >
      <div class="flex justify-between items-center p-4">
        <a href="/admin" class="flex items-center">
          <img
            src="/baberrih-logo.svg"
            alt="Baberrih Logo"
            class="w-auto h-8"
          />
          <span
            class="ml-2 font-montserrat font-light text-primary-900 uppercase tracking-wider"
            >Admin</span
          >
        </a>

        <button
          onclick={toggleMobileMenu}
          class="hover:bg-primary-200/30 p-2 rounded-sm text-primary-700"
        >
          {#if showMobileMenu}
            <X class="w-5 h-5" />
          {:else}
            <Menu class="w-5 h-5" />
          {/if}
        </button>
      </div>
    </div>

    <!-- Mobile Menu -->
    {#if showMobileMenu}
      <div class="md:hidden z-20 fixed inset-0 bg-primary-950/70">
        <div
          class="top-0 right-0 bottom-0 absolute bg-primary-50 shadow-lg w-64"
        >
          <div
            class="flex justify-between items-center p-4 border-primary-200 border-b"
          >
            <span
              class="font-montserrat font-light text-primary-900 text-sm uppercase tracking-wider"
              >Menu</span
            >
            <button
              onclick={toggleMobileMenu}
              class="hover:bg-primary-200/30 p-2 rounded-sm text-primary-700"
            >
              <X class="w-4 h-4" />
            </button>
          </div>

          <nav class="p-4">
            <div class="bg-primary-300 mx-auto mb-6 w-16 h-0.5"></div>

            <!-- Site Selector (Mobile) -->
            <div class="mb-6">
              <SiteSelector />
            </div>

            <ul class="space-y-2">
              {#each navItems as item}
                <li>
                  <a
                    href={item.path}
                    onclick={toggleMobileMenu}
                    class="flex items-center px-4 py-3 rounded-sm font-montserrat uppercase tracking-wide text-xs {isActive(
                      item.path
                    )
                      ? 'bg-primary-200/50 text-primary-800 font-medium'
                      : 'text-primary-700 hover:bg-primary-200/30'}"
                  >
                    {#if item.icon}
                      <div class="mr-3">
                        <item.icon class="w-4 h-4" />
                      </div>
                    {/if}
                    {item.label}
                  </a>
                </li>
              {/each}
            </ul>

            <div class="mt-8 pt-4 border-primary-200 border-t">
              <form
                action="/admin/login?/logout"
                method="POST"
                use:enhance={async () => {
                  console.log(
                    "[CLIENT DEBUG /admin] Botón de logout (mobile) presionado, iniciando use:enhance"
                  );
                  try {
                    console.log(
                      "[CLIENT DEBUG /admin] Estado de localStorage ANTES de supabase.auth.signOut() (mobile):",
                      JSON.stringify(localStorage)
                    );

                    // Step 1: Client-side sign out
                    const { error: signOutError } =
                      (await supabase?.auth.signOut()) || { error: null };
                    if (signOutError) {
                      console.error(
                        "[CLIENT DEBUG /admin] Error en supabase.auth.signOut() (mobile):",
                        signOutError
                      );
                    } else {
                      console.log(
                        "[CLIENT DEBUG /admin] supabase.auth.signOut() completado en cliente (mobile)."
                      );
                    }

                    // Step 2: Update the auth store
                    authStore.clearSession();

                    // Step 3: Manually clear all auth cookies and localStorage
                    clearBrowserAuthCookies();

                    console.log(
                      "[CLIENT DEBUG /admin] Estado de localStorage DESPUÉS de limpieza (mobile):",
                      JSON.stringify(localStorage)
                    );
                    console.log(
                      "[CLIENT DEBUG /admin] Cookies después de limpieza (mobile):",
                      document.cookie
                    );
                  } catch (e) {
                    console.error(
                      "[CLIENT DEBUG /admin] Excepción durante supabase.auth.signOut() en cliente (mobile):",
                      e
                    );
                  }

                  return async ({ result }) => {
                    console.log(
                      "[CLIENT DEBUG /admin] use:enhance callback (mobile) - resultado del servidor:",
                      result
                    );
                    // Invalidar la sesión en el cliente también
                    if (result.type === "redirect") {
                      console.log(
                        `[CLIENT DEBUG /admin] Redirigiendo a (mobile): ${result.location}`
                      );
                      // Forzar una recarga completa para asegurar que todas las cookies se limpien
                      window.location.href = result.location;
                    }
                  };
                }}
              >
                <button
                  type="submit"
                  class="flex items-center hover:bg-primary-200/30 px-4 py-3 rounded-sm w-full font-montserrat text-primary-700 text-xs uppercase tracking-wide"
                >
                  <LogOut class="mr-3 w-4 h-4" />
                  Cerrar sesión
                </button>
              </form>
              <a
                href="/"
                class="flex items-center hover:bg-primary-200/30 mt-2 px-4 py-3 rounded-sm font-montserrat text-primary-700 text-xs uppercase tracking-wide"
              >
                <span class="mr-3 w-4 h-4"></span>
                Volver al sitio
              </a>
            </div>
          </nav>
        </div>
      </div>
    {/if}
  {/if}

  <!-- Main Content -->
  <div class="flex flex-col flex-1">
    <main class="flex-1 {showSidebar ? 'mt-16 md:mt-0' : ''} p-4 md:p-8">
      {@render children()}
    </main>
  </div>
</div>
