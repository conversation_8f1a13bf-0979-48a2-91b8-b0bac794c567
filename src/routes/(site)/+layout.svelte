<script lang="ts">
  import "../../app.css";
  import { onMount } from "svelte";
  import "$lib/cloudbeds/init"; // Keep Cloudbeds initialization
  import "$lib/currency/init"; // Initialize currency service
  import { authStore } from "$lib/auth";

  // Import layout components
  import Header from "$lib/components/layout/Header.svelte";
  import MobileMenu from "$lib/components/layout/MobileMenu.svelte";
  import Footer from "$lib/components/layout/Footer.svelte";

  let { children, data } = $props();

  // Mobile menu handling
  let showMobileMenu = $state(false);
  let isScrolled = $state(false);

  function toggleMobileMenu() {
    showMobileMenu = !showMobileMenu;
    // Prevent scrolling when mobile menu is open
    if (showMobileMenu) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
  }

  // Handle scroll events for header styling
  function handleScroll() {
    isScrolled = window.scrollY > 20;
  }

  onMount(() => {
    // Initialize the auth store (solo una vez)
    console.log(
      "[Layout Svelte] Initializing authStore. Supabase client from data:",
      data.supabase
    );

    // Función asíncrona para inicializar el auth store
    const initializeAuthStore = async () => {
      try {
        if (data.supabase) {
          // Inicializar con el cliente proporcionado por data
          await authStore.initialize(data.supabase);
          console.log(
            "[Layout Svelte] Auth store initialized with data.supabase client"
          );
        } else {
          console.warn(
            "[Layout Svelte] data.supabase is not available, authStore.initialize() called without client. This might lead to issues."
          );
          await authStore.initialize(); // Fallback, but ideally data.supabase should always be present
        }

        // Forzar una sincronización para asegurar que tenemos los datos más recientes
        console.log("[Layout Svelte] Forcing sync to ensure latest auth state");
        await authStore.forceSync();
        console.log("[Layout Svelte] Auth store sync complete");
      } catch (error) {
        console.error("[Layout Svelte] Error initializing auth store:", error);
      }
    };

    // Ejecutar la función de inicialización
    initializeAuthStore();

    // Add scroll event listener
    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Check initial scroll position

    return () => {
      window.removeEventListener("scroll", handleScroll);
      // Reset body overflow when component is destroyed
      document.body.style.overflow = "";
    };
  });
</script>

<div class="flex flex-col bg-primary-50 min-h-screen">
  <Header {isScrolled} {showMobileMenu} {toggleMobileMenu} />

  <MobileMenu {showMobileMenu} {toggleMobileMenu} />

  <main class="flex-grow">
    {@render children()}
  </main>

  <Footer />
</div>
