<script lang="ts">
  import { onMount } from "svelte";
  import OptimizedImage from "$lib/components/ui/OptimizedImage.svelte";
  import ImageErrorDebug from "$lib/components/debug/ImageErrorDebug.svelte";

  // Animation state
  let overviewVisible = $state(false);
  let menuVisible = $state(false);
  let diningVisible = $state(false);

  // Menu items
  const menuItems = {
    breakfast: [
      {
        name: "Moroccan Breakfast",
        description: "Traditional Moroccan breakfast with fresh bread, olive oil, honey, amlou, fresh cheese, olives, and mint tea",
        price: "120 MAD"
      },
      {
        name: "Continental Breakfast",
        description: "Freshly baked pastries, bread, butter, jam, fresh fruit, yogurt, and coffee or tea",
        price: "100 MAD"
      },
      {
        name: "Healthy Start",
        description: "Seasonal fruit platter, homemade granola, yogurt, honey, and fresh juice",
        price: "90 MAD"
      }
    ],
    lunch: [
      {
        name: "Seafood Tagine",
        description: "Fresh local fish and seafood cooked with vegetables, preserved lemon, and Moroccan spices",
        price: "180 MAD"
      },
      {
        name: "Vegetable Couscous",
        description: "Fluffy couscous topped with seasonal vegetables, chickpeas, and aromatic broth",
        price: "140 MAD"
      },
      {
        name: "Mediterranean Salad",
        description: "Fresh garden vegetables, olives, feta cheese, and herbs with olive oil dressing",
        price: "120 MAD"
      }
    ],
    dinner: [
      {
        name: "Lamb Tagine",
        description: "Slow-cooked lamb with prunes, almonds, and Moroccan spices, served with couscous",
        price: "220 MAD"
      },
      {
        name: "Grilled Fish",
        description: "Catch of the day grilled with herbs, served with roasted vegetables and saffron rice",
        price: "200 MAD"
      },
      {
        name: "Vegetable Pastilla",
        description: "Layers of thin pastry filled with spiced vegetables, topped with cinnamon and sugar",
        price: "160 MAD"
      }
    ]
  };

  // Dining experiences
  const diningExperiences = [
    {
      title: "Garden Dining",
      description: "Enjoy your meal in our lush garden, surrounded by aromatic herbs and flowers. The perfect setting for a relaxed lunch or romantic dinner under the stars.",
      image: "https://baberrih.ma/media/restaurant/garden_dining.webp"
    },
    {
      title: "Beach Picnic",
      description: "Experience a private picnic on the beach with a specially prepared basket of Moroccan delicacies and refreshments. Available for lunch or sunset dining.",
      image: "https://baberrih.ma/media/restaurant/beach_picnic.webp"
    },
    {
      title: "Cooking Class",
      description: "Learn the secrets of Moroccan cuisine with our chef. Pick herbs from our garden, learn traditional techniques, and enjoy the meal you've prepared.",
      image: "https://baberrih.ma/media/restaurant/cooking_class.webp"
    }
  ];

  // Intersection Observer for scroll animations
  function setupIntersectionObserver() {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };

    const overviewObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          overviewVisible = true;
          overviewObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);

    const menuObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          menuVisible = true;
          menuObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);

    const diningObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          diningVisible = true;
          diningObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe elements
    const overviewElement = document.getElementById('overview-section');
    if (overviewElement) overviewObserver.observe(overviewElement);

    const menuElement = document.getElementById('menu-section');
    if (menuElement) menuObserver.observe(menuElement);

    const diningElement = document.getElementById('dining-section');
    if (diningElement) diningObserver.observe(diningElement);
  }

  onMount(() => {
    setupIntersectionObserver();
  });
</script>

<svelte:head>
  <title>Restaurant - Baberrih Hotel</title>
  <meta name="description" content="Experience authentic Moroccan cuisine at Baberrih Hotel's restaurant, featuring fresh local ingredients and traditional recipes with a modern twist." />
</svelte:head>

<!-- Hero Section -->
<section class="relative">
  <div class="w-full h-[50vh] md:h-[60vh]">
    <OptimizedImage
      src="https://baberrih.ma/media/restaurant/1_f.webp"
      alt="Baberrih Restaurant"
      objectFit="cover"
      loading="eager"
      fetchpriority="high"
      fallbackSrc="/images/placeholder.svg"
    />
  </div>
  <div class="absolute inset-0 flex justify-center items-center bg-primary-950/50">
    <h1 class="font-montserrat font-light text-primary-50 text-3xl md:text-5xl uppercase">Our Restaurant</h1>
  </div>
</section>

<!-- Overview Section -->
<section id="overview-section" class="px-4 py-12">
  <div class="mx-auto max-w-4xl container">
    <div class={`scroll-animation fade ${overviewVisible ? 'visible' : ''}`} style="--delay: 300ms;">
      <h2 class="mb-6 font-montserrat font-light text-primary-900 text-xl uppercase">Culinary Experience</h2>

      <p class="mb-6 font-eb-garamond font-light text-primary-800 text-lg">
        At Baberrih, we believe that food is an essential part of the travel experience. Our restaurant celebrates the rich culinary heritage of Morocco while incorporating fresh, local ingredients and contemporary techniques.
      </p>

      <p class="mb-6 font-eb-garamond font-light text-primary-800 text-lg">
        Our chef sources ingredients from local markets and our own organic garden, ensuring that each dish captures the authentic flavors of the region. From traditional tagines to fresh seafood caught daily, our menu offers a journey through Moroccan cuisine.
      </p>

      <div class="gap-6 grid grid-cols-1 md:grid-cols-3 mt-8">
        <div class="p-6 border border-primary-100">
          <h3 class="mb-2 font-montserrat font-light text-primary-900 text-base uppercase">Breakfast</h3>
          <p class="font-eb-garamond font-light text-primary-800">
            7:30 AM - 10:30 AM
          </p>
        </div>

        <div class="p-6 border border-primary-100">
          <h3 class="mb-2 font-montserrat font-light text-primary-900 text-base uppercase">Lunch</h3>
          <p class="font-eb-garamond font-light text-primary-800">
            12:30 PM - 3:00 PM
          </p>
        </div>

        <div class="p-6 border border-primary-100">
          <h3 class="mb-2 font-montserrat font-light text-primary-900 text-base uppercase">Dinner</h3>
          <p class="font-eb-garamond font-light text-primary-800">
            7:00 PM - 10:00 PM
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Menu Section -->
<section id="menu-section" class="bg-primary-50 px-4 py-12">
  <div class="mx-auto max-w-4xl container">
    <div class={`scroll-animation slide-up ${menuVisible ? 'visible' : ''}`} style="--delay: 300ms;">
      <h2 class="mb-6 font-montserrat font-light text-primary-900 text-xl uppercase">Sample Menu</h2>

      <div class="space-y-12">
        <!-- Breakfast -->
        <div>
          <h3 class="mb-4 pb-2 border-primary-200 border-b font-montserrat font-medium text-primary-900 text-lg uppercase">Breakfast</h3>
          <div class="space-y-6">
            {#each menuItems.breakfast as item}
              <div>
                <div class="flex justify-between items-baseline mb-1">
                  <h4 class="font-montserrat font-medium text-primary-900">{item.name}</h4>
                  <span class="font-montserrat font-light text-primary-800">{item.price}</span>
                </div>
                <p class="font-eb-garamond font-light text-primary-800">{item.description}</p>
              </div>
            {/each}
          </div>
        </div>

        <!-- Lunch -->
        <div>
          <h3 class="mb-4 pb-2 border-primary-200 border-b font-montserrat font-medium text-primary-900 text-lg uppercase">Lunch</h3>
          <div class="space-y-6">
            {#each menuItems.lunch as item}
              <div>
                <div class="flex justify-between items-baseline mb-1">
                  <h4 class="font-montserrat font-medium text-primary-900">{item.name}</h4>
                  <span class="font-montserrat font-light text-primary-800">{item.price}</span>
                </div>
                <p class="font-eb-garamond font-light text-primary-800">{item.description}</p>
              </div>
            {/each}
          </div>
        </div>

        <!-- Dinner -->
        <div>
          <h3 class="mb-4 pb-2 border-primary-200 border-b font-montserrat font-medium text-primary-900 text-lg uppercase">Dinner</h3>
          <div class="space-y-6">
            {#each menuItems.dinner as item}
              <div>
                <div class="flex justify-between items-baseline mb-1">
                  <h4 class="font-montserrat font-medium text-primary-900">{item.name}</h4>
                  <span class="font-montserrat font-light text-primary-800">{item.price}</span>
                </div>
                <p class="font-eb-garamond font-light text-primary-800">{item.description}</p>
              </div>
            {/each}
          </div>
        </div>
      </div>

      <p class="mt-8 font-montserrat font-light text-primary-800 italic">
        * This is a sample menu. Our offerings change seasonally to showcase the freshest ingredients.
      </p>
    </div>
  </div>
</section>

<!-- Dining Experiences Section -->
<section id="dining-section" class="px-4 py-12">
  <div class="mx-auto max-w-4xl container">
    <div class={`scroll-animation fade ${diningVisible ? 'visible' : ''}`} style="--delay: 300ms;">
      <h2 class="mb-6 font-montserrat font-light text-primary-900 text-xl uppercase">Special Dining Experiences</h2>

      <div class="gap-8 grid grid-cols-1 md:grid-cols-2">
        {#each diningExperiences as experience, index}
          <div class={index === 0 ? "md:col-span-2" : ""}>
            <div class="mb-4">
              <OptimizedImage
                src={experience.image}
                alt={experience.title}
                aspectRatio="16/9"
                objectFit="cover"
                fallbackSrc="/images/placeholder.svg"
              />
            </div>
            <h3 class="mb-2 font-montserrat font-medium text-primary-900 text-lg">{experience.title}</h3>
            <p class="font-eb-garamond font-light text-primary-800">{experience.description}</p>
          </div>
        {/each}
      </div>

      <div class="mt-8 text-center">
        <p class="mb-6 font-eb-garamond font-light text-primary-800 text-lg">
          To book a special dining experience or for any dietary requirements, please contact us in advance.
        </p>
        <a href="/contact" class="button">
          Contact Us
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Removed test with baberrih.ma URL -->

<!-- Image Error Debug Component -->
<ImageErrorDebug position="bottom-right" showOnLoad={true} />