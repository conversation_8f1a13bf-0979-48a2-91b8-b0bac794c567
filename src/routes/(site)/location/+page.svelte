<script lang="ts">
  import { onMount } from "svelte";
  import { T, t } from "$lib/i18n";

  // Animation state
  let overviewVisible = $state(false);
  let directionsVisible = $state(false);
  let surroundingsVisible = $state(false);

  // Intersection Observer for scroll animations
  function setupIntersectionObserver() {
    const observerOptions = {
      root: null,
      rootMargin: "0px",
      threshold: 0.1,
    };

    const overviewObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          overviewVisible = true;
          overviewObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);

    const directionsObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          directionsVisible = true;
          directionsObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);

    const surroundingsObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          surroundingsVisible = true;
          surroundingsObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe elements
    const overviewElement = document.getElementById("overview-section");
    if (overviewElement) overviewObserver.observe(overviewElement);

    const directionsElement = document.getElementById("directions-section");
    if (directionsElement) directionsObserver.observe(directionsElement);

    const surroundingsElement = document.getElementById("surroundings-section");
    if (surroundingsElement) surroundingsObserver.observe(surroundingsElement);
  }

  onMount(() => {
    setupIntersectionObserver();
  });
</script>

<svelte:head>
  <title>{$t("location.meta.title")} - Baberrih Hotel</title>
  <meta name="description" content={$t("location.meta.description")} />
</svelte:head>

<!-- Hero Section -->
<section class="relative">
  <img
    src="https://baberrih.ma/media/location/1_f.webp"
    alt="Baberrih Hotel Location"
    class="w-full h-[50vh] md:h-[60vh] object-cover"
  />
  <div
    class="absolute inset-0 flex justify-center items-center bg-primary-950/50"
  >
    <h1
      class="font-montserrat font-light text-primary-50 text-3xl md:text-5xl uppercase"
    >
      <T key="location.title" />
    </h1>
  </div>
</section>

<!-- Overview Section -->
<section id="overview-section" class="px-4 py-12">
  <div class="mx-auto max-w-4xl container">
    <div
      class={`scroll-animation fade ${overviewVisible ? "visible" : ""}`}
      style="--delay: 300ms;"
    >
      <h2
        class="mb-6 font-montserrat font-light text-primary-900 text-xl uppercase"
      >
        <T key="location.overview.title" />
      </h2>

      <p class="mb-6 font-eb-garamond font-light text-primary-800 text-lg">
        <T key="location.overview.description" />
      </p>

      <p class="mb-6 font-eb-garamond font-light text-primary-800 text-lg">
        <T key="location.overview.paragraph2" />
      </p>

      <div class="gap-6 grid grid-cols-1 md:grid-cols-2 mt-8">
        <div class="p-6 border border-primary-100">
          <h3
            class="mb-2 font-montserrat font-light text-primary-900 text-base uppercase"
          >
            <T key="location.distances.essaouira" />
          </h3>
          <p class="font-eb-garamond font-light text-primary-800 text-lg">
            <T key="location.distances.essaouiraValue" />
          </p>
        </div>

        <div class="p-6 border border-primary-100">
          <h3
            class="mb-2 font-montserrat font-light text-primary-900 text-base uppercase"
          >
            <T key="location.distances.marrakech" />
          </h3>
          <p class="font-eb-garamond font-light text-primary-800 text-lg">
            <T key="location.distances.marrakechValue" />
          </p>
        </div>

        <div class="p-6 border border-primary-100">
          <h3
            class="mb-2 font-montserrat font-light text-primary-900 text-base uppercase"
          >
            <T key="location.distances.airport" />
          </h3>
          <p class="font-eb-garamond font-light text-primary-800 text-lg">
            <T key="location.distances.airportValue" />
          </p>
        </div>

        <div class="p-6 border border-primary-100">
          <h3
            class="mb-2 font-montserrat font-light text-primary-900 text-base uppercase"
          >
            <T key="location.distances.beach" />
          </h3>
          <p class="font-eb-garamond font-light text-primary-800 text-lg">
            <T key="location.distances.beachValue" />
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Map Section -->
<section class="bg-primary-50 px-4 py-12">
  <div class="mx-auto max-w-4xl container">
    <h2
      class="mb-6 font-montserrat font-light text-primary-900 text-xl uppercase"
    >
      <T key="location.map" />
    </h2>

    <div class="aspect-ratio-container" style="--aspect-ratio:16/9">
      <iframe
        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3441.1076928526394!2d-9.780000!3d31.510000!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xdad9a4e9f8c12d7%3A0x57421a176d5d7f30!2sBaberrih!5e0!3m2!1sen!2sma!4v1623456789012!5m2!1sen!2sma"
        width="100%"
        height="100%"
        style="border:0;"
        allowfullscreen=""
        loading="lazy"
        title="Baberrih Hotel Location Map"
      ></iframe>
    </div>
  </div>
</section>

<!-- Directions Section -->
<section id="directions-section" class="px-4 py-12">
  <div class="mx-auto max-w-4xl container">
    <div
      class={`scroll-animation slide-up ${directionsVisible ? "visible" : ""}`}
      style="--delay: 300ms;"
    >
      <h2
        class="mb-6 font-montserrat font-light text-primary-900 text-xl uppercase"
      >
        <T key="location.directions.title" />
      </h2>

      <div class="space-y-8">
        <div>
          <h3
            class="mb-2 font-montserrat font-medium text-primary-900 text-base uppercase"
          >
            <T key="location.directions.fromEssaouira" />
          </h3>
          <p class="font-eb-garamond font-light text-primary-800 text-lg">
            <T key="location.directions.fromEssaouiraDesc" />
          </p>
        </div>

        <div>
          <h3
            class="mb-2 font-montserrat font-medium text-primary-900 text-base uppercase"
          >
            <T key="location.directions.fromMarrakech" />
          </h3>
          <p class="font-eb-garamond font-light text-primary-800 text-lg">
            <T key="location.directions.fromMarrakechDesc" />
          </p>
        </div>

        <div>
          <h3
            class="mb-2 font-montserrat font-medium text-primary-900 text-base uppercase"
          >
            <T key="location.directions.fromAirport" />
          </h3>
          <p class="font-eb-garamond font-light text-primary-800 text-lg">
            <T key="location.directions.fromAirportDesc" />
          </p>
        </div>

        <div>
          <h3
            class="mb-2 font-montserrat font-medium text-primary-900 text-base uppercase"
          >
            <T key="location.directions.transfers" />
          </h3>
          <p class="mb-4 font-eb-garamond font-light text-primary-800 text-lg">
            <T key="location.directions.transfersDesc" />
          </p>
          <a href="/contact" class="button-secondary">
            <T key="location.directions.contactForTransfer" />
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Surroundings Section -->
<section id="surroundings-section" class="bg-primary-50 px-4 py-12">
  <div class="mx-auto max-w-4xl container">
    <div
      class={`scroll-animation fade ${surroundingsVisible ? "visible" : ""}`}
      style="--delay: 300ms;"
    >
      <h2
        class="mb-6 font-montserrat font-light text-primary-900 text-xl uppercase"
      >
        <T key="location.attractions.title" />
      </h2>

      <p class="mb-8 font-eb-garamond font-light text-primary-800 text-lg">
        <T key="location.attractions.description" />
      </p>

      <div class="gap-6 grid grid-cols-1 md:grid-cols-2">
        <div class="bg-primary-50 p-6 border border-primary-100">
          <h3
            class="mb-2 font-montserrat font-medium text-primary-900 text-base uppercase"
          >
            <T key="location.attractions.medina" />
          </h3>
          <p class="font-eb-garamond font-light text-primary-800">
            <T key="location.attractions.medinaDesc" />
          </p>
        </div>

        <div class="bg-primary-50 p-6 border border-primary-100">
          <h3
            class="mb-2 font-montserrat font-medium text-primary-900 text-base uppercase"
          >
            <T key="location.attractions.argan" />
          </h3>
          <p class="font-eb-garamond font-light text-primary-800">
            <T key="location.attractions.arganDesc" />
          </p>
        </div>

        <div class="bg-primary-50 p-6 border border-primary-100">
          <h3
            class="mb-2 font-montserrat font-medium text-primary-900 text-base uppercase"
          >
            <T key="location.attractions.sidiKaouki" />
          </h3>
          <p class="font-eb-garamond font-light text-primary-800">
            <T key="location.attractions.sidiKaoukiDesc" />
          </p>
        </div>

        <div class="bg-primary-50 p-6 border border-primary-100">
          <h3
            class="mb-2 font-montserrat font-medium text-primary-900 text-base uppercase"
          >
            <T key="location.attractions.vineyard" />
          </h3>
          <p class="font-eb-garamond font-light text-primary-800">
            <T key="location.attractions.vineyardDesc" />
          </p>
        </div>
      </div>
    </div>
  </div>
</section>
