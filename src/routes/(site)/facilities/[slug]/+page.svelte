<script lang="ts">
  import { page } from "$app/stores";
  import { onMount } from "svelte";
  
  // Get the slug from the URL
  const slug = $derived($page.params.slug);
  
  // State for scroll animations
  let detailsVisible = $state(false);
  let featuresVisible = $state(false);
  
  // Facility data (in a real app, this would come from an API or CMS)
  const facilities = {
    "garden": {
      title: "Garden and Orchard",
      description: "Our lush garden and orchard are the heart of Baberrih, offering a peaceful retreat where you can connect with nature. Wander through aromatic herbs, flowering plants, and fruit trees that provide fresh ingredients for our restaurant.",
      images: [
        "https://baberrih.ma/media/facilities/garden/1_f.webp",
        "https://baberrih.ma/media/facilities/garden/2_f.webp",
        "https://baberrih.ma/media/facilities/garden/3_f.webp",
        "https://baberrih.ma/media/facilities/garden/4_f.webp"
      ],
      features: [
        "Organic vegetable garden",
        "Fruit trees including citrus and olive",
        "Aromatic herb garden",
        "Shaded seating areas",
        "Hammocks for relaxation",
        "Outdoor dining spaces"
      ]
    },
    "common-areas": {
      title: "Common Areas",
      description: "The common areas at Baberrih are designed to foster connection and relaxation. From our welcoming reception to cozy lounges and outdoor terraces, these spaces blend traditional Moroccan design with modern comfort.",
      images: [
        "https://baberrih.ma/media/facilities/common/1_f.webp",
        "https://baberrih.ma/media/facilities/common/2_f.webp",
        "https://baberrih.ma/media/facilities/common/3_f.webp",
        "https://baberrih.ma/media/facilities/common/4_f.webp"
      ],
      features: [
        "Welcoming reception area",
        "Comfortable lounge with fireplace",
        "Library with books in multiple languages",
        "Outdoor terraces with ocean views",
        "Communal dining spaces",
        "Traditional Moroccan salon"
      ]
    },
    "yoga-room": {
      title: "Yoga Room",
      description: "Our dedicated yoga room provides a serene space for practice and meditation. With natural light, beautiful views, and all necessary equipment, it's the perfect environment to nurture your body and mind during your stay.",
      images: [
        "https://baberrih.ma/media/facilities/yoga/1_f.webp",
        "https://baberrih.ma/media/facilities/yoga/2_f.webp",
        "https://baberrih.ma/media/facilities/yoga/3_f.webp",
        "https://baberrih.ma/media/facilities/yoga/4_f.webp"
      ],
      features: [
        "Spacious practice area",
        "Natural light and ventilation",
        "Yoga mats and props provided",
        "Sound system for guided practice",
        "Views of the garden",
        "Available for private sessions or group classes"
      ]
    },
    "stable": {
      title: "Stable",
      description: "Our well-maintained stable houses our gentle horses, which are available for guided rides along the beach and surrounding countryside. The facility is designed with animal welfare as a priority, ensuring a positive experience for both horses and riders.",
      images: [
        "https://baberrih.ma/media/facilities/stable/1_f.webp",
        "https://baberrih.ma/media/facilities/stable/2_f.webp",
        "https://baberrih.ma/media/facilities/stable/3_f.webp",
        "https://baberrih.ma/media/facilities/stable/4_f.webp"
      ],
      features: [
        "Well-ventilated horse stalls",
        "Experienced stable staff",
        "Riding equipment for all levels",
        "Grooming area",
        "Riding arena for beginners",
        "Direct access to beach riding trails"
      ]
    },
    "pool": {
      title: "Pool",
      description: "Our refreshing pool offers a perfect alternative to the ocean, with calm waters and comfortable lounging areas. Surrounded by palm trees and offering views of the garden, it's an ideal spot for relaxation throughout the day.",
      images: [
        "https://baberrih.ma/media/facilities/pool/1_f.webp",
        "https://baberrih.ma/media/facilities/pool/2_f.webp",
        "https://baberrih.ma/media/facilities/pool/3_f.webp",
        "https://baberrih.ma/media/facilities/pool/4_f.webp"
      ],
      features: [
        "Freshwater pool",
        "Comfortable sun loungers",
        "Poolside service",
        "Shaded areas",
        "Towel service",
        "Children's shallow area"
      ]
    },
    "beach": {
      title: "Beach",
      description: "Baberrih enjoys a privileged location with direct access to Tissa Beach, a pristine stretch of Atlantic coastline. Whether you want to swim, stroll, or simply relax with the sound of waves, the beach is just steps away from your accommodation.",
      images: [
        "https://baberrih.ma/media/facilities/beach/1_f.webp",
        "https://baberrih.ma/media/facilities/beach/2_f.webp",
        "https://baberrih.ma/media/facilities/beach/3_f.webp",
        "https://baberrih.ma/media/facilities/beach/4_f.webp"
      ],
      features: [
        "Direct beach access",
        "Beach chairs and umbrellas available",
        "Beach towel service",
        "Perfect for long walks",
        "Sunset viewing spot",
        "Water sports available nearby"
      ]
    }
  };
  
  // Get the current facility
  const facility = $derived(facilities[slug as keyof typeof facilities]);
  
  // Carousel state
  let currentImageIndex = $state(0);
  
  function nextImage() {
    if (facility) {
      currentImageIndex = (currentImageIndex + 1) % facility.images.length;
    }
  }
  
  function prevImage() {
    if (facility) {
      currentImageIndex = (currentImageIndex - 1 + facility.images.length) % facility.images.length;
    }
  }
  
  // Intersection Observer for scroll animations
  function setupIntersectionObserver() {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };
    
    const detailsObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          detailsVisible = true;
          detailsObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    const featuresObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          featuresVisible = true;
          featuresObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    // Observe elements
    const detailsElement = document.getElementById('details-section');
    if (detailsElement) detailsObserver.observe(detailsElement);
    
    const featuresElement = document.getElementById('features-section');
    if (featuresElement) featuresObserver.observe(featuresElement);
  }
  
  onMount(() => {
    setupIntersectionObserver();
  });
</script>

<svelte:head>
  <title>{facility?.title || 'Facility'} - Baberrih Hotel</title>
  <meta name="description" content={facility?.description || 'Discover our facilities at Baberrih Hotel in Essaouira, Morocco.'} />
</svelte:head>

{#if facility}
  <!-- Hero Section with Carousel -->
  <section class="relative">
    <div class="carousel">
      <img 
        src={facility.images[currentImageIndex]} 
        alt={`${facility.title} - Image ${currentImageIndex + 1}`} 
        class="carousel-item"
      />
      <div class="carousel-nav">
        <button onclick={prevImage} class="carousel-button" aria-label="Previous image">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button onclick={nextImage} class="carousel-button" aria-label="Next image">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
      <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-primary-950/75 to-transparent">
        <h1 class="font-montserrat font-light text-primary-50 text-2xl md:text-4xl uppercase">{facility.title}</h1>
      </div>
    </div>
  </section>

  <!-- Details Section -->
  <section id="details-section" class="py-12 px-4">
    <div class="container mx-auto max-w-4xl">
      <div class={`scroll-animation fade ${detailsVisible ? 'visible' : ''}`} style="--delay: 300ms;">
        <h2 class="font-montserrat font-light text-primary-900 text-xl uppercase mb-6">About this facility</h2>
        <p class="font-eb-garamond font-light text-primary-800 text-lg mb-8">{facility.description}</p>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features-section" class="py-12 px-4 bg-primary-50">
    <div class="container mx-auto max-w-4xl">
      <div class={`scroll-animation slide-up ${featuresVisible ? 'visible' : ''}`} style="--delay: 300ms;">
        <h2 class="font-montserrat font-light text-primary-900 text-xl uppercase mb-6">Features</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          {#each facility.features as feature}
            <div class="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-5 h-5 text-primary-700">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <p class="font-montserrat font-light text-primary-800">{feature}</p>
            </div>
          {/each}
        </div>
      </div>
    </div>
  </section>
{:else}
  <div class="flex items-center justify-center min-h-[50vh]">
    <p class="font-montserrat font-light text-primary-900 text-xl">Facility not found</p>
  </div>
{/if}
