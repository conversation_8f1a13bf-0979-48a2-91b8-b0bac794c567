/**
 * Endpoint para cancelar una suscripción de Stripe
 */
import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';
import { stripe } from '$lib/stripe';

/**
 * Maneja la solicitud POST para cancelar una suscripción
 */
export async function POST({ params, locals }: RequestEvent) {
  try {
    // Verificar si el usuario está autenticado
    if (!locals.session) {
      return json({ success: false, error: 'No autorizado' }, { status: 401 });
    }

    // Obtener el ID de la suscripción de los parámetros de la ruta
    const subscriptionId = params.id;

    // Verificar que el ID de la suscripción existe
    if (!subscriptionId) {
      return json({ success: false, error: 'ID de suscripción no proporcionado' }, { status: 400 });
    }

    // Obtener la suscripción para verificar que pertenece al usuario actual
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    // Verificar que la suscripción existe
    if (!subscription) {
      return json({ success: false, error: 'Suscripción no encontrada' }, { status: 404 });
    }

    // Cancelar la suscripción al final del período actual
    const canceledSubscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    });

    // Devolver la respuesta
    return json({
      success: true,
      subscription: {
        id: canceledSubscription.id,
        status: canceledSubscription.status,
        cancel_at_period_end: canceledSubscription.cancel_at_period_end,
        current_period_end: canceledSubscription.current_period_end,
      }
    });
  } catch (error) {
    // Manejar errores
    console.error('Error al cancelar la suscripción:', error);
    return json({
      success: false,
      error: error instanceof Error ? error.message : 'Error interno del servidor'
    }, { status: 500 });
  }
} 