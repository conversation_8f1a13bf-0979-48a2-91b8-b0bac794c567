/**
 * Endpoint para crear un portal de cliente de Stripe
 */
import { json } from '@sveltejs/kit';
import { StripeService } from '$lib/stripe.server';
import { createCustomerPortalSchema } from '$lib/stripe/schemas';
import { z } from 'zod';

/**
 * Maneja la solicitud POST para crear un portal de cliente
 */
export async function POST({ request, locals }) {
  try {
    // Verificar si el usuario está autenticado
    if (!locals.session) {
      return json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener los datos de la solicitud
    const body = await request.json();

    // Validar los datos con Zod
    const validatedData = createCustomerPortalSchema.parse(body);

    // Crear el portal de cliente usando el servicio de Stripe
    const result = await locals.stripe.createCustomerPortal(validatedData);

    // Verificar si la operación fue exitosa
    if (!result.success) {
      console.error('Error al crear el portal de cliente:', result.error);
      return json({ error: 'Error al crear el portal de cliente' }, { status: 500 });
    }

    // Devolver la URL del portal
    return json({ url: result.url });
  } catch (error) {
    // Manejar errores de validación de Zod
    if (error instanceof z.ZodError) {
      return json({ error: error.errors }, { status: 400 });
    }

    // Manejar otros errores
    console.error('Error al crear el portal de cliente:', error);
    return json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}