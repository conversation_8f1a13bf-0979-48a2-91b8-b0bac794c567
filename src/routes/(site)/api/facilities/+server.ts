import { json } from '@sveltejs/kit';
import type { Request<PERSON>and<PERSON> } from './$types';

/**
 * GET handler for facilities
 * Fetches facilities from the database
 *
 * Query parameters:
 * - slug: Optional. If provided, returns a single facility with the given slug
 * - site_id: Optional. If provided, filters facilities by site ID
 * - limit: Optional. Limits the number of facilities returned (default: 10)
 * - active_only: Optional. If '1', only returns active facilities
 */
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Get query parameters
    const slug = url.searchParams.get('slug');
    const siteId = url.searchParams.get('site_id');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const activeOnly = url.searchParams.get('active_only') === '1';

    console.log('API: Fetching facilities with params:', { slug, siteId, limit, activeOnly });

    // Initialize query
    let query = locals.supabase
      .from('facilities_with_site_info')
      .select('*');

    // Apply filters
    if (slug) {
      console.log('API: Filtering by slug:', slug);
      query = query.eq('slug', slug);
    }

    if (siteId) {
      console.log('API: Filtering by site_id:', siteId);
      query = query.eq('site_id', siteId);
    }

    if (activeOnly) {
      console.log('API: Filtering by active status');
      query = query.eq('status', 'active');
    }

    // Apply limit
    query = query.limit(limit);
    console.log('API: Applying limit:', limit);

    // Execute query
    console.log('API: Executing query');
    const { data, error } = await query;
    console.log('API: Query result:', { dataLength: data?.length, error });

    if (error) {
      console.error('Error fetching facilities:', error);
      return json({
        success: false,
        error: error.message
      }, { status: 500 });
    }

    // If slug was provided, return a single facility or 404
    if (slug) {
      if (data && data.length > 0) {
        console.log('API: Returning single facility for slug:', slug);
        return json({
          success: true,
          data: data[0]
        });
      } else {
        console.log('API: Facility not found for slug:', slug);
        return json({
          success: false,
          error: 'Facility not found'
        }, { status: 404 });
      }
    }

    // Return all facilities
    console.log('API: Returning all facilities:', data?.length);
    return json({
      success: true,
      data: data || []
    });
  } catch (err) {
    console.error('Error in facilities API:', err);
    return json({
      success: false,
      error: 'An error occurred while fetching facilities'
    }, { status: 500 });
  }
};
