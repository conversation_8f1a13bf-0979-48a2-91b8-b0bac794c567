import { json } from '@sveltejs/kit';
import { cloudbedsApiService } from '$lib/cloudbeds/api.server';
import type { RequestHandler } from './$types';
import { z } from 'zod';

/**
 * API endpoint to get availability for ALL room types, including private ones
 * This endpoint is a workaround for the issue where all room types are marked as isPrivate=true
 * in Cloudbeds, which causes the public-availability endpoint to return empty data.
 * 
 * IMPORTANT: This is a temporary solution until the room configuration in Cloudbeds is fixed.
 * The proper solution is to change the isPrivate flag to false for room types that should be
 * publicly bookable.
 */
export const GET: RequestHandler = async ({ url, request }) => {
  console.group("API Endpoint - /api/cloudbeds/all-availability");
  try {
    // Get request info for debugging
    const referer = request.headers.get('referer') || 'unknown';
    console.log("🔍 DEBUGGING: Request context:", {
      referer,
      url: url.toString(),
      headers: Object.fromEntries(request.headers)
    });

    // Validate and extract parameters
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const roomTypeId = url.searchParams.get('roomTypeId') || undefined;

    console.log("Request parameters:", { startDate, endDate, roomTypeId });

    // Validate required parameters
    if (!startDate || !endDate) {
      console.error("Missing required parameters");
      const errorResponse = {
        success: false,
        error: {
          code: 'MISSING_PARAMETERS',
          message: 'startDate and endDate parameters are required'
        }
      };
      console.log("Returning error response:", errorResponse);
      console.groupEnd();
      return json(errorResponse, { status: 400 });
    }

    // Validate date format
    const dateSchema = z.string().regex(/^\d{4}-\d{2}-\d{2}$/);
    try {
      dateSchema.parse(startDate);
      dateSchema.parse(endDate);
      console.log("Date format validation passed");
    } catch (validationError) {
      console.error("Invalid date format:", validationError);
      const errorResponse = {
        success: false,
        error: {
          code: 'INVALID_DATE_FORMAT',
          message: 'Date format must be YYYY-MM-DD'
        }
      };
      console.log("Returning error response:", errorResponse);
      console.groupEnd();
      return json(errorResponse, { status: 400 });
    }

    // Get availability data directly without checking isPrivate flag
    console.log("Getting availability data for ALL room types (including private)");
    console.time("Server API call duration");
    
    const availability = await cloudbedsApiService.getAvailability(startDate, endDate, roomTypeId);
    console.timeEnd("Server API call duration");

    console.log("API response:", {
      success: availability.success,
      dataLength: availability.data?.length || 0,
      error: availability.error
    });

    // If successful but empty data, provide a more helpful message
    if (availability.success && (!availability.data || availability.data.length === 0)) {
      const emptyResponse = {
        ...availability,
        message: "No availability data found. This may be because there are no rooms available for the selected dates."
      };
      console.log("Returning empty response with helpful message");
      console.groupEnd();
      return json(emptyResponse);
    }

    console.log("Returning response to client");
    console.groupEnd();
    return json(availability);
  } catch (error) {
    console.error('Error getting availability:', error);
    const errorResponse = {
      success: false,
      error: {
        code: 'CLOUDBEDS_API_ERROR',
        message: error instanceof Error ? error.message : 'Unknown error'
      }
    };
    console.log("Returning error response:", errorResponse);
    console.groupEnd();
    return json(errorResponse, { status: 500 });
  }
};
