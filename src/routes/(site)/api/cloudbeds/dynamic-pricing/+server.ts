import { json } from '@sveltejs/kit';
import { cloudbedsApiService } from '$lib/cloudbeds/api.server';
import type { RequestHandler } from './$types';
import { z } from 'zod';

/**
 * API endpoint to get pricing information using dynamic room type resolution
 * This endpoint resolves the room type ID from either a direct ID or a room name
 */
export const GET: RequestHandler = async ({ url, request }) => {
  console.group("API Endpoint - /api/cloudbeds/dynamic-pricing");
  try {
    // Get request info for debugging
    const referer = request.headers.get('referer') || 'unknown';
    console.log("🔍 DEBUGGING: Request context:", {
      referer,
      url: url.toString()
    });

    // Validate and extract parameters
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const roomTypeId = url.searchParams.get('roomTypeId') || undefined;
    const roomName = url.searchParams.get('roomName') || undefined;
    const days = parseInt(url.searchParams.get('days') || '14', 10);

    console.log("🔍 DEBUGGING: Request parameters:", {
      startDate,
      endDate,
      roomTypeId,
      roomName,
      days
    });

    // Validate required parameters
    if (!startDate || !endDate) {
      const errorResponse = {
        success: false,
        error: {
          code: 'MISSING_PARAMETERS',
          message: 'startDate and endDate parameters are required'
        }
      };
      console.log("Returning error response:", errorResponse);
      console.groupEnd();
      return json(errorResponse, { status: 400 });
    }

    // Validate date format
    const dateSchema = z.string().regex(/^\d{4}-\d{2}-\d{2}$/);
    try {
      dateSchema.parse(startDate);
      dateSchema.parse(endDate);
    } catch (validationError) {
      const errorResponse = {
        success: false,
        error: {
          code: 'INVALID_DATE_FORMAT',
          message: 'Date format must be YYYY-MM-DD'
        }
      };
      console.log("Returning error response:", errorResponse);
      console.groupEnd();
      return json(errorResponse, { status: 400 });
    }

    // Validate that at least one of roomTypeId or roomName is provided
    if (!roomTypeId && !roomName) {
      const errorResponse = {
        success: false,
        error: {
          code: 'MISSING_ROOM_IDENTIFIER',
          message: 'Either roomTypeId or roomName must be provided'
        }
      };
      console.log("Returning error response:", errorResponse);
      console.groupEnd();
      return json(errorResponse, { status: 400 });
    }

    // Resolve the room type ID using the dynamic resolution system
    console.log("Resolving room type ID...");
    const resolvedRoomTypeId = await cloudbedsApiService.validateRoomTypeId(roomTypeId, roomName);

    if (!resolvedRoomTypeId) {
      const errorResponse = {
        success: false,
        error: {
          code: 'ROOM_TYPE_NOT_FOUND',
          message: 'Could not resolve a valid room type ID'
        }
      };
      console.log("Returning error response:", errorResponse);
      console.groupEnd();
      return json(errorResponse, { status: 404 });
    }

    console.log(`Room type ID resolved: ${resolvedRoomTypeId}`);

    // Get availability data with the resolved room type ID
    console.log("Getting availability data with resolved room type ID");
    console.time("Server API call duration");

    const availability = await cloudbedsApiService.getAvailability(startDate, endDate, resolvedRoomTypeId);
    console.timeEnd("Server API call duration");

    console.log("API response:", {
      success: availability.success,
      dataLength: availability.data?.length || 0,
      error: availability.error
    });

    // If successful but empty data, provide a more helpful message
    if (availability.success && (!availability.data || availability.data.length === 0)) {
      const emptyResponse = {
        ...availability,
        message: "No pricing data found for the resolved room type.",
        resolution: {
          originalRoomTypeId: roomTypeId,
          originalRoomName: roomName,
          resolvedRoomTypeId
        }
      };
      console.log("Returning empty response with helpful message and resolution info");
      console.groupEnd();
      return json(emptyResponse);
    }

    // Add resolution info to the response
    const enhancedResponse = {
      ...availability,
      resolution: {
        originalRoomTypeId: roomTypeId,
        originalRoomName: roomName,
        resolvedRoomTypeId
      }
    };

    console.log("Returning response to client");
    console.groupEnd();
    return json(enhancedResponse);
  } catch (error) {
    console.error('Error getting pricing:', error);
    const errorResponse = {
      success: false,
      error: {
        code: 'CLOUDBEDS_API_ERROR',
        message: error instanceof Error ? error.message : 'Unknown error'
      }
    };
    console.log("Returning error response:", errorResponse);
    console.groupEnd();
    return json(errorResponse, { status: 500 });
  }
};
