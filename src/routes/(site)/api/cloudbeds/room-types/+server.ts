import { json } from '@sveltejs/kit';
import { cloudbedsApiService } from '$lib/cloudbeds/api.server';
import type { RequestEvent } from '@sveltejs/kit';

export const GET = async ({ url }: RequestEvent) => {
  try {
    console.log('API endpoint room-types: Inicio de la solicitud');

    // Obtener parámetros de la URL
    const includePhotos = url.searchParams.get('includePhotos') === '1';
    const includeAmenities = url.searchParams.get('includeAmenities') === '1';

    console.log('Parámetros:', { includePhotos, includeAmenities });

    // Obtener tipos de habitaciones con los parámetros solicitados
    console.log('Llamando a cloudbedsApiService.getRoomTypes...');
    const roomTypes = await cloudbedsApiService.getRoomTypes(includePhotos, includeAmenities);

    // Log para depuración
    console.log('¿Respuesta exitosa?:', roomTypes.success);
    console.log('¿Respuesta con datos?:', !!roomTypes.data);
    console.log('Número de habitaciones encontradas:', roomTypes.data?.length || 0);

    if (roomTypes.data && roomTypes.data.length > 0) {
      // Verificamos los campos presentes en el primer elemento para depuración
      const sampleRoom = roomTypes.data[0];
      console.log('Estructura de muestra de habitación:', {
        id: sampleRoom.id,
        roomTypeID: sampleRoom.roomTypeID,
        name: sampleRoom.name,
        roomTypeName: sampleRoom.roomTypeName
      });
    } else {
      console.log('No se encontraron habitaciones en la respuesta');
    }

    return json(roomTypes);
  } catch (error) {
    console.error('Error obteniendo tipos de habitaciones:', error);
    return json({
      success: false,
      error: {
        code: 'CLOUDBEDS_API_ERROR',
        message: error instanceof Error ? error.message : 'Error desconocido'
      }
    }, { status: 500 });
  }
}; 