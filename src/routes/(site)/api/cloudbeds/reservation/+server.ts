import { error, json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';
import type { CloudbedsReservationRequest } from '$lib/cloudbeds/types';
import { CloudbedsApiService } from '$lib/cloudbeds/api.server';

/**
 * Valida los datos de la solicitud de reserva
 * @param data Datos de reserva a validar
 * @returns Datos validados
 */
function validateReservationRequest(data: any): CloudbedsReservationRequest {
  if (!data) {
    throw new Error('No se proporcionaron datos de reserva');
  }

  // Convertir formato de API directa a formato interno
  if (data.guestFirstName && data.guestLastName && data.guestEmail) {
    console.log('Detectado formato de API directa, convirtiendo a formato interno');

    // Crear estructura guestData si no existe
    if (!data.guestData) {
      data.guestData = {
        firstName: data.guestFirstName,
        lastName: data.guestLastName,
        email: data.guestEmail,
        phone: data.guestPhone || '',
        address: data.guestAddress || '',
        city: data.guestCity || '',
        state: data.guestState || '',
        country: data.guestCountry || '',
        postalCode: data.guestZip || '',
        notes: data.guestNotes || ''
      };
    }

    // Crear estructura roomsData si no existe
    if (!data.roomsData || !Array.isArray(data.roomsData) || data.roomsData.length === 0) {
      // Si hay un array de rooms, usarlo para crear roomsData
      if (data.rooms && Array.isArray(data.rooms) && data.rooms.length > 0) {
        data.roomsData = data.rooms.map(room => ({
          roomTypeID: room.roomTypeID || data.roomTypeID,
          startDate: room.startDate || data.startDate,
          endDate: room.endDate || data.endDate,
          adults: room.adults || data.adults || 1,
          children: room.children !== undefined ? room.children : (data.children || 0),
          notes: room.notes || ''
        }));
      } else {
        // Crear un solo elemento en roomsData con los datos a nivel raíz
        data.roomsData = [{
          roomTypeID: data.roomTypeID,
          startDate: data.startDate,
          endDate: data.endDate,
          adults: data.adults || 1,
          children: data.children || 0,
          notes: ''
        }];
      }
    }
  }

  // Validar datos del huésped
  if (!data.guestData || !data.guestData.firstName || !data.guestData.lastName || !data.guestData.email) {
    throw new Error('Se requieren nombre, apellido y email del huésped');
  }

  // Validar datos de habitaciones
  if (!data.roomsData || !Array.isArray(data.roomsData) || data.roomsData.length === 0) {
    throw new Error('Se requiere al menos una habitación');
  }

  for (const room of data.roomsData) {
    if (!room.roomTypeID || !room.startDate || !room.endDate || room.adults === undefined) {
      throw new Error('Datos de habitación incompletos');
    }

    // No need to validate room type IDs here, the service will handle it dynamically
    // Just log for debugging purposes
    console.log(`Room type ID in request: ${room.roomTypeID}, Room name: ${room.roomName || 'N/A'}`);
  }

  // Verificar que propertyID esté presente
  if (!data.propertyID) {
    if (!env.CLOUDBEDS_PROPERTY_ID) {
      throw new Error('No se ha configurado CLOUDBEDS_PROPERTY_ID en las variables de entorno');
    }
    data.propertyID = env.CLOUDBEDS_PROPERTY_ID;
  }

  // Formatear sourceID según requerimientos de Cloudbeds
  const sourceIdValue = data.sourceID || 2;
  data.sourceID = typeof sourceIdValue === 'string' && sourceIdValue.startsWith('s-')
    ? sourceIdValue
    : `s-${sourceIdValue}-1`;

  // Generar thirdPartyIdentifier si no existe
  if (!data.thirdPartyIdentifier) {
    data.thirdPartyIdentifier = `server-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  }

  // Valores por defecto
  data.status = data.status || 'confirmed';
  data.sendEmailConfirmation = data.sendEmailConfirmation !== undefined ? data.sendEmailConfirmation : true;

  // Asegurarse de que startDate y endDate estén presentes a nivel de reserva
  if (!data.startDate || !data.endDate) {
    if (data.roomsData && data.roomsData.length > 0) {
      data.startDate = data.roomsData[0].startDate;
      data.endDate = data.roomsData[0].endDate;
      console.log('Añadidos startDate y endDate a nivel de reserva desde roomsData:', data.startDate, data.endDate);
    }
  }

  return data as CloudbedsReservationRequest;
}

/**
 * Controlador para manejar la creación de reservas en Cloudbeds
 */
export async function POST(event: RequestEvent): Promise<Response> {
  try {
    console.log('=== INICIO PROCESO RESERVA EN API ===');

    // Validar y procesar datos de entrada
    const reservationDataRaw = await event.request.json();
    console.log('Datos de reserva recibidos:', JSON.stringify(reservationDataRaw, null, 2));

    let reservationData: CloudbedsReservationRequest;
    try {
      reservationData = validateReservationRequest(reservationDataRaw);
      console.log('Datos de reserva validados correctamente');
    } catch (validationError) {
      console.error('Error de validación:', validationError);
      return json({
        success: false,
        message: validationError instanceof Error ? validationError.message : 'Error de validación'
      }, { status: 400 });
    }

    // Extraer fechas para añadirlas al nivel raíz
    if (reservationData.roomsData && reservationData.roomsData.length > 0) {
      const firstRoom = reservationData.roomsData[0];
      const startDate = firstRoom.startDate;
      const endDate = firstRoom.endDate;

      console.log('Fechas extraídas del primer room:', { startDate, endDate });

      // Añadir a nivel raíz (cast para evitar errores de TypeScript con readonly)
      (reservationData as any).startDate = startDate;
      (reservationData as any).endDate = endDate;
    }

    console.log('Datos finales para Cloudbeds:', JSON.stringify(reservationData, null, 2));

    // Inicializar el servicio de Cloudbeds
    const cloudbedsService = new CloudbedsApiService();

    // Usar solo el método directo que sabemos que funciona
    console.log('Intentando crear reserva con método directo...');
    let result = await cloudbedsService.createReservationDirect(reservationData);

    if (result.success) {
      console.log('Reserva creada exitosamente:', result);

      // Devolver respuesta exitosa con todos los datos disponibles
      return json({
        success: true,
        reservationID: result.reservationID,
        confirmationCode: result.confirmationCode,
        message: result.message,
        data: result.data || {
          reservationID: result.reservationID,
          confirmationCode: result.confirmationCode
        }
      });
    } else {
      console.error('Error creando reserva:', result.message);
      return json({
        success: false,
        message: result.message
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error interno procesando la reserva:', error);
    return json({
      success: false,
      message: error instanceof Error ? error.message : 'Error interno del servidor'
    }, { status: 500 });
  } finally {
    console.log('=== FIN PROCESO RESERVA EN API ===');
  }
}