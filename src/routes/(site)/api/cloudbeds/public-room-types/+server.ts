import { json } from '@sveltejs/kit';
import { cloudbedsApiService } from '$lib/cloudbeds/api.server';
import type { RequestEvent } from '@sveltejs/kit';

/**
 * API endpoint to get room types that are available for public booking
 * This endpoint filters out room types that have isPrivate=true
 */
export const GET = async ({ url }: RequestEvent) => {
  try {
    console.group('API endpoint public-room-types: Starting request');

    // Get URL parameters
    const includePhotos = url.searchParams.get('includePhotos') === '1';
    const includeAmenities = url.searchParams.get('includeAmenities') === '1';

    console.log('Parameters:', { includePhotos, includeAmenities });

    // Get room types from Cloudbeds API
    console.log('Calling cloudbedsApiService.getRoomTypes...');
    const roomTypes = await cloudbedsApiService.getRoomTypes(includePhotos, includeAmenities);

    // Log response details
    console.log('Response successful?:', roomTypes.success);
    console.log('Response has data?:', !!roomTypes.data);
    console.log('Number of room types found:', roomTypes.data?.length || 0);

    // Filter out private room types
    if (roomTypes.success && roomTypes.data && roomTypes.data.length > 0) {
      const publicRoomTypes = roomTypes.data.filter(roomType => !roomType.isPrivate);
      
      console.log('Number of public room types:', publicRoomTypes.length);
      console.log('Number of private room types filtered out:', roomTypes.data.length - publicRoomTypes.length);
      
      if (publicRoomTypes.length > 0) {
        // Log sample room type for debugging
        const sampleRoom = publicRoomTypes[0];
        console.log('Sample public room type:', {
          id: sampleRoom.id,
          roomTypeID: sampleRoom.roomTypeID,
          name: sampleRoom.name,
          roomTypeName: sampleRoom.roomTypeName,
          isPrivate: sampleRoom.isPrivate
        });
      } else {
        console.log('No public room types found - all room types are marked as private');
      }

      // Return filtered room types
      const response = {
        ...roomTypes,
        data: publicRoomTypes,
        filteredCount: roomTypes.data.length - publicRoomTypes.length
      };
      
      console.log('Returning filtered response');
      console.groupEnd();
      return json(response);
    }

    // If no data or error, return original response
    console.log('Returning original response');
    console.groupEnd();
    return json(roomTypes);
  } catch (error) {
    console.error('Error getting public room types:', error);
    console.groupEnd();
    return json({
      success: false,
      error: {
        code: 'CLOUDBEDS_API_ERROR',
        message: error instanceof Error ? error.message : 'Unknown error'
      }
    }, { status: 500 });
  }
};
