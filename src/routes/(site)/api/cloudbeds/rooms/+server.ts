import { json } from '@sveltejs/kit';
import { cloudbedsApiService } from '$lib/cloudbeds/api.server';
import type { RequestEvent } from '@sveltejs/kit';

export const GET = async ({ url }: RequestEvent) => {
  try {
    // Obtener parámetros de la URL
    const includePhotos = url.searchParams.get('includePhotos') === '1';
    const includeAmenities = url.searchParams.get('includeAmenities') === '1';
    const includeRoomRelations = url.searchParams.get('includeRoomRelations') === '1';

    // Obtener habitaciones con los parámetros solicitados
    const rooms = await cloudbedsApiService.getRooms(includePhotos, includeAmenities, includeRoomRelations);
    return json(rooms);
  } catch (error) {
    console.error('Error obteniendo habitaciones:', error);
    return json({
      success: false,
      error: {
        code: 'CLOUDBEDS_API_ERROR',
        message: error instanceof Error ? error.message : 'Error desconocido'
      }
    }, { status: 500 });
  }
}; 