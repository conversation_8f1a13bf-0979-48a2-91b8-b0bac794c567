import { json } from '@sveltejs/kit';
import { cloudbedsApiService } from '$lib/cloudbeds/api.server';
import type { RequestHandler } from './$types';
import { z } from 'zod';

export const GET: RequestHandler = async ({ url, request }) => {
  console.group("API Endpoint - /api/cloudbeds/availability");
  try {
    // Get request info for debugging
    const referer = request.headers.get('referer') || 'unknown';
    const isAdmin = referer.includes('/admin/');
    console.log("🔍 DEBUGGING: Request context:", {
      referer,
      isAdmin,
      url: url.toString(),
      headers: Object.fromEntries(request.headers)
    });

    // Validar y extraer parámetros
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const roomTypeId = url.searchParams.get('roomTypeId') || undefined;

    console.log("Request parameters:", { startDate, endDate, roomTypeId });

    // Validar parámetros requeridos
    if (!startDate || !endDate) {
      console.error("Missing required parameters");
      const errorResponse = {
        success: false,
        error: {
          code: 'MISSING_PARAMETERS',
          message: 'Se requieren los parámetros startDate y endDate'
        }
      };
      console.log("Returning error response:", errorResponse);
      console.groupEnd();
      return json(errorResponse, { status: 400 });
    }

    // Validar formato de fechas
    const dateSchema = z.string().regex(/^\d{4}-\d{2}-\d{2}$/);
    try {
      dateSchema.parse(startDate);
      dateSchema.parse(endDate);
      console.log("Date format validation passed");
    } catch (validationError) {
      console.error("Invalid date format:", validationError);
      const errorResponse = {
        success: false,
        error: {
          code: 'INVALID_DATE_FORMAT',
          message: 'El formato de fecha debe ser YYYY-MM-DD'
        }
      };
      console.log("Returning error response:", errorResponse);
      console.groupEnd();
      return json(errorResponse, { status: 400 });
    }

    console.log("Calling cloudbedsApiService.getAvailability");
    console.time("Server API call duration");

    // Add detailed debugging for the API call
    console.log("🔍 DEBUGGING: Cloudbeds API call details:", {
      method: "getAvailability",
      parameters: {
        startDate,
        endDate,
        roomTypeId
      },
      propertyId: process.env.CLOUDBEDS_PROPERTY_ID,
      apiKeyPresent: !!process.env.CLOUDBEDS_API_KEY,
      clientIdPresent: !!process.env.CLOUDBEDS_CLIENT_ID,
      clientSecretPresent: !!process.env.CLOUDBEDS_CLIENT_SECRET
    });

    const availability = await cloudbedsApiService.getAvailability(startDate, endDate, roomTypeId);
    console.timeEnd("Server API call duration");

    console.log("API response:", {
      success: availability.success,
      dataLength: availability.data?.length || 0,
      error: availability.error
    });

    // Add more detailed debugging for the response
    console.log("🔍 DEBUGGING: Cloudbeds API response details:", {
      success: availability.success,
      hasData: availability.data && availability.data.length > 0,
      dataLength: availability.data?.length || 0,
      errorMessage: availability.error?.message,
      firstItemRoomTypeId: availability.data?.[0]?.roomTypeId || 'N/A',
      firstItemDatesCount: availability.data?.[0]?.dates?.length || 0,
      emptyResponse: !availability.data || availability.data.length === 0
    });

    if (availability.success && availability.data) {
      console.log("First room availability data:", availability.data[0]);
    }

    console.log("Returning response to client");
    console.groupEnd();
    return json(availability);
  } catch (error) {
    console.error('Error obteniendo disponibilidad:', error);
    const errorResponse = {
      success: false,
      error: {
        code: 'CLOUDBEDS_API_ERROR',
        message: error instanceof Error ? error.message : 'Error desconocido'
      }
    };
    console.log("Returning error response:", errorResponse);
    console.groupEnd();
    return json(errorResponse, { status: 500 });
  }
};