import { json } from '@sveltejs/kit';
import { cloudbedsApiService } from '$lib/cloudbeds/api.server';
import { WebhookHandler } from '$lib/cloudbeds/webhook-handler';
import { RoomTypeCache } from '$lib/cloudbeds/room-type-cache';
import type { RequestEvent } from '@sveltejs/kit';

/**
 * Webhook endpoint for Cloudbeds events
 * This endpoint receives webhook events from Cloudbeds and updates the room type cache
 */
export async function POST(event: RequestEvent): Promise<Response> {
  try {
    console.log('Received Cloudbeds webhook event');
    
    // Get the webhook data
    const webhookData = await event.request.json();
    console.log('Webhook data:', JSON.stringify(webhookData, null, 2));
    
    // Create a room type cache instance
    // Note: We're using the cloudbedsApiService singleton that's already initialized
    const roomTypeCache = new RoomTypeCache(cloudbedsApiService as any);
    
    // Create a webhook handler
    const webhookHandler = new WebhookHandler(roomTypeCache);
    
    // Handle the webhook event
    const result = await webhookHandler.handleWebhookEvent(webhookData);
    
    // Return the result
    return json(result);
  } catch (error) {
    console.error('Error handling webhook:', error);
    return json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error handling webhook'
    }, { status: 500 });
  }
}
