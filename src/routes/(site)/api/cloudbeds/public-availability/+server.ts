import { json } from '@sveltejs/kit';
import { cloudbedsApiService } from '$lib/cloudbeds/api.server';
import type { RequestHandler } from './$types';
import { z } from 'zod';

/**
 * API endpoint to get availability for public room types only
 * This endpoint first gets all room types, filters out private ones,
 * then gets availability only for public room types
 */
export const GET: RequestHandler = async ({ url, request }) => {
  console.group("API Endpoint - /api/cloudbeds/public-availability");
  try {
    // Get request info for debugging
    const referer = request.headers.get('referer') || 'unknown';
    console.log("🔍 DEBUGGING: Request context:", {
      referer,
      url: url.toString(),
      headers: Object.fromEntries(request.headers)
    });

    // Validate and extract parameters
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const roomTypeId = url.searchParams.get('roomTypeId') || undefined;

    console.log("Request parameters:", { startDate, endDate, roomTypeId });

    // Validate required parameters
    if (!startDate || !endDate) {
      console.error("Missing required parameters");
      const errorResponse = {
        success: false,
        error: {
          code: 'MISSING_PARAMETERS',
          message: 'startDate and endDate parameters are required'
        }
      };
      console.log("Returning error response:", errorResponse);
      console.groupEnd();
      return json(errorResponse, { status: 400 });
    }

    // Validate date format
    const dateSchema = z.string().regex(/^\d{4}-\d{2}-\d{2}$/);
    try {
      dateSchema.parse(startDate);
      dateSchema.parse(endDate);
      console.log("Date format validation passed");
    } catch (validationError) {
      console.error("Invalid date format:", validationError);
      const errorResponse = {
        success: false,
        error: {
          code: 'INVALID_DATE_FORMAT',
          message: 'Date format must be YYYY-MM-DD'
        }
      };
      console.log("Returning error response:", errorResponse);
      console.groupEnd();
      return json(errorResponse, { status: 400 });
    }

    // If a specific room type ID is provided, first check if it's public
    if (roomTypeId) {
      console.log("Checking if room type is public:", roomTypeId);
      
      // Get room type details
      const roomTypeDetails = await cloudbedsApiService.getRoomTypeDetails(roomTypeId, false, false);
      
      if (roomTypeDetails.success && roomTypeDetails.data && roomTypeDetails.data.length > 0) {
        const roomType = roomTypeDetails.data[0];
        
        // If room type is private, return empty data array
        if (roomType.isPrivate) {
          console.log("Room type is private, returning empty data array");
          const emptyResponse = {
            success: true,
            data: [],
            roomCount: 0,
            count: 0,
            total: 0,
            message: "Room type is not available for public booking"
          };
          console.groupEnd();
          return json(emptyResponse);
        }
      }
    } else {
      // If no specific room type ID is provided, get all public room types
      console.log("No specific room type ID provided, getting all public room types");
      
      // Get all room types
      const roomTypes = await cloudbedsApiService.getRoomTypes(false, false);
      
      if (roomTypes.success && roomTypes.data && roomTypes.data.length > 0) {
        // Filter out private room types
        const publicRoomTypes = roomTypes.data.filter(roomType => !roomType.isPrivate);
        
        if (publicRoomTypes.length === 0) {
          console.log("No public room types found, returning empty data array");
          const emptyResponse = {
            success: true,
            data: [],
            roomCount: 0,
            count: 0,
            total: 0,
            message: "No room types available for public booking"
          };
          console.groupEnd();
          return json(emptyResponse);
        }
      }
    }

    // Get availability data
    console.log("Getting availability data");
    console.time("Server API call duration");
    
    const availability = await cloudbedsApiService.getAvailability(startDate, endDate, roomTypeId);
    console.timeEnd("Server API call duration");

    console.log("API response:", {
      success: availability.success,
      dataLength: availability.data?.length || 0,
      error: availability.error
    });

    // If successful but empty data, provide a more helpful message
    if (availability.success && (!availability.data || availability.data.length === 0)) {
      const emptyResponse = {
        ...availability,
        message: "No availability data found. This may be because the room types are not configured for public booking."
      };
      console.log("Returning empty response with helpful message");
      console.groupEnd();
      return json(emptyResponse);
    }

    console.log("Returning response to client");
    console.groupEnd();
    return json(availability);
  } catch (error) {
    console.error('Error getting availability:', error);
    const errorResponse = {
      success: false,
      error: {
        code: 'CLOUDBEDS_API_ERROR',
        message: error instanceof Error ? error.message : 'Unknown error'
      }
    };
    console.log("Returning error response:", errorResponse);
    console.groupEnd();
    return json(errorResponse, { status: 500 });
  }
};
