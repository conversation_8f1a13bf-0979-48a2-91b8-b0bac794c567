import { json } from '@sveltejs/kit';
import { cloudbedsApiService } from '$lib/cloudbeds/api.server';
import type { RequestEvent } from '@sveltejs/kit';

/**
 * Test endpoint for the dynamic room type resolution system
 * This endpoint allows testing the room type resolution with different inputs
 */
export async function GET(event: RequestEvent): Promise<Response> {
  try {
    console.log('Testing dynamic room type resolution');
    
    // Get parameters from the URL
    const roomTypeId = event.url.searchParams.get('roomTypeId') || undefined;
    const roomName = event.url.searchParams.get('roomName') || undefined;
    
    // Get the property ID
    const propertyId = await cloudbedsApiService.fetchPropertyId();
    
    // Get room types for reference
    const roomTypesResponse = await cloudbedsApiService.getRoomTypes(false, false);
    const roomTypes = roomTypesResponse.success && roomTypesResponse.data ? roomTypesResponse.data : [];
    
    // Test the room type resolution
    const resolvedRoomTypeId = await cloudbedsApiService.validateRoomTypeId(roomTypeId, roomName);
    
    // Find the resolved room type details
    const resolvedRoomType = roomTypes.find(rt => 
      (rt.roomTypeID === resolvedRoomTypeId) || (rt.id === resolvedRoomTypeId)
    );
    
    // Return the results
    return json({
      success: true,
      input: {
        roomTypeId,
        roomName,
        propertyId
      },
      resolution: {
        resolvedRoomTypeId,
        resolvedRoomTypeName: resolvedRoomType ? (resolvedRoomType.roomTypeName || resolvedRoomType.name) : 'Unknown',
        resolutionMethod: roomTypeId && resolvedRoomTypeId === roomTypeId ? 'direct' : 
                          roomName ? 'name-based' : 'default'
      },
      availableRoomTypes: roomTypes.map(rt => ({
        id: rt.roomTypeID || rt.id,
        name: rt.roomTypeName || rt.name
      }))
    });
  } catch (error) {
    console.error('Error testing room type resolution:', error);
    return json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error testing room type resolution'
    }, { status: 500 });
  }
}
