import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON>and<PERSON> } from './$types';

/**
 * GET handler for suites
 * Fetches suites from the database
 *
 * Query parameters:
 * - slug: Optional. If provided, returns a single suite with the given slug
 * - site_id: Optional. If provided, filters suites by site ID
 * - limit: Optional. Limits the number of suites returned (default: 10)
 * - active_only: Optional. If '1', only returns active suites
 */
export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    // Get query parameters
    const slug = url.searchParams.get('slug');
    const siteId = url.searchParams.get('site_id');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const activeOnly = url.searchParams.get('active_only') === '1';

    console.log('API: Fetching suites with params:', { slug, siteId, limit, activeOnly });

    // Initialize query
    let query = locals.supabase
      .from('suites_with_mappings')
      .select('*');

    // Apply filters
    if (slug) {
      console.log('API: Filtering by slug:', slug);
      query = query.eq('slug', slug);
    }

    if (siteId) {
      console.log('API: Filtering by site_id:', siteId);
      query = query.eq('site_id', siteId);
    }

    if (activeOnly) {
      console.log('API: Filtering by active status');
      query = query.eq('status', 'active');
    }

    // Apply limit
    query = query.limit(limit);
    console.log('API: Applying limit:', limit);

    // Execute query
    console.log('API: Executing query');
    const { data, error } = await query;
    console.log('API: Query result:', { dataLength: data?.length, error });

    if (error) {
      console.error('Error fetching suites:', error);
      return json({
        success: false,
        error: error.message
      }, { status: 500 });
    }

    // If slug was provided, return a single suite or 404
    if (slug) {
      if (data && data.length > 0) {
        console.log('API: Returning single suite for slug:', slug);

        // Process the suite data to ensure proper format
        const suiteData = { ...data[0] };

        // Ensure arrays are properly formatted
        ['features', 'amenities', 'images', 'videos'].forEach(field => {
          // Check if the field exists and is not already an array
          if (suiteData[field] && !Array.isArray(suiteData[field])) {
            try {
              // If it's a string that looks like JSON, try to parse it
              if (typeof suiteData[field] === 'string' &&
                  (suiteData[field].startsWith('[') || suiteData[field].startsWith('{'))) {
                suiteData[field] = JSON.parse(suiteData[field]);
                console.log(`API: Successfully parsed ${field} as JSON:`, suiteData[field]);
              } else {
                console.warn(`API: ${field} is not a valid JSON string, initializing as empty array`);
                suiteData[field] = [];
              }
            } catch (err) {
              console.error(`API: Error parsing ${field}:`, err);
              suiteData[field] = [];
            }
          }

          // Final check to ensure it's an array
          if (!Array.isArray(suiteData[field])) {
            console.warn(`API: ${field} is still not an array after processing, initializing as empty array`);
            suiteData[field] = [];
          }
        });

        console.log('API: Processed suite data fields:', {
          id: suiteData.id,
          name: suiteData.name,
          description: suiteData.description ? (suiteData.description.length > 50 ? suiteData.description.substring(0, 50) + '...' : suiteData.description) : 'N/A',
          features: Array.isArray(suiteData.features) ? `${suiteData.features.length} items` : 'not an array',
          amenities: Array.isArray(suiteData.amenities) ? `${suiteData.amenities.length} items` : 'not an array',
          images: Array.isArray(suiteData.images) ? `${suiteData.images.length} items` : 'not an array',
          videos: Array.isArray(suiteData.videos) ? `${suiteData.videos.length} items` : 'not an array',
          size: suiteData.size || 'N/A',
          capacity: suiteData.capacity || 'N/A',
          cloudbeds_room_type_id: suiteData.cloudbeds_room_type_id || 'N/A',
          cloudbeds_property_id: suiteData.cloudbeds_property_id || 'N/A'
        });

        return json({
          success: true,
          data: suiteData
        });
      } else {
        console.log('API: Suite not found for slug:', slug);
        return json({
          success: false,
          error: 'Suite not found'
        }, { status: 404 });
      }
    }

    // Return all suites
    console.log('API: Returning all suites, count:', data?.length);

    // Process all suites to ensure proper format
    const processedData = (data || []).map(suite => {
      const processedSuite = { ...suite };

      // Ensure arrays are properly formatted
      ['features', 'amenities', 'images', 'videos'].forEach(field => {
        // Check if the field exists and is not already an array
        if (processedSuite[field] && !Array.isArray(processedSuite[field])) {
          try {
            // If it's a string that looks like JSON, try to parse it
            if (typeof processedSuite[field] === 'string' &&
                (processedSuite[field].startsWith('[') || processedSuite[field].startsWith('{'))) {
              processedSuite[field] = JSON.parse(processedSuite[field]);
            } else {
              processedSuite[field] = [];
            }
          } catch (err) {
            processedSuite[field] = [];
          }
        }

        // Final check to ensure it's an array
        if (!Array.isArray(processedSuite[field])) {
          processedSuite[field] = [];
        }
      });

      return processedSuite;
    });

    return json({
      success: true,
      data: processedData
    });
  } catch (err) {
    console.error('Error in suites API:', err);
    return json({
      success: false,
      error: err instanceof Error ? err.message : 'An unknown error occurred'
    }, { status: 500 });
  }
};
