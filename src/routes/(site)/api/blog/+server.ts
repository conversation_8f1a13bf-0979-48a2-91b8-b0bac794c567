import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { z } from 'zod';
import { env } from '$env/dynamic/public';

// Define the blog post schema for validation
const BlogPostSchema = z.object({
  id: z.string(),
  status: z.string(),
  created_at: z.string(),
  updated_at: z.string(),
  published_at: z.string(),
  data: z.object({
    slug: z.string(),
    title: z.string(),
    banner: z.string(),
    content: z.string()
  }),
  translations: z.record(z.object({
    source_language: z.string(),
    data: z.object({
      slug: z.string(),
      title: z.string(),
      content: z.string()
    }),
    is_machine_translated: z.boolean(),
    is_reviewed: z.boolean(),
    is_published: z.boolean()
  })).optional()
});

// Define the blog collection schema
const BlogCollectionSchema = z.object({
  collection: z.object({
    id: z.string(),
    name: z.string(),
    slug: z.string()
  }),
  items: z.array(BlogPostSchema),
  meta: z.object({
    total: z.number(),
    offset: z.number(),
    limit: z.number(),
    status: z.string(),
    language: z.string().nullable(),
    include_translations: z.boolean(),
    available_languages: z.array(z.string())
  })
});

/**
 * GET handler for blog posts
 * Fetches blog posts from the external API
 */
export const GET: RequestHandler = async ({ url, fetch }) => {
  try {
    // Get query parameters
    const slug = url.searchParams.get('slug');
    const language = url.searchParams.get('language') || 'en';

    // API endpoint - using the correct port
    const apiUrl = env.PUBLIC_BLOG_API_URL || 'http://localhost:5174/api/collections/a14a1443-00df-4c16-88ea-5eae6e6c9bcc/export?status=published&format=json&include_translations=true&token=23Lx5M3r7ymTw2EqZP73BoMfoKiaE2qM';
    console.log('API URL:', apiUrl);

    // Fetch data from API
    const response = await fetch(apiUrl);

    if (!response.ok) {
      console.error('Error fetching blog data:', response.statusText);
      return json({
        success: false,
        error: {
          code: 'API_ERROR',
          message: `Error fetching blog data: ${response.statusText}`
        }
      }, { status: response.status });
    }

    // Parse response
    const data = await response.json();

    // Validate data against schema
    try {
      const validatedData = BlogCollectionSchema.parse(data);

      // If slug is provided, return specific post
      if (slug) {
        const post = validatedData.items.find(item => item.data.slug === slug);

        if (!post) {
          return json({
            success: false,
            error: {
              code: 'POST_NOT_FOUND',
              message: `Blog post with slug "${slug}" not found`
            }
          }, { status: 404 });
        }

        // Return post with requested language if available
        if (language !== 'en' && post.translations && post.translations[language]) {
          // Merge English post with translation
          const translatedPost = {
            ...post,
            data: {
              ...post.data,
              title: post.translations[language].data.title,
              content: post.translations[language].data.content,
              translatedSlug: post.translations[language].data.slug
            }
          };

          return json({
            success: true,
            post: translatedPost
          });
        }

        return json({
          success: true,
          post
        });
      }

      // Return all posts
      return json({
        success: true,
        collection: validatedData.collection,
        posts: validatedData.items,
        meta: validatedData.meta
      });
    } catch (validationError) {
      console.error('Data validation error:', validationError);
      return json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid blog data format',
          details: validationError
        }
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error in blog API:', error);
    return json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: error instanceof Error ? error.message : 'Unknown error'
      }
    }, { status: 500 });
  }
};
