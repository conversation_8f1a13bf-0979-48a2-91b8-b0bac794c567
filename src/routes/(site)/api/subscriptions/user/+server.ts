/**
 * Endpoint para obtener las suscripciones del usuario actual
 */
import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';
import { getUserSubscriptions } from '$lib/stripe/subscription.server';
import { createServerSupabaseClient } from '$lib/supabase';

/**
 * Maneja la solicitud GET para obtener las suscripciones del usuario actual
 */
export async function GET({ cookies }: RequestEvent) {
  try {
    // Crear un cliente de Supabase para el servidor
    const supabase = createServerSupabaseClient(cookies);

    // Obtener la información autenticada del usuario
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    // Verificar si el usuario está autenticado
    if (userError || !user) {
      console.error('Error de autenticación:', userError);
      return json({ success: false, error: 'No autorizado' }, { status: 401 });
    }

    // Obtener el ID y email del usuario autenticado
    const userId = user.id;
    const userEmail = user.email;

    // Verificar que el ID del usuario existe
    if (!userId) {
      return json({ success: false, error: 'ID de usuario no disponible' }, { status: 400 });
    }

    // Obtener las suscripciones del usuario usando el servicio de suscripciones
    const result = await getUserSubscriptions(userId, userEmail);

    // Verificar si hubo un error
    if (result.error) {
      console.error('Error al obtener las suscripciones del usuario:', result.error);
      return json({ success: false, error: 'Error al obtener las suscripciones' }, { status: 500 });
    }

    // Transformar las suscripciones para incluir solo la información necesaria
    const subscriptions = result.subscriptions.map(subscription => {
      // Asegurarse de que customer sea un string
      const customerString = typeof subscription.customer === 'string'
        ? subscription.customer
        : subscription.customer.id;

      return {
        id: subscription.id,
        customer: customerString,
        status: subscription.status,
        current_period_start: subscription.current_period_start,
        current_period_end: subscription.current_period_end,
        cancel_at_period_end: subscription.cancel_at_period_end,
        items: {
          data: subscription.items.data.map(item => {
            // Asegurarse de que product sea un string
            const productString = typeof item.price.product === 'string'
              ? item.price.product
              : (item.price.product as any).id;

            return {
              id: item.id,
              price: {
                id: item.price.id,
                product: productString,
                currency: item.price.currency,
                unit_amount: item.price.unit_amount || 0,
                type: item.price.type,
                active: item.price.active,
                recurring: item.price.recurring
              }
            };
          })
        },
        default_payment_method: subscription.default_payment_method,
        latest_invoice: subscription.latest_invoice
      };
    });

    // Devolver las suscripciones
    return json({
      success: true,
      subscriptions
    });
  } catch (error) {
    // Manejar errores
    console.error('Error al obtener las suscripciones del usuario:', error);
    return json({ success: false, error: 'Error interno del servidor' }, { status: 500 });
  }
}