import { json } from '@sveltejs/kit';
import type { RequestHandler } from '@sveltejs/kit';
import { checkSubscription } from '$lib/stripe/subscription.server';

export const GET: RequestHandler = async ({ url, locals }) => {
  try {
    const userId = url.searchParams.get('userId');

    if (!userId) {
      return json({ error: 'Se requiere el ID de usuario' }, { status: 400 });
    }

    // Obtener el email del usuario si está disponible
    let userEmail: string | undefined;
    if (locals.session && locals.user && locals.user.email) {
      userEmail = locals.user.email;
    }

    // Utilizar el servicio centralizado para verificar la suscripción
    const { hasActiveSubscription, subscriptions, error } = await checkSubscription(userId, userEmail);

    if (error) {
      console.error('Error al verificar la suscripción:', error);
      return json({ error }, { status: 500 });
    }

    return json({
      hasActiveSubscription,
      subscriptions
    });
  } catch (error) {
    console.error('Error al verificar la suscripción:', error);
    return json({ error: 'Error interno del servidor' }, { status: 500 });
  }
};