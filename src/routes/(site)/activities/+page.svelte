<script lang="ts">
  import { onMount } from "svelte";
  import ActivityCard from "$lib/components/activities/ActivityCard.svelte";
  import { T, t } from "$lib/i18n";

  // Animation state
  let overviewVisible = $state(false);
  let activitiesVisible = $state(false);

  // State for activities
  let activities = $state<any[]>([]);
  let loading = $state(true);
  let error = $state<string | null>(null);

  // Load activities from API
  async function loadActivities() {
    try {
      loading = true;
      error = null;

      // Fetch activities from API
      const response = await fetch('/api/activities?active_only=1');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to load activities');
      }

      if (!result.success || !result.data) {
        throw new Error('No activities found');
      }

      // Transform the data to match the expected format
      activities = result.data.map((activity: any) => ({
        id: activity.slug,
        title: activity.name,
        description: activity.description,
        image: activity.images && activity.images.length > 0 ? activity.images[0] : '',
        url: `/activities/${activity.slug}`,
        features: Array.isArray(activity.features) ? [...activity.features] : [],
        schedule: activity.schedule || '',
        duration: activity.duration || '',
        location: activity.location || '',
        price: activity.price_info || null,
        // Add videos array for hover effect
        videos: Array.isArray(activity.videos) ? [...activity.videos] : [],
      }));

      console.log('Loaded activities for page:', activities);
    } catch (err) {
      console.error('Error loading activities:', err);
      error = err instanceof Error ? err.message : 'An error occurred while loading activities';

      // Fallback to empty array if there's an error
      activities = [];
    } finally {
      loading = false;
    }
  }

  // Handle intersection observer for animations
  function handleIntersection(entries: IntersectionObserverEntry[]) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        if (entry.target.id === 'overview-section') {
          overviewVisible = true;
        } else if (entry.target.id === 'activities-section') {
          activitiesVisible = true;
        }
      }
    });
  }

  onMount(() => {
    loadActivities();

    // Set up intersection observer for animations
    const observer = new IntersectionObserver(handleIntersection, {
      threshold: 0.1,
    });

    const overviewSection = document.getElementById('overview-section');
    const activitiesSection = document.getElementById('activities-section');

    if (overviewSection) observer.observe(overviewSection);
    if (activitiesSection) observer.observe(activitiesSection);

    return () => {
      observer.disconnect();
    };
  });
</script>

<svelte:head>
  <title>Activities - Baberrih</title>
</svelte:head>

<!-- Hero Section -->
<section class="bg-primary-900 relative h-[50vh] min-h-[400px] w-full">
  <div class="absolute inset-0 bg-black/40 z-10"></div>
  <div class="absolute inset-0 bg-gradient-to-b from-transparent to-black/60 z-10"></div>
  <img
    src="https://baberrih.ma/media/activities/1_f.webp"
    alt="Activities"
    class="absolute inset-0 w-full h-full object-cover"
  />
  <div class="absolute inset-0 flex flex-col justify-center items-center text-center z-20">
    <h1 class="font-montserrat font-light text-white text-4xl md:text-6xl uppercase tracking-wider">
      <T key="activities.title" />
    </h1>
    <div class="bg-white mt-4 w-24 h-0.5"></div>
  </div>
</section>

<!-- Overview Section -->
<section id="overview-section" class="px-4 py-12">
  <div class="mx-auto max-w-4xl container">
    <div
      class={`scroll-animation fade ${overviewVisible ? "visible" : ""}`}
      style="--delay: 300ms;"
    >
      <h2
        class="mb-6 font-montserrat font-light text-primary-900 text-xl uppercase"
      >
        <T key="activities.overview" />
      </h2>

      <p class="mb-6 font-eb-garamond font-light text-primary-800 text-lg">
        <T key="activities.overview_text" />
      </p>
    </div>
  </div>
</section>

<!-- Activities Section -->
<section
  id="activities-section"
  class="bg-primary-50/50 px-4 py-12"
>
  <div class="mx-auto max-w-4xl container">
    <div
      class={`scroll-animation fade ${activitiesVisible ? "visible" : ""}`}
      style="--delay: 300ms;"
    >
      <h2
        class="mb-6 font-montserrat font-light text-primary-900 text-xl uppercase"
      >
        <T key="activities.our_activities" />
      </h2>

      {#if loading}
        <div class="p-8 text-center">
          <div
            class="inline-block border-4 border-gray-300 border-t-blue-600 rounded-full w-8 h-8 animate-spin"
          ></div>
          <p class="mt-2 text-gray-600">Loading activities...</p>
        </div>
      {:else if error}
        <div class="p-8 text-center">
          <p class="text-red-600">{error}</p>
        </div>
      {:else if activities.length === 0}
        <div class="p-8 text-center">
          <p class="text-gray-600">No activities found.</p>
        </div>
      {:else}
        <div class="gap-8 grid grid-cols-1 md:grid-cols-2">
          {#each activities as activity}
            <ActivityCard {activity} />
          {/each}
        </div>
      {/if}
    </div>
  </div>
</section>
