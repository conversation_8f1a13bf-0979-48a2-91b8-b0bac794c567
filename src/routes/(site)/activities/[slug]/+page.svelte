<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import ActivityDisplay from '$lib/components/activities/ActivityDisplay.svelte';
  import { T, t } from "$lib/i18n";
  
  // Get activity slug from URL
  const activitySlug = $page.params.slug;
  
  // State
  let activity = $state(null);
  let loading = $state(true);
  let error = $state(null);
  
  // Load activity data
  async function loadActivity() {
    try {
      loading = true;
      error = null;
      
      // Fetch activity from API
      const response = await fetch(`/api/activities?slug=${activitySlug}`);
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to load activity');
      }
      
      if (!result.success || !result.data) {
        throw new Error('Activity not found');
      }
      
      activity = result.data;
      console.log('Loaded activity:', activity);
    } catch (err) {
      console.error('Error loading activity:', err);
      error = err instanceof Error ? err.message : 'An error occurred';
    } finally {
      loading = false;
    }
  }
  
  onMount(() => {
    loadActivity();
  });
</script>

<svelte:head>
  <title>{activity ? `${activity.name} - Baberrih` : 'Activity - Baberrih'}</title>
</svelte:head>

<div class="mx-auto px-4 py-8 max-w-4xl container">
  <div class="mb-6">
    <a
      href="/activities"
      class="text-blue-600 hover:text-blue-800 hover:underline"
    >
      &larr; <T key="common.back_to_activities" />
    </a>
  </div>

  {#if loading}
    <div class="p-8 text-center">
      <div
        class="inline-block border-4 border-gray-300 border-t-blue-600 rounded-full w-8 h-8 animate-spin"
      ></div>
      <p class="mt-2 text-gray-600">Loading activity information...</p>
    </div>
  {:else if error}
    <div class="p-8 text-center">
      <p class="text-red-600">{error}</p>
    </div>
  {:else if activity}
    <!-- Use the ActivityDisplay component with activity data -->
    <ActivityDisplay activityData={activity} />
  {:else}
    <div class="flex justify-center items-center min-h-[50vh]">
      <p class="font-montserrat font-light text-primary-900 text-xl">
        Activity not found
      </p>
    </div>
  {/if}
</div>
