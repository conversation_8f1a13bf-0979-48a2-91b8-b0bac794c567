import { redirect, fail } from '@sveltejs/kit';
import { z } from 'zod';
import type { Actions } from './$types';

// Esquema de validación para el formulario de verificación
const verifySchema = z.object({
  email: z.string().email('El correo electrónico no es válido'),
  code: z.string().min(6, 'El código debe tener al menos 6 caracteres').max(6, 'El código debe tener máximo 6 caracteres')
});

export const actions: Actions = {
  // Acción para verificar el código
  verify: async ({ request, locals }) => {
    const formData = await request.formData();
    const email = formData.get('email')?.toString() || '';
    const code = formData.get('code')?.toString() || '';

    try {
      // Validar los datos del formulario
      const result = verifySchema.parse({ email, code });

      // Verificar el código con Supabase
      const { error } = await locals.supabase.auth.verifyOtp({
        email: result.email,
        token: result.code,
        type: 'signup'
      });

      // Si hay un error, devolver un mensaje de error
      if (error) {
        return fail(400, {
          error: error.message,
          email
        });
      }

      // Si todo está bien, devolver un mensaje de éxito
      return {
        success: true,
        message: 'Tu correo electrónico ha sido verificado correctamente. Ahora puedes iniciar sesión.'
      };
    } catch (error) {
      // Si hay un error de validación, devolver un mensaje de error
      if (error instanceof z.ZodError) {
        const fieldErrors = error.flatten().fieldErrors;
        return fail(400, {
          error: 'Datos de formulario inválidos',
          fieldErrors,
          email
        });
      }

      // Para cualquier otro error, devolver un mensaje genérico
      return fail(500, {
        error: 'Error interno del servidor',
        email
      });
    }
  }
}; 