<script>
  import { enhance } from "$app/forms";
  let { form } = $props();

  let code = $state("");
  let email = $state("");
</script>

<div class="mb-6 text-center">
  <h1 class="font-bold text-2xl">Verificar correo electrónico</h1>
  <p class="mt-2">
    Ingresa el código de verificación que recibiste en tu correo electrónico.
  </p>
</div>

{#if form?.error}
  <div class="mb-4 alert alert-error">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="stroke-current w-6 h-6 shrink-0"
      fill="none"
      viewBox="0 0 24 24"
      ><path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
      /></svg
    >
    <span>{form.error}</span>
  </div>
{/if}

{#if form?.success}
  <div class="mb-4 alert alert-success">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="stroke-current w-6 h-6 shrink-0"
      fill="none"
      viewBox="0 0 24 24"
      ><path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      /></svg
    >
    <span>{form.message}</span>
  </div>

  <div class="mt-4 text-center">
    <a href="/auth" class="btn btn-primary">Ir a iniciar sesión</a>
  </div>
{:else}
  <form method="POST" action="?/verify" use:enhance class="space-y-4">
    <div class="form-control">
      <label class="label" for="email">
        <span class="label-text">Correo electrónico</span>
      </label>
      <input
        type="email"
        id="email"
        name="email"
        required
        bind:value={email}
        class="input-bordered w-full input"
      />
    </div>

    <div class="form-control">
      <label class="label" for="code">
        <span class="label-text">Código de verificación</span>
      </label>
      <input
        type="text"
        id="code"
        name="code"
        required
        bind:value={code}
        class="input-bordered w-full text-xl text-center tracking-wider input"
        maxlength="6"
        placeholder="000000"
      />
    </div>

    <button type="submit" class="w-full btn btn-primary">Verificar</button>
  </form>

  <div class="divider">O</div>

  <div class="text-center">
    <a href="/auth" class="btn-outline btn btn-sm"
      >Volver a la página de autenticación</a
    >
  </div>
{/if}
