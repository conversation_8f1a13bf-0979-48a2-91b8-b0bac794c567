import { redirect } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import type { EmailOtpType } from '@supabase/supabase-js';

export const GET: RequestHandler = async ({ url, locals }) => {
  const token_hash = url.searchParams.get('token_hash');
  const type = url.searchParams.get('type') as EmailOtpType | null;
  const next = url.searchParams.get('next') ?? '/';

  // Si no hay token_hash o type, redirigir a la página de error
  if (!token_hash || !type) {
    throw redirect(303, '/auth/error');
  }

  // Verificar el token con Supabase
  const { error } = await locals.supabase.auth.verifyOtp({
    token_hash,
    type
  });

  // Si hay un error, redirigir a la página de error
  if (error) {
    console.error('Error al verificar OTP:', error);
    throw redirect(303, '/auth/error');
  }

  // Si todo está bien, redirigir a la página especificada en next o a la página principal
  throw redirect(303, next);
}; 