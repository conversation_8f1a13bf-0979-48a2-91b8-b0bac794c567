<script lang="ts">
  import { enhance } from "$app/forms";
  import { goto } from "$app/navigation";
  import { onMount } from "svelte";
  import { authStore, user, getAuthUser } from "$lib/auth";

  let { data, form } = $props();

  // Función de depuración
  function logDebug(message: string, data?: any) {
    console.log(`[AUTH CLIENT] ${message}`, data || "");
  }

  // Estado para alternar entre inicio de sesión y registro
  let isLogin = $state(true);

  // Estados para validación en tiempo real
  let email = $state("");
  let password = $state("");
  let confirmPassword = $state("");
  let emailError = $state("");
  let passwordError = $state("");
  let confirmPasswordError = $state("");
  let formSubmitted = $state(false);

  // Estados para mostrar/ocultar contraseña
  let showPassword = $state(false);
  let showConfirmPassword = $state(false);

  // Estado para la fortaleza de la contraseña
  let passwordStrength = $state(0); // 0-100

  // Función para validar el correo electrónico
  function validateEmail() {
    if (!email) {
      emailError = "El correo electrónico es obligatorio";
      return false;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      emailError = "El correo electrónico no es válido";
      return false;
    }
    emailError = "";
    return true;
  }

  // Función para validar la contraseña
  function validatePassword() {
    if (!password) {
      passwordError = "La contraseña es obligatoria";
      return false;
    }
    if (password.length < 6) {
      passwordError = "La contraseña debe tener al menos 6 caracteres";
      return false;
    }
    passwordError = "";
    return true;
  }

  // Función para validar la confirmación de contraseña
  function validateConfirmPassword() {
    if (!isLogin) {
      if (!confirmPassword) {
        confirmPasswordError = "La confirmación de contraseña es obligatoria";
        return false;
      }
      if (confirmPassword !== password) {
        confirmPasswordError = "Las contraseñas no coinciden";
        return false;
      }
      confirmPasswordError = "";
    }
    return true;
  }

  // Función para calcular la fortaleza de la contraseña
  function calculatePasswordStrength(pass: string): number {
    if (!pass) return 0;

    let score = 0;

    // Longitud básica
    score += Math.min(pass.length * 4, 25);

    // Complejidad
    const hasLowercase = /[a-z]/.test(pass);
    const hasUppercase = /[A-Z]/.test(pass);
    const hasNumbers = /[0-9]/.test(pass);
    const hasSpecialChars = /[^a-zA-Z0-9]/.test(pass);

    if (hasLowercase) score += 10;
    if (hasUppercase) score += 15;
    if (hasNumbers) score += 10;
    if (hasSpecialChars) score += 15;

    // Variedad de caracteres
    const uniqueChars = new Set(pass.split("")).size;
    score += uniqueChars * 2;

    // Limitar a 100
    return Math.min(score, 100);
  }

  // Función para obtener el color de la barra de fortaleza
  function getStrengthColor(strength: number): string {
    if (strength < 30) return "bg-error";
    if (strength < 60) return "bg-warning";
    return "bg-success";
  }

  // Función para obtener el texto de fortaleza
  function getStrengthText(strength: number): string {
    if (strength < 30) return "Débil";
    if (strength < 60) return "Media";
    return "Fuerte";
  }

  // Actualizar la fortaleza de la contraseña cuando cambia
  $effect(() => {
    passwordStrength = calculatePasswordStrength(password);
  });

  // Función para validar el formulario completo
  function validateForm() {
    const isEmailValid = validateEmail();
    const isPasswordValid = validatePassword();
    const isConfirmPasswordValid = validateConfirmPassword();
    return isEmailValid && isPasswordValid && isConfirmPasswordValid;
  }

  // Función para cambiar entre inicio de sesión y registro
  function toggleAuthMode() {
    isLogin = !isLogin;
    // Limpiar errores al cambiar de modo
    emailError = "";
    passwordError = "";
    confirmPasswordError = "";
    formSubmitted = false;
    // Resetear estados
    showPassword = false;
    showConfirmPassword = false;
  }

  // Función para manejar el envío del formulario
  function handleSubmit(e: Event) {
    formSubmitted = true;
    if (!validateForm()) {
      e.preventDefault();
    }
  }

  // Definir el tipo para el formulario
  type FormData = {
    email?: string;
    error?: string;
    success?: boolean;
    message?: string;
    fieldErrors?: {
      email?: string[];
      password?: string[];
      confirmPassword?: string[];
    };
  };

  // Actualizar valores de los campos cuando cambia el formulario
  $effect(() => {
    if (form) {
      const formData = form as FormData;
      email = formData.email || "";
      // Asignar errores de campo si existen
      if (formData.fieldErrors) {
        emailError = formData.fieldErrors.email?.[0] || "";
        passwordError = formData.fieldErrors.password?.[0] || "";
        confirmPasswordError = formData.fieldErrors.confirmPassword?.[0] || "";
      }
    }
  });

  // Esta función ya está definida arriba

  onMount(() => {
    logDebug("Componente de autenticación montado");
  });
</script>

<div class="mb-4 text-center">
  <h1 class="font-bold text-2xl">
    {isLogin ? "Iniciar sesión" : "Registrarse"}
  </h1>
</div>

{#if form?.error}
  <div class="mb-4 alert alert-error">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="stroke-current w-6 h-6 shrink-0"
      fill="none"
      viewBox="0 0 24 24"
      ><path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
      /></svg
    >
    <span>{form.error}</span>
  </div>
{/if}

{#if form?.success}
  <div class="mb-4 alert alert-success">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="stroke-current w-6 h-6 shrink-0"
      fill="none"
      viewBox="0 0 24 24"
      ><path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      /></svg
    >
    <span>{form.message}</span>
  </div>
{/if}

{#if isLogin}
  <!-- Formulario de inicio de sesión -->
  <form
    method="POST"
    action="?/login"
    use:enhance={({ formData, cancel }) => {
      // Antes de enviar el formulario
      logDebug("Iniciando envío del formulario de login");
      formSubmitted = true;
      if (!validateForm()) {
        logDebug("Validación del formulario fallida");
        cancel();
        return;
      }

      logDebug("Formulario validado, enviando...");

      // Enviar el formulario y manejar la respuesta
      return async ({ result, update }) => {
        logDebug("Respuesta recibida del servidor:", result);

        if (result.type === "redirect") {
          logDebug("Redirección detectada a:", result.location);
          // Si es una redirección, navegar a la URL especificada
          goto(result.location);
        } else if (result.type === "failure") {
          logDebug("Fallo en la autenticación:", result);
          // Si hay errores, actualizar el formulario
          await update();
        } else if (result.type === "success") {
          logDebug("Inicio de sesión exitoso:", result);
          await update();

          // Actualizar el store de autenticación y esperar a que se complete
          logDebug("Actualizando el store de autenticación usando forceSync");

          try {
            // Usar la nueva función forceSync para forzar la sincronización de la sesión
            await authStore.forceSync();
            logDebug("Store actualizado correctamente con forceSync");
          } catch (error) {
            logDebug("Error al sincronizar el store:", error);
          }

          // Verificar si hay una URL de redirección en la respuesta
          if (result.data?.redirectTo) {
            logDebug(
              "Navegando a la URL especificada:",
              result.data.redirectTo
            );
            goto(result.data.redirectTo as string);
          } else {
            logDebug("Navegando a la página privada por defecto");
            goto("/private");
          }
        } else {
          logDebug("Respuesta no reconocida:", result);
          // Para cualquier otro resultado, actualizar y navegar a la página privada
          await update();
          logDebug("Navegando manualmente a /private");
          goto("/private");
        }
      };
    }}
    class="space-y-4"
  >
    <div class="form-control">
      <label class="label" for="email">
        <span class="label-text">Correo electrónico</span>
      </label>
      <input
        type="email"
        id="email"
        name="email"
        required
        bind:value={email}
        onblur={validateEmail}
        class="input-bordered w-full input {emailError && formSubmitted
          ? 'input-error'
          : ''}"
      />
      {#if emailError && formSubmitted}
        <label class="label">
          <span class="label-text-alt text-error">{emailError}</span>
        </label>
      {/if}
    </div>

    <div class="form-control">
      <label class="label" for="password">
        <span class="label-text">Contraseña</span>
      </label>
      <div class="relative">
        <input
          type={showPassword ? "text" : "password"}
          id="password"
          name="password"
          required
          bind:value={password}
          onblur={validatePassword}
          class="input-bordered w-full input pr-10 {passwordError &&
          formSubmitted
            ? 'input-error'
            : ''}"
        />
        <button
          type="button"
          class="top-1/2 right-2 absolute text-gray-500 hover:text-gray-700 -translate-y-1/2 transform"
          onclick={() => (showPassword = !showPassword)}
        >
          {#if showPassword}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-5 h-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
              />
            </svg>
          {:else}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-5 h-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              />
            </svg>
          {/if}
        </button>
      </div>
      {#if passwordError && formSubmitted}
        <label class="label">
          <span class="label-text-alt text-error">{passwordError}</span>
        </label>
      {/if}
    </div>

    <button type="submit" class="w-full btn btn-primary" onclick={handleSubmit}
      >Iniciar sesión</button
    >
  </form>

  <div class="divider">O</div>

  <div class="text-center">
    <p class="mb-2">¿No tienes una cuenta?</p>
    <button
      type="button"
      class="btn-outline btn btn-sm"
      onclick={toggleAuthMode}
    >
      Regístrate
    </button>
    <p class="mt-4 text-sm">
      ¿Necesitas verificar tu correo? <a
        href="/auth/verify"
        class="link link-primary">Verificar con código</a
      >
    </p>
  </div>
{:else}
  <!-- Formulario de registro -->
  <form
    method="POST"
    action="?/register"
    use:enhance={({ formData, cancel }) => {
      // Antes de enviar el formulario
      logDebug("Iniciando envío del formulario de registro");
      formSubmitted = true;
      if (!validateForm()) {
        logDebug("Validación del formulario de registro fallida");
        cancel();
        return;
      }

      logDebug("Formulario de registro validado, enviando...");

      // Enviar el formulario y manejar la respuesta
      return async ({ result, update }) => {
        logDebug("Respuesta recibida del servidor para registro:", result);

        if (result.type === "redirect") {
          logDebug("Redirección detectada a:", result.location);
          // Si es una redirección, navegar a la URL especificada
          goto(result.location);
        } else if (result.type === "failure") {
          logDebug("Fallo en el registro:", result);
          // Si hay errores, actualizar el formulario
          await update();
        } else if (result.type === "success") {
          logDebug("Registro exitoso:", result);
          await update();

          // Actualizar el store de autenticación y esperar a que se complete
          logDebug("Actualizando el store de autenticación usando forceSync");

          try {
            // Usar la nueva función forceSync para forzar la sincronización de la sesión
            await authStore.forceSync();
            logDebug("Store actualizado correctamente con forceSync");
          } catch (error) {
            logDebug("Error al sincronizar el store:", error);
          }

          // Verificar si hay una URL de redirección en la respuesta
          if (result.data?.redirectTo) {
            logDebug(
              "Navegando a la URL especificada:",
              result.data.redirectTo
            );
            goto(result.data.redirectTo as string);
          } else {
            logDebug("Navegando a la página de autenticación por defecto");
            goto("/auth");
          }
        } else {
          logDebug("Respuesta no reconocida para registro:", result);
          // Para cualquier otro resultado, actualizar y navegar a la página de autenticación
          await update();
          logDebug("Navegando manualmente a /auth");
          goto("/auth");
        }
      };
    }}
    class="space-y-4"
  >
    <div class="form-control">
      <label class="label" for="email">
        <span class="label-text">Correo electrónico</span>
      </label>
      <input
        type="email"
        id="email"
        name="email"
        required
        bind:value={email}
        onblur={validateEmail}
        class="input-bordered w-full input {emailError && formSubmitted
          ? 'input-error'
          : ''}"
      />
      {#if emailError && formSubmitted}
        <label class="label">
          <span class="label-text-alt text-error">{emailError}</span>
        </label>
      {/if}
    </div>

    <div class="form-control">
      <label class="label" for="password">
        <span class="label-text">Contraseña</span>
      </label>
      <div class="relative">
        <input
          type={showPassword ? "text" : "password"}
          id="password"
          name="password"
          required
          bind:value={password}
          onblur={validatePassword}
          class="input-bordered w-full input pr-10 {passwordError &&
          formSubmitted
            ? 'input-error'
            : ''}"
        />
        <button
          type="button"
          class="top-1/2 right-2 absolute text-gray-500 hover:text-gray-700 -translate-y-1/2 transform"
          onclick={() => (showPassword = !showPassword)}
        >
          {#if showPassword}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-5 h-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
              />
            </svg>
          {:else}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-5 h-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              />
            </svg>
          {/if}
        </button>
      </div>
      {#if passwordError && formSubmitted}
        <label class="label">
          <span class="label-text-alt text-error">{passwordError}</span>
        </label>
      {/if}

      <!-- Indicador de fortaleza de contraseña -->
      {#if password && !isLogin}
        <div class="mt-2">
          <div class="flex justify-between items-center mb-1">
            <span class="text-xs">Fortaleza de contraseña:</span>
            <span
              class="text-xs {getStrengthColor(passwordStrength).replace(
                'bg-',
                'text-'
              )}"
            >
              {getStrengthText(passwordStrength)}
            </span>
          </div>
          <div class="bg-gray-200 rounded-full w-full h-2">
            <div
              class="{getStrengthColor(passwordStrength)} h-2 rounded-full"
              style="width: {passwordStrength}%"
            ></div>
          </div>
        </div>
      {/if}
    </div>

    <div class="form-control">
      <label class="label" for="confirmPassword">
        <span class="label-text">Confirmar contraseña</span>
      </label>
      <div class="relative">
        <input
          type={showConfirmPassword ? "text" : "password"}
          id="confirmPassword"
          name="confirmPassword"
          required
          bind:value={confirmPassword}
          onblur={validateConfirmPassword}
          class="input-bordered w-full input pr-10 {confirmPasswordError &&
          formSubmitted
            ? 'input-error'
            : ''}"
        />
        <button
          type="button"
          class="top-1/2 right-2 absolute text-gray-500 hover:text-gray-700 -translate-y-1/2 transform"
          onclick={() => (showConfirmPassword = !showConfirmPassword)}
        >
          {#if showConfirmPassword}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-5 h-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
              />
            </svg>
          {:else}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-5 h-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              />
            </svg>
          {/if}
        </button>
      </div>
      {#if confirmPasswordError && formSubmitted}
        <label class="label">
          <span class="label-text-alt text-error">{confirmPasswordError}</span>
        </label>
      {/if}
    </div>

    <button type="submit" class="w-full btn btn-primary" onclick={handleSubmit}
      >Registrarse</button
    >

    <div class="mt-2 text-sm text-center">
      Después de registrarte, recibirás un código de verificación en tu correo
      electrónico.
    </div>
  </form>

  <div class="divider">O</div>

  <div class="text-center">
    <p class="mb-2">¿Ya tienes una cuenta?</p>
    <button
      type="button"
      class="btn-outline btn btn-sm"
      onclick={toggleAuthMode}
    >
      Inicia sesión
    </button>
    <p class="mt-4 text-sm">
      ¿Necesitas verificar tu correo? <a
        href="/auth/verify"
        class="link link-primary">Verificar con código</a
      >
    </p>
  </div>
{/if}
