import { redirect, fail } from '@sveltejs/kit';
import { z } from 'zod';
import type { Actions } from './$types';
import { clearSupabaseAuthCookies } from '$lib/auth/cookie-utils';

// Función de depuración
function logDebug(message: string, data?: any) {
  console.log(`[SERVER DEBUG] ${message}`, data || '');
}

// Esquema de validación para el formulario de inicio de sesión
const loginSchema = z.object({
  email: z.string().email('El correo electrónico no es válido'),
  password: z.string().min(6, 'La contraseña debe tener al menos 6 caracteres')
});

// Esquema de validación para el formulario de registro
const registerSchema = z.object({
  email: z.string().email('El correo electrónico no es válido'),
  password: z.string().min(6, 'La contraseña debe tener al menos 6 caracteres'),
  confirmPassword: z.string().min(6, 'La contraseña debe tener al menos 6 caracteres')
}).refine(data => data.password === data.confirmPassword, {
  message: 'Las contraseñas no coinciden',
  path: ['confirmPassword']
});

// Mapa de mensajes de error de Supabase a mensajes amigables para el usuario
const errorMessages: Record<string, string> = {
  'Invalid login credentials': 'Correo electrónico o contraseña incorrectos',
  'Email not confirmed': 'Por favor, verifica tu correo electrónico antes de iniciar sesión',
  'Email already in use': 'Este correo electrónico ya está registrado',
  'Password should be at least 6 characters': 'La contraseña debe tener al menos 6 caracteres',
  'User not found': 'No existe una cuenta con este correo electrónico',
  'Too many requests': 'Demasiados intentos. Por favor, intenta de nuevo más tarde'
};

// Función para traducir mensajes de error de Supabase
function translateErrorMessage(message: string): string {
  return errorMessages[message] || 'Ha ocurrido un error. Por favor, intenta de nuevo';
}

export const actions: Actions = {
  // Acción para iniciar sesión
  login: async ({ request, locals }) => {
    logDebug("Procesando solicitud de inicio de sesión");
    const formData = await request.formData();
    const email = formData.get('email')?.toString() || '';
    const password = formData.get('password')?.toString() || '';

    try {
      // Validar los datos del formulario
      logDebug("Validando datos del formulario");
      const result = loginSchema.parse({ email, password });

      // Iniciar sesión con Supabase
      logDebug("Intentando iniciar sesión con Supabase");
      const { data, error } = await locals.supabase.auth.signInWithPassword({
        email: result.email,
        password: result.password
      });

      logDebug("Respuesta de Supabase:", { data, error });

      // Si hay un error, devolver un mensaje de error traducido
      if (error) {
        logDebug("Error de autenticación:", error);
        return fail(400, {
          error: translateErrorMessage(error.message),
          email,
          fieldErrors: {
            // Si el error es de credenciales inválidas, marcamos ambos campos como erróneos
            ...(error.message === 'Invalid login credentials' && {
              email: ['Verifica tus credenciales'],
              password: ['Verifica tus credenciales']
            })
          }
        });
      }

      // En lugar de redirigir, devolver un resultado exitoso
      logDebug("Inicio de sesión exitoso, devolviendo resultado exitoso");
      return {
        success: true,
        message: 'Inicio de sesión exitoso',
        redirectTo: '/private'
      };
    } catch (error) {
      // Si hay un error de validación, devolver un mensaje de error
      if (error instanceof z.ZodError) {
        logDebug("Error de validación:", error);
        const fieldErrors = error.flatten().fieldErrors;
        return fail(400, {
          error: 'Por favor, corrige los errores en el formulario',
          fieldErrors,
          email
        });
      }

      // Si es un error de redirección, propagarlo
      if (error instanceof Response) {
        logDebug("Propagando redirección:", error);
        throw error;
      }

      // Para cualquier otro error, devolver un mensaje genérico
      logDebug("Error inesperado:", error);
      console.error('Error de inicio de sesión:', error);
      return fail(500, {
        error: 'Ha ocurrido un error al procesar tu solicitud. Por favor, intenta de nuevo más tarde',
        email
      });
    }
  },

  // Acción para registrarse
  register: async ({ request, locals }) => {
    logDebug("Procesando solicitud de registro");
    const formData = await request.formData();
    const email = formData.get('email')?.toString() || '';
    const password = formData.get('password')?.toString() || '';
    const confirmPassword = formData.get('confirmPassword')?.toString() || '';

    try {
      // Validar los datos del formulario
      logDebug("Validando datos del formulario de registro");
      const result = registerSchema.parse({ email, password, confirmPassword });

      // Registrar al usuario con Supabase
      logDebug("Intentando registrar usuario con Supabase");
      const { data, error } = await locals.supabase.auth.signUp({
        email: result.email,
        password: result.password,
        options: {
          emailRedirectTo: `${new URL(request.url).origin}/auth/confirm`
        }
      });

      logDebug("Respuesta de registro de Supabase:", { data, error });

      // Si hay un error, devolver un mensaje de error traducido
      if (error) {
        logDebug("Error de registro:", error);
        return fail(400, {
          error: translateErrorMessage(error.message),
          email,
          fieldErrors: {
            ...(error.message === 'Email already in use' && {
              email: ['Este correo electrónico ya está registrado']
            })
          }
        });
      }

      // En lugar de redirigir, devolver un resultado exitoso
      logDebug("Registro exitoso, devolviendo resultado exitoso");
      return {
        success: true,
        message: 'Te has registrado correctamente. Por favor, verifica tu correo electrónico para confirmar tu cuenta.',
        redirectTo: '/auth'
      };
    } catch (error) {
      // Si hay un error de validación, devolver un mensaje de error
      if (error instanceof z.ZodError) {
        logDebug("Error de validación en registro:", error);
        const fieldErrors = error.flatten().fieldErrors;
        return fail(400, {
          error: 'Por favor, corrige los errores en el formulario',
          fieldErrors,
          email
        });
      }

      // Si es un error de redirección, propagarlo
      if (error instanceof Response) {
        logDebug("Propagando redirección en registro:", error);
        throw error;
      }

      // Para cualquier otro error, devolver un mensaje genérico
      logDebug("Error inesperado en registro:", error);
      console.error('Error de registro:', error);
      return fail(500, {
        error: 'Ha ocurrido un error al procesar tu solicitud. Por favor, intenta de nuevo más tarde',
        email
      });
    }
  },

  // Acción para cerrar sesión
  logout: async ({ locals, cookies }) => {
    logDebug("Iniciando proceso de logout para usuario normal");

    // Registrar el ID del usuario que está cerrando sesión
    const userId = locals.user?.id;
    logDebug("Usuario que cierra sesión:", userId || "No hay usuario autenticado");

    const allCookiesBefore = cookies.getAll();
    logDebug("Cookies ANTES de locals.supabase.auth.signOut():", allCookiesBefore.map(c => ({ name: c.name, value: c.value.substring(0, 30) + "..." })));

    // Cerrar sesión en Supabase. El método signOut() de @supabase/ssr debería manejar la eliminación de cookies.
    logDebug("Intentando cerrar sesión en Supabase (servidor) usando locals.supabase.auth.signOut()");
    const { error } = await locals.supabase.auth.signOut({ scope: 'global' });

    if (error) {
      logDebug("Error de Supabase al cerrar sesión (servidor):", error);
      console.error('Error al cerrar sesión en Supabase:', error);
      // Incluso con error, intentamos limpiar las cookies que podamos y redirigir.
      // Opcionalmente, podríamos devolver fail() aquí si el error es crítico.
      // return fail(500, {
      //   error: 'Error al cerrar sesión con Supabase'
      // });
    } else {
      logDebug("Cierre de sesión en Supabase (servidor) aparentemente exitoso (sin error devuelto por signOut)");
    }

    // Eliminar manualmente todas las cookies relacionadas con Supabase
    logDebug("Eliminando manualmente todas las cookies relacionadas con Supabase");
    clearSupabaseAuthCookies(cookies);

    // Verificar cookies DESPUÉS de la eliminación manual
    const allCookiesAfter = cookies.getAll();
    logDebug("Cookies DESPUÉS de la eliminación manual:", allCookiesAfter.map(c => ({ name: c.name, value: c.value.substring(0, 30) + "..." })));

    // Limpiar locals para asegurar que no quede información de sesión
    locals.session = null;
    locals.user = null;
    logDebug("Locals limpiados: session y user establecidos a null");

    // Redirigir al usuario a la página de inicio
    logDebug("Redirigiendo a /");
    throw redirect(303, '/');
  }
};