import type { PageServerLoad } from './$types';
import { getUserReservations } from '$lib/services/reservations';

export const load = (async ({ locals }) => {
  // Verificar si el usuario está autenticado
  if (locals.session && locals.user) {
    const userId = locals.user.id;
    
    // Obtener las reservas del usuario
    const { data: userReservations, error } = await getUserReservations(locals.supabase, userId);
    
    // Devolver los datos del usuario y sus reservas
    return {
      user: locals.user,
      userReservations,
      error
    };
  }
  
  // Si el usuario no está autenticado, devolver datos vacíos
  return {
    user: null,
    userReservations: null,
    error: null
  };
}) satisfies PageServerLoad;
