<script lang="ts">
  import { T } from "$lib/i18n";
  import ReservationSearch from "$lib/components/reservations/ReservationSearch.svelte";
  import ReservationList from "$lib/components/reservations/ReservationList.svelte";
  import { supabase } from "$lib";
  import { findReservationByEmailAndCode } from "$lib/services/reservations";
  import type { Reservation } from "$lib/services/reservations";
  import { onMount } from "svelte";

  // Obtener datos del servidor
  let { data } = $props();
  let { user, userReservations } = $derived(data);

  // Estado
  let reservations = $state<Reservation[]>([]);
  let error = $state<string | null>(null);
  let loading = $state(false);
  let searchPerformed = $state(false);
  let isAuthenticated = $state(false);

  // Verificar si el usuario está autenticado y tiene reservas
  onMount(() => {
    isAuthenticated = !!user;

    // Si el usuario está autenticado y tiene reservas, mostrarlas automáticamente
    if (isAuthenticated && userReservations && userReservations.length > 0) {
      reservations = userReservations;
      searchPerformed = true;
    }
  });

  // Función para buscar reservas
  async function handleSearch(
    email: string,
    confirmationCode: string
  ): Promise<void> {
    loading = true;
    error = null;
    reservations = [];

    try {
      const result = await findReservationByEmailAndCode(
        supabase,
        email,
        confirmationCode
      );

      if (result.error) {
        error = result.error;
      } else if (result.data) {
        reservations = result.data;
      }

      searchPerformed = true;
    } catch (err) {
      console.error("Error al buscar reservas:", err);
      error =
        "Ha ocurrido un error al buscar las reservas. Por favor, inténtalo de nuevo.";
    } finally {
      loading = false;
    }
  }
</script>

<svelte:head>
  <title>Buscar Reserva - Baberrih</title>
</svelte:head>

<div class="mx-auto max-w-4xl">
  <div class="mb-8">
    <h1 class="mb-2 font-montserrat font-medium text-primary-900 text-2xl">
      <T key="reservations.findYourReservation" />
    </h1>
    <p class="text-primary-600">
      <T key="reservations.findYourReservationPageDescription" />
    </p>
  </div>

  <div class="gap-8 grid grid-cols-1 md:grid-cols-3">
    <div class="md:col-span-1">
      {#if isAuthenticated}
        <div
          class="bg-primary-100 mb-6 p-4 border border-primary-200 rounded-sm"
        >
          <h3 class="mb-2 font-montserrat font-medium text-primary-900">
            Bienvenido de nuevo
          </h3>
          <p class="mb-4 text-primary-600 text-sm">
            {#if userReservations && userReservations.length > 0}
              Tus reservas se han cargado automáticamente.
            {:else}
              No tienes reservas activas en este momento.
            {/if}
          </p>
          <div class="flex flex-wrap gap-2">
            <a
              href="/private/reservations"
              class="inline-block bg-primary-700 hover:bg-primary-800 px-4 py-2 rounded-sm font-montserrat text-white text-sm transition-colors"
            >
              Mi cuenta
            </a>
            <a
              href="/accommodation/reservations"
              class="inline-block bg-white hover:bg-primary-50 px-4 py-2 border border-primary-200 rounded-sm font-montserrat text-primary-700 text-sm transition-colors"
            >
              Nueva reserva
            </a>
          </div>
        </div>
      {/if}

      <ReservationSearch onSearch={handleSearch} />

      <div class="bg-primary-50 mt-6 p-4 border border-primary-200 rounded-sm">
        <h3 class="mb-2 font-montserrat font-medium text-primary-900">
          <T key="reservations.needHelp" />
        </h3>
        <p class="mb-4 text-primary-600 text-sm">
          <T key="reservations.needHelpDescription" />
        </p>
        <a
          href="/contact"
          class="inline-block bg-primary-700 hover:bg-primary-800 px-4 py-2 rounded-sm font-montserrat text-white text-sm transition-colors"
        >
          <T key="common.contactUs" />
        </a>
      </div>
    </div>

    <div class="md:col-span-2">
      {#if searchPerformed}
        {#if loading}
          <div class="flex justify-center items-center py-12">
            <div
              class="border-primary-700 border-b-2 rounded-full w-12 h-12 animate-spin"
            ></div>
          </div>
        {:else if error}
          <div
            class="bg-red-50 p-6 border border-red-200 rounded-sm text-red-700"
          >
            <h3 class="mb-2 font-montserrat font-medium text-lg">
              <T key="reservations.searchError" />
            </h3>
            <p>{error}</p>
            <p class="mt-4">
              <T key="reservations.searchErrorHelp" />
            </p>
          </div>
        {:else if reservations.length === 0}
          <div
            class="bg-primary-50 p-6 border border-primary-200 rounded-sm text-primary-700"
          >
            <h3 class="mb-2 font-montserrat font-medium text-lg">
              <T key="reservations.noReservationsFound" />
            </h3>
            <p>
              <T key="reservations.noReservationsFoundDescription" />
            </p>
          </div>
        {:else}
          <div
            class="bg-white shadow-sm p-6 border border-primary-200 rounded-sm"
          >
            <h2
              class="mb-4 font-montserrat font-medium text-primary-900 text-xl"
            >
              <T
                key="reservations.foundReservations"
                params={{ count: reservations.length }}
              />
            </h2>

            <ReservationList {reservations} error={null} loading={false} />

            {#if !isAuthenticated}
              <div class="mt-6 pt-6 border-primary-100 border-t">
                <p class="text-primary-600 text-sm">
                  <T key="reservations.createAccountPrompt" />
                </p>
                <div class="mt-4">
                  <a
                    href="/auth"
                    class="inline-block bg-primary-700 hover:bg-primary-800 mr-2 px-4 py-2 rounded-sm font-montserrat text-white text-sm transition-colors"
                  >
                    <T key="common.login" />
                  </a>
                  <a
                    href="/auth?register=true"
                    class="inline-block bg-white hover:bg-primary-50 px-4 py-2 border border-primary-200 rounded-sm font-montserrat text-primary-700 text-sm transition-colors"
                  >
                    <T key="common.register" />
                  </a>
                </div>
              </div>
            {/if}
          </div>
        {/if}
      {:else}
        <div
          class="bg-primary-50 p-6 border border-primary-200 rounded-sm text-primary-700"
        >
          <h3 class="mb-2 font-montserrat font-medium text-lg">
            <T key="reservations.searchInstructions" />
          </h3>
          <p>
            <T key="reservations.searchInstructionsDescription" />
          </p>
          <ul class="space-y-2 mt-4 list-disc list-inside">
            <li>
              <T key="reservations.searchInstructionsEmail" />
            </li>
            <li>
              <T key="reservations.searchInstructionsCode" />
            </li>
          </ul>
        </div>
      {/if}
    </div>
  </div>
</div>
