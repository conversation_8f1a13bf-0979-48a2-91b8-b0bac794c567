<script lang="ts">
  import { onMount } from "svelte";
  import { page } from "$app/stores";
  import { CloudbedsApiClient } from "$lib/cloudbeds/api";
  import AvailabilityCalendar from "$lib/components/cloudbeds/availability-calendar.svelte";
  import type { CloudbedsRoomType } from "$lib/cloudbeds/types";

  // Variables para controlar el estado de la página
  let propertyId = ""; // ID de la propiedad
  let startDate = new Date(); // Fecha de inicio por defecto (hoy)
  let endDate = new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000); // Fecha de fin por defecto (30 días después)
  let roomTypes: { id: string; name: string }[] = [];
  let selectedRoomTypeId = "";
  let isLoading = true;
  let error = "";
  let noRoomTypesAvailable = false;

  // Datos completos de las habitaciones
  let roomTypesData: CloudbedsRoomType[] = [];
  let selectedRoomDetails: CloudbedsRoomType | null = null;

  // Cliente de Cloudbeds para hacer peticiones a la API
  const cloudbedsClient = new CloudbedsApiClient();

  // Formatear fechas para la API
  $: formattedStartDate =
    startDate instanceof Date
      ? startDate.toISOString().split("T")[0]
      : startDate;

  $: formattedEndDate =
    endDate instanceof Date ? endDate.toISOString().split("T")[0] : endDate;

  // Actualizar los detalles de la habitación seleccionada cuando cambie el ID
  $: {
    if (selectedRoomTypeId && roomTypesData.length > 0) {
      selectedRoomDetails =
        roomTypesData.find(
          (room) => (room.roomTypeID || room.id) === selectedRoomTypeId
        ) || null;

      console.log("Detalles de habitación seleccionada:", selectedRoomDetails);
    }
  }

  // Cargar los tipos de habitaciones al montar el componente
  onMount(async () => {
    try {
      isLoading = true;
      noRoomTypesAvailable = false;

      // Obtener roomTypeId de la URL si existe
      const urlRoomTypeId = $page.url.searchParams.get("roomTypeId");
      console.log("RoomTypeId desde URL:", urlRoomTypeId);

      console.log("Iniciando solicitud para obtener tipos de habitaciones...");
      let response;

      try {
        response = await cloudbedsClient.getRoomTypes();
        console.log("Respuesta completa de getRoomTypes:", response);
      } catch (apiError) {
        console.error("Error en la llamada a la API:", apiError);
        // Creamos una respuesta simulada para poder continuar
        response = {
          success: false,
          error: {
            message:
              apiError instanceof Error
                ? apiError.message
                : "Error desconocido",
          },
        };
      }

      if (response.success && response.data && response.data.length > 0) {
        console.log("Datos originales de tipos de habitación:", response.data);

        // Guardar los datos completos de las habitaciones
        roomTypesData = response.data;

        // Procesar los datos mapeando correctamente los nombres de los campos
        roomTypes = response.data
          .map((room) => {
            console.log("Procesando habitación:", room);
            return {
              id: room.roomTypeID || room.id || "",
              name: room.roomTypeName || room.name || "Habitación sin nombre",
            };
          })
          .filter((room) => room.id !== "");

        console.log("Tipos de habitación procesados:", roomTypes);

        if (roomTypes.length > 0) {
          // Si se proporcionó un roomTypeId en la URL y existe entre los tipos de habitación, seleccionarlo
          if (
            urlRoomTypeId &&
            roomTypes.some((room) => room.id === urlRoomTypeId)
          ) {
            selectedRoomTypeId = urlRoomTypeId;
            console.log(
              "Tipo de habitación seleccionado desde URL:",
              selectedRoomTypeId
            );
          } else {
            // Si no, seleccionar el primero por defecto
            selectedRoomTypeId = roomTypes[0].id;
            console.log(
              "Seleccionado primer tipo de habitación:",
              selectedRoomTypeId
            );
          }
        } else {
          console.log(
            "No se encontraron tipos de habitación después del mapeo"
          );
          noRoomTypesAvailable = true;
        }
      } else {
        console.log(
          "No hay datos válidos en la respuesta. Creando tipo de habitación de prueba."
        );

        // No hay tipos de habitación disponibles
        noRoomTypesAvailable = true;
        roomTypesData = [];
        roomTypes = [];
        selectedRoomTypeId = "";
        console.log("No hay tipos de habitación disponibles");

        // Mostrar mensaje de error
        error = "No hay tipos de habitación disponibles. Por favor, contacte al administrador.";

        // Si hay un ID en la URL, mostrarlo en el mensaje de error
        if (urlRoomTypeId) {
          error += ` (ID solicitado: ${urlRoomTypeId})`;
        }

        if (response.error) {
          error =
            response.error.message ||
            "Error al obtener los tipos de habitaciones";
        }
      }

      // Si después de todo el procesamiento no hay tipos de habitación, mostrar mensaje
      if (roomTypes.length === 0) {
        noRoomTypesAvailable = true;
        console.log(
          "No hay tipos de habitación disponibles después de todo el procesamiento"
        );
      }

      isLoading = false;
    } catch (e) {
      console.error("Error al cargar los tipos de habitaciones:", e);
      error =
        "Error al cargar los tipos de habitaciones. Por favor, inténtelo de nuevo más tarde.";
      isLoading = false;
      noRoomTypesAvailable = true;
    }
  });

  // Función para cambiar el tipo de habitación seleccionado
  function handleRoomTypeChange(event: Event) {
    selectedRoomTypeId = (event.target as HTMLSelectElement).value;
  }
</script>

<svelte:head>
  <title>Reservas - Cloudbeds</title>
</svelte:head>

<div class="mx-auto p-4 container">
  <h1 class="mb-6 font-bold text-3xl">Reservas de Habitaciones</h1>

  {#if error}
    <div class="mb-4 alert alert-error">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="stroke-current w-6 h-6 shrink-0"
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
      <span>{error}</span>
    </div>
  {/if}

  {#if isLoading}
    <div class="flex justify-center my-12">
      <span class="text-primary loading loading-spinner loading-lg"></span>
    </div>
  {:else if noRoomTypesAvailable}
    <div class="mb-4 alert alert-warning">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="stroke-current w-6 h-6 shrink-0"
        fill="none"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
        />
      </svg>
      <span
        >No hay tipos de habitación disponibles en este momento. Por favor,
        inténtelo más tarde.</span
      >
    </div>
  {:else}
    <div class="gap-6 grid grid-cols-1 lg:grid-cols-3">
      <div class="col-span-1 lg:col-span-2">
        <div class="bg-base-100 shadow-xl mb-6 card">
          <div class="card-body">
            <h2 class="mb-4 card-title">Buscar disponibilidad</h2>

            <div class="gap-4 grid grid-cols-1 md:grid-cols-3 mb-6">
              <div>
                <label for="roomType" class="label">Tipo de habitación</label>
                <select
                  id="roomType"
                  class="w-full select-bordered select"
                  value={selectedRoomTypeId}
                  on:change={handleRoomTypeChange}
                >
                  {#each roomTypes as roomType}
                    <option value={roomType.id}>{roomType.name}</option>
                  {/each}
                </select>
              </div>

              <div>
                <label for="startDate" class="label">Fecha de llegada</label>
                <input
                  id="startDate"
                  type="date"
                  class="input-bordered w-full input"
                  bind:value={formattedStartDate}
                />
              </div>

              <div>
                <label for="endDate" class="label">Fecha de salida</label>
                <input
                  id="endDate"
                  type="date"
                  class="input-bordered w-full input"
                  bind:value={formattedEndDate}
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Calendario de disponibilidad -->
        <div class="bg-base-100 shadow-xl card">
          <div class="card-body">
            <h3 class="mb-4 font-semibold text-xl">Disponibilidad</h3>

            {#if selectedRoomTypeId}
              <AvailabilityCalendar roomTypeId={selectedRoomTypeId} />
            {:else}
              <div class="alert alert-info">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  class="stroke-current w-6 h-6 shrink-0"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span
                  >Seleccione un tipo de habitación para ver la disponibilidad.</span
                >
              </div>
            {/if}
          </div>
        </div>
      </div>

      <!-- Tarjeta de habitación seleccionada -->
      <div class="col-span-1">
        {#if selectedRoomDetails}
          <div class="top-4 sticky bg-base-100 shadow-xl card">
            <figure class="h-48 overflow-hidden">
              {#if selectedRoomDetails.roomTypePhotos && selectedRoomDetails.roomTypePhotos.length > 0}
                <img
                  src={selectedRoomDetails.roomTypePhotos[0]}
                  alt={selectedRoomDetails.roomTypeName ||
                    selectedRoomDetails.name ||
                    "Habitación"}
                  class="w-full h-full object-cover"
                />
              {:else if selectedRoomDetails.photos && selectedRoomDetails.photos.length > 0}
                <img
                  src={selectedRoomDetails.photos[0]}
                  alt={selectedRoomDetails.roomTypeName ||
                    selectedRoomDetails.name ||
                    "Habitación"}
                  class="w-full h-full object-cover"
                />
              {:else}
                <div
                  class="flex justify-center items-center bg-base-200 w-full h-full"
                >
                  <span class="opacity-50 text-base-content">Sin imagen</span>
                </div>
              {/if}
            </figure>

            <div class="card-body">
              <h3 class="card-title">
                {selectedRoomDetails.roomTypeName ||
                  selectedRoomDetails.name ||
                  "Habitación sin nombre"}
              </h3>

              {#if selectedRoomDetails.roomTypeDescription || selectedRoomDetails.description}
                <p class="mb-3 text-sm">
                  {selectedRoomDetails.roomTypeDescription ||
                    selectedRoomDetails.description}
                </p>
              {/if}

              <div class="flex flex-col gap-2">
                <div class="flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="mr-1 w-5 h-5"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                  <span
                    >Max: {selectedRoomDetails.maxGuests ||
                      selectedRoomDetails.maxOccupancy ||
                      "N/A"} personas</span
                  >
                </div>

                {#if selectedRoomDetails.roomTypeFeatures && Object.keys(selectedRoomDetails.roomTypeFeatures).length > 0}
                  <div class="mt-2">
                    <h4 class="mb-1 font-semibold text-sm">Comodidades:</h4>
                    <div class="flex flex-wrap gap-1">
                      {#each Object.values(selectedRoomDetails.roomTypeFeatures).slice(0, 3) as amenity}
                        <span class="badge-outline badge">{amenity}</span>
                      {/each}
                      {#if Object.keys(selectedRoomDetails.roomTypeFeatures).length > 3}
                        <span class="badge-outline badge">
                          +{Object.keys(selectedRoomDetails.roomTypeFeatures)
                            .length - 3} más
                        </span>
                      {/if}
                    </div>
                  </div>
                {:else if selectedRoomDetails.amenities && selectedRoomDetails.amenities.length > 0}
                  <div class="mt-2">
                    <h4 class="mb-1 font-semibold text-sm">Comodidades:</h4>
                    <div class="flex flex-wrap gap-1">
                      {#each selectedRoomDetails.amenities.slice(0, 3) as amenity}
                        <span class="badge-outline badge">{amenity}</span>
                      {/each}
                      {#if selectedRoomDetails.amenities.length > 3}
                        <span class="badge-outline badge">
                          +{selectedRoomDetails.amenities.length - 3} más
                        </span>
                      {/if}
                    </div>
                  </div>
                {/if}
              </div>

              <div class="divider"></div>

              <button class="w-full btn btn-primary">Reservar ahora</button>
            </div>
          </div>
        {/if}
      </div>
    </div>
  {/if}
</div>
