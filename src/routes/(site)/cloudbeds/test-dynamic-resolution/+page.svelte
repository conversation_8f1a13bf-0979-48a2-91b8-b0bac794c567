<script>
  import { onMount } from 'svelte';
  
  let roomTypes = [];
  let selectedRoomTypeId = '';
  let roomName = '';
  let startDate = '';
  let endDate = '';
  let firstName = '';
  let lastName = '';
  let email = '';
  let phone = '';
  let loading = false;
  let result = null;
  let resolutionResult = null;
  
  // Set default dates (today and tomorrow)
  onMount(() => {
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 1);
    
    startDate = formatDate(today);
    endDate = formatDate(tomorrow);
    
    // Load room types
    loadRoomTypes();
  });
  
  function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
  
  async function loadRoomTypes() {
    try {
      loading = true;
      const response = await fetch('/api/cloudbeds/room-types?includePhotos=0&includeAmenities=0');
      const data = await response.json();
      
      if (data.success && data.data) {
        roomTypes = data.data;
        if (roomTypes.length > 0) {
          selectedRoomTypeId = roomTypes[0].roomTypeID || roomTypes[0].id;
        }
      }
    } catch (error) {
      console.error('Error loading room types:', error);
    } finally {
      loading = false;
    }
  }
  
  async function testResolution() {
    try {
      loading = true;
      const url = `/api/cloudbeds/test-room-type-resolution?roomTypeId=${selectedRoomTypeId}&roomName=${encodeURIComponent(roomName)}`;
      const response = await fetch(url);
      resolutionResult = await response.json();
    } catch (error) {
      console.error('Error testing resolution:', error);
    } finally {
      loading = false;
    }
  }
  
  async function createReservation() {
    try {
      loading = true;
      
      // Create reservation data
      const reservationData = {
        propertyID: '', // Will be filled by the server
        guestData: {
          firstName,
          lastName,
          email,
          phone
        },
        roomsData: [
          {
            roomTypeID: selectedRoomTypeId,
            roomName: roomName,
            startDate,
            endDate,
            adults: 2,
            children: 0
          }
        ],
        status: 'confirmed',
        thirdPartyIdentifier: `test-${Date.now()}`,
        sendEmailConfirmation: false
      };
      
      // Send the request
      const response = await fetch('/api/cloudbeds/reservation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(reservationData)
      });
      
      result = await response.json();
    } catch (error) {
      console.error('Error creating reservation:', error);
      result = {
        success: false,
        message: error.message || 'Unknown error'
      };
    } finally {
      loading = false;
    }
  }
</script>

<svelte:head>
  <title>Test Dynamic Room Type Resolution</title>
</svelte:head>

<div class="container mx-auto p-4">
  <h1 class="text-2xl font-bold mb-4">Test Dynamic Room Type Resolution</h1>
  
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div class="bg-white p-4 rounded shadow">
      <h2 class="text-xl font-semibold mb-2">Test Resolution</h2>
      
      <div class="mb-4">
        <label class="block mb-1">Room Type ID</label>
        <select bind:value={selectedRoomTypeId} class="w-full p-2 border rounded">
          <option value="">-- None --</option>
          {#each roomTypes as roomType}
            <option value={roomType.roomTypeID || roomType.id}>
              {roomType.roomTypeName || roomType.name} ({roomType.roomTypeID || roomType.id})
            </option>
          {/each}
        </select>
      </div>
      
      <div class="mb-4">
        <label class="block mb-1">Room Name</label>
        <input type="text" bind:value={roomName} class="w-full p-2 border rounded" 
               placeholder="e.g., Garden Deluxe" />
      </div>
      
      <button on:click={testResolution} class="bg-blue-500 text-white px-4 py-2 rounded"
              disabled={loading}>
        {loading ? 'Testing...' : 'Test Resolution'}
      </button>
      
      {#if resolutionResult}
        <div class="mt-4 p-3 bg-gray-100 rounded">
          <h3 class="font-semibold">Resolution Result:</h3>
          <pre class="text-sm overflow-auto">{JSON.stringify(resolutionResult, null, 2)}</pre>
        </div>
      {/if}
    </div>
    
    <div class="bg-white p-4 rounded shadow">
      <h2 class="text-xl font-semibold mb-2">Create Reservation</h2>
      
      <div class="mb-4">
        <label class="block mb-1">First Name</label>
        <input type="text" bind:value={firstName} class="w-full p-2 border rounded" 
               placeholder="John" />
      </div>
      
      <div class="mb-4">
        <label class="block mb-1">Last Name</label>
        <input type="text" bind:value={lastName} class="w-full p-2 border rounded" 
               placeholder="Doe" />
      </div>
      
      <div class="mb-4">
        <label class="block mb-1">Email</label>
        <input type="email" bind:value={email} class="w-full p-2 border rounded" 
               placeholder="<EMAIL>" />
      </div>
      
      <div class="mb-4">
        <label class="block mb-1">Phone</label>
        <input type="text" bind:value={phone} class="w-full p-2 border rounded" 
               placeholder="+1234567890" />
      </div>
      
      <div class="mb-4">
        <label class="block mb-1">Start Date</label>
        <input type="date" bind:value={startDate} class="w-full p-2 border rounded" />
      </div>
      
      <div class="mb-4">
        <label class="block mb-1">End Date</label>
        <input type="date" bind:value={endDate} class="w-full p-2 border rounded" />
      </div>
      
      <button on:click={createReservation} class="bg-green-500 text-white px-4 py-2 rounded"
              disabled={loading || !firstName || !lastName || !email}>
        {loading ? 'Creating...' : 'Create Reservation'}
      </button>
      
      {#if result}
        <div class="mt-4 p-3 {result.success ? 'bg-green-100' : 'bg-red-100'} rounded">
          <h3 class="font-semibold">{result.success ? 'Success' : 'Error'}:</h3>
          <pre class="text-sm overflow-auto">{JSON.stringify(result, null, 2)}</pre>
        </div>
      {/if}
    </div>
  </div>
</div>
