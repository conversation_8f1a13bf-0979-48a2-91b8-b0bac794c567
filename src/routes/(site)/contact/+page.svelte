<script lang="ts">
  import { onMount } from "svelte";
  
  // Form state
  let name = $state("");
  let email = $state("");
  let subject = $state("");
  let message = $state("");
  let isSubmitting = $state(false);
  let isSubmitted = $state(false);
  let error = $state("");
  
  // Animation state
  let formVisible = $state(false);
  let infoVisible = $state(false);
  
  // Handle form submission
  function handleSubmit() {
    // Validate form
    if (!name || !email || !message) {
      error = "Please fill in all required fields.";
      return;
    }
    
    // Reset error
    error = "";
    
    // Set submitting state
    isSubmitting = true;
    
    // Simulate form submission (in a real app, this would be an API call)
    setTimeout(() => {
      isSubmitting = false;
      isSubmitted = true;
      
      // Reset form
      name = "";
      email = "";
      subject = "";
      message = "";
    }, 1500);
  }
  
  // Intersection Observer for scroll animations
  function setupIntersectionObserver() {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };
    
    const formObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          formVisible = true;
          formObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    const infoObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          infoVisible = true;
          infoObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    // Observe elements
    const formElement = document.getElementById('contact-form-section');
    if (formElement) formObserver.observe(formElement);
    
    const infoElement = document.getElementById('contact-info-section');
    if (infoElement) infoObserver.observe(infoElement);
  }
  
  onMount(() => {
    setupIntersectionObserver();
  });
</script>

<svelte:head>
  <title>Contact Us - Baberrih Hotel</title>
  <meta name="description" content="Contact Baberrih Hotel in Essaouira, Morocco for inquiries, reservations, or special requests." />
</svelte:head>

<!-- Hero Section -->
<section class="relative">
  <img 
    src="https://baberrih.ma/media/contact/1_f.webp" 
    alt="Baberrih Hotel Contact" 
    class="w-full h-[40vh] md:h-[50vh] object-cover"
  />
  <div class="absolute inset-0 flex items-center justify-center bg-primary-950/50">
    <h1 class="font-montserrat font-light text-primary-50 text-3xl md:text-5xl uppercase">Contact Us</h1>
  </div>
</section>

<div class="container mx-auto px-4 py-12 md:py-16">
  <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
    <!-- Contact Form -->
    <section id="contact-form-section" class="order-2 md:order-1">
      <div class={`scroll-animation slide-up ${formVisible ? 'visible' : ''}`} style="--delay: 300ms;">
        <h2 class="font-montserrat font-light text-primary-900 text-xl uppercase mb-6">Send us a message</h2>
        
        {#if isSubmitted}
          <div class="bg-primary-100 border border-primary-200 p-6 mb-6">
            <h3 class="font-montserrat font-medium text-primary-900 text-lg mb-2">Thank you for your message!</h3>
            <p class="font-montserrat font-light text-primary-800">We have received your inquiry and will get back to you as soon as possible.</p>
          </div>
          <button 
            onclick={() => isSubmitted = false}
            class="button-secondary"
          >
            Send another message
          </button>
        {:else}
          <form class="space-y-6" onsubmit={handleSubmit}>
            {#if error}
              <div class="bg-red-50 border border-red-200 p-4 text-red-700">
                {error}
              </div>
            {/if}
            
            <div>
              <label for="name" class="block font-montserrat font-light text-primary-900 mb-1">Name *</label>
              <input 
                type="text" 
                id="name" 
                bind:value={name}
                class="w-full border border-primary-200 p-2 focus:outline-none focus:ring-1 focus:ring-primary-500"
                required
              />
            </div>
            
            <div>
              <label for="email" class="block font-montserrat font-light text-primary-900 mb-1">Email *</label>
              <input 
                type="email" 
                id="email" 
                bind:value={email}
                class="w-full border border-primary-200 p-2 focus:outline-none focus:ring-1 focus:ring-primary-500"
                required
              />
            </div>
            
            <div>
              <label for="subject" class="block font-montserrat font-light text-primary-900 mb-1">Subject</label>
              <input 
                type="text" 
                id="subject" 
                bind:value={subject}
                class="w-full border border-primary-200 p-2 focus:outline-none focus:ring-1 focus:ring-primary-500"
              />
            </div>
            
            <div>
              <label for="message" class="block font-montserrat font-light text-primary-900 mb-1">Message *</label>
              <textarea 
                id="message" 
                bind:value={message}
                rows="5"
                class="w-full border border-primary-200 p-2 focus:outline-none focus:ring-1 focus:ring-primary-500"
                required
              ></textarea>
            </div>
            
            <div>
              <button 
                type="submit" 
                class="button"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </button>
            </div>
          </form>
        {/if}
      </div>
    </section>
    
    <!-- Contact Information -->
    <section id="contact-info-section" class="order-1 md:order-2">
      <div class={`scroll-animation fade ${infoVisible ? 'visible' : ''}`} style="--delay: 300ms;">
        <h2 class="font-montserrat font-light text-primary-900 text-xl uppercase mb-6">Contact Information</h2>
        
        <div class="space-y-8">
          <div>
            <h3 class="font-montserrat font-medium text-primary-900 text-base uppercase mb-2">Address</h3>
            <p class="font-montserrat font-light text-primary-800">
              Baberrih Hotel<br />
              Tissa, Essaouira<br />
              Morocco
            </p>
          </div>
          
          <div>
            <h3 class="font-montserrat font-medium text-primary-900 text-base uppercase mb-2">Phone</h3>
            <a href="tel:+212633333398" class="font-montserrat font-light text-primary-800 hover:text-primary-700">
              +212 633333398
            </a>
          </div>
          
          <div>
            <h3 class="font-montserrat font-medium text-primary-900 text-base uppercase mb-2">Email</h3>
            <a href="mailto:<EMAIL>" class="font-montserrat font-light text-primary-800 hover:text-primary-700">
              <EMAIL>
            </a>
          </div>
          
          <div>
            <h3 class="font-montserrat font-medium text-primary-900 text-base uppercase mb-2">Reservations</h3>
            <p class="font-montserrat font-light text-primary-800 mb-4">
              For reservations, please use our online booking system or contact us directly.
            </p>
            <a 
              href="https://hotels.cloudbeds.com/en/reservas/lmKzDQ#checkin=2025-05-21&checkout=2025-05-28&currency=eur" 
              target="_blank"
              class="button"
            >
              Book Now
            </a>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>
