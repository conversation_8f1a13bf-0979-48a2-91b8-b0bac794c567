<script lang="ts">
  import type { PageData } from "./$types";

  export let data: PageData;
</script>

<svelte:head>
  <title>Contenido Premium - Mi SaaS</title>
</svelte:head>

<div class="mx-auto p-8 container">
  <div class="mb-8">
    <h1 class="mb-4 font-bold text-3xl">Contenido Premium Exclusivo</h1>
    <p class="text-lg">
      Bienvenido a nuestra sección de contenido premium. Aquí encontrarás
      recursos exclusivos disponibles solo para suscriptores.
    </p>
  </div>

  <div class="gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
    {#each data.premiumContent as item}
      <div class="bg-base-100 shadow-xl card">
        <figure>
          <img
            src={item.imageUrl}
            alt={item.title}
            class="w-full h-48 object-cover"
          />
        </figure>
        <div class="card-body">
          <h2 class="card-title">{item.title}</h2>
          <p>{item.description}</p>
          <div class="justify-end mt-4 card-actions">
            <button class="btn btn-primary">Ver detalles</button>
          </div>
        </div>
      </div>
    {/each}
  </div>

  <div class="flex justify-between mt-8">
    <a href="/" class="btn-outline btn">Volver al inicio</a>
    <a href="/account/subscriptions" class="btn btn-primary">
      Gestionar suscripción
    </a>
  </div>
</div>
