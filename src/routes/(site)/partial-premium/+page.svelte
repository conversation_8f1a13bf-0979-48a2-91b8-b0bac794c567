<script lang="ts">
  import SubscriptionRequired from "$lib/components/stripe/SubscriptionRequired.svelte";
</script>

<div class="mx-auto p-8 container">
  <div class="bg-base-100 shadow-xl card">
    <div class="card-body">
      <h1 class="font-bold text-3xl card-title">
        Contenido Parcialmente Premium
      </h1>

      <p class="py-4">
        Esta página contiene contenido público y contenido premium. El contenido
        público es visible para todos los usuarios, mientras que el contenido
        premium solo es visible para usuarios con una suscripción activa.
      </p>

      <h2 class="mt-6 font-semibold text-2xl">Contenido Público</h2>
      <p class="py-2">
        Este contenido es visible para todos los usuarios, independientemente de
        si tienen una suscripción activa o no.
      </p>

      <div class="divider"></div>

      <h2 class="font-semibold text-2xl">Contenido Premium</h2>

      <SubscriptionRequired
        paywallTitle="Contenido Exclusivo"
        paywallMessage="Esta sección contiene contenido exclusivo para suscriptores premium."
        paywallButtonText="Ver planes disponibles"
        redirectUrl="/products"
      >
        <div class="bg-primary/10 mt-4 p-4 rounded-lg">
          <p class="py-2">
            Este es un contenido premium que solo es visible para usuarios con
            una suscripción activa. Estás viendo este contenido porque tienes
            una suscripción activa.
          </p>
          <p class="py-2">
            Este es un ejemplo de cómo proteger solo una parte de una página
            utilizando el componente <code>SubscriptionRequired</code>.
          </p>
        </div>
      </SubscriptionRequired>

      <div class="mt-6 card-actions">
        <a href="/" class="btn-outline btn">Volver al inicio</a>
      </div>
    </div>
  </div>
</div>
