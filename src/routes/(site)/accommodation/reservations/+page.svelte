<script lang="ts">
  import { onMount } from "svelte";
  import AvailabilityCalendar from "$lib/components/cloudbeds/availability-calendar.svelte";
  import type { CloudbedsRoomType } from "$lib/cloudbeds/types";
  import { ChevronDown } from "lucide-svelte";
  import LoadingSpinner from "$lib/components/ui/LoadingSpinner.svelte";
  import ErrorMessage from "$lib/components/ui/ErrorMessage.svelte";

  // Variables para controlar el estado de la página
  let propertyId = ""; // ID de la propiedad
  let startDate = new Date(); // Fecha de inicio por defecto (hoy)
  let roomTypes: { id: string; name: string; photo?: string }[] = [];
  let selectedRoomTypeId = "";
  let isLoading = true;
  let error = "";
  let noRoomTypesAvailable = false;
  let selectedRoomDetails: CloudbedsRoomType | null = null;

  // Estado para el selector personalizado
  let isDropdownOpen = false;

  // Obtener el ID del tipo de habitación de la URL si existe
  onMount(async () => {
    try {
      // Obtener el ID del tipo de habitación de la URL si existe
      const urlParams = new URLSearchParams(window.location.search);
      const roomTypeIdFromUrl = urlParams.get("roomTypeId");

      if (roomTypeIdFromUrl) {
        selectedRoomTypeId = roomTypeIdFromUrl;
      }

      // Obtener la configuración de Cloudbeds
      const configResponse = await fetch("/api/cloudbeds/config");
      const configData = await configResponse.json();

      if (configData.success && configData.propertyID) {
        propertyId = configData.propertyID;
        console.log("Property ID obtenido:", propertyId);
      } else {
        console.warn(
          "No se pudo obtener el Property ID, usando valor por defecto"
        );
      }

      // Cargar tipos de habitaciones
      await loadRoomTypes();

      // Si hay un ID de tipo de habitación seleccionado, cargar sus detalles
      if (selectedRoomTypeId) {
        await loadRoomDetails(selectedRoomTypeId);
      }
    } catch (err) {
      console.error("Error en la inicialización:", err);
      error = err instanceof Error ? err.message : "Error desconocido";
    } finally {
      isLoading = false;
    }
  });

  // Cargar tipos de habitaciones
  async function loadRoomTypes() {
    try {
      const response = await fetch(
        "/api/cloudbeds/room-types?includePhotos=1&includeAmenities=1"
      );
      const data = await response.json();

      if (data.success && data.data && data.data.length > 0) {
        roomTypes = data.data.map((roomType: any) => {
          // Obtener la primera foto disponible
          let photo = "";
          if (roomType.roomTypePhotos && roomType.roomTypePhotos.length > 0) {
            photo = roomType.roomTypePhotos[0];
          } else if (roomType.photos && roomType.photos.length > 0) {
            photo = roomType.photos[0];
          } else if (roomType.rooms?.[0]?.photos?.[0]) {
            photo = roomType.rooms[0].photos[0];
          }

          return {
            id: roomType.roomTypeID || roomType.id,
            name: roomType.roomTypeName || roomType.name,
            photo,
          };
        });
        console.log("Tipos de habitaciones cargados:", roomTypes);
      } else {
        noRoomTypesAvailable = true;
        console.warn("No hay tipos de habitación disponibles");
      }
    } catch (err) {
      console.error("Error al cargar tipos de habitaciones:", err);
      error = err instanceof Error ? err.message : "Error desconocido";
    }
  }

  // Cargar detalles de un tipo de habitación específico
  async function loadRoomDetails(roomTypeId: string) {
    try {
      const response = await fetch(
        `/api/cloudbeds/room-types?includePhotos=1&includeAmenities=1`
      );
      const data = await response.json();

      if (data.success && data.data) {
        const roomType = data.data.find(
          (rt: any) => (rt.roomTypeID || rt.id) === roomTypeId
        );
        if (roomType) {
          selectedRoomDetails = roomType;
          console.log("Detalles de habitación cargados:", selectedRoomDetails);
        } else {
          console.warn(
            `No se encontró el tipo de habitación con ID ${roomTypeId}`
          );
        }
      } else {
        console.warn("No se pudieron cargar los detalles de la habitación");
      }
    } catch (err) {
      console.error("Error al cargar detalles de habitación:", err);
    }
  }

  // Manejar cambio de tipo de habitación
  async function handleRoomTypeChange(roomTypeId: string) {
    selectedRoomTypeId = roomTypeId;
    isDropdownOpen = false;

    if (selectedRoomTypeId) {
      await loadRoomDetails(selectedRoomTypeId);
    } else {
      selectedRoomDetails = null;
    }
  }

  // Toggle dropdown
  function toggleDropdown() {
    isDropdownOpen = !isDropdownOpen;
  }

  // Cerrar dropdown al hacer clic fuera
  function handleClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest(".custom-dropdown")) {
      isDropdownOpen = false;
    }
  }

  // Añadir event listener para cerrar dropdown al hacer clic fuera
  onMount(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  });
</script>

<svelte:head>
  <title>Reservations - Baberrih Hotel</title>
  <meta
    name="description"
    content="Book your stay at Baberrih Hotel in Essaouira, Morocco. Check availability and make reservations for our luxury suites."
  />
</svelte:head>

<!-- Hero Section -->
<section class="relative">
  <img
    src="https://baberrih.ma/media/accommodation/1_f.webp"
    alt="Baberrih Reservations"
    class="w-full h-[50vh] md:h-[60vh] object-cover"
  />
  <div
    class="absolute inset-0 flex flex-col justify-center items-center bg-gradient-to-b from-primary-950/30 to-primary-950/70"
  >
    <div class="visible scroll-animation slide-up">
      <h1
        class="font-montserrat font-light text-primary-50 text-3xl md:text-5xl lg:text-6xl uppercase tracking-wider"
      >
        Reservations
      </h1>
      <div class="bg-primary-200 mx-auto mt-4 mb-2 w-24 h-0.5"></div>
      <p
        class="font-eb-garamond text-primary-100 text-lg md:text-xl text-center italic"
      >
        Book your perfect stay at Baberrih
      </p>
    </div>
  </div>
</section>

<div class="mx-auto px-4 py-12 container">
  {#if error}
    <ErrorMessage message={error} type="error" />
  {/if}

  {#if isLoading}
    <div class="flex justify-center my-12">
      <LoadingSpinner size="3rem" color="var(--primary-500)" />
    </div>
  {:else if noRoomTypesAvailable}
    <ErrorMessage
      message="No hay tipos de habitación disponibles en este momento. Por favor, inténtelo más tarde."
      type="warning"
    />
  {:else}
    <div class="gap-6 grid grid-cols-1 lg:grid-cols-3">
      <div class="col-span-1 lg:col-span-2">
        <!-- Selector de tipo de habitación -->
        <div class="bg-primary-50 mb-6 p-6 border border-primary-100">
          <h3
            class="mb-4 font-montserrat font-light text-primary-900 text-xl uppercase"
          >
            Select Room Type
          </h3>

          <!-- Custom dropdown -->
          <div class="relative custom-dropdown">
            <button
              type="button"
              class="flex justify-between items-center bg-white p-3 border border-primary-200 hover:border-primary-300 w-full font-montserrat font-light text-primary-800 transition-colors"
              on:click|stopPropagation={() =>
                (isDropdownOpen = !isDropdownOpen)}
            >
              <span>
                {selectedRoomTypeId
                  ? roomTypes.find((rt) => rt.id === selectedRoomTypeId)
                      ?.name || "Select a room type"
                  : "Select a room type"}
              </span>
              <ChevronDown class="w-5 h-5 text-primary-500" />
            </button>

            {#if isDropdownOpen}
              <div
                class="z-10 absolute bg-white shadow-lg mt-1 border border-primary-200 w-full max-h-80 overflow-y-auto"
              >
                <button
                  class="flex items-center hover:bg-primary-50 p-3 border-primary-100 border-b w-full font-montserrat font-light text-primary-800 text-left"
                  on:click={() => handleRoomTypeChange("")}
                >
                  <span>Select a room type</span>
                </button>

                {#each roomTypes as roomType}
                  <button
                    class="flex items-center w-full p-3 hover:bg-primary-50 text-left {selectedRoomTypeId ===
                    roomType.id
                      ? 'bg-primary-50'
                      : ''}"
                    on:click={() => handleRoomTypeChange(roomType.id)}
                  >
                    {#if roomType.photo}
                      <div class="flex-shrink-0 mr-3 w-12 h-12">
                        <img
                          src={roomType.photo}
                          alt={roomType.name}
                          class="w-full h-full object-cover"
                        />
                      </div>
                    {/if}
                    <span>{roomType.name}</span>
                  </button>
                {/each}
              </div>
            {/if}
          </div>
        </div>

        <!-- Calendario de disponibilidad -->
        <div class="bg-primary-50 shadow-sm p-6 border border-primary-100">
          <h3
            class="mb-4 font-montserrat font-light text-primary-900 text-xl uppercase"
          >
            Availability Calendar
          </h3>

          {#if selectedRoomTypeId}
            <div
              class="bg-white shadow-inner p-4 border border-primary-100 rounded-sm"
            >
              <AvailabilityCalendar roomTypeId={selectedRoomTypeId} />
            </div>
          {:else}
            <div
              class="flex items-center bg-blue-50 p-5 border border-blue-200 rounded-sm text-blue-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="flex-shrink-0 mr-3 w-6 h-6"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clip-rule="evenodd"
                />
              </svg>
              <div>
                <p class="font-montserrat font-medium text-blue-800">
                  Please select a room type
                </p>
                <p class="text-sm">
                  Choose a room type from the dropdown above to view its
                  availability calendar.
                </p>
              </div>
            </div>
          {/if}
        </div>
      </div>

      <!-- Detalles de la habitación seleccionada -->
      <div class="col-span-1">
        {#if selectedRoomDetails}
          <div class="bg-primary-50 shadow-sm p-6 border border-primary-100">
            <h3
              class="mb-4 font-montserrat font-light text-primary-900 text-xl uppercase"
            >
              Room Details
            </h3>

            <div class="group relative mb-4 rounded-sm overflow-hidden">
              {#if selectedRoomDetails.roomTypePhotos && selectedRoomDetails.roomTypePhotos.length > 0}
                <img
                  src={selectedRoomDetails.roomTypePhotos[0]}
                  alt={selectedRoomDetails.roomTypeName ||
                    selectedRoomDetails.name ||
                    "Room"}
                  class="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-500"
                />
              {:else if selectedRoomDetails.photos && selectedRoomDetails.photos.length > 0}
                <img
                  src={selectedRoomDetails.photos[0]}
                  alt={selectedRoomDetails.roomTypeName ||
                    selectedRoomDetails.name ||
                    "Room"}
                  class="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-500"
                />
              {/if}
              <div
                class="absolute inset-0 bg-gradient-to-t from-primary-950/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              ></div>
            </div>

            <h4
              class="mb-3 font-montserrat font-medium text-primary-900 text-xl"
            >
              {selectedRoomDetails.roomTypeName ||
                selectedRoomDetails.name ||
                "Room"}
            </h4>

            {#if selectedRoomDetails.roomTypeDescription || selectedRoomDetails.description}
              <p
                class="mb-5 font-eb-garamond font-light text-primary-800 text-lg leading-relaxed"
              >
                {selectedRoomDetails.roomTypeDescription ||
                  selectedRoomDetails.description}
              </p>
            {/if}

            {#if selectedRoomDetails.amenities && selectedRoomDetails.amenities.length > 0}
              <div class="mb-6">
                <h5
                  class="mb-3 font-montserrat font-light text-primary-900 text-sm uppercase"
                >
                  Amenities
                </h5>
                <div class="flex flex-wrap gap-2">
                  {#each selectedRoomDetails.amenities as amenity}
                    <span
                      class="bg-primary-100 px-3 py-1.5 rounded-sm font-montserrat font-light text-primary-800 text-xs"
                      >{amenity}</span
                    >
                  {/each}
                </div>
              </div>
            {/if}

            <div class="mt-6 pt-6 border-primary-200 border-t"></div>

            <div class="flex flex-col gap-3">
              <a
                href="https://hotels.cloudbeds.com/en/reservas/lmKzDQ#checkin=2025-05-21&checkout=2025-05-28&currency=eur"
                target="_blank"
                class="block hover:bg-primary-600 py-3 w-full text-center transition-colors button"
              >
                Book Now
              </a>
              <a
                href="/accommodation"
                class="block hover:bg-primary-100 py-3 w-full text-center transition-colors button-secondary"
              >
                View All Rooms
              </a>
            </div>
          </div>
        {:else}
          <div
            class="flex flex-col justify-center items-center bg-primary-50 shadow-sm p-6 border border-primary-100 h-full text-center"
          >
            <div class="py-8">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="mx-auto mb-4 w-16 h-16 text-primary-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="1"
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                />
              </svg>
              <h4
                class="mb-2 font-montserrat font-medium text-primary-900 text-lg"
              >
                Select a Room
              </h4>
              <p class="font-eb-garamond font-light text-primary-700">
                Choose a room type from the dropdown to view details and
                availability.
              </p>
            </div>
          </div>
        {/if}
      </div>
    </div>
  {/if}
</div>
