<script lang="ts">
  import { onMount } from "svelte";
  import SuiteCard from "$lib/components/cloudbeds/SuiteCard.svelte";

  // Animation state
  let overviewVisible = $state(false);
  let suitesVisible = $state(false);

  // State for suites
  let suites = $state<any[]>([]);
  let loading = $state(true);
  let error = $state<string | null>(null);

  // Load suites from API
  async function loadSuites() {
    try {
      loading = true;
      error = null;

      // Fetch suites from API
      const response = await fetch('/api/suites?active_only=1');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to load suites');
      }

      if (!result.success || !result.data) {
        throw new Error('No suites found');
      }

      // Transform the data to match the expected format
      suites = result.data.map((suite: any) => ({
        id: suite.slug,
        title: suite.name,
        description: suite.description,
        image: suite.images && suite.images.length > 0 ? suite.images[0] : '',
        url: `/suites/${suite.slug}`,
        bookUrl: "/accommodation/reservations",
        features: [
          suite.size || '',
          ...(suite.features || [])
        ].filter(Boolean),
        // Add Cloudbeds integration data
        cloudbeds_room_type_id: suite.cloudbeds_room_type_id || null,
        cloudbeds_property_id: suite.cloudbeds_property_id || null,
        size: suite.size || '',
        capacity: suite.capacity || '',
        // Add videos array for hover effect
        videos: Array.isArray(suite.videos) ? [...suite.videos] : [],
      }));

      console.log('Loaded suites with Cloudbeds data:', suites);
    } catch (err) {
      console.error('Error loading suites:', err);
      error = err instanceof Error ? err.message : 'An error occurred while loading suites';

      // Fallback to empty array if there's an error
      suites = [];
    } finally {
      loading = false;
    }
  }

  // Intersection Observer for scroll animations
  function setupIntersectionObserver() {
    const observerOptions = {
      root: null,
      rootMargin: "0px",
      threshold: 0.1,
    };

    const overviewObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          overviewVisible = true;
          overviewObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);

    const suitesObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          suitesVisible = true;
          suitesObserver.unobserve(entry.target);
        }
      });
    }, observerOptions);

    // Observe elements
    const overviewElement = document.getElementById("overview-section");
    if (overviewElement) overviewObserver.observe(overviewElement);

    const suitesElement = document.getElementById("suites-section");
    if (suitesElement) suitesObserver.observe(suitesElement);
  }

  onMount(() => {
    loadSuites();
    setupIntersectionObserver();
  });
</script>

<svelte:head>
  <title>Accommodation - Baberrih Hotel</title>
  <meta
    name="description"
    content="Discover our luxurious suites at Baberrih Hotel in Essaouira, Morocco, offering ocean and garden views with modern amenities and traditional Moroccan design."
  />
</svelte:head>

<!-- Hero Section -->
<section class="relative">
  <img
    src="https://baberrih.ma/media/accommodation/1_f.webp"
    alt="Baberrih Accommodation"
    class="w-full h-[50vh] md:h-[60vh] object-cover"
  />
  <div
    class="absolute inset-0 flex justify-center items-center bg-primary-950/50"
  >
    <h1
      class="font-montserrat font-light text-primary-50 text-3xl md:text-5xl uppercase"
    >
      Accommodation
    </h1>
  </div>
</section>

<!-- Overview Section -->
<section id="overview-section" class="px-4 py-12">
  <div class="mx-auto max-w-4xl container">
    <div
      class={`scroll-animation fade ${overviewVisible ? "visible" : ""}`}
      style="--delay: 300ms;"
    >
      <h2
        class="mb-6 font-montserrat font-light text-primary-900 text-xl uppercase"
      >
        Our Suites
      </h2>

      <p class="mb-6 font-eb-garamond font-light text-primary-800 text-lg">
        At Baberrih, we offer a selection of beautifully appointed suites
        designed to provide comfort, privacy, and a connection to the natural
        beauty that surrounds us. Each suite features a blend of traditional
        Moroccan design elements and modern amenities to ensure a memorable
        stay.
      </p>

      <p class="mb-6 font-eb-garamond font-light text-primary-800 text-lg">
        Choose between our ocean-facing suites with panoramic views of the
        Atlantic or our garden-view suites overlooking our lush gardens. All
        accommodations include private outdoor spaces, en-suite bathrooms, and
        thoughtful amenities to enhance your experience.
      </p>

      <div class="gap-6 grid grid-cols-1 md:grid-cols-2 mt-8">
        <div class="p-6 border border-primary-100">
          <h3
            class="mb-2 font-montserrat font-light text-primary-900 text-base uppercase"
          >
            Standard Amenities
          </h3>
          <ul class="space-y-2 font-eb-garamond font-light text-primary-800">
            <li>• En-suite bathroom with rain shower</li>
            <li>• Air conditioning</li>
            <li>• Free Wi-Fi</li>
            <li>• Mini-bar</li>
            <li>• Coffee and tea facilities</li>
            <li>• Safe</li>
            <li>• Organic toiletries</li>
          </ul>
        </div>

        <div class="p-6 border border-primary-100">
          <h3
            class="mb-2 font-montserrat font-light text-primary-900 text-base uppercase"
          >
            Services
          </h3>
          <ul class="space-y-2 font-eb-garamond font-light text-primary-800">
            <li>• Daily housekeeping</li>
            <li>• Turndown service</li>
            <li>• Room service</li>
            <li>• Laundry service</li>
            <li>• Concierge assistance</li>
            <li>• Beach towels and accessories</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Suites Section -->
<section id="suites-section" class="bg-primary-50 px-4 py-12">
  <div class="mx-auto max-w-6xl container">
    <div
      class={`scroll-animation slide-up ${suitesVisible ? "visible" : ""}`}
      style="--delay: 300ms;"
    >
      <h2
        class="mb-10 font-montserrat font-light text-primary-900 text-xl text-center uppercase"
      >
        Our Suite Collection
      </h2>

      {#if loading}
        <div class="p-8 text-center">
          <div
            class="inline-block border-4 border-gray-300 border-t-blue-600 rounded-full w-8 h-8 animate-spin"
          ></div>
          <p class="mt-2 text-gray-600">Loading suites...</p>
        </div>
      {:else if error}
        <div class="p-8 text-center">
          <p class="text-red-600">{error}</p>
        </div>
      {:else if suites.length === 0}
        <div class="p-8 text-center">
          <p class="text-gray-600">No suites found.</p>
        </div>
      {:else}
        <div class="gap-8 grid grid-cols-1 md:grid-cols-2">
          {#each suites as suite}
            <SuiteCard suite={suite} />
          {/each}
        </div>
      {/if}
    </div>
  </div>
</section>

<!-- Booking Section -->
<section class="px-4 py-12">
  <div class="mx-auto max-w-4xl text-center container">
    <h2
      class="mb-6 font-montserrat font-light text-primary-900 text-xl uppercase"
    >
      Book Your Stay
    </h2>
    <p
      class="mx-auto mb-8 max-w-2xl font-eb-garamond font-light text-primary-800 text-lg"
    >
      Ready to experience the tranquility and beauty of Baberrih? Book your stay
      directly through our online reservation system for the best rates and
      special offers.
    </p>
    <div class="flex justify-center gap-4">
      <a href="/accommodation/reservations" class="button-secondary">
        Check Availability
      </a>
      <a href="/accommodation/reservations" class="button"> Book Now </a>
    </div>
  </div>
</section>
