<!--
  Página de suscripciones
  Esta página muestra las suscripciones del usuario.
-->
<script lang="ts">
  import { page } from "$app/stores";
  import { goto } from "$app/navigation";
  import { onMount } from "svelte";
  import SubscriptionList from "$lib/components/stripe/SubscriptionList.svelte";
  import { AlertCircle } from "lucide-svelte";

  // Estado
  let loading = true;
  let error: string | null = null;

  onMount(() => {
    // Verificar si el usuario está autenticado
    if (!$page.data.session) {
      // Redirigir al login si no está autenticado
      goto("/login?redirect=/account/subscriptions");
      return;
    }

    loading = false;
  });
</script>

<svelte:head>
  <title>Mis suscripciones - Mi SaaS</title>
</svelte:head>

<div class="mx-auto px-4 py-8 container">
  <div class="mb-8 pb-4 border-b border-base-300">
    <h1 class="mb-2 font-bold text-3xl">Mis suscripciones</h1>
    <p class="text-base-content/70">
      Gestiona tus suscripciones y planes activos
    </p>
  </div>

  {#if loading}
    <div class="flex justify-center items-center py-16">
      <div class="text-primary loading loading-spinner loading-lg"></div>
    </div>
  {:else if error}
    <div class="shadow-lg alert alert-error">
      <AlertCircle class="w-6 h-6" />
      <span>{error}</span>
    </div>
  {:else if !$page.data.session}
    <div class="shadow-lg alert alert-warning">
      <AlertCircle class="w-6 h-6" />
      <span>Debes iniciar sesión para ver tus suscripciones.</span>
      <div class="flex-none">
        <a
          href="/login?redirect=/account/subscriptions"
          class="btn btn-sm btn-primary"
        >
          Iniciar sesión
        </a>
      </div>
    </div>
  {:else}
    <!-- Usar el componente SubscriptionList sin pasar customerId -->
    <SubscriptionList />
  {/if}
</div>
