<script lang="ts">
  import { onMount } from "svelte";
  import SimpleImage from "$lib/components/ui/SimpleImage.svelte";
  import VideoHero from "$lib/components/ui/VideoHero.svelte";
  import ScrollIndicator from "$lib/components/ui/ScrollIndicator.svelte";
  import { T, t } from "$lib/i18n";
  import SuiteCard from "$lib/components/cloudbeds/SuiteCard.svelte";

  // State for scroll animations
  let heroVisible = $state(false);
  let suitesVisible = $state(false);
  let testimonialVisible = $state(false);
  let experiencesVisible = $state(false);
  let facilitiesVisible = $state(false);

  // State for suites
  let suites = $state<any[]>([]);
  let suitesLoading = $state(true);
  let suitesError = $state<string | null>(null);

  // Load suites from API
  async function loadSuites() {
    try {
      suitesLoading = true;
      suitesError = null;

      // Fetch suites from API
      const response = await fetch('/api/suites?active_only=1&limit=4');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to load suites');
      }

      if (!result.success || !result.data) {
        throw new Error('No suites found');
      }

      // Transform the data to match the expected format
      suites = result.data.map((suite: any) => ({
        id: suite.slug,
        title: suite.name,
        description: suite.description,
        image: suite.images && suite.images.length > 0 ? suite.images[0] : '',
        url: `/suites/${suite.slug}`,
        bookUrl: "/accommodation/reservations",
        // Add Cloudbeds integration data
        cloudbeds_room_type_id: suite.cloudbeds_room_type_id || null,
        cloudbeds_property_id: suite.cloudbeds_property_id || null,
        size: suite.size || '',
        capacity: suite.capacity || '',
        // Add videos array for hover effect
        videos: Array.isArray(suite.videos) ? [...suite.videos] : [],
      }));

      console.log('Loaded suites with Cloudbeds data for homepage:', suites);
    } catch (err) {
      console.error('Error loading suites:', err);
      suitesError = err instanceof Error ? err.message : 'An error occurred while loading suites';

      // Fallback to empty array if there's an error
      suites = [];
    } finally {
      suitesLoading = false;
    }
  }

  // Experiences data
  const experiences = [
    {
      id: "horseback-riding",
      title: "Horseback Riding",
      image: "https://baberrih.ma/media/experiences/horse/1_f.webp",
      url: "/experiences/horseback-riding",
    },
    {
      id: "quads",
      title: "Quads",
      image: "https://baberrih.ma/media/experiences/quads/1_f.webp",
      url: "/experiences/quads",
    },
    {
      id: "land-sailing",
      title: "Land Sailing",
      image: "https://baberrih.ma/media/experiences/sand_sailing/1_f.webp",
      url: "/experiences/land-sailing",
    },
    {
      id: "fatbikes",
      title: "Fatbikes",
      image: "https://baberrih.ma/media/experiences/fatbikes/1_f.webp",
      url: "/experiences/fatbikes",
    },
    {
      id: "yoga",
      title: "Yoga",
      image: "https://baberrih.ma/media/experiences/yoga/1_f.webp",
      url: "/experiences/yoga",
    },
    {
      id: "kite-surf",
      title: "Kite Surf",
      image: "https://baberrih.ma/media/experiences/kitesurf/1_f.webp",
      url: "/experiences/kite-surf",
    },
  ];

  // Facilities data
  const facilities = [
    {
      id: "garden",
      title: "Garden and Orchard",
      image: "https://baberrih.ma/media/facilities/garden/1_f.webp",
      url: "/facilities/garden",
    },
    {
      id: "common-areas",
      title: "Common Areas",
      image: "https://baberrih.ma/media/facilities/common/1_f.webp",
      url: "/facilities/common-areas",
    },
    {
      id: "yoga-room",
      title: "Yoga Room",
      image: "https://baberrih.ma/media/facilities/yoga/1_f.webp",
      url: "/facilities/yoga-room",
    },
    {
      id: "stable",
      title: "Stable",
      image: "https://baberrih.ma/media/facilities/stable/1_f.webp",
      url: "/facilities/stable",
    },
    {
      id: "pool",
      title: "Pool",
      image: "https://baberrih.ma/media/facilities/pool/1_f.webp",
      url: "/facilities/pool",
    },
    {
      id: "beach",
      title: "Beach",
      image: "https://baberrih.ma/media/facilities/beach/1_f.webp",
      url: "/facilities/beach",
    },
  ];

  // Optimized Intersection Observer for scroll animations
  function setupIntersectionObserver() {
    // Activar inmediatamente la sección del héroe para mejorar la percepción de velocidad
    heroVisible = true;

    // Usar requestIdleCallback para configurar el observer cuando el navegador esté inactivo
    if (typeof window !== "undefined") {
      const setupObserver = () => {
        const observerOptions = {
          root: null,
          rootMargin: "100px", // Aumentamos aún más el margen para cargar antes
          threshold: 0.01, // Umbral muy bajo para activar lo antes posible
        };

        // Un solo observer para todos los elementos
        const sectionObserver = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const sectionId = entry.target.id;

              // Activar la visibilidad según el ID de la sección
              if (sectionId === "suites-section") suitesVisible = true;
              else if (sectionId === "testimonial-section")
                testimonialVisible = true;
              else if (sectionId === "experiences-section")
                experiencesVisible = true;
              else if (sectionId === "facilities-section")
                facilitiesVisible = true;

              // Dejar de observar después de activar
              sectionObserver.unobserve(entry.target);
            }
          });
        }, observerOptions);

        // Observar todos los elementos con un solo observer (excepto hero que ya está activado)
        const sections = [
          "suites-section",
          "testimonial-section",
          "experiences-section",
          "facilities-section",
        ];

        sections.forEach((id) => {
          const element = document.getElementById(id);
          if (element) sectionObserver.observe(element);
        });
      };

      // Usar requestIdleCallback si está disponible, o setTimeout como fallback
      if ("requestIdleCallback" in window) {
        window.requestIdleCallback(setupObserver, { timeout: 1000 });
      } else {
        setTimeout(setupObserver, 100);
      }
    }
  }

  onMount(() => {
    loadSuites();
    setupIntersectionObserver();
  });
</script>

<svelte:head>
  <title>Baberrih - {$t("home.meta.title")}</title>
  <meta name="description" content={$t("home.meta.description")} />
</svelte:head>

<!-- Hero Section -->
<section id="hero-section" class="hero-container">
  <div class="relative w-full h-full overflow-hidden">
    <VideoHero
      videoSrc="https://minio-ig0g48s4kkc8kw0okogcoc4k.danieldev.xyz/media/hero/1.webm?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=qA8ASLWdYPdtZvEPQbvv%2F20250519%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250519T173322Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=5a4071ed4cfdd4386728b45b4bf70b3d29b38011efb937d9278021138bb745fb"
      fallbackImageSrc="https://baberrih.ma/media/hero/1_f.webp"
      alt="Baberrih Hotel"
      className="hero-video"
      autoplay={true}
      loop={true}
      muted={true}
    />
    <div
      class="absolute inset-0 bg-gradient-to-b from-primary-950/30 via-primary-950/20 to-primary-950/80"
    ></div>

    <!-- Desktop Scroll Indicator -->
    <div class="hidden md:block">
      <ScrollIndicator
        hideAfterScroll={true}
        scrollThreshold={100}
        arrowColor="text-primary-50"
        pulseColor="bg-primary-50/30"
      />
    </div>
  </div>

  <div class="mx-auto max-w-4xl hero-content">
    <!-- Mobile Scroll Indicator -->
    <div class="md:hidden">
      <ScrollIndicator
        className="bottom-4 left-1/2 absolute flex -translate-x-1/2 transform"
        hideAfterScroll={true}
        scrollThreshold={50}
        arrowSize="w-5 h-5"
        arrowColor="text-primary-50"
        pulseColor="bg-primary-50/30"
      />
    </div>

    <div
      class={`scroll-animation fade ${heroVisible ? "visible" : ""}`}
      style="--delay: 300ms;"
    >
      <h1
        class="font-eb-garamond font-extralight text-primary-100 text-base md:text-lg italic"
      >
        <T key="home.hero.subtitle" />
      </h1>
    </div>
    <div
      class={`scroll-animation slide-up ${heroVisible ? "visible" : ""}`}
      style="--delay: 500ms;"
    >
      <h2
        class="font-montserrat font-extralight text-primary-50 text-4xl md:text-5xl lg:text-6xl uppercase tracking-wider"
      >
        <T key="home.hero.title" />
      </h2>
      <div class="bg-primary-300 opacity-70 mx-auto mt-4 mb-2 w-24 h-0.5"></div>
    </div>
    <div
      class={`scroll-animation slide-up ${heroVisible ? "visible" : ""}`}
      style="--delay: 700ms;"
    >
      <p
        class="mt-4 font-montserrat font-light text-primary-100 text-sm md:text-base uppercase tracking-wide"
      >
        <T key="home.hero.location" />
      </p>
    </div>
    <div
      class={`flex justify-center gap-6 mt-6 scroll-animation slide-up ${heroVisible ? "visible" : ""}`}
      style="--delay: 900ms;"
    >
      <a
        href="/contact"
        class="font-montserrat font-light text-primary-50 hover:text-primary-200 text-xs md:text-sm underline underline-offset-4 uppercase transition-colors"
      >
        <T key="common.contactUs" />
      </a>
      <a
        href="tel:+212633333398"
        class="flex items-center font-montserrat font-light text-primary-50 hover:text-primary-200 text-xs md:text-sm underline underline-offset-4 transition-colors"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="mr-1 w-3 h-3"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path
            d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
          ></path>
        </svg>
        +212 633333398
      </a>
    </div>

    <div
      class={`mt-12 flex justify-center scroll-animation slide-up ${heroVisible ? "visible" : ""}`}
      style="--delay: 1100ms;"
    >
      <a
        href="/accommodation/reservations"
        class="inline-flex items-center hover:bg-primary-600 px-8 py-4 transition-colors button"
      >
        <span><T key="home.bookYourStay" /></span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="ml-2 w-4 h-4"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M5 12h14"></path>
          <path d="M12 5l7 7-7 7"></path>
        </svg>
      </a>
    </div>
  </div>
</section>

<!-- Introduction Section -->
<section class="flex flex-col items-center px-4 md:px-8 py-16 w-full">
  <div
    class="flex flex-col items-center gap-8 bg-gradient-to-b from-transparent to-primary-100/45 p-8 md:p-16 rounded-sm max-w-7xl"
  >
    <div class="bg-primary-300 mx-auto w-24 h-0.5"></div>

    <div class="flex flex-col items-center gap-6">
      <h2
        class="font-montserrat font-light text-primary-950 text-2xl md:text-3xl text-center uppercase tracking-wide"
      >
        <T key="home.intro.title" />
      </h2>

      <div class="max-w-4xl">
        <p
          class="font-eb-garamond font-light text-primary-900 text-lg md:text-xl text-center text-balance leading-relaxed"
        >
          <T key="home.intro.paragraph1" />
        </p>

        <p
          class="mt-4 font-eb-garamond font-light text-primary-900 text-lg md:text-xl text-center text-balance leading-relaxed"
        >
          <T key="home.intro.paragraph2" />
        </p>
      </div>

      <div class="flex flex-wrap justify-center gap-4 mt-4">
        <a
          href="/accommodation"
          class="hover:bg-primary-100 transition-colors button-secondary"
        >
          <T key="nav.accommodation" />
        </a>
        <a
          href="/experiences"
          class="hover:bg-primary-100 transition-colors button-secondary"
        >
          <T key="nav.experiences" />
        </a>
        <a
          href="/facilities"
          class="hover:bg-primary-100 transition-colors button-secondary"
        >
          <T key="nav.facilities" />
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Suites Section -->
<section
  id="suites-section"
  class="flex flex-col items-center bg-primary-50/50 py-12 w-full content-visibility-auto"
>
  <div class="flex flex-col px-4 w-full max-w-screen-xl">
    <div class="flex flex-col items-center gap-10">
      <div class="text-center">
        <div class="bg-primary-300 mx-auto mb-6 w-16 h-0.5"></div>
        <h3
          class={`font-montserrat font-light text-primary-900 text-2xl md:text-3xl text-center uppercase tracking-wider mb-2 scroll-animation fade ${suitesVisible ? "visible" : ""}`}
          style="--delay: 300ms;"
        >
          <T key="home.suites.title" />
        </h3>
        <p
          class="mx-auto mb-8 max-w-2xl font-eb-garamond font-light text-primary-800 text-lg"
        >
          <T key="home.suites.description" />
        </p>
      </div>

      {#if suitesLoading}
        <div class="p-8 text-center">
          <div
            class="inline-block border-4 border-gray-300 border-t-blue-600 rounded-full w-8 h-8 animate-spin"
          ></div>
          <p class="mt-2 text-gray-600">Loading suites...</p>
        </div>
      {:else if suitesError}
        <div class="p-8 text-center">
          <p class="text-red-600">{suitesError}</p>
        </div>
      {:else if suites.length === 0}
        <div class="p-8 text-center">
          <p class="text-gray-600">No suites found.</p>
        </div>
      {:else}
        <div
          class={`flex gap-6 md:gap-6 md:grid md:grid-cols-2 w-full overflow-x-auto md:overflow-x-hidden snap-mandatory snap-x md:snap-none scrollbar-hide scroll-animation slide-up ${suitesVisible ? "visible" : ""}`}
          style="--delay: 500ms;"
        >
          {#each suites as suite}
            <SuiteCard suite={suite} compact={true} />
          {/each}
        </div>
      {/if}

      <div class="mt-8 text-center">
        <a
          href="/accommodation"
          class="inline-flex items-center font-montserrat font-light text-primary-800 hover:text-primary-700 transition-colors"
        >
          <span><T key="home.viewAllAccommodations" /></span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="ml-2 w-4 h-4"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M5 12h14"></path>
            <path d="M12 5l7 7-7 7"></path>
          </svg>
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Testimonial Section -->
<section id="testimonial-section" class="relative my-16">
  <div class="relative overflow-hidden">
    <SimpleImage
      src="https://baberrih.ma/media/testimonial/1.webp"
      alt="Baberrih hotel testimonial background"
      className="h-[60vh] md:h-auto lg:aspect-video hover:scale-105 transition-transform duration-700 ease-out will-change-transform"
    />
    <div
      class="absolute inset-0 bg-gradient-to-b from-primary-50/90 via-primary-50/70 to-primary-50/30"
    ></div>
  </div>

  <div
    class="absolute inset-0 flex flex-col justify-center items-center px-6 md:px-12 lg:px-24 py-12 lg:py-24"
  >
    <div class="mx-auto max-w-5xl">
      <div
        class={`scroll-animation fade ${testimonialVisible ? "visible" : ""}`}
        style="--delay: 300ms;"
      >
        <svg
          class="mx-auto mb-6 w-12 h-12 text-primary-400"
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"
          />
        </svg>

        <p
          class="lg:max-w-5xl font-eb-garamond text-primary-900 text-2xl md:text-4xl lg:text-5xl text-center italic leading-relaxed"
        >
          "<T key="home.testimonial.quote" />"
        </p>
      </div>

      <div
        class={`flex flex-col items-center mt-8 scroll-animation slide-up ${testimonialVisible ? "visible" : ""}`}
        style="--delay: 600ms;"
      >
        <div class="bg-primary-300 mx-auto mb-6 w-16 h-0.5"></div>
        <p
          class="font-montserrat font-semibold text-primary-900 text-lg text-center"
        >
          <T key="home.testimonial.author" />
        </p>
        <p
          class="mt-1 font-montserrat text-primary-800 text-sm text-center uppercase tracking-wider"
        >
          <T key="home.testimonial.role" />
        </p>
      </div>
    </div>
  </div>
</section>

<!-- Experiences Section -->
<section
  id="experiences-section"
  class="flex flex-col items-center py-16 w-full content-visibility-auto"
>
  <div class="flex flex-col px-4 w-full max-w-screen-xl">
    <div class="flex flex-col items-center gap-10">
      <div class="text-center">
        <div class="bg-primary-300 mx-auto mb-6 w-16 h-0.5"></div>
        <h3
          class={`font-montserrat font-light text-primary-900 text-2xl md:text-3xl text-center uppercase tracking-wider mb-2 scroll-animation fade ${experiencesVisible ? "visible" : ""}`}
          style="--delay: 300ms;"
        >
          <T key="home.experiences.title" />
        </h3>
        <p
          class="mx-auto mb-8 max-w-2xl font-eb-garamond font-light text-primary-800 text-lg"
        >
          <T key="home.experiences.description" />
        </p>
      </div>

      <div
        class={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full scroll-animation slide-up ${experiencesVisible ? "visible" : ""}`}
        style="--delay: 500ms;"
      >
        {#each experiences as experience, i}
          <div
            class="group bg-white shadow-sm hover:shadow-md border border-primary-100 overflow-hidden transition-all duration-300"
            style={`--animation-delay: ${i * 100}ms`}
          >
            <a href={experience.url} class="block relative overflow-hidden">
              <div
                style="--aspect-ratio:4/3;--aspect-ratio-mobile:4/3"
                class="aspect-ratio-container"
              >
                <div class="relative w-full h-full overflow-hidden">
                  <SimpleImage
                    src={experience.image}
                    alt={experience.title}
                    className="group-hover:scale-110 transition-transform duration-500 ease-out will-change-transform"
                  />
                  <div
                    class="absolute inset-0 flex items-end bg-gradient-to-t from-primary-950/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  >
                    <div class="p-4 w-full">
                      <h4
                        class="opacity-0 group-hover:opacity-100 font-montserrat font-medium text-primary-50 text-xl transition-opacity translate-y-4 group-hover:translate-y-0 duration-300 transform"
                      >
                        {experience.title}
                      </h4>
                    </div>
                  </div>
                </div>
              </div>
            </a>
            <div class="p-4 md:p-5">
              <h4
                class="mb-2 font-montserrat font-medium text-primary-900 text-lg"
              >
                {experience.title}
              </h4>
              <a
                href={experience.url}
                class="inline-flex items-center mt-2 font-montserrat font-light text-primary-700 hover:text-primary-600 text-sm transition-colors"
              >
                <span><T key="common.readMore" /></span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="ml-1 w-4 h-4"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M5 12h14"></path>
                  <path d="M12 5l7 7-7 7"></path>
                </svg>
              </a>
            </div>
          </div>
        {/each}
      </div>

      <div class="mt-8 text-center">
        <a
          href="/experiences"
          class="hover:bg-primary-600 transition-colors button"
        >
          <T key="home.exploreAllExperiences" />
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Facilities Section -->
<section
  id="facilities-section"
  class="flex flex-col items-center bg-primary-50/50 py-16 w-full content-visibility-auto"
>
  <div class="flex flex-col px-4 w-full max-w-screen-xl">
    <div class="flex flex-col items-center gap-10">
      <div class="text-center">
        <div class="bg-primary-300 mx-auto mb-6 w-16 h-0.5"></div>
        <h3
          class={`font-montserrat font-light text-primary-900 text-2xl md:text-3xl text-center uppercase tracking-wider mb-2 scroll-animation fade ${facilitiesVisible ? "visible" : ""}`}
          style="--delay: 300ms;"
        >
          <T key="home.facilities.title" />
        </h3>
        <p
          class="mx-auto mb-8 max-w-2xl font-eb-garamond font-light text-primary-800 text-lg"
        >
          <T key="home.facilities.description" />
        </p>
      </div>

      <div
        class={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full scroll-animation slide-up ${facilitiesVisible ? "visible" : ""}`}
        style="--delay: 500ms;"
      >
        {#each facilities as facility, i}
          <div
            class="group bg-white shadow-sm hover:shadow-md border border-primary-100 overflow-hidden transition-all duration-300"
            style={`--animation-delay: ${i * 100}ms`}
          >
            <a href={facility.url} class="block relative overflow-hidden">
              <div
                style="--aspect-ratio:4/3;--aspect-ratio-mobile:4/3"
                class="aspect-ratio-container"
              >
                <div class="relative w-full h-full overflow-hidden">
                  <SimpleImage
                    src={facility.image}
                    alt={facility.title}
                    className="group-hover:scale-110 transition-transform duration-500 ease-out will-change-transform"
                  />
                  <div
                    class="absolute inset-0 flex items-end bg-gradient-to-t from-primary-950/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  >
                    <div class="p-4 w-full">
                      <span
                        class="inline-block bg-primary-50/80 mb-2 px-3 py-1 font-montserrat text-primary-900 text-xs uppercase tracking-wider"
                      >
                        <T key="nav.facilities" />
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </a>
            <div class="p-4 md:p-5">
              <h4
                class="mb-2 font-montserrat font-medium text-primary-900 text-lg"
              >
                {facility.title}
              </h4>
              <a
                href={facility.url}
                class="inline-flex items-center mt-2 font-montserrat font-light text-primary-700 hover:text-primary-600 text-sm transition-colors"
              >
                <span><T key="common.readMore" /></span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="ml-1 w-4 h-4"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M5 12h14"></path>
                  <path d="M12 5l7 7-7 7"></path>
                </svg>
              </a>
            </div>
          </div>
        {/each}
      </div>

      <div class="mt-8 text-center">
        <a
          href="/facilities"
          class="hover:bg-primary-600 transition-colors button"
        >
          <T key="home.exploreAllFacilities" />
        </a>
      </div>
    </div>
  </div>
</section>
