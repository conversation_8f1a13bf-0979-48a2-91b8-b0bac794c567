<script lang="ts">
  import { currency, supportedCurrencies, convertPrice, formatPrice } from "$lib/currency/store";
  import { T } from "$lib/i18n";
  
  // Sample prices in different currencies
  const prices = {
    MAD: 1000, // 1000 MAD
    EUR: 100,  // 100 EUR
    USD: 120,  // 120 USD
    GBP: 80    // 80 GBP
  };
  
  // Function to convert and format a price
  function displayPrice(amount: number, sourceCurrency: string): string {
    const convertedAmount = convertPrice(amount, sourceCurrency, $currency);
    return formatPrice(convertedAmount, $currency);
  }
</script>

<div class="container mx-auto px-4 py-12">
  <h1 class="mb-8 text-3xl font-montserrat font-light text-primary-900 uppercase tracking-wider">
    Currency Test Page
  </h1>
  
  <div class="mb-8">
    <h2 class="mb-4 text-xl font-montserrat font-light text-primary-800 uppercase tracking-wider">
      Current Currency: {$currency}
    </h2>
    
    <div class="flex flex-wrap gap-4">
      {#each supportedCurrencies as curr}
        <button
          onclick={() => currency.set(curr.code)}
          class="px-4 py-2 border rounded-md {$currency === curr.code ? 'bg-primary-100 border-primary-300' : 'bg-white border-primary-200'}"
        >
          <span class="mr-2">{curr.symbol}</span>
          {curr.name}
        </button>
      {/each}
    </div>
  </div>
  
  <div class="mb-8">
    <h2 class="mb-4 text-xl font-montserrat font-light text-primary-800 uppercase tracking-wider">
      Price Conversion Test
    </h2>
    
    <div class="overflow-x-auto">
      <table class="w-full border-collapse">
        <thead>
          <tr class="bg-primary-100">
            <th class="p-3 text-left font-montserrat font-medium text-primary-900">Source Currency</th>
            <th class="p-3 text-left font-montserrat font-medium text-primary-900">Original Amount</th>
            <th class="p-3 text-left font-montserrat font-medium text-primary-900">Converted to {$currency}</th>
          </tr>
        </thead>
        <tbody>
          {#each Object.entries(prices) as [sourceCurrency, amount]}
            <tr class="border-b border-primary-100">
              <td class="p-3 font-montserrat text-primary-800">{sourceCurrency}</td>
              <td class="p-3 font-montserrat text-primary-800">{formatPrice(amount, sourceCurrency)}</td>
              <td class="p-3 font-montserrat text-primary-800">{displayPrice(amount, sourceCurrency)}</td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  </div>
  
  <div class="mb-8">
    <h2 class="mb-4 text-xl font-montserrat font-light text-primary-800 uppercase tracking-wider">
      Exchange Rates (Base: MAD)
    </h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {#each supportedCurrencies as curr}
        <div class="p-4 bg-white border border-primary-100 rounded-md shadow-sm">
          <div class="flex items-center justify-between">
            <div>
              <span class="text-lg font-montserrat font-medium text-primary-900">{curr.code}</span>
              <span class="ml-2 text-sm text-primary-600">({curr.name})</span>
            </div>
            <span class="text-xl font-montserrat font-medium text-primary-900">{curr.symbol}</span>
          </div>
          <div class="mt-2 text-sm text-primary-700">
            <p>1 MAD = {convertPrice(1, 'MAD', curr.code).toFixed(4)} {curr.code}</p>
            <p>1 {curr.code} = {convertPrice(1, curr.code, 'MAD').toFixed(4)} MAD</p>
          </div>
        </div>
      {/each}
    </div>
  </div>
</div>
