<script lang="ts">
  import { onMount } from "svelte";
  import { marked } from "marked";
  import LoadingSpinner from "$lib/components/ui/LoadingSpinner.svelte";
  import ErrorMessage from "$lib/components/ui/ErrorMessage.svelte";
  import SimpleImage from "$lib/components/ui/SimpleImage.svelte";

  // Types
  interface BlogPost {
    id: string;
    status: string;
    created_at: string;
    updated_at: string;
    published_at: string;
    data: {
      slug: string;
      title: string;
      banner: string;
      content: string;
      translatedSlug?: string;
    };
    translations?: Record<
      string,
      {
        source_language: string;
        data: {
          slug: string;
          title: string;
          content: string;
        };
        is_machine_translated: boolean;
        is_reviewed: boolean;
        is_published: boolean;
      }
    >;
  }

  // State variables
  let post = $state<BlogPost | null>(null);
  let loading = $state(true);
  let error = $state<string | null>(null);
  let contentVisible = $state(false);
  let renderedContent = $state("");

  // Define the base URL for your API/media files.
  // Using the correct port for the server
  const API_BASE_URL = "http://localhost:5174"; // Or your actual CMS base URL

  // Get slug from URL params (will be set in onMount for client-side)
  let slug = $state("");

  // Format date for display
  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }).format(date);
  }

  // Setup intersection observer for animations
  function setupIntersectionObserver(): void {
    if (typeof IntersectionObserver === "undefined") {
      // Fallback for browsers that don't support IntersectionObserver
      contentVisible = true;
      return;
    }

    const contentObserver = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          contentVisible = true;
          contentObserver.disconnect();
        }
      },
      {
        rootMargin: "0px",
        threshold: 0.1,
      }
    );

    const contentElement = document.getElementById("content-section");
    if (contentElement) contentObserver.observe(contentElement);
  }

  // Fetch blog post
  async function fetchBlogPost(): Promise<void> {
    try {
      loading = true;
      error = null;

      console.log("Fetching blog post with slug:", slug);

      // Use the correct port for the API
      const apiUrl = `http://localhost:5174/api/blog?slug=${slug}`;
      console.log("API URL:", apiUrl);

      const response = await fetch(apiUrl);
      console.log("Response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response:", errorText);
        throw new Error(`Error fetching blog post: ${response.statusText}`);
      }

      const responseText = await response.text();
      console.log(
        "Response text (first 200 chars):",
        responseText.substring(0, 200)
      );

      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error("Error parsing JSON:", parseError);
        throw new Error("Invalid JSON response from API");
      }

      if (!data.success) {
        throw new Error(data.error?.message || "Unknown error");
      }

      console.log("Blog post data received:", data.post ? "Yes" : "No");
      post = data.post;

      // Render markdown content
      if (post) {
        console.log("Processing blog post with title:", post.data.title);
        console.log("Banner URL before processing:", post.data.banner);

        // Ensure banner URL is absolute
        if (
          post.data.banner &&
          !post.data.banner.startsWith("http") &&
          !post.data.banner.startsWith("/")
        ) {
          post.data.banner = `${API_BASE_URL}/${post.data.banner.startsWith("/") ? post.data.banner.substring(1) : post.data.banner}`;
        } else if (post.data.banner && post.data.banner.startsWith("/")) {
          post.data.banner = `${API_BASE_URL}${post.data.banner}`;
        }

        console.log("Banner URL after processing:", post.data.banner);
        console.log("Content length:", post.data.content.length);

        renderedContent = processMarkdown(post.data.content);
        console.log("Rendered content length:", renderedContent.length);

        // Force content to be visible
        contentVisible = true;
      }
    } catch (err) {
      console.error("Error fetching blog post:", err);
      error = err instanceof Error ? err.message : "Unknown error";
    } finally {
      loading = false;
    }
  }

  // Process markdown content with marked and enhance images
  function processMarkdown(content: string): string {
    console.log("Processing markdown content of length:", content.length);

    // First enhance image syntax with custom HTML
    const enhancedContent = content.replace(
      /!\[(.*?)\]\((.*?)(?:\s+"(.*?)")?\)/g,
      (_match, alt, src, title) => {
        let absoluteSrc = src;

        // Verificar si la URL de la imagen es relativa o absoluta
        if (src && !src.startsWith("http") && !src.startsWith("/")) {
          // Si es relativa (e.g., "uploads/image.jpg"), prefijar con API_BASE_URL
          console.log("Markdown image with relative path detected:", src);
          absoluteSrc = `${API_BASE_URL}/${src}`;
        } else if (src && src.startsWith("/")) {
          // Si es absoluta al servidor (e.g., "/uploads/image.jpg"), prefijar con API_BASE_URL
          console.log(
            "Markdown image with server-relative path detected:",
            src
          );
          absoluteSrc = `${API_BASE_URL}${src}`;
        }

        console.log("Image source converted from", src, "to", absoluteSrc);

        return `<figure class="my-6">
          <div class="shadow-md rounded-md overflow-hidden">
            <div class="w-full image-container" style="aspect-ratio: 16/9;">
              <img src="${absoluteSrc}" alt="${alt || ""}" ${title ? `title="${title}"` : ""} class="w-full h-full object-cover" loading="lazy" />
            </div>
          </div>
          ${title ? `<figcaption class="mt-2 text-primary-600 text-sm text-center">${title}</figcaption>` : ""}
        </figure>`;
      }
    );

    // Count how many images were processed
    const originalImageCount = (
      content.match(/!\[(.*?)\]\((.*?)(?:\s+"(.*?)")?\)/g) || []
    ).length;
    const enhancedImageCount = (
      enhancedContent.match(/<figure class="my-6">/g) || []
    ).length;
    console.log(
      `Processed ${originalImageCount} markdown images into ${enhancedImageCount} HTML figures`
    );

    // Configure marked options
    marked.setOptions({
      gfm: true,
      breaks: true,
    });

    // Parse markdown to HTML
    const parsedContent = marked.parse(enhancedContent) as string;

    // Count HTML images after parsing
    const finalImageCount = (parsedContent.match(/<img/g) || []).length;
    console.log(`Final HTML contains ${finalImageCount} img tags`);

    // Log para depuración
    console.log(
      "Contenido markdown procesado (first 200 chars):",
      parsedContent.substring(0, 200) + "..."
    );

    return parsedContent;
  }

  onMount(() => {
    // Set the slug from the URL on the client side
    if (typeof window !== "undefined") {
      slug = window.location.pathname.split("/").pop() || "";
      console.log("Slug set from URL:", slug);
    }

    fetchBlogPost();
    setupIntersectionObserver();
  });
</script>

<svelte:head>
  {#if post}
    <title>{post.data.title} - Baberrih Blog</title>
    <meta
      name="description"
      content={post.data.content.substring(0, 160).replace(/[#*[\]()]/g, "")}
    />
  {:else}
    <title>Blog Post - Baberrih Hotel</title>
    <meta
      name="description"
      content="Read our latest blog post from Baberrih Hotel in Essaouira, Morocco."
    />
  {/if}
</svelte:head>

<div class="mx-auto p-4 container">
  <div class="mb-6">
    <a
      href="/blog"
      class="flex items-center text-primary-600 hover:text-primary-800 text-sm transition-colors"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="mr-1 w-4 h-4"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <polyline points="15 18 9 12 15 6"></polyline>
      </svg>
      Back to Blog
    </a>
  </div>

  {#if loading}
    <div class="flex justify-center items-center py-12">
      <LoadingSpinner size="2rem" />
    </div>
  {:else if error}
    <ErrorMessage message={error} />
  {:else if post}
    <article>
      <!-- Banner Image -->
      <div
        class="shadow-md mb-8 rounded-md overflow-hidden"
        style="height: 400px; width: 100%;"
      >
        <SimpleImage
          src={post.data.banner}
          alt={post.data.title}
          className="w-full h-full"
          fetchpriority="high"
        />
      </div>

      <!-- Post Header -->
      <header class="mb-8">
        <h1 class="mb-4 font-bold text-3xl md:text-4xl">{post.data.title}</h1>
        <p class="text-primary-600 text-sm">
          Published on {formatDate(post.published_at)}
        </p>
      </header>

      <!-- Post Content -->
      <div
        id="content-section"
        class="prose prose-primary max-w-none scroll-animation {contentVisible
          ? 'visible'
          : ''}"
        style="--delay: 100ms;"
      >
        {@html renderedContent}
      </div>

      <style>
        /* Estilos para las imágenes en el contenido markdown */
        :global(.prose img) {
          display: block;
          max-width: 100%;
          height: auto;
          border-radius: 0.375rem;
          box-shadow:
            0 4px 6px -1px rgba(0, 0, 0, 0.1),
            0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        :global(.prose figure) {
          margin: 1.5rem 0;
        }

        :global(.prose figcaption) {
          font-size: 0.875rem;
          color: var(--color-primary-600);
          text-align: center;
          margin-top: 0.5rem;
        }

        :global(.image-container) {
          position: relative;
          overflow: hidden;
          width: 100%;
        }

        :global(.image-container img) {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        /* Additional styles for better content display */
        :global(.prose) {
          font-size: 1.125rem;
          line-height: 1.75;
          color: #374151;
        }

        :global(.prose h1) {
          font-size: 2.25rem;
          font-weight: 700;
          margin-top: 2rem;
          margin-bottom: 1rem;
          color: #111827;
        }

        :global(.prose h2) {
          font-size: 1.875rem;
          font-weight: 600;
          margin-top: 1.75rem;
          margin-bottom: 0.75rem;
          color: #1f2937;
        }

        :global(.prose h3) {
          font-size: 1.5rem;
          font-weight: 600;
          margin-top: 1.5rem;
          margin-bottom: 0.5rem;
          color: #1f2937;
        }

        :global(.prose p) {
          margin-top: 1rem;
          margin-bottom: 1rem;
        }

        :global(.prose a) {
          color: #2563eb;
          text-decoration: underline;
          text-underline-offset: 2px;
        }

        :global(.prose a:hover) {
          color: #1d4ed8;
        }

        :global(.prose ul) {
          list-style-type: disc;
          margin-top: 1rem;
          margin-bottom: 1rem;
          padding-left: 1.5rem;
        }

        :global(.prose ol) {
          list-style-type: decimal;
          margin-top: 1rem;
          margin-bottom: 1rem;
          padding-left: 1.5rem;
        }

        :global(.prose li) {
          margin-top: 0.5rem;
          margin-bottom: 0.5rem;
        }

        :global(.prose blockquote) {
          border-left: 4px solid #e5e7eb;
          padding-left: 1rem;
          font-style: italic;
          margin-top: 1.5rem;
          margin-bottom: 1.5rem;
          color: #4b5563;
        }

        :global(.prose code) {
          font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
            "Liberation Mono", "Courier New", monospace;
          font-size: 0.875em;
          background-color: #f3f4f6;
          padding: 0.2em 0.4em;
          border-radius: 0.25rem;
        }

        :global(.prose pre) {
          background-color: #1f2937;
          color: #f9fafb;
          padding: 1rem;
          border-radius: 0.375rem;
          overflow-x: auto;
          margin-top: 1.5rem;
          margin-bottom: 1.5rem;
        }

        :global(.prose pre code) {
          background-color: transparent;
          padding: 0;
          font-size: 0.875em;
          color: inherit;
        }
      </style>

      <!-- Share Links -->
      <div class="mt-12 pt-6 border-primary-100 border-t">
        <h3 class="mb-4 font-light text-lg">Share this article</h3>
        <div class="flex space-x-4">
          <a
            href={`https://twitter.com/intent/tweet?text=${encodeURIComponent(post.data.title)}&url=${encodeURIComponent(`${API_BASE_URL}/blog/${post.data.slug}`)}`}
            target="_blank"
            rel="noopener noreferrer"
            class="text-primary-800 hover:text-primary-600 transition-colors"
            aria-label="Share on Twitter"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-5 h-5"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path
                d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"
              ></path>
            </svg>
          </a>
          <a
            href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(`${API_BASE_URL}/blog/${post.data.slug}`)}`}
            target="_blank"
            rel="noopener noreferrer"
            class="text-primary-800 hover:text-primary-600 transition-colors"
            aria-label="Share on Facebook"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-5 h-5"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path
                d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"
              ></path>
            </svg>
          </a>
          <a
            href={`mailto:?subject=${encodeURIComponent(post.data.title)}&body=${encodeURIComponent(`Check out this article from Baberrih Hotel: ${API_BASE_URL}/blog/${post.data.slug}`)}`}
            class="text-primary-800 hover:text-primary-600 transition-colors"
            aria-label="Share via Email"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="w-5 h-5"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path
                d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
              ></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
          </a>
        </div>
      </div>
    </article>
  {:else}
    <div class="py-8 text-center">
      <p class="text-primary-700">Blog post not found.</p>
    </div>
  {/if}
</div>
