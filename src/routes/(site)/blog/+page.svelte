<script lang="ts">
  import { onMount } from "svelte";
  import { goto } from "$app/navigation";
  import LoadingSpinner from "$lib/components/ui/LoadingSpinner.svelte";
  import ErrorMessage from "$lib/components/ui/ErrorMessage.svelte";
  import SimpleImage from "$lib/components/ui/SimpleImage.svelte";

  // Types
  interface BlogPost {
    id: string;
    status: string;
    created_at: string;
    updated_at: string;
    published_at: string;
    data: {
      slug: string;
      title: string;
      banner: string;
      content: string;
    };
  }

  // State variables
  let posts = $state<BlogPost[]>([]);
  let loading = $state(true);
  let error = $state<string | null>(null);

  // Format date for display
  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }).format(date);
  }

  // Extract excerpt from markdown content
  function getExcerpt(content: string, maxLength: number = 150): string {
    // Remove markdown headings, links, images, etc.
    const plainText = content
      .replace(/#+\s+(.*)/g, "$1") // Remove headings
      .replace(/\[([^\]]+)\]\([^)]+\)/g, "$1") // Remove links
      .replace(/!\[[^\]]*\]\([^)]+\)/g, "") // Remove images
      .replace(/(\*\*|__)(.*?)\1/g, "$2") // Remove bold
      .replace(/(\*|_)(.*?)\1/g, "$2") // Remove italic
      .replace(/~~(.*?)~~/g, "$1") // Remove strikethrough
      .replace(/```[\s\S]*?```/g, "") // Remove code blocks
      .replace(/`([^`]+)`/g, "$1") // Remove inline code
      .replace(/\n/g, " ") // Replace newlines with spaces
      .replace(/\s+/g, " ") // Replace multiple spaces with a single space
      .trim();

    // Return excerpt with ellipsis if needed
    if (plainText.length <= maxLength) {
      return plainText;
    }

    return plainText.substring(0, maxLength) + "...";
  }

  // Navigate to blog post detail page
  function goToBlogPost(slug: string): void {
    goto(`/blog/${slug}`);
  }

  // Fetch blog posts
  async function fetchBlogPosts(): Promise<void> {
    try {
      loading = true;
      error = null;

      console.log("Fetching blog posts");

      // Use the correct port for the API
      const apiUrl = "http://localhost:5174/api/blog";
      console.log("API URL:", apiUrl);

      const response = await fetch(apiUrl);
      console.log("Response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response:", errorText);
        throw new Error(`Error fetching blog posts: ${response.statusText}`);
      }

      const responseText = await response.text();
      console.log(
        "Response text (first 200 chars):",
        responseText.substring(0, 200)
      );

      // Try to parse the response as JSON
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error("Error parsing JSON:", parseError);
        throw new Error("Invalid JSON response from API");
      }

      if (!data.success) {
        throw new Error(data.error?.message || "Unknown error");
      }

      console.log("Blog posts received:", data.posts ? data.posts.length : 0);
      posts = data.posts;

      // Process banner URLs for each post
      if (posts && posts.length > 0) {
        posts.forEach((post) => {
          // Ensure banner URL is absolute if it's not already
          if (post.data.banner && !post.data.banner.startsWith("http")) {
            if (post.data.banner.startsWith("/")) {
              post.data.banner = `http://localhost:5174${post.data.banner}`;
            } else {
              post.data.banner = `http://localhost:5174/${post.data.banner}`;
            }
          }
          console.log(
            `Processed banner URL for "${post.data.title}":`,
            post.data.banner
          );
        });
      }
    } catch (err) {
      console.error("Error fetching blog posts:", err);
      error = err instanceof Error ? err.message : "Unknown error";
    } finally {
      loading = false;
    }
  }

  onMount(() => {
    fetchBlogPosts();
  });
</script>

<svelte:head>
  <title>Blog - Baberrih Hotel</title>
  <meta
    name="description"
    content="Explore our blog for insights about Essaouira, travel tips, and updates from Baberrih Hotel."
  />
</svelte:head>

<div class="mx-auto p-4 container">
  <h1 class="mb-8 font-bold text-3xl">Baberrih Blog</h1>

  {#if loading}
    <div class="flex justify-center items-center py-12">
      <LoadingSpinner size="2rem" />
    </div>
  {:else if error}
    <ErrorMessage message={error} />
  {:else if posts.length === 0}
    <div class="py-8 text-center">
      <p class="text-primary-700">
        No blog posts found. Check back soon for new content!
      </p>
    </div>
  {:else}
    <div class="blog-grid">
      {#each posts as post}
        <div
          class="blog-card"
          onclick={() => goToBlogPost(post.data.slug)}
          onkeydown={(e) => e.key === "Enter" && goToBlogPost(post.data.slug)}
          tabindex="0"
          role="link"
          aria-label={post.data.title}
        >
          <div class="blog-image-container">
            <SimpleImage
              src={post.data.banner}
              alt={post.data.title}
              className="blog-image"
              loading="lazy"
            />
          </div>
          <div class="blog-content">
            <h2 class="blog-title">{post.data.title}</h2>
            <p class="blog-date">{formatDate(post.published_at)}</p>
            <p class="blog-excerpt">{getExcerpt(post.data.content, 120)}</p>
            <div class="blog-read-more">Read More →</div>
          </div>
        </div>
      {/each}
    </div>
  {/if}
</div>

<style>
  .blog-card {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .blog-card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .blog-image-container {
    width: 100%;
    height: 200px;
    overflow: hidden;
    position: relative;
  }

  /* Removed unused CSS selector */

  .blog-content {
    padding: 16px;
    background-color: #f9fafb;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  .blog-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 8px;
    text-transform: uppercase;
    color: #1f2937;
  }

  .blog-date {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 8px;
  }

  .blog-excerpt {
    font-size: 14px;
    color: #4b5563;
    margin-bottom: 16px;
    line-height: 1.5;
    flex-grow: 1;
  }

  .blog-read-more {
    font-size: 12px;
    color: #4f46e5;
    text-align: right;
    transition: color 0.3s ease;
  }

  .blog-read-more:hover {
    color: #4338ca;
  }

  .blog-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 24px;
  }

  @media (min-width: 640px) {
    .blog-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .blog-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }
</style>
