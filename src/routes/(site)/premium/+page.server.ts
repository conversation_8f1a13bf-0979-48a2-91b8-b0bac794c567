import { requireSubscription } from '$lib/stripe';
import type { PageServerLoad } from './$types';

/**
 * Este archivo protege la ruta /premium para que solo sea accesible
 * para usuarios con una suscripción activa.
 */
export const load = (async (event) => {
  // Verificar si el usuario tiene una suscripción activa
  // Si no tiene una suscripción activa, será redirigido a /products
  await requireSubscription(event);

  // Si llega aquí, el usuario tiene una suscripción activa
  return {
    premium: true,
    // Pasar la sesión y el usuario desde event.locals
    session: event.locals.session,
    user: event.locals.user
  };
}) satisfies PageServerLoad; 