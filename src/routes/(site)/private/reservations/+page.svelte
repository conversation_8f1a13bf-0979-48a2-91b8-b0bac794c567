<script lang="ts">
  import ReservationList from '$lib/components/reservations/ReservationList.svelte';
  import { T } from '$lib/i18n';
  
  let { data } = $props();
  let { reservations, error, user } = $derived(data);
</script>

<svelte:head>
  <title>Mis Reservas - Baberrih</title>
</svelte:head>

<div class="space-y-6">
  <div class="flex justify-between items-center">
    <h1 class="font-montserrat font-medium text-2xl text-primary-900">
      <T key="reservations.myReservations" />
    </h1>
    
    <a 
      href="/accommodation/reservations" 
      class="bg-primary-700 hover:bg-primary-800 text-white font-montserrat py-2 px-4 rounded-sm transition-colors text-sm"
    >
      <T key="reservations.newReservation" />
    </a>
  </div>
  
  <div class="bg-primary-50 p-4 rounded-sm border border-primary-200">
    <p class="text-primary-700">
      <T key="reservations.welcomeMessage" values={{ name: user?.email?.split('@')[0] || 'Usuario' }} />
    </p>
  </div>
  
  <ReservationList 
    {reservations} 
    {error} 
    emptyMessage="No tienes reservas activas. ¡Haz tu primera reserva ahora!"
  />
</div>
