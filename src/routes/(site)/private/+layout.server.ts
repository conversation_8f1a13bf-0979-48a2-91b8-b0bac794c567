// Este archivo es necesario para asegurar que todas las rutas en el directorio 'private'
// estén protegidas. Hace que las rutas en este directorio sean rutas dinámicas,
// lo que envía una solicitud al servidor y activa hooks.server.ts.
import type { LayoutServerLoad } from './$types';
import { redirect } from '@sveltejs/kit';

export const load = (async (event) => {
  // Verificar si el usuario está autenticado
  if (!event.locals.session || !event.locals.user) {
    throw redirect(303, '/auth');
  }

  // Si llega aquí, el usuario está autenticado
  return {
    // Pasar la sesión y el usuario desde event.locals
    session: event.locals.session,
    user: event.locals.user
  };
}) satisfies LayoutServerLoad;