<!--
  Página de éxito de checkout
  Esta página se muestra cuando el pago se ha completado con éxito.
-->
<script lang="ts">
  import { page } from "$app/stores";
  import { onMount } from "svelte";
  import { CheckCircle } from "lucide-svelte";

  // Obtener el ID de la sesión de los parámetros de la URL
  $: sessionId = $page.url.searchParams.get("session_id");

  let customerEmail = "";
  let loading = true;
  let error = false;

  // Cargar los detalles de la sesión
  onMount(async () => {
    if (!sessionId) {
      loading = false;
      return;
    }

    try {
      // Aquí podrías hacer una llamada a tu API para obtener más detalles sobre la sesión
      // Por ahora, simplemente simulamos una carga
      await new Promise((resolve) => setTimeout(resolve, 1000));

      customerEmail = "<EMAIL>"; // Esto vendría de tu API
      loading = false;
    } catch (err) {
      console.error("Error al cargar los detalles de la sesión:", err);
      error = true;
      loading = false;
    }
  });
</script>

<svelte:head>
  <title>Pago completado - Mi SaaS</title>
</svelte:head>

<div class="mx-auto px-4 py-16 container">
  <div class="mx-auto max-w-lg text-center">
    <div class="flex justify-center mb-6">
      <CheckCircle class="w-16 h-16 text-success" />
    </div>

    <h1 class="mb-4 font-bold text-3xl">¡Pago completado con éxito!</h1>

    {#if loading}
      <p class="mb-8 text-lg">Cargando detalles de tu compra...</p>
    {:else if error}
      <p class="mb-8 text-lg">
        Ha ocurrido un error al cargar los detalles de tu compra.
      </p>
    {:else}
      <p class="mb-8 text-lg">
        Gracias por tu compra. Hemos enviado un correo electrónico de
        confirmación a
        <strong>{customerEmail}</strong>.
      </p>

      <p class="mb-8 text-lg">
        Tu ID de sesión es: <code class="bg-base-200 px-2 py-1 rounded"
          >{sessionId}</code
        >
      </p>
    {/if}

    <div class="flex justify-center gap-4">
      <a href="/products" class="btn btn-primary">Ver más productos</a>
      <a href="/" class="btn-outline btn">Volver al inicio</a>
    </div>
  </div>
</div>
