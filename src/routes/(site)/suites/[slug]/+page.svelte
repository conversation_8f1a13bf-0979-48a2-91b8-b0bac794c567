<script lang="ts">
  import { onMount } from "svelte";
  import SimpleSuiteDisplay from "$lib/components/cloudbeds/SimpleSuiteDisplay.svelte";

  // Get the slug from the URL (using the route parameter)
  let { data } = $props();
  const slug = $derived(data.slug);

  // State
  let loading = $state(true);
  let error = $state<string | null>(null);
  let suite = $state<any | null>(null);

  // Load suite data directly
  async function loadSuiteData() {
    try {
      loading = true;
      error = null;

      console.log("Suite detail page: Loading suite data for slug:", slug);

      // Fetch the suite data directly using the slug
      const response = await fetch(`/api/suites?slug=${slug}`);
      console.log("Suite detail page: API response status:", response.status);

      const result = await response.json();
      console.log("Suite detail page: API response data:", result);

      if (!response.ok) {
        throw new Error(result.error || 'Failed to load suite data');
      }

      if (!result.success || !result.data) {
        throw new Error('Suite not found');
      }

      // Store the suite data
      suite = result.data;

      console.log("Suite detail page: Suite data loaded:", {
        id: suite.id,
        name: suite.name,
        slug: suite.slug,
        site_id: suite.site_id,
        features: typeof suite.features === 'string' ? 'string to parse' : Array.isArray(suite.features) ? suite.features.length + ' items' : 'not an array',
        amenities: typeof suite.amenities === 'string' ? 'string to parse' : Array.isArray(suite.amenities) ? suite.amenities.length + ' items' : 'not an array'
      });
    } catch (err) {
      console.error("Error loading suite data:", err);
      error = err instanceof Error ? err.message : 'An error occurred while loading suite data';
    } finally {
      loading = false;
    }
  }

  onMount(() => {
    loadSuiteData();
  });
</script>

<svelte:head>
  <title>{suite?.name || "Suite"} - Baberrih Hotel</title>
  <meta
    name="description"
    content={suite?.description ||
      "Discover our luxurious suites at Baberrih Hotel in Essaouira, Morocco."}
  />
</svelte:head>

<div class="mx-auto px-4 py-8 max-w-4xl container">
  {#if loading}
    <div class="p-8 text-center">
      <div
        class="inline-block border-4 border-gray-300 border-t-blue-600 rounded-full w-8 h-8 animate-spin"
      ></div>
      <p class="mt-2 text-gray-600">Loading suite information...</p>
    </div>
  {:else if error}
    <div class="p-8 text-center">
      <p class="text-red-600">{error}</p>
    </div>
  {:else if suite}
    <!-- Use the SimpleSuiteDisplay component with suite data -->
    <SimpleSuiteDisplay suiteData={suite} />
  {:else}
    <div class="flex justify-center items-center min-h-[50vh]">
      <p class="font-montserrat font-light text-primary-900 text-xl">
        Suite not found
      </p>
    </div>
  {/if}
</div>
