import { createClient } from '$lib/supabase';
import { redirect } from '@sveltejs/kit';
import type { LayoutLoad } from './$types';

export const load: LayoutLoad = async ({ fetch, depends, data }) => {
  // Declarar una dependencia para que el layout se invalide cuando cambie la sesión
  depends('supabase:auth');

  // Crear el cliente de Supabase para el navegador
  const supabase = createClient();

  // Obtener la sesión actual
  const {
    data: { session }
  } = await supabase.auth.getSession();

  // Inicializar el usuario como null
  let user = null;

  // Si hay una sesión, obtener el usuario autenticado
  if (session) {
    const { data: userData, error } = await supabase.auth.getUser();

    // Si no hay error y hay datos de usuario, asignar el usuario
    if (!error && userData) {
      user = userData.user;
    }
  }

  return {
    supabase,
    session,
    user
  };
};
