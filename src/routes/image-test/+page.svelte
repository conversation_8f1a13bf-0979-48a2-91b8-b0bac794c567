<script lang="ts">
  import OptimizedImage from "$lib/components/ui/OptimizedImage.svelte";
  import ImageErrorDebug from "$lib/components/debug/ImageErrorDebug.svelte";
  import { onMount } from "svelte";
  
  // Test images
  const testImages = [
    {
      name: "Unsplash Image (Should Work)",
      url: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&w=1000&auto=format&fit=crop"
    },
    {
      name: "Baberrih Image 1 (May Fail)",
      url: "https://baberrih.ma/media/restaurant/1_f.webp"
    },
    {
      name: "Baberrih Image 2 (May Fail)",
      url: "https://baberrih.ma/media/hero/1_f.webp"
    },
    {
      name: "Non-existent Image (Should Fail)",
      url: "https://example.com/non-existent-image.jpg"
    },
    {
      name: "Local Image (Should Work)",
      url: "/favicon.png"
    }
  ];
  
  // Manual image test
  let manualTestResults = $state<Record<string, boolean>>({});
  
  // Test image loading
  async function testImageLoading(url: string): Promise<boolean> {
    return new Promise((resolve) => {
      const img = new Image();
      
      img.onload = () => {
        console.log(`Image loaded successfully: ${url}`);
        resolve(true);
      };
      
      img.onerror = () => {
        console.error(`Image failed to load: ${url}`);
        resolve(false);
      };
      
      // Set timeout to avoid hanging
      setTimeout(() => {
        console.warn(`Image load timeout: ${url}`);
        resolve(false);
      }, 5000);
      
      img.src = url;
    });
  }
  
  // Run manual tests
  onMount(async () => {
    console.log("Running manual image tests...");
    
    for (const image of testImages) {
      manualTestResults[image.url] = await testImageLoading(image.url);
    }
    
    console.log("Manual test results:", manualTestResults);
  });
</script>

<svelte:head>
  <title>Image Loading Test</title>
</svelte:head>

<div class="p-8 max-w-4xl mx-auto">
  <h1 class="text-2xl font-bold mb-6">Image Loading Test</h1>
  
  <div class="mb-8">
    <h2 class="text-xl font-semibold mb-4">Manual Test Results</h2>
    <div class="grid grid-cols-1 gap-4">
      {#each testImages as image}
        <div class="border p-4 rounded-md">
          <h3 class="font-medium">{image.name}</h3>
          <p class="text-sm text-gray-500 mb-2">{image.url}</p>
          
          <div class="flex items-center mb-4">
            <span class="mr-2">Status:</span>
            {#if image.url in manualTestResults}
              {#if manualTestResults[image.url]}
                <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Loaded</span>
              {:else}
                <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs">Failed</span>
              {/if}
            {:else}
              <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs">Testing...</span>
            {/if}
          </div>
          
          <div class="h-40 bg-gray-100 rounded-md overflow-hidden">
            <OptimizedImage 
              src={image.url} 
              alt={image.name}
              aspectRatio="16/9"
            />
          </div>
        </div>
      {/each}
    </div>
  </div>
  
  <div class="mb-8">
    <h2 class="text-xl font-semibold mb-4">Network Test</h2>
    <p class="mb-4">Testing direct network access to images:</p>
    
    <div class="grid grid-cols-1 gap-4">
      {#each testImages as image}
        <div class="border p-4 rounded-md">
          <h3 class="font-medium">{image.name}</h3>
          <p class="text-sm text-gray-500 mb-2">{image.url}</p>
          
          <div class="flex items-center">
            <a href={image.url} target="_blank" rel="noopener noreferrer" class="bg-blue-100 text-blue-800 px-3 py-1 rounded-md text-sm">
              Open Direct URL
            </a>
          </div>
        </div>
      {/each}
    </div>
  </div>
  
  <div class="mb-8">
    <h2 class="text-xl font-semibold mb-4">CORS Test</h2>
    <p>Testing if CORS is the issue:</p>
    
    <div class="mt-4">
      <button 
        class="bg-blue-500 text-white px-4 py-2 rounded-md"
        onclick={() => {
          testImages.forEach(image => {
            fetch(image.url, { mode: 'no-cors' })
              .then(() => console.log(`CORS test passed for ${image.url}`))
              .catch(err => console.error(`CORS test failed for ${image.url}:`, err));
          });
        }}
      >
        Run CORS Test
      </button>
    </div>
  </div>
</div>

<!-- Image Error Debug Component -->
<ImageErrorDebug position="bottom-right" showOnLoad={true} />
