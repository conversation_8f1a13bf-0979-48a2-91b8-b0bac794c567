<script lang="ts">
  // State
  let activeTab = $state('tab1');

  // Set active tab
  function setActiveTab(tab) {
    console.log(`Setting active tab to: ${tab}`);
    activeTab = tab;
  }
</script>

<div class="container mx-auto p-8">
  <h1 class="text-2xl font-bold mb-6">Tab Test Page</h1>

  <!-- Tabs Navigation -->
  <div class="mb-8 border-b border-gray-200">
    <div class="flex flex-wrap -mb-px">
      <button
        type="button"
        class={`inline-block py-4 px-6 border-b-2 font-medium text-sm transition-all duration-200 ${activeTab === 'tab1' ? 'border-blue-600 text-blue-800' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-200'}`}
        onclick={() => setActiveTab('tab1')}
      >
        Tab 1
      </button>
      <button
        type="button"
        class={`inline-block py-4 px-6 border-b-2 font-medium text-sm transition-all duration-200 ${activeTab === 'tab2' ? 'border-blue-600 text-blue-800' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-200'}`}
        onclick={() => setActiveTab('tab2')}
      >
        Tab 2
      </button>
      <button
        type="button"
        class={`inline-block py-4 px-6 border-b-2 font-medium text-sm transition-all duration-200 ${activeTab === 'tab3' ? 'border-blue-600 text-blue-800' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-200'}`}
        onclick={() => setActiveTab('tab3')}
      >
        Tab 3
      </button>
    </div>
  </div>

  <!-- Tab Content -->
  <div class="tab-content">
    {#if activeTab === 'tab1'}
      <div class="p-4 bg-gray-100 rounded">
        <h2 class="text-xl font-semibold mb-4">Tab 1 Content</h2>
        <p>This is the content for Tab 1.</p>
      </div>
    {:else if activeTab === 'tab2'}
      <div class="p-4 bg-gray-100 rounded">
        <h2 class="text-xl font-semibold mb-4">Tab 2 Content</h2>
        <p>This is the content for Tab 2.</p>
      </div>
    {:else if activeTab === 'tab3'}
      <div class="p-4 bg-gray-100 rounded">
        <h2 class="text-xl font-semibold mb-4">Tab 3 Content</h2>
        <p>This is the content for Tab 3.</p>
      </div>
    {/if}
  </div>

  <div class="mt-8">
    <p>Current active tab: <strong>{activeTab}</strong></p>
  </div>
</div>
