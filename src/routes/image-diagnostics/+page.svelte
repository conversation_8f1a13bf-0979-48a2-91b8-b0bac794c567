<script lang="ts">
  import { onMount } from "svelte";
  import OptimizedImage from "$lib/components/ui/OptimizedImage.svelte";
  import ImageErrorDebug from "$lib/components/debug/ImageErrorDebug.svelte";
  
  // Test URLs
  const testUrls = [
    "https://baberrih.ma/media/restaurant/1_f.webp",
    "https://baberrih.ma/media/restaurant/garden_dining.webp",
    "https://baberrih.ma/media/restaurant/beach_picnic.webp",
    "https://baberrih.ma/media/restaurant/cooking_class.webp",
    "https://baberrih.ma/media/hero/1_f.webp",
    "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4", // Control URL that should work
    "/favicon.png" // Local image that should work
  ];
  
  // Test results
  let testResults = $state<Record<string, any>>({});
  let networkResults = $state<Record<string, any>>({});
  let corsResults = $state<Record<string, any>>({});
  let dnsResults = $state<Record<string, any>>({});
  let isLoading = $state(true);
  
  // Test image loading with Image element
  async function testImageLoading(url: string): Promise<{success: boolean, error?: any}> {
    return new Promise((resolve) => {
      const img = new Image();
      
      img.onload = () => {
        resolve({
          success: true,
          dimensions: {
            width: img.naturalWidth,
            height: img.naturalHeight
          }
        });
      };
      
      img.onerror = (e) => {
        resolve({
          success: false,
          error: e
        });
      };
      
      // Set timeout to avoid hanging
      const timeout = setTimeout(() => {
        resolve({
          success: false,
          error: "Timeout"
        });
      }, 10000);
      
      // Set the src to start loading
      img.src = url;
    });
  }
  
  // Test network request with fetch
  async function testNetworkRequest(url: string): Promise<{success: boolean, status?: number, statusText?: string, error?: any}> {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-cache'
      });
      
      return {
        success: response.ok,
        status: response.status,
        statusText: response.statusText,
        type: response.type
      };
    } catch (error) {
      return {
        success: false,
        error: error
      };
    }
  }
  
  // Test CORS with XMLHttpRequest
  async function testCORS(url: string): Promise<{success: boolean, corsIssue: boolean, error?: any}> {
    return new Promise((resolve) => {
      const xhr = new XMLHttpRequest();
      
      xhr.onload = function() {
        resolve({
          success: true,
          corsIssue: false,
          status: xhr.status,
          statusText: xhr.statusText
        });
      };
      
      xhr.onerror = function(e) {
        // CORS errors typically trigger onerror without status information
        resolve({
          success: false,
          corsIssue: true,
          error: e
        });
      };
      
      xhr.open('GET', url, true);
      xhr.timeout = 5000;
      
      try {
        xhr.send();
      } catch (sendError) {
        resolve({
          success: false,
          corsIssue: false,
          error: sendError
        });
      }
    });
  }
  
  // Test DNS resolution
  async function testDNS(url: string): Promise<{success: boolean, error?: any}> {
    try {
      const hostname = new URL(url).hostname;
      
      // We can't directly test DNS in the browser, but we can make a request to the hostname
      const response = await fetch(`https://${hostname}/favicon.ico`, {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-cache'
      });
      
      return {
        success: true,
        hostname: hostname
      };
    } catch (error) {
      return {
        success: false,
        error: error
      };
    }
  }
  
  // Run all tests
  async function runTests() {
    isLoading = true;
    
    for (const url of testUrls) {
      console.log(`Running tests for ${url}`);
      
      // Test image loading
      testResults[url] = await testImageLoading(url);
      
      // Test network request
      networkResults[url] = await testNetworkRequest(url);
      
      // Test CORS
      corsResults[url] = await testCORS(url);
      
      // Test DNS
      if (url.startsWith('http')) {
        dnsResults[url] = await testDNS(url);
      }
    }
    
    isLoading = false;
  }
  
  // Initialize
  onMount(() => {
    runTests();
  });
</script>

<svelte:head>
  <title>Image Diagnostics</title>
</svelte:head>

<div class="p-8 max-w-6xl mx-auto">
  <h1 class="text-2xl font-bold mb-6">Image Loading Diagnostics</h1>
  
  {#if isLoading}
    <div class="p-4 bg-blue-100 text-blue-800 rounded-md mb-6">
      Running diagnostics... Please wait.
    </div>
  {:else}
    <div class="p-4 bg-green-100 text-green-800 rounded-md mb-6">
      Diagnostics complete. See results below.
    </div>
  {/if}
  
  <div class="mb-8">
    <h2 class="text-xl font-semibold mb-4">Test Results</h2>
    
    <div class="overflow-x-auto">
      <table class="min-w-full bg-white border border-gray-200">
        <thead>
          <tr>
            <th class="px-4 py-2 border">URL</th>
            <th class="px-4 py-2 border">Image Load</th>
            <th class="px-4 py-2 border">Network</th>
            <th class="px-4 py-2 border">CORS</th>
            <th class="px-4 py-2 border">DNS</th>
          </tr>
        </thead>
        <tbody>
          {#each testUrls as url}
            <tr>
              <td class="px-4 py-2 border">
                <div class="text-sm break-all">{url}</div>
              </td>
              <td class="px-4 py-2 border">
                {#if url in testResults}
                  {#if testResults[url].success}
                    <div class="text-green-600">✓ Success</div>
                    {#if testResults[url].dimensions}
                      <div class="text-xs text-gray-500">
                        {testResults[url].dimensions.width}x{testResults[url].dimensions.height}
                      </div>
                    {/if}
                  {:else}
                    <div class="text-red-600">✗ Failed</div>
                    {#if testResults[url].error}
                      <div class="text-xs text-gray-500">
                        {typeof testResults[url].error === 'string' ? testResults[url].error : 'Error object'}
                      </div>
                    {/if}
                  {/if}
                {:else}
                  <div class="text-gray-400">Testing...</div>
                {/if}
              </td>
              <td class="px-4 py-2 border">
                {#if url in networkResults}
                  {#if networkResults[url].success}
                    <div class="text-green-600">✓ Success</div>
                    <div class="text-xs text-gray-500">
                      Status: {networkResults[url].status}
                    </div>
                  {:else}
                    <div class="text-red-600">✗ Failed</div>
                    {#if networkResults[url].status}
                      <div class="text-xs text-gray-500">
                        Status: {networkResults[url].status}
                      </div>
                    {/if}
                    {#if networkResults[url].type === 'opaque'}
                      <div class="text-xs text-orange-500">
                        Opaque Response (CORS)
                      </div>
                    {/if}
                  {/if}
                {:else}
                  <div class="text-gray-400">Testing...</div>
                {/if}
              </td>
              <td class="px-4 py-2 border">
                {#if url in corsResults}
                  {#if corsResults[url].corsIssue}
                    <div class="text-orange-600">⚠ CORS Issue</div>
                  {:else if corsResults[url].success}
                    <div class="text-green-600">✓ No CORS Issue</div>
                  {:else}
                    <div class="text-red-600">✗ Failed (Not CORS)</div>
                  {/if}
                {:else}
                  <div class="text-gray-400">Testing...</div>
                {/if}
              </td>
              <td class="px-4 py-2 border">
                {#if url.startsWith('http') && url in dnsResults}
                  {#if dnsResults[url].success}
                    <div class="text-green-600">✓ Resolved</div>
                    <div class="text-xs text-gray-500">
                      {dnsResults[url].hostname}
                    </div>
                  {:else}
                    <div class="text-red-600">✗ Failed</div>
                  {/if}
                {:else if !url.startsWith('http')}
                  <div class="text-gray-400">N/A (Local)</div>
                {:else}
                  <div class="text-gray-400">Testing...</div>
                {/if}
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  </div>
  
  <div class="mb-8">
    <h2 class="text-xl font-semibold mb-4">Visual Test</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {#each testUrls as url}
        <div class="border p-4 rounded-md">
          <h3 class="font-medium mb-2 break-all text-sm">{url}</h3>
          
          <div class="h-40 bg-gray-100 rounded-md overflow-hidden">
            <OptimizedImage 
              src={url} 
              alt={`Test image: ${url}`}
              aspectRatio="16/9"
              fallbackSrc="/images/placeholder.svg"
            />
          </div>
        </div>
      {/each}
    </div>
  </div>
</div>

<!-- Image Error Debug Component -->
<ImageErrorDebug position="bottom-right" showOnLoad={true} />
