<script lang="ts">
  import { onMount } from "svelte";
  import OptimizedImage from "$lib/components/ui/OptimizedImage.svelte";
  import ImageErrorDebug from "$lib/components/debug/ImageErrorDebug.svelte";
  
  // Test URLs
  const testUrls = [
    // Baberrih.ma images (should use proxy)
    "https://baberrih.ma/media/restaurant/1_f.webp",
    "https://baberrih.ma/media/hero/1_f.webp",
    
    // Control images (should load directly)
    "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4", // Unsplash
    "/images/baberrih-placeholder.svg", // Local
    
    // Non-existent image (should show fallback)
    "https://example.com/non-existent-image.jpg"
  ];
  
  // Test results
  let proxyResults = $state<Record<string, any>>({});
  let directResults = $state<Record<string, any>>({});
  
  // Test direct image loading
  async function testDirectLoading(url: string): Promise<{success: boolean, error?: any}> {
    return new Promise((resolve) => {
      const img = new Image();
      
      img.onload = () => {
        resolve({
          success: true,
          dimensions: {
            width: img.naturalWidth,
            height: img.naturalHeight
          }
        });
      };
      
      img.onerror = (e) => {
        resolve({
          success: false,
          error: e
        });
      };
      
      // Set timeout to avoid hanging
      const timeout = setTimeout(() => {
        resolve({
          success: false,
          error: "Timeout"
        });
      }, 10000);
      
      // Set the src to start loading
      img.src = url;
    });
  }
  
  // Test proxy image loading
  async function testProxyLoading(url: string): Promise<{success: boolean, error?: any}> {
    const encodedUrl = encodeURIComponent(url);
    const proxyUrl = `/api/image-proxy?url=${encodedUrl}`;
    
    return testDirectLoading(proxyUrl);
  }
  
  // Run tests
  onMount(async () => {
    for (const url of testUrls) {
      // Test direct loading
      directResults[url] = await testDirectLoading(url);
      
      // Test proxy loading
      proxyResults[url] = await testProxyLoading(url);
    }
  });
</script>

<svelte:head>
  <title>Image Proxy Test</title>
</svelte:head>

<div class="p-8 max-w-6xl mx-auto">
  <h1 class="text-2xl font-bold mb-6">Image Proxy Test</h1>
  
  <div class="mb-8">
    <p class="mb-4">
      This page tests the image proxy solution for handling images from the baberrih.ma domain.
      It compares direct image loading with proxy-based loading for various image sources.
    </p>
  </div>
  
  <div class="mb-8">
    <h2 class="text-xl font-semibold mb-4">Test Results</h2>
    
    <div class="overflow-x-auto">
      <table class="min-w-full bg-white border border-gray-200">
        <thead>
          <tr>
            <th class="px-4 py-2 border">URL</th>
            <th class="px-4 py-2 border">Direct Loading</th>
            <th class="px-4 py-2 border">Proxy Loading</th>
          </tr>
        </thead>
        <tbody>
          {#each testUrls as url}
            <tr>
              <td class="px-4 py-2 border">
                <div class="text-sm break-all">{url}</div>
                <div class="text-xs text-gray-500 mt-1">
                  {url.includes('baberrih.ma') ? 'Baberrih Domain' : url.startsWith('/') ? 'Local Image' : 'External Domain'}
                </div>
              </td>
              <td class="px-4 py-2 border">
                {#if url in directResults}
                  {#if directResults[url].success}
                    <div class="text-green-600">✓ Success</div>
                    {#if directResults[url].dimensions}
                      <div class="text-xs text-gray-500">
                        {directResults[url].dimensions.width}x{directResults[url].dimensions.height}
                      </div>
                    {/if}
                  {:else}
                    <div class="text-red-600">✗ Failed</div>
                    <div class="text-xs text-gray-500">
                      {typeof directResults[url].error === 'string' ? directResults[url].error : 'Error object'}
                    </div>
                  {/if}
                {:else}
                  <div class="text-gray-400">Testing...</div>
                {/if}
              </td>
              <td class="px-4 py-2 border">
                {#if url in proxyResults}
                  {#if proxyResults[url].success}
                    <div class="text-green-600">✓ Success</div>
                    {#if proxyResults[url].dimensions}
                      <div class="text-xs text-gray-500">
                        {proxyResults[url].dimensions.width}x{proxyResults[url].dimensions.height}
                      </div>
                    {/if}
                  {:else}
                    <div class="text-red-600">✗ Failed</div>
                    <div class="text-xs text-gray-500">
                      {typeof proxyResults[url].error === 'string' ? proxyResults[url].error : 'Error object'}
                    </div>
                  {/if}
                {:else}
                  <div class="text-gray-400">Testing...</div>
                {/if}
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  </div>
  
  <div class="mb-8">
    <h2 class="text-xl font-semibold mb-4">Visual Test</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      {#each testUrls as url}
        <div class="border p-4 rounded-md">
          <h3 class="font-medium mb-2 break-all text-sm">{url}</h3>
          
          <div class="grid grid-cols-2 gap-4">
            <div>
              <h4 class="text-sm font-medium mb-2">Direct Loading</h4>
              <div class="h-40 bg-gray-100 rounded-md overflow-hidden">
                <img 
                  src={url} 
                  alt={`Direct: ${url}`}
                  class="w-full h-full object-cover"
                  onerror="this.onerror=null; this.src='/images/baberrih-placeholder.svg';"
                />
              </div>
            </div>
            
            <div>
              <h4 class="text-sm font-medium mb-2">Proxy Loading</h4>
              <div class="h-40 bg-gray-100 rounded-md overflow-hidden">
                <OptimizedImage 
                  src={url} 
                  alt={`Proxy: ${url}`}
                  aspectRatio="16/9"
                />
              </div>
            </div>
          </div>
        </div>
      {/each}
    </div>
  </div>
</div>

<!-- Image Error Debug Component -->
<ImageErrorDebug position="bottom-right" showOnLoad={true} />
