# Archivos y directorios de control de versiones
.git
.gitignore

# Directorios de dependencias
node_modules
.svelte-kit

# Archivos de construcción
build
.output

# Archivos de entorno (se cargarán a través de docker-compose)
# .env  # Comentado para permitir que .env sea copiado durante la construcción
.env.*
!.env.example

# Archivos de configuración del editor
.vscode
.idea
*.sublime-*
.cursor

# Archivos de logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*

# Archivos del sistema
.DS_Store
Thumbs.db

# Archivos Docker
Dockerfile
docker-compose.yml
.dockerignore

# Otros archivos que no son necesarios en la imagen
README.md
docs
scripts
*.md
*.txt 